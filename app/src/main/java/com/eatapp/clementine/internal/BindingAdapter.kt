package com.eatapp.clementine.internal

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.graphics.drawable.VectorDrawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.databinding.BindingAdapter
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.views.LoadingButton
import com.eatapp.clementine.views.StatusFilterView


@BindingAdapter("selected")
fun StatusFilterView.isSelected(selected: Boolean) {

    val selectedColor = ContextCompat.getColor(context!!, R.color.grey800)
    val unSelectedColor = ContextCompat.getColor(context!!, R.color.grey700)

    when (selected) {
        true -> {
            title.setTextColor(selectedColor)
            iconLeft.setColorFilter(selectedColor, PorterDuff.Mode.SRC_ATOP)
            iconRight.setColorFilter(selectedColor, PorterDuff.Mode.SRC_ATOP)
            bcg.setBackgroundResource(R.drawable.shape_pill_selected)
        }

        else -> {
            title.setTextColor(unSelectedColor)
            iconLeft.setColorFilter(unSelectedColor, PorterDuff.Mode.SRC_ATOP)
            iconRight.setColorFilter(unSelectedColor, PorterDuff.Mode.SRC_ATOP)
            bcg.setBackgroundResource(R.drawable.shape_pill_unselected)

        }
    }
}

@BindingAdapter("iconLeft")
fun StatusFilterView.setLeftIcon(drawable: Drawable) {
    if (drawable is VectorDrawable) {
        iconLeft.setImageDrawable(drawable)
    } else {
        iconLeft.visibility = View.GONE
    }
}

@BindingAdapter("iconRight")
fun StatusFilterView.setRightIcon(drawable: Drawable) {
    if (drawable is VectorDrawable) {
        iconRight.setImageDrawable(drawable)
    } else {
        iconRight.visibility = View.GONE
    }
}

@BindingAdapter("icon")
fun ImageView.setIcon(icon: Int) {
    if (icon != 0) {
        this.setImageResource(icon)
    }
}

@BindingAdapter("tintColor")
fun LoadingButton.setTintColor(color: Int) {
    if (color == -1) return
    title.setTextColor(ContextCompat.getColor(context, color))
    iconLeft.tintColor(color)
    iconRight.tintColor(color)
}

@BindingAdapter("disabled")
fun LoadingButton.setDisabled(disabled: Boolean) {
    state = when (disabled) {
        true -> LoadingButton.LoadingButtonState.Disabled
        else -> LoadingButton.LoadingButtonState.Available
    }
}

@BindingAdapter("loading")
fun LoadingButton.setLoading(loading: Boolean) {
    if (loading) {
        showLoading()
    } else {
        hideLoading()
    }
}

@BindingAdapter("backgroundTint")
fun View.setBackgroundTint(color: Int) {
    if (color == -1) return
    backgroundTintList = ContextCompat.getColorStateList(context, color)
}

@BindingAdapter("textColor")
fun TextView.textColor(color: Int) {
    if (color == -1) return
    setTextColor(ContextCompat.getColor(context, color))
}

@BindingAdapter("tintColor")
fun ImageView.tintColor(color: Int) {
    if (color == -1) return
    setColorFilter(ContextCompat.getColor(context, color), PorterDuff.Mode.SRC_ATOP)
}

@BindingAdapter("statusColor")
fun ConstraintLayout.statusColor(color: Int) {
    if (color == -1) return
    background.setColorFilter(color, PorterDuff.Mode.SRC_ATOP)
}

@BindingAdapter("statusColor")
fun ImageView.statusColor(color: Int) {
    if (color == -1) return
    setColorFilter(color, PorterDuff.Mode.SRC_IN)
}

@BindingAdapter("tintColor")
fun View.tintColor(color: Int) {
    if (color == -1) return
    background.setColorFilter(ContextCompat.getColor(context, color), PorterDuff.Mode.SRC_ATOP)
}

@BindingAdapter("backgroundColour")
fun View.setBackgroundColour(color: Int) {
    if (color == -1) return
    setBackgroundColor(ContextCompat.getColor(context, color))
}

@BindingAdapter("visible_or_gone")
fun View.setVisibleOrGone(visible: Boolean) {
    visibleOrGone = visible
}

@BindingAdapter("android:tagIcon")
fun setImageResource(imageView: ImageView, tag: SelectorItem) {
    if (tag.icon is Int) {
        imageView.setImageResource(tag.icon as Int)
    } else if (tag.icon is String) {
        imageView.setImageDrawable(
            TextDrawable(
                imageView.resources, imageView.context, tag.icon as String,
                imageView.resources.getDimension(R.dimen.abbreviation_large_text_size),
                imageView.resources.getDimension(R.dimen.abbreviation_text_position)
            )
        )
    }
    if (tag.value is Tagging) {
        imageView.colorFilter = PorterDuffColorFilter(
            ((tag.value as Tagging)
                .color ?: "#808080").toColorInt(), PorterDuff.Mode.SRC_ATOP
        )
    } else if (tag.value is Tag) {
        imageView.colorFilter = PorterDuffColorFilter(
            ((tag.value as Tag)
                .category?.color ?: "#808080").toColorInt(), PorterDuff.Mode.SRC_ATOP
        )
    }
}