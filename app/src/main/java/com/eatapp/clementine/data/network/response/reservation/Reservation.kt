package com.eatapp.clementine.data.network.response.reservation

import android.os.Parcelable
import android.util.Log
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.eatapp.clementine.R
import com.eatapp.clementine.data.database.converters.AttributesConverter
import com.eatapp.clementine.data.database.converters.ChannelConverter
import com.eatapp.clementine.data.database.converters.DateTypeConverter
import com.eatapp.clementine.data.database.converters.GuestConverter
import com.eatapp.clementine.data.database.converters.PaymentsConverter
import com.eatapp.clementine.data.database.converters.PermissionConverter
import com.eatapp.clementine.data.database.converters.RelationshipsConverter
import com.eatapp.clementine.data.database.converters.TablesConverter
import com.eatapp.clementine.data.database.converters.TaggingsConverter
import com.eatapp.clementine.data.database.converters.VoucherAssignmentConverter
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.response.comment.Comment
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.addTimeInterval
import com.eatapp.clementine.internal.durationFormatted
import com.eatapp.clementine.internal.eatDateISO8601TimezoneDateFormatter
import com.eatapp.clementine.internal.formatNonLocal
import com.eatapp.clementine.internal.hasPassedNow
import com.eatapp.clementine.internal.managers.TimerType
import com.eatapp.clementine.internal.secondsSinceNow
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import kotlin.math.abs

@Entity(tableName = "reservation_table")
@TypeConverters(
    AttributesConverter::class,
    PermissionConverter::class,
    GuestConverter::class,
    ChannelConverter::class,
    TablesConverter::class,
    TaggingsConverter::class,
    PaymentsConverter::class,
    DateTypeConverter::class,
    RelationshipsConverter::class,
    VoucherAssignmentConverter::class
)
@Parcelize
data class Reservation(

    @PrimaryKey
    @SerializedName("id")
    var id: String,
    @SerializedName("type")
    var type: String,

    @SerializedName("relationships")
    val relationships: ReservationRelationships?,
    @SerializedName("attributes")
    var attributes: ReservationAttributes,

    var guest: Guest?,
    var channel: Channel?,
    var taggings: MutableList<Tagging>?,
    var tablesList: List<Table>?,
    var paymentsList: MutableList<Payment>?,
    var voucherAssignments: MutableList<VoucherAssignment>? = null

) : Parcelable, BaseObservable() {

    @IgnoredOnParcel
    var dbStartTime: Date? = null

    @IgnoredOnParcel
    var dbRestaurantId: String? = null

    @IgnoredOnParcel
    var isHeader: Boolean = false

    @IgnoredOnParcel
    @Ignore
    var sendMessage: Boolean = false

    @IgnoredOnParcel
    var timerPermission: Permission? = null

    @IgnoredOnParcel
    @Ignore
    var lastOpenedAt: Date? = null

    @IgnoredOnParcel
    val showWaitQuote: Boolean
        @Bindable get() {
            return attributes.status == Status.Value.WAITLIST.code
        }

    constructor(date: Date?) : this(
        "", "", null, ReservationAttributes(date),
        null, null, mutableListOf(), null, null
    )

    var status: String
        get() {
            return attributes.status
        }
        set(v) {
            attributes.status = v
            notifyPropertyChanged(BR.statusTitle)
            notifyPropertyChanged(BR.statusColor)
            notifyPropertyChanged(BR.statusIcon)
            notifyPropertyChanged(BR.showWaitQuote)
            notifyPropertyChanged(BR.dateS)
            notifyPropertyChanged(BR.timeHourS)
            notifyPropertyChanged(BR.timeMarkerS)
            notifyPropertyChanged(BR.tableS)
        }

    val statusTitle: String
        @Bindable get() {
            return Status.getTitle(attributes.status)
        }

    val statusIcon: Int
        @Bindable get() {
            return Status.getIcon(attributes.status)
        }

    val statusColor: Int
        @Bindable get() {
            return Status.getColor(attributes.status)
        }

    var tables: List<Table>?
        get() {
            return tablesList
        }
        set(v) {
            tablesList = v
            notifyPropertyChanged(BR.tableS)
        }

    var payments: MutableList<Payment>
        get() {
            return paymentsList ?: mutableListOf()
        }
        set(v) {
            paymentsList = v
        }

    var startTime: Date
        get() {
            return attributes.startTime
        }
        set(v) {
            attributes.startTime = v
            notifyPropertyChanged(BR.dateS)
            notifyPropertyChanged(BR.timeHourS)
            notifyPropertyChanged(BR.timeMarkerS)
        }

    val endTime: Date
        get() {
            return attributes.startTime.addTimeInterval(Calendar.SECOND, attributes.duration)
        }

    var waitlistQueuedAt: Date?
        get() {
            return attributes.waitlistQueuedAt
        }
        set(v) {
            attributes.waitlistQueuedAt = v
            notifyPropertyChanged(BR.dateS)
            notifyPropertyChanged(BR.timeHourS)
            notifyPropertyChanged(BR.timeMarkerS)
        }

    var waitlistTableReadyAt: String?
        get() {
            return if (attributes.waitlistTableReadyAt != null) {
                eatDateISO8601TimezoneDateFormatter().parse(attributes.waitlistTableReadyAt!!)
                    ?.let { SimpleDateFormat("hh:mm a", Locale.US).format(it) }
            } else null
        }
        set(v) {
            attributes.waitlistTableReadyAt = v
        }

    val tableS: String
        @Bindable get() {
            return tables?.joinToString { it.number } ?: ""
        }

    val tablesS: String?
        @Bindable get() {
            return if (tables?.isEmpty() == true) "--" else tables?.joinToString { it.number }
        }

    val dateS: String?
        @Bindable get() {
            val date = when (attributes.status == Status.Value.WAITLIST.code) {
                true -> attributes.waitlistQueuedAt
                else -> attributes.startTime
            } ?: Date()

            return SimpleDateFormat("EEE, dd MMMM yyyy", Locale.US).format(date)
        }

    val dateShortS: String
        @Bindable get() {
            val date = when (attributes.status == Status.Value.WAITLIST.code) {
                true -> attributes.waitlistQueuedAt
                else -> attributes.startTime
            } ?: Date()

            return SimpleDateFormat("d MMM yy", Locale.US).format(date)
        }

    val dateSs: String?
        @Bindable get() {
            val date = when (attributes.status == Status.Value.WAITLIST.code) {
                true -> attributes.waitlistQueuedAt
                else -> attributes.startTime
            } ?: Date()

            return SimpleDateFormat("EEE, d MMM yyyy", Locale.US).format(date)
        }

    val timeHourS: String
        @Bindable get() {
            val date = when (attributes.status == Status.Value.WAITLIST.code) {
                true -> attributes.waitlistQueuedAt
                else -> attributes.startTime
            } ?: Date()

            return SimpleDateFormat("hh:mm", Locale.US).format(date)
        }

    val timeMarkerS: String
        @Bindable get() {
            val date = when (attributes.status == Status.Value.WAITLIST.code) {
                true -> attributes.waitlistQueuedAt
                else -> attributes.startTime
            } ?: Date()

            return SimpleDateFormat("a", Locale.US).format(date)
        }

    var covers: Int
        get() {
            return attributes.covers
        }
        set(v) {
            attributes.covers = v
            notifyPropertyChanged(BR.coversS)
        }

    val coversS: String
        @Bindable get() {
            return String.format("%s ", attributes.covers.toString())
        }

    var duration: Int
        get() {
            return attributes.duration
        }
        set(v) {
            attributes.duration = v
            notifyPropertyChanged(BR.durationS)
        }

    val durationS: String
        @Bindable get() {
            return durationFormatted(attributes.duration)
        }

    var notes: String?
        @Bindable get() {
            return attributes.notes
        }
        set(v) {
            attributes.notes = v ?: ""
            notifyPropertyChanged(BR.notes)
        }

    var tempName: String?
        @Bindable get() {
            return attributes.tempName
        }
        set(v) {
            attributes.tempName = v
        }

    var createdBy: String?
        @Bindable get() {
            return attributes.createdBy
        }
        set(v) {
            attributes.createdBy = v ?: ""
            notifyPropertyChanged(BR.createdBy)
        }

    var tags: MutableList<String>?
        get() {
            return attributes.tags
        }
        set(v) {
            attributes.tags = v
        }

    val guestName: String
        @Bindable get() {
            return when {
                guest != null -> java.lang.String.format(
                    "%s %s",
                    guest?.firstName, guest?.lastName
                )

                attributes.tempName != null && tempName?.isNotEmpty() == true -> tempName!!
                walkIn -> return "Walk-in"
                else -> "--"
            }
        }

    var waitQuote: Int
        get() {
            return attributes.waitQuote
        }
        set(v) {
            attributes.waitQuote = v
            notifyPropertyChanged(BR.waitQuoteS)
        }

    val waitQuoteS: String
        @Bindable get() {
            return String.format("%s min", (attributes.waitQuote / 60).toString())
        }

    private val waitlistStatus: String?
        get() {
            return attributes.waitlistStatus
        }

    val waitlistConfirmedIcon: Int?
        get() {
            if (status == Status.Value.WAITLIST.code) {
                when (waitlistStatus) {
                    "confirmed" -> return R.drawable.ic_icon_pos_matched
                    "canceled" -> return R.drawable.ic_icon_pos_unmatched
                }
            }

            return null
        }

    val createdAt: Date?
        get() {
            return eatDateISO8601TimezoneDateFormatter().parse(attributes.createdAt)
        }

    val customTags: List<String>
        get() {
            return attributes.customTags.filter { it != "POS_MATCHED" && it != "voucher_redeemed" }
        }

    val key: String?
        get() {
            return attributes.key
        }

    val source: String?
        get() {
            return attributes.source
        }

    val online: Boolean
        get() {
            return attributes.online
        }

    val updatedAt: Date?
        get() {
            return attributes.updatedAt?.let { eatDateISO8601TimezoneDateFormatter().parse(it) }
        }

    var walkIn: Boolean
        get() {
            return attributes.walkIn
        }
        set(value) {
            attributes.walkIn = value
        }

    val locked: Boolean
        get() {
            return attributes.lockStatus == "locked"
        }

    val review: Review?
        get() {
            return attributes.review
        }

    var comments: MutableList<Comment>?
        get() {
            return attributes.comments
        }
        set(value) {
            attributes.comments = value
            notifyPropertyChanged(BR.lastComment)
        }

    val lastComment: String
        @Bindable get() {
            return attributes.comments?.lastOrNull()?.comment ?: ""
        }

    @IgnoredOnParcel
    val isLate: Boolean
        @Bindable get() = startTime.hasPassedNow()

    @IgnoredOnParcel
    val seatedTime: Int
        @Bindable get() = startTime.secondsSinceNow()

    @IgnoredOnParcel
    val isOverSeated: Boolean
        @Bindable get() = seatedTime > duration

    val statusHistory: List<StatusHistory>?
        get() {
            return attributes.statusHistory
        }

    val demo: Boolean
        get() = attributes.demo

    @IgnoredOnParcel
    @Bindable
    var waitlistTimerError: Boolean = false

    @IgnoredOnParcel
    val waitlistInterval: String
        @Bindable get() {

            val cMin = Calendar.getInstance()
            cMin.set(Calendar.HOUR_OF_DAY, 4)
            cMin.set(Calendar.MINUTE, 0)

            if (attributes.waitlistQueuedAt?.before(cMin.time) == true) {
                return "0:00"
            }

            var intrvl = attributes.waitQuote

            var minutes = intrvl / 60
            var seconds = intrvl - minutes * 60

            if (timerPermission?.optionValue.equals(TimerType.Down.type)) {

                if (attributes.waitlistQueuedAt != null && attributes.waitlistQueuedAt!! < Date()) {

                    intrvl = (attributes.waitQuote - TimeUnit.MILLISECONDS.toSeconds(
                        Date().time - attributes.waitlistQueuedAt!!.time
                    )).toInt()

                    minutes = intrvl / 60
                    seconds = intrvl - minutes * 60
                }

                waitlistTimerError = intrvl < 0
                notifyPropertyChanged(BR.waitlistTimerError)

                return if (intrvl < 0) {
                    String.format("-%d:%02d", abs(minutes), abs(seconds))
                } else {
                    String.format("%d:%02d", abs(minutes), abs(seconds))
                }

            } else if (timerPermission?.optionValue.equals(TimerType.Up.type)) {

                minutes = 0;
                seconds = 0;

                if (attributes.waitlistQueuedAt != null && attributes.waitlistQueuedAt!! < Date()) {

                    intrvl = (TimeUnit.MILLISECONDS.toSeconds(
                        Date().time - attributes.waitlistQueuedAt!!.time
                    )).toInt()

                    minutes = intrvl / 60
                    seconds = intrvl - minutes * 60
                }

                waitlistTimerError = intrvl > attributes.waitQuote
                notifyPropertyChanged(BR.waitlistTimerError)

                return String.format("%d:%02d", abs(minutes), abs(seconds))
            }

            Log.v("timer_permission", timerPermission?.optionValue.toString())

            return ""
        }

    @IgnoredOnParcel
    val showUpdateInterval: Boolean
        @Bindable get() {
            val secondsPassed = (System.currentTimeMillis() - (updatedAt?.time
                ?: System.currentTimeMillis())) / 1000
            val tenMinutesInSeconds = 10 * 60

            return secondsPassed < tenMinutesInSeconds &&
                    (lastOpenedAt == null || lastOpenedAt!!.before(updatedAt))
        }

    @IgnoredOnParcel
    val updatedAtInterval: Int
        @Bindable get() {
            notifyPropertyChanged(BR.showUpdateInterval)
            return ((System.currentTimeMillis() - (updatedAt?.time
                ?: System.currentTimeMillis())) / 1000).toInt()
        }

    fun toReservationBody(
        addedTags: List<String>?,
        removedTags: List<String>?
    ): ReservationBody {
        val source =
            if (BookingSource.sourcesWithImage?.any { it.name == attributes.source } == true) {
                attributes.source
            } else {
                null
            }
        return ReservationBody(
            covers,
            createdBy,
            customTags,
            addedTags,
            removedTags,
            duration,
            guest?.id,
            tempName,
            notes,
            formatNonLocal().format(startTime).toString(),
            status,
            tables?.map { it.id },
            walkIn,
            waitQuote,
            waitlistQueuedAt,
            comments,
            sendMessage,
            attributes.customFields,
            attributes.editedBy,
            source
        )
    }
}