package com.eatapp.clementine.data.network.response.guest


import com.eatapp.clementine.data.network.response.IncludedResponse
import com.eatapp.clementine.data.network.response.Meta
import com.google.gson.annotations.SerializedName

data class GuestsResponse(
    @SerializedName("data")
    val guests: List<Guest>,
    @SerializedName("meta")
    val meta: Meta,
    @SerializedName("included")
    val included: List<IncludedResponse>
)