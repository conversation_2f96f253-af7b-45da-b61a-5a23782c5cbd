package com.eatapp.clementine.ui.guest

import android.os.Bundle
import android.view.View
import com.eatapp.clementine.adapter.ReservationAdapter
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.GuestHistoryFragmentBinding
import com.eatapp.clementine.internal.Constants.GUEST_EXTRA
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.home.overview.ReservationsViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GuestHistoryFragment : BaseFragment<GuestHistoryViewModel, GuestHistoryFragmentBinding>() {

    var adapter: ReservationAdapter? = null
    private var pendingGuestUpdate: Guest? = null

    companion object {

        fun newInstance(g: Guest?) = GuestHistoryFragment().apply {
            arguments = Bundle().apply {
                putParcelable(GUEST_EXTRA, g)
            }
        }
    }

    override fun inflateLayout() = GuestHistoryFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = GuestHistoryViewModel::class.java

    override fun viewCreated() {
        // First check if we have a pending update
        pendingGuestUpdate?.let { guest ->
            vm.guest = guest
            pendingGuestUpdate = null
        } ?: run {
            // If no pending update, use the one from arguments
            arguments?.getParcelable<Guest>(GUEST_EXTRA)?.let { guest ->
                vm.guest = guest
            }
        }

        adapter = ReservationAdapter(
            ReservationAdapter.ListType.GUEST,
            vm.posActive,
            vm.eatManager.openedReservations,
            vm.paymentsActive,
            null,
            {}, {}, {}, {})

        binding.reservationsList.adapter = adapter

        observe()

        vm.getReservations()
    }

    override fun onDestroy() {
        super.onDestroy()
        adapter?.stopTimers()
    }

    override fun onResume() {
        super.onResume()
        // Resume timer and refresh time indicators when user returns to the list
        adapter?.resumeTimer()
        adapter?.refreshTimeIndicators()
    }

    override fun onPause() {
        super.onPause()
        // Stop timer when user leaves the list to save battery
        adapter?.stopTimers()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    fun updateGuest(guest: Guest) {
        if (isViewModelInitialized) {
            vm.guest = guest
            vm.getReservations()
        } else {
            pendingGuestUpdate = guest
        }
    }

    private fun observe() {
        progress = binding.progress

        vm.reservations.observe(viewLifecycleOwner) { reservations ->

            if (reservations.isEmpty()) {
                binding.emptyList.root.visibility = View.VISIBLE
                binding.reservationsList.visibility = View.INVISIBLE
                adapter?.submitList(null)
            } else {
                binding.emptyList.root.visibility = View.GONE
                binding.reservationsList.visibility = View.VISIBLE
                adapter?.submitList(
                    reservations.toMutableList(),
                    ReservationsViewModel.FilterType.ALL,
                    vm.reservationViewMode
                )
            }
        }
    }
}