package com.eatapp.clementine.ui.launch

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.asMutableLiveData
import com.eatapp.clementine.internal.isValidEmail
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.CaptchaManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import com.google.android.recaptcha.RecaptchaAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SignUpViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val analyticsManager: AnalyticsManager,
    private val captchaManager: CaptchaManager,
    private val fabricateRepository: FabricateRepository
) : BaseViewModel() {

    private val reCaptchaSignupAction = "clementine_signup"

    var email = MutableLiveData<String>()
    var password = MutableLiveData<String>()
    var name = MutableLiveData<String>()
    var phonePrefix = MutableLiveData<String>()
    var phoneNumber = MutableLiveData<String>()
    var zone = MutableLiveData<Pair<String, String>?>()
    var country = MutableLiveData<Country>()
    private var countryCode = MutableLiveData<String>()

    var authentication = MutableLiveData<SharedLaunchViewModel.AuthenticationState>()

    private val _captchaError = SingleLiveEvent<Boolean>()
    val captchaError: LiveData<Boolean> = _captchaError

    val signInEnabled = false.asMutableLiveData()

    init {
        phonePrefix.value = "CODE"

        viewModelScope.launch {
            captchaManager.recaptchaClient = captchaManager.fetchRecaptchaClient().getOrNull()
        }
    }

    fun signUpBtnClick() {

        if (validate()) return

        loading(true)

        viewModelScope.launch{
            try {
                val client = captchaManager.fetchRecaptchaClient().getOrThrow()
                val token = client.execute(RecaptchaAction.custom(reCaptchaSignupAction)).getOrThrow()

                launch ({
                    val auth = fabricateRepository.register(registerBody(token))

                    if (!auth.data.token.isNullOrBlank()) {
                        eatManager.userData(auth.data)
                        analyticsManager.trackSignup()

                        authentication.postValue(SharedLaunchViewModel.AuthenticationState.AUTHENTICATED)
                    }

                    loading(false)
                })

            } catch (e: Exception){
                _captchaError.postValue(true)
                loading(false)
            }
        }
    }

    fun timeZone(z: Pair<String, String>?) {
        zone.value = z
    }

    fun prefixAndCode(country: Country?) {
        this.country.value = country
        phonePrefix.value = country?.phonePrefix
        countryCode.value = country?.code
    }

    private fun validate(): Boolean {

        val email: String = email.value?.toString() ?: ""
        val password: String = password.value?.toString() ?: ""
        val name: String? = name.value?.toString()
        val zone: String? = zone.value?.second.toString()
        val phoneNumber: String? = phoneNumber.value?.toString()
        val phonePrefix: String? = phonePrefix.value?.toString()

        if (password.length < 8) {
            setError(EatException("Validation", "Password must contain minimum 8 characters"))
        }

        if (!email.isValidEmail()) {
            setError(EatException("Validation", "Please enter valid email address"))
        }

        if (email.isBlank() || password.isBlank() || name.isNullOrBlank()
            || zone.isNullOrBlank() || phoneNumber.isNullOrBlank()
        ) {

            setError(EatException("Validation", "Field can not be empty"))
            return true
        }

        return if (phonePrefix == "CODE") {

            setError(EatException("Validation", "Phone prefix can not be empty"))
            true

        } else false
    }

    fun timeZones(): MutableList<SelectorItem> {
        return eatManager.timeZones()
    }

    fun countries(): MutableList<SelectorItem> {
        return eatManager.countriesSelectorItems(country.value)
    }

    private fun registerBody(token: String): RegisterBody {

        return RegisterBody(
            email.value,
            password.value,
            name.value,
            String.format("+%s%s", phonePrefix.value, phoneNumber.value),
            zone.value?.second,
            countryCode.value?.uppercase(Locale.getDefault()),
            token
        )
    }
}
