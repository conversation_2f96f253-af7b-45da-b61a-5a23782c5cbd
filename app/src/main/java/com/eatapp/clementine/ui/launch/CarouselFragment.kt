package com.eatapp.clementine.ui.launch

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.eatapp.clementine.databinding.CarouselFragmentBinding
import com.eatapp.clementine.internal.Constants.CAROUSEL_DESC_EXTRA
import com.eatapp.clementine.internal.Constants.CAROUSEL_IMAGE_EXTRA
import com.eatapp.clementine.internal.Constants.CAROUSEL_TITLE_EXTRA

class CarouselFragment : Fragment() {

    private lateinit var binding: CarouselFragmentBinding

    companion object {

        fun newInstance(t: Int, d: Int, i: Int) = CarouselFragment().apply {

            arguments = Bundle().apply {
                putInt(CAROUSEL_TITLE_EXTRA, t)
                putInt(CAROUSEL_DESC_EXTRA, d)
                putInt(CAROUSEL_IMAGE_EXTRA, i)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = CarouselFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.run {
            carouselTitle.text = resources.getString(arguments?.getInt(CAROUSEL_TITLE_EXTRA)!!)
            carouselDescription.text =
                resources.getString(arguments?.getInt(CAROUSEL_DESC_EXTRA)!!)
            carouselImage.setImageDrawable(
                ContextCompat.getDrawable(
                    requireContext(), arguments?.getInt(CAROUSEL_IMAGE_EXTRA)!!
                )
            )
        }
    }
}