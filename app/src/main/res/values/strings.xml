<resources>

    <!--/** Activity labels **/-->
    <string name="label_launch_activity">Login</string>
    <string name="label_home_activity">Home</string>
    <string name="label_guest_activity">Guest</string>
    <string name="label_guests_activity">Guests</string>
    <string name="label_notes_activity">Notes</string>
    <string name="label_res_activity">Reservation</string>
    <string name="label_restaurants_activity">Restaurants</string>
    <string name="label_activity_splash">Splash</string>
    <string name="label_redeem_activity">Redeem Voucher</string>
    <string name="label_hubspot_activity">Hubspot Support</string>
    <string name="label_print_settings">Print Settings</string>
    <string name="label_select_table_activity">Select a table</string>

    <!--/** Bottom nav and fragment title items **/-->
    <string name="nav_item_list">Overview</string>
    <string name="nav_item_guests">Guests</string>
    <string name="nav_item_reserve">Reserve</string>
    <string name="nav_item_reports">Reports</string>
    <string name="nav_item_reviews">Reviews</string>
    <string name="nav_item_more">More</string>
    <string name="nav_item_list_">OVERVIEW</string>
    <string name="nav_item_guests_">GUESTS</string>
    <string name="nav_item_floor_">FLOOR</string>
    <string name="nav_item_reserve_">RESERVE</string>
    <string name="nav_item_reports_">REPORTS</string>
    <string name="nav_item_more_">MORE</string>

    <!--/** Tabs **/-->
    <string name="reservations">Reservations</string>
    <string name="waitlist">Waitlist</string>
    <string name="guest_spend">Guest spend</string>
    <string name="daily_notes">Daily Notes</string>
    <string name="servers">Servers</string>
    <string name="filter_all">ALL</string>
    <string name="filter_seated">Seated</string>
    <string name="filter_upcoming">Upcoming</string>
    <string name="profile">Profile</string>
    <string name="guest_history">History</string>
    <string name="details">Details</string>
    <string name="pos">Pos</string>
    <string name="payment">Payment</string>
    <string name="messages">Messages</string>
    <string name="block_hours">Block hours</string>
    <string name="block_tables">Block tables</string>
    <string name="table_labels">Table labels</string>

    <!--/** Reservation list item **/-->
    <string name="covers_">Covers:</string>
    <string name="table_">Table:</string>
    <string name="walkin">Walk-In</string>

    <!--/** View types **/-->
    <string name="title_compact_view">Compact view</string>
    <string name="title_detail_view">Detail view</string>
    <string name="title_detail_view_with_time">Detail view + timer</string>

    <!--/** Loading **/-->
    <string name="loading_restaurants">Loading restaurant</string>
    <string name="loading_reservations">Loading reservations</string>
    <string name="loading_pos_data">Loading Pos data</string>
    <string name="loading_guests">Loading guests</string>
    <string name="loading_reports">Loading reports</string>
    <string name="loading_reviews">Loading reviews</string>
    <string name="loading_servers">Loading servers</string>
    <string name="loading_notifications">Loading notifications</string>

    <!--/** Hint **/-->
    <string name="guests_search_hint">Search for guests</string>
    <string name="restaurants_search_hint">Search for restaurants</string>
    <string name="voucher_code_hint">Enter 6 digit voucher code</string>
    <string name="reservations_search_hint">Search by name or phone</string>
    <string name="review_search_hint">Search for guest</string>
    <string name="pos_search_hint">Search by ticket</string>
    <string name="email_hint">E-MAIL</string>
    <string name="password_hint">PASSWORD</string>
    <string name="venue_name_hint">VENUE NAME</string>
    <string name="venue_phone_number_hint">VENUE PHONE NUMBER</string>
    <string name="time_zone_hint">TIME ZONE</string>
    <string name="daily_note_hint">Add a daily note here</string>
    <string name="note_hint">Add a note here</string>
    <string name="guest_note_hint">Enter guest note</string>
    <string name="table_hint">Select a table</string>
    <string name="reservation_notes_hint">Add reservation notes</string>
    <string name="reservation_by_hint">Select reservation taker</string>
    <string name="reservation_edited_by_hint">Select reservation editor</string>
    <string name="first_name_hint">Enter first name</string>
    <string name="last_name_hint">Enter last name</string>
    <string name="guest_phone_hint">Enter guest phone number</string>
    <string name="guest_email_hint">Enter guest e-mail</string>
    <string name="guest_notes_hint">Add guest notes</string>
    <string name="birthday_hint">Enter birthday</string>
    <string name="anniversary_hint">Enter anniversary</string>
    <string name="comments_hint">Add private comments</string>
    <string name="enter_comment_hint">Enter your comment</string>
    <string name="type_your_message">Type your message</string>
    <string name="cant_send_text">Can\'t send text</string>

    <!--/** Reservation **/-->
    <string name="new_reservation">New reservation</string>
    <string name="guest_details">Guest details</string>
    <string name="reservation_details">Reservation details</string>
    <string name="reservation_type">Reservation type</string>
    <string name="walk_in">Walk-in</string>
    <string name="preference">Preference</string>
    <string name="source">Booking source</string>
    <string name="status">Status</string>
    <string name="all_shifts">All shifts</string>
    <string name="table">Table</string>
    <string name="date">Date</string>
    <string name="time">Time</string>
    <string name="covers">Covers</string>
    <string name="duration">Duration</string>
    <string name="wait">Wait</string>
    <string name="save">Save</string>
    <string name="notes">Visit notes &amp; special requests</string>
    <string name="reservation_notes">Reservation notes</string>
    <string name="comments">Team chat</string>
    <string name="reservation_by">Employee</string>
    <string name="reservation_tags">Reservation tags</string>
    <string name="custom_tags">Custom tags</string>
    <string name="update_reservation">Update reservation</string>
    <string name="update_walk_in">Update walk-in</string>
    <string name="edit_reservation">Edit reservation</string>
    <string name="create_reservation">Create reservation</string>
    <string name="table_ready">Table is ready</string>
    <string name="sent_at">Sent at</string>
    <string name="create_walk_in">Create walk-in</string>
    <string name="delete_reservation">Delete reservation</string>
    <string name="comment">Comment</string>
    <string name="select_user">Select user</string>
    <string name="enter_pin">Enter reservation taker pin</string>
    <string name="wrong_pin">Wrong pin</string>
    <string name="comment_info_message">These comments are for internal communication only. They will not be shared with guest.</string>
    <string name="empty_comment_title">This reservation has no comments</string>
    <string name="empty_comment_message">Make a comment by clicking below.</string>
    <string name="no_staff_title">No staff configured</string>
    <string name="no_staff_desc">Please configure your staff on admin.</string>
    <string name="reservation_created">Reservation created</string>
    <string name="activity">Activity</string>
    <string name="manage_reservation">Manage reservation</string>
    <string name="no_active_conversations">No active conversations</string>
    <string name="no_conversations_yet">No conversations yet</string>
    <string name="secure_messages">Your messages are secure</string>
    <string name="empty_template_title">No templates</string>
    <string name="empty_template_message">You have not configured any templates.</string>
    <string name="confirm">Confirm</string>
    <string name="decline">Decline</string>

    <!--/** Closings **/-->
    <string name="block_new_reservation">Block new reservations</string>
    <string name="start_block_time">Start block time</string>
    <string name="end_block_time">End block time</string>
    <string name="currently_blocked">Currently blocked</string>
    <string name="select_date">Select date</string>
    <string name="select_time">Select time</string>
    <string name="longer_range">This is part of a longer range – delete in admin</string>
    <string name="create_closing">Block selected hours</string>
    <string name="block_from">Block from</string>
    <string name="block_to">Block to</string>
    <string name="select_tables_to_block">Select tables to block</string>
    <string name="no_tables_selected">No tables selected</string>
    <string name="create_table_blocking">Block selected tables</string>

    <!--/** Pos **/-->
    <string name="ticket_id">Ticket Id</string>
    <string name="pos_details">POS details</string>
    <string name="ticket_opened_at">Ticket opened at</string>
    <string name="ticket_closed_at">Ticket closed at</string>
    <string name="server_name">server name</string>
    <string name="menu_items">Menu items</string>
    <string name="voided_items">Voided items</string>
    <string name="name">Name</string>
    <string name="quantity">Quantity</string>
    <string name="price">Price</string>
    <string name="total">Total</string>
    <string name="pos_inactive_title">POS is not activated</string>
    <string name="pos_inactive_subtitle">Please contact support to enable POS</string>
    <string name="pos_unmatched">No POS tickets have been matched to this reservation.</string>
    <string name="loading_pos">Fetching POS data</string>
    <string name="sort_high_to_low">Sort: High to low</string>
    <string name="sort_low_to_high">Sort: Low to high</string>

    <!--/** Servers **/-->
    <string name="now">Now:</string>
    <string name="last_seated">Last seated:</string>
    <string name="total_">Total:</string>
    <string name="update_assigned_tables">Update assigned tables</string>
    <string name="not_available">N/A</string>

    <!--/** Guest **/-->
    <string name="new_guest">New guest</string>
    <string name="add_guest">ADD GUEST</string>
    <string name="total_visits">Total visits</string>
    <string name="edit_guest">Edit guest</string>
    <string name="edit_guest_desc">Do you want to edit or remove guest?</string>
    <string name="add_new_guest">Add new guest</string>
    <string name="remove_guest">Remove guest</string>
    <string name="phone_and_email">Phone &amp; email</string>
    <string name="no_phone_and_email"> No phone &amp; email ID found</string>
    <string name="add_more_details">Add more details</string>
    <string name="guests_found">Guests found</string>
    <string name="guest_found">Guest found</string>

    <!--/** Guest **/-->
    <string name="whatsapp">WhatsApp</string>
    <string name="sms">SMS</string>

    <string name="pos_tickets">POS tickets</string>
    <string name="avg_per_visit">Avg. / visit</string>
    <string name="avg_per_cover">Avg. / cover </string>

    <string name="no_shows">No-shows</string>
    <string name="denied">Denied</string>
    <string name="cancellations">Cancellations</string>
    <string name="personal_details">Personal details</string>
    <string name="more_details">More details</string>
    <string name="first_name">First name</string>
    <string name="last_name">Last name</string>
    <string name="email">E-mail</string>
    <string name="phone">Phone</string>
    <string name="guest_notes">Guest notes</string>
    <string name="birthday">Birthday</string>
    <string name="marketing_opt_in">Marketing Opt-In</string>

    <string name="anniversary">Anniversary</string>
    <string name="delete_user">Delete guest</string>
    <string name="guest_tags">Guest tags</string>
    <string name="update_user">Update guest</string>
    <string name="create_user">Add guest</string>
    <string name="update_notes">Update notes</string>
    <string name="save_notes">Save notes</string>

    <!--/** Reports **/-->
    <string name="performance">Performance</string>
    <string name="reviews">Reviews</string>
    <string name="total_covers">Total covers</string>
    <string name="materialised_covers">Materialised covers</string>
    <string name="breakfast">Breakfast</string>
    <string name="lunch">Lunch</string>
    <string name="dinner">Dinner</string>
    <string name="in_house">In-House</string>
    <string name="menus">Menus</string>
    <string name="online">Online</string>
    <string name="walk_ins">Walk-ins</string>
    <string name="materialised">Materialised</string>
    <string name="upcoming">Upcoming</string>
    <string name="cancelled">Cancelled</string>
    <string name="no_show">No-show</string>
    <string name="overall">Overall:</string>
    <string name="food">Food:</string>
    <string name="service">Service:</string>
    <string name="ambience">Ambience:</string>
    <string name="read_more">Read More</string>
    <string name="read_less">Read Less</string>

    <!--/** Settings **/-->
    <string name="restaurant">Restaurant</string>
    <string name="guest_management">Guest Management</string>
    <string name="account">Account</string>
    <string name="log_out">Log out</string>
    <string name="support">Support</string>
    <string name="change_restaurant">Change restaurant</string>
    <string name="printer_settings">Printer settings</string>
    <string name="settings">Settings</string>
    <string name="reservation_mode_view">Reservation list view</string>
    <string name="chit_print_config">Chit printer configuration</string>
    <string name="go_back">Go back</string>

    <!--/** Random **/-->
    <string name="error">Error!</string>
    <string name="send">Send</string>
    <string name="show_more">Show more</string>
    <string name="show_less">Show less</string>
    <string name="continue_setup">Continue</string>
    <string name="error_message">Something went wrong.</string>
    <string name="otp_error_message">Invalid OTP</string>
    <string name="reservations_empty_list">No reservations for selected date or filter</string>
    <string name="pos_records_empty_list">No pos records for selected date or filter</string>
    <string name="guests_empty_list">Looks like you don’t have any guests yet</string>
    <string name="nothing_yet">Nothing yet..</string>
    <string name="reviews_empty_list">Looks like there aren\'t any reviews for selected day, try having a look at reviews for another day.</string>
    <string name="reviews_empty_search_list">No reviews</string>
    <string name="servers_empty_list">No servers available</string>
    <string name="today_note">Today’s note</string>
    <string name="sign_in">Log In</string>
    <string name="sign_up">Sign Up</string>
    <string name="register">Register</string>
    <string name="in_house_source">in_house</string>
    <string name="tags_bottom_sheet_title">Select tags:</string>
    <string name="tags">Tags</string>
    <string name="add_tags">Add tags</string>
    <string name="add_tables">Add tables</string>
    <string name="voucher_code">Voucher Code</string>
    <string name="redeem_voucher">Redeem</string>
    <string name="survey">Survey</string>
    <string name="packages">Packages</string>
    <string name="popup_activate_and_enable">Activate now and enable:</string>
    <string name="popup_upgrade_package">Upgrade your package and:</string>
    <string name="setup_onboarding_title">Get setup on our site</string>
    <string name="setup_onboarding_description"><b>Use your computer</b> to get set up in 5 minutes. Type <b>app.eatapp.co</b> on your browser and log in.</string>
    <string name="setup_onboarding_loading_in_progress">Waiting for you to finish quick onboarding at app.eatapp.co</string>
    <string name="setup_onboarding_loading_completed">Congrats! The basics are set up. Click continue to start using eat app!</string>
    <string name="enable_features">Enable in features page to use</string>
    <string name="purchase_addon">Add-on must be purchased</string>
    <string name="ask_me_every_time">Ask me every time I want to print</string>
    <string name="ask_me_info">This can be changed any time through the printer settings.</string>
    <string name="no_rooms_title">You don\'t seem to have any rooms added</string>
    <string name="captcha_error_description">Please check your internet connection and restart the app</string>
    <string name="print_when_seated">Print when guest is seated</string>
    <string name="print_when_seated_info">Print guest details when reservation status is changed to seated</string>

    <!--/** Chit printer **/-->
    <string name="print">Print</string>
    <string name="chit_printer">Chit Printer</string>
    <string name="chit_printer_connect">Connect</string>
    <string name="no_printers_connected">No printers connected</string>
    <string name="printer_desc_1">Your chit printer should be automatically connected. If it\'s not, please follow below steps.</string>
    <string name="printer_desc_2">1. Go to your phone\'s settings and pair chit printer via bluetooth</string>
    <string name="printer_desc_3">2. Make sure the bluetooth permission is enabled</string>
    <string name="test_print">Test print</string>
    <string name="no_printer_connected_title">No connected devices</string>
    <string name="no_printer_connected_desc">Please connect your chit printer properly and try again. For instructions go to Settings > Print settings</string>
    <string name="no_printer_connected_short_desc">Please connect your chit printer properly and try again.</string>

    <!--/** Validation **/-->
    <string name="validation_title">Validation</string>
    <string name="no_reservation_taker">No reservation taker selected</string>
    <string name="no_reservation_taker_configured">"No reservation taker configured"</string>
    <string name="no_custom_tags_taker">No Custom tags selected</string>

    <!--/** Alerts **/-->
    <string name="logout_title">Log out</string>
    <string name="logout_desc">Are you sure you want to logout?</string>
    <string name="switch_restaurant_title">Switch restaurants</string>
    <string name="switch_restaurant_desc">Are you sure you want to switch restaurants?</string>
    <string name="delete_reservation_title">Delete reservation</string>
    <string name="delete_reservation_desc">Are you sure you want to delete reservation?</string>
    <string name="reservation_locked_title">Reservation locked</string>
    <string name="reservation_locked_desc">Visit Admin -> Billing to modify this reservation</string>
    <string name="delete_closing_title">Delete blocked hours</string>
    <string name="delete_closing_desc">Are you sure you want to delete blocked hours?</string>
    <string name="invalid_times_title">Invalid times</string>
    <string name="invalid_times_desc">End block time has to be after start block time</string>
    <string name="send_message">Send message?</string>
    <string name="send_message_desc">The guest will receive a text message and email with a confirmation that their table is ready</string>
    <string name="sms_messaging_disabled">"Your SMS messaging is disabled. Please contact support to enable it"</string>
    <string name="whatsapp_messaging_disabled">"Your Whatsapp messaging is disabled. Please contact support to enable it"</string>
    <string name="message_error">Message error</string>
    <string name="message_suggestion">Suggestion</string>
    <string name="see_more_info">See more info</string>
    <string name="retry">Retry</string>

    <!--/** Carousel **/-->
    <string name="carousel_title_one">Manage your tables</string>
    <string name="carousel_desc_one">Join the thousands of restaurants that use Eat App to manage reservations and
        track loyal guests
    </string>
    <string name="carousel_title_two">On every platform</string>
    <string name="carousel_desc_two">Apps for hosts, servers and managers across iPhone, Android, iPad and desktop
        browsers.
    </string>
    <string name="carousel_title_three">Understand your guest</string>
    <string name="carousel_desc_three">Guest segmentation, CRM, and analytics that drive better marketing results and
        increase guest happiness.
    </string>
    <string name="carousel_title_four">Optimize operations</string>
    <string name="carousel_desc_four">Fast, intuitive table management to help you focus on maximising revenue per
        table.
    </string>
    <string name="carousel_title_five">Get more reservations</string>
    <string name="carousel_desc_five">Online restaurant reservation system for more bookings via your website, Google,
        TripAdvisor, and more.
    </string>

    <!--/** Lockdown subscription expiring **/-->
    <string name="subscription_expiring_title">Subscription about to expire</string>
    <string name="subscription_expiring_text">Please enable the auto-renewal of your subscription by date to continue having full access to your reservations, floor plan and guest data.</string>

    <!--/** Lockdown subscription expired **/-->
    <string name="subscription_expired_title">Subscription expired</string>
    <string name="subscription_expired_text">Don’t worry, you’ll still have access to all your current reservations - but you won’t be able to add new ones.\n\nPlease renew your subscription and restore full access to our revenue generation and optimization features. </string>

    <!--/** Lockdown trial expired **/-->
    <string name="trial_expired_title">Your free trial has ended</string>
    <string name="trial_expired_text">Don\'t worry, you\'ll still have access to all your current reservations - but you won’t be able to add new ones.</string>
    <string name="trial_expired_text_1">· Manage bookings from all channels in one place </string>
    <string name="trial_expired_text_2">· Real-time 24/7 online reservations</string>
    <string name="trial_expired_text_3">· Your guest database, reports and marketing tools</string>

    <!--/** Lockdown trial active **/-->
    <string name="trial_active_title">You have x days left on your free trial.</string>
    <string name="trial_active_text">Subscribe today and lets make this official.</string>

    <!--/** Lockdown billing inactive **/-->
    <string name="billing_blocked_title">Your account has been temporarily blocked.</string>
    <string name="billing_blocked_text">Don’t worry, you’ll still have access to all your current reservations, but you won’t be able to add new ones.\n\nClick below to add your card information and restore full access to your reservations, floor plan and guest data.</string>

    <!--/** Lockdown billing warning **/-->
    <string name="billing_warning_title">You have outstanding payments</string>
    <string name="billing_warning_text">Please settle your payments within x days or risk your account getting blocked from further use.\n\nClick below to add your card information so you can continue using Eat App uninterrupted. </string>

    <!--/** Covers 50 expiring **/-->
    <string name="covers_50_expiring_title">Reached 80% of your cover limit.</string>
    <string name="covers_50_expiring_text">You are getting close to reaching the 50 cover limit in your Free account. Upgrade now to continue receiving new reservations.</string>

    <!--/** Covers 50 expired **/-->
    <string name="covers_50_expired_title">You\'ve reached your cover limit</string>
    <string name="covers_50_expired_text">You have reached the 50 cover limit on your Free account. You still have access to current reservations - but won\'t be able to add new ones.</string>

    <!--/** Covers 50 expiring **/-->
    <string name="covers_500_expiring_title">Reached 80% of your cover limit.</string>
    <string name="covers_500_expiring_text">You are getting close to reaching the 500 cover limit in your Free account. Upgrade now to continue receiving new reservations.</string>

    <!--/** Covers 50 expired **/-->
    <string name="covers_500_expired_title">You\'ve reached your cover limit</string>
    <string name="covers_500_expired_text">You have reached the 500 cover limit on your Free account. You still have access to current reservations - but won\'t be able to add new ones.</string>

    <!--/** Covers 50 **/-->
    <string name="covers_50_text_1">· Choose from 500 or Unlimited covers per month</string>
    <string name="covers_50_text_2">· Increase reservations and boost loyalty</string>
    <string name="covers_50_text_3">· Get access to your guest database, reports and marketing tools</string>

    <!--/** Covers 500 **/-->
    <string name="covers_500_text_1">· Get unlimited covers every month</string>
    <string name="covers_500_text_2">· Increase reservations and boost loyalty</string>
    <string name="covers_500_text_3">· Get access to your guest database, reports and marketing tools</string>

    <string name="html"><Data><![CDATA[
        <!DOCTYPE html>\n
        <html>\n
            <body>\n
                <script src=\"https://js.chilipiper.com/marketing.js\" type=\"text/javascript\"></script>\n
                <script>ChiliPiper.scheduling(\"eatapp\", \"mobile-apps-request-demo\")</script>\n
                <script>ChiliPiper.submit(\"eatapp\", \"mobile-apps-request-demo\", {map:true,\n
                    lead: {\n
                        firstname: \"%s\",\n
                        lastname: \"undefined\",\n
                        email: \"%s\",\n
                        phone: \"%s\",\n
                        countrydropdown: \"%s\",\n
                        restaurant_name: \"%s\"\n
                    }\n
                })\n
                </script>\n
            </body>\n
        </html>
    ]]>
    </Data></string>

    <string name="qr_code_prompt_text">Please scan reservation QR code</string>
    <string name="scan_reservation_title">Scan reservation</string>
    <string name="reservation_error_title">Reservation not found</string>
    <string name="reservation_error_desc">This reservation could not be found. It may have been removed or is invalid.</string>

    <string name="bluetooth_permission_title">Permission needed</string>
    <string name="bluetooth_permission_description">You won\'t be able to use this feature until you enable bluetooth permission</string>

    <string name="notifications">Notifications</string>
    <string name="requested_reservations">Requested reservations</string>
    <string name="notifications_permission_title">Permission needed</string>
    <string name="notifications_permission_description">You won\'t be able to see push notifications until you enable notifications permission</string>
    <string name="enable_notifications_title">Enable Notifications?</string>
    <string name="enable_notifications_desc">Enable push notifications to receive immediate updates on new reservations — helping you stay informed and manage operations smoothly.</string>

    <string name="missing_tables_title">Missing tables</string>
    <string name="missing_tables_description">Looks like you didn\'t add any tables to your venue yet. Login to app.eatapp.co to edit floor plan.</string>
    <string name="selected_table_label">Selected table</string>
    <string name="no_tables_selected_label">No tables selected</string>

    <!--Whatsapp templates-->
    <string name="whatsapp_template">Hi %s. This is the reservation team at %s. We\'re messaging you regarding your booking on %s at %s for %d people.</string>
    <string name="whatsapp_web_url">"https://web.whatsapp.com"</string>
    <string name="whatsapp_package_name">com.whatsapp</string>
    <string name="whatsapp_api_template">https://api.whatsapp.com/send?phone=%s&amp;text=%s</string>

    <!--Google query template-->
    <string name="google_query_template">https://www.google.com/search?q=%s</string>
    <string name="whatsapp_update_required_title">Update required</string>
    <string name="whatsapp_update_now">Update now</string>
    <string name="whatsapp_update_cancel">Cancel</string>
    <string name="whatsapp_update_required_description">Please update the app to use this addon</string>

    <!--Playstore-->
    <string name="playstore_app_template">market://details?id=%s</string>
    <string name="playstore_web_template">https://play.google.com/store/apps/details?id=%s</string>

    <!--Reservation payments-->
    <string name="payment_details_title">Payment details</string>
    <string name="create_payment">Create a new payment</string>
    <string name="create_payment_screen_title">Create payment</string>
    <string name="edit_payment_screen_title">Edit payment</string>
    <string name="refund_payment_screen_title">Refund payment</string>
    <string name="refund_amount_label">Refund amount</string>
    <string name="refund_amount_description">"You are about to refund %s, do you want to continue?"</string>
    <string name="amount_label">Amount</string>
    <string name="payment_internal_notes_label">Internal notes (Not shown to your guest):</string>
    <string name="payment_description_label">Description (Shown to your guest):</string>
    <string name="created_label">Created:</string>
    <string name="updated_label">Updated:</string>
    <string name="expires_label">Expires:</string>
    <string name="payment_status_icon_hint">Payment status icon</string>
    <string name="amount_label_caps">AMOUNT:</string>
    <string name="status_label_caps">STATUS:</string>
    <string name="payment_link_label">Payment link (Send to your guest):</string>
    <string name="reservation_payment_empty_state_payment_title">No payments have been created yet for this reservation.</string>
    <string name="reservation_payment_empty_state_payment_subtitle">Click below to create a new payment</string>
    <string name="reservation_payment_empty_state_guest_title">Payments are not available for reservations without a name attached.</string>
    <string name="reservation_payment_empty_state_guest_subtitle">Try adding a name to this reservation first</string>
    <string name="void_label">Void</string>
    <string name="refund_label">Refund</string>
    <string name="cancel_label">Cancel</string>
    <string name="capture_label">Capture</string>
    <string name="edit_label">Edit</string>
    <string name="send_reminder_label">Send reminder</string>
    <string name="external_label">external</string>
    <string name="refund_type_label">REFUND TYPE</string>
    <string name="partial_amount_label">Partial amount</string>
    <string name="enter_amount_to_be_refunded_label">Enter amount to be refunded</string>
    <string name="refund_error_label">Refund amount can\'t be empty or more than captured amount.</string>
    <string name="payment_type_label">Payment type</string>
    <string name="payment_type_hint">A payment is required to hold this slot</string>
    <string name="payment_description_title">Description (shown to guest)</string>
    <string name="payment_description_hint">A payment is required to hold this slot</string>
    <string name="payment_amount_title">Payment amount</string>
    <string name="payment_total_title">Total (incl. %.2f%% fee)</string>
    <string name="payment_amount_auto_cancellation_date_title">Auto cancellation date</string>
    <string name="payment_amount_auto_cancellation_time_title">Auto cancellation time</string>
    <string name="payment_amount_auto_cancellation_date_hint">No cancellation</string>
    <string name="payment_amount_charge_type_title">Charge type</string>
    <string name="payment_amount_charge_type_description">
        <![CDATA[
        <b>Smart</b> (recommended): If the reservation start time is within 7 days from the time the booking is captured, the payment will be held (authorized) only. If the start time is greater than 7 days, the payment will be captured directly
        <br/>
        <br/><b>Authorization</b>: The payments will always be authorized. Authorized payments can expire (Eat App will notify you of these details beforehand)
        <br/>
        <br/><b>Pre-payment</b>: The payments will always be captured. If any refunds are required, there may be additional charges
        ]]>
        </string>
    <string name="create_payment_label">Create and send payment</string>
    <string name="payment_package_label">Payment package</string>
    <string name="unable_to_refund_label">Unable to refund</string>
    <string name="unable_to_refund_description">None of the users have permission to refund a customers payment, please sign in with manager login to access user permissions</string>

    <string name="gateway_action_status_authorized">Authorized</string>
    <string name="gateway_action_status_captured">Captured</string>
    <string name="gateway_action_status_voided">Voided</string>
    <string name="gateway_action_status_refunded">Refunded</string>
    <string name="gateway_action_status_canceled">Canceled</string>
    <string name="gateway_action_status_declined">Declined</string>

    <string name="refund_take_hint">Only users with refund permission are listed below</string>
    <string name="omnisearch_hint"><![CDATA[Search guests & reservations]]></string>
    <string name="search_label">Search</string>
    <string name="chip_all_label">All</string>
    <string name="chip_guests_label">Guests</string>
    <string name="chip_reservations_label">Reservations</string>
    <string name="chip_venues_label">Venues</string>
    <string name="omnisearch_empty_state_title">Omnisearch</string>
    <string name="omnisearch_empty_state_subtitle">Find guests and reservations, and venues at the speed of a falling bowl of soup.</string>
    <string name="new_label">New</string>
    <string name="omnisearch_no_results">No results</string>
    <string name="omnisearch_no_results_description">Try entering something else, we search through reservations and guests.</string>

    <string name="label_empty_guests">No guests</string>
    <string name="label_empty_reservations">No reservations</string>

    <string name="privacy_policy_link">By checking this box you agree to our <a href="https://restaurant.eatapp.co/privacy">Privacy policy</a></string>
    <string name="privacy_policy_label"><a href="https://restaurant.eatapp.co/privacy">View our privacy policy</a></string>

    <string name="send_message_label">Send %s message %s</string>
    <string name="send_message_add_guest_hint">(Select a guest)</string>
    <string name="send_message_request_label">request</string>
    <string name="send_message_confirmed_label">confirm</string>
    <string name="custom_fields_label">Fields</string>
    <string name="custom_fields_empty_hint">Add additional details</string>
    <string name="done_label">Done</string>
    <string name="deleted_custom_fields_label">Deleted custom fields</string>
    <string name="existing_custom_fields_label">Existing custom fields</string>
    <string name="deleted_custom_field_label">" (Deleted)"</string>
    <string name="custom_field_text_input_hint">Add details</string>
    <string name="custom_field_number_input_hint">Enter a number</string>
    <string name="switching_restaurant_label">Switching restaurant…</string>
    <string name="select_different_editor_label">Select different editor</string>
    <string name="otp_title">Authenticate your login</string>
    <string name="otp_subtitle">Enter your one-time passcode from your authenticator app</string>
    <string name="otp_hint">000000</string>
    <string name="otp_description">This helps to keep your Eat App account safe</string>
    <string name="options">Options</string>

    <string name="select_reservation_source">Select booking source</string>
    <string name="online_booking_source_message">Booking source can\'t be changed for online reservations</string>
    <string name="concierge_booking_source_message">Booking source can\'t be changed for reservations with concierge</string>
    <string name="phone_selector_title">Select phone code</string>
    <string name="example_label">Example</string>
    <string name="no_staff_comments_desc">Please enable at least 1 reservation taker, to be able to add comments</string>
    <string name="cancel">Cancel</string>
    <string name="yes">yes</string>
    <string name="no_thanks">No thanks</string>

    <!-- Vouchers -->
    <string name="vouchers_label">Vouchers</string>
    <string name="voucher_redeemed_label">Voucher already redeemed</string>
    <string name="code_required_label">Code required</string>
    <string name="voucher_code_redeem_hint">Enter voucher code to redeem</string>
    <string name="invalid_voucher_code_label">Invalid voucher code</string>
    <string name="assign_voucher_label">Assign voucher</string>
    <string name="redeem_voucher_label">Redeem voucher</string>
    <string name="empty_assign_list_label">Please select vouchers to assign</string>
    <string name="assigned_vouchers_label_reservation">All vouchers are already assigned to this reservation</string>
    <string name="assigned_vouchers_label_guest">All vouchers are already assigned to this guest</string>

    <!-- Loyalty -->
    <string name="loyalty_label">Loyalty</string>
    <string name="loyalty_points_label">Loyalty points</string>
    <string name="loyalty_popup_title">Add or remove points</string>
    <string name="points_label">Points</string>
    <string name="current_points">%s currently has %d points</string>
    <string name="added_points">%d will be added</string>
    <string name="removed_points">%d will be removed</string>
    <string name="late">Late</string>
</resources>
