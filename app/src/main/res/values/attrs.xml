<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="StatusFilterView">
        <attr name="text" format="string"/>
    </declare-styleable>

    <declare-styleable name="StepperView">
        <attr name="key" format="string"/>
        <attr name="disabled" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="LoadingButton">
        <attr name="title" format="string"/>
        <attr name="progressBarColor" format="color"/>
        <attr name="leftIcon" format="integer"/>
        <attr name="rightIcon" format="integer"/>
    </declare-styleable>

    <declare-styleable name="LoyaltyModificationView">
        <attr name="value" format="string"/>
        <attr name="operator" format="enum">
            <enum name="plus" value="0"/>
            <enum name="minus" value="1"/>
        </attr>
    </declare-styleable>

</resources>