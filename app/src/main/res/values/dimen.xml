<?xml version="1.0" encoding="utf-8"?>
<resources>

    <dimen name="reservation_item_height">76dp</dimen>
    <dimen name="guest_item_height">44dp</dimen>
    <dimen name="tag_item_height">32dp</dimen>
    <dimen name="selector_item_height">50dp</dimen>
    <dimen name="restaurant_item_height">44dp</dimen>

    <dimen name="toolbar_icon_size">40dp</dimen>
    <dimen name="toolbar_icon_padding">8dp</dimen>

    <dimen name="global_margin_16">16dp</dimen>
    <dimen name="global_icon_size_24">24dp</dimen>
    <dimen name="global_icon_size_20">20dp</dimen>
    <dimen name="global_icon_size_16">16dp</dimen>
    <dimen name="global_icon_size_12">12dp</dimen>
    <dimen name="button_height">44dp</dimen>
    <dimen name="tab_height">50dp</dimen>
    <dimen name="progress_size">30dp</dimen>
    <dimen name="tag_reservation_item_size">16dp</dimen>
    <dimen name="tag_reservation_item_margin">6dp</dimen>
    <dimen name="separator_chart_view_width">2sp</dimen>
    <dimen name="abbreviation_reservation_item_size">20dp</dimen>
    <dimen name="abbreviation_mega_text_size">22sp</dimen>
    <dimen name="abbreviation_large_text_size">12sp</dimen>
    <dimen name="abbreviation_small_text_size">10sp</dimen>
    <dimen name="abbreviation_text_position">14dp</dimen>
    <dimen name="abbreviation_text_mega_position">18dp</dimen>
    <dimen name="hubspot_chat_top_padding">65dp</dimen>
    <dimen name="bottom_sheet_nav_height">47dp</dimen>
    <dimen name="bottom_sheet_wv_keyboard_padding">100dp</dimen>
    <dimen name="table_title_height">14dp</dimen>
    <dimen name="room_view_adjustment_padding">8dp</dimen>
    <dimen name="tableview_inner_padding">3dp</dimen>
    <dimen name="tableview_border_width">1dp</dimen>
    <dimen name="tableview_border_width_server">2dp</dimen>
    <dimen name="tableview_border_width_bold">3dp</dimen>
    <dimen name="tableview_shape_radius">5dp</dimen>

    <dimen name="section_title_size">14sp</dimen>
    <dimen name="section_title_margin_top">20dp</dimen>
    <dimen name="section_title_margin_bottom">8dp</dimen>

    <dimen name="input_padding_side">16dp</dimen>
    <dimen name="input_padding_bottom">10dp</dimen>
    <dimen name="input_spacing">6dp</dimen>
    <dimen name="input_height">61dp</dimen>
    <dimen name="input_key_size">13sp</dimen>
    <dimen name="input_value_size">16sp</dimen>

    <dimen name="edittext_height">44dp</dimen>

    <dimen name="register_button_height">50dp</dimen>
    <dimen name="register_input_height">48dp</dimen>
    <dimen name="register_input_padding">12dp</dimen>

    <dimen name="margin_2">2dp</dimen>
    <dimen name="margin_3">3dp</dimen>
    <dimen name="margin_4">4dp</dimen>
    <dimen name="margin_5">5dp</dimen>
    <dimen name="margin_6">6dp</dimen>
    <dimen name="margin_8">8dp</dimen>
    <dimen name="margin_10">10dp</dimen>
    <dimen name="margin_11">11dp</dimen>
    <dimen name="margin_12">12dp</dimen>
    <dimen name="margin_16">16dp</dimen>
    <dimen name="margin_20">20dp</dimen>
    <dimen name="margin_24">24dp</dimen>
    <dimen name="margin_28">28dp</dimen>
    <dimen name="margin_32">32dp</dimen>
    <dimen name="margin_48">48dp</dimen>
    <dimen name="margin_64">64dp</dimen>

    <dimen name="text_size_8">8sp</dimen>
    <dimen name="text_size_10">10sp</dimen>
    <dimen name="text_size_11">11sp</dimen>
    <dimen name="text_size_12">12sp</dimen>
    <dimen name="text_size_13">13sp</dimen>
    <dimen name="text_size_14">14sp</dimen>
    <dimen name="text_size_15">15sp</dimen>
    <dimen name="text_size_16">16sp</dimen>
    <dimen name="text_size_17">17sp</dimen>
    <dimen name="text_size_18">18sp</dimen>
    <dimen name="text_size_19">19sp</dimen>
    <dimen name="text_size_20">20sp</dimen>
    <dimen name="text_size_24">24sp</dimen>
    <dimen name="text_size_28">28sp</dimen>
    <dimen name="text_size_32">32sp</dimen>

</resources>