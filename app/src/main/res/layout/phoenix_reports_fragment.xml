<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.reports.PhoenixReportsViewModel" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/nav_item_reports"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/tab_height"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout"
            app:tabGravity="center"
            app:tabMode="auto"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabPaddingEnd="@dimen/global_margin_16"
            app:tabPaddingStart="@dimen/global_margin_16"
            app:tabRippleColor="@color/colorGreen05"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/EatTab"
            app:tabTextColor="@color/colorDark50">

            <com.google.android.material.tabs.TabItem
                android:id="@+id/tab_reservations"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/reservations" />

            <com.google.android.material.tabs.TabItem
                android:id="@+id/tab_guests"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_item_guests" />

            <com.google.android.material.tabs.TabItem
                android:id="@+id/tab_reviews"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_item_reviews" />

        </com.google.android.material.tabs.TabLayout>

        <FrameLayout
            android:id="@+id/main_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tab_layout" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>