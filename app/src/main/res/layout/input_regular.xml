<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="key"
            type="String" />

        <variable
            name="value"
            type="String" />

        <variable
            name="hint"
            type="String" />

        <variable
            name="enabled"
            type="Boolean" />

        <variable
            name="inputType"
            type="int" />

        <variable
            name="lineCount"
            type="int" />

        <variable
            name="ctaVisible"
            type="Boolean" />

        <variable
            name="showMoreVisible"
            type="Boolean" />

        <variable
            name="showMoreExpanded"
            type="Boolean" />

        <variable
            name="ctaIcon"
            type="int" />

        <variable
            name="keyLabelCaps"
            type="Boolean" />

        <variable
            name="ctaOnClick"
            type="android.view.View.OnClickListener" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView30"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@{key}"
            android:textAllCaps="@{keyLabelCaps}"
            android:textColor="@color/colorGrey200"
            android:textSize="@dimen/input_key_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="KEY" />

        <EditText
            android:id="@+id/et_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="@dimen/margin_8"
            android:background="@android:color/transparent"
            android:enabled="@{enabled}"
            android:fontFamily="@font/inter_medium"
            android:hint="@{hint}"
            android:importantForAutofill="no"
            android:inputType="@{inputType}"
            android:maxLines="@{showMoreExpanded ? lineCount : 5}"
            android:singleLine="false"
            android:text="@={value}"
            android:textColor="@{enabled ? @color/grey800 : @color/grey500}"
            android:textColorHint="@color/grey400"
            android:textSize="@dimen/input_value_size"
            app:layout_constraintBottom_toTopOf="@+id/expand_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView30"
            tools:ignore="TouchTargetSizeCheck"
            tools:text=" sdfs fsdfs fsd fs fsdf sdf s fsdf sfdsd fsdf sd fsd fsd fs fs" />

        <Button
            android:id="@+id/expand_btn"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginBottom="6dp"
            android:background="@android:color/transparent"
            android:text="@{showMoreExpanded ? @string/show_less : @string/show_more}"
            android:textAllCaps="false"
            android:textColor="@color/green500"
            android:textSize="12sp"
            android:visibility="@{showMoreVisible ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/view21"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/et_value"
            tools:text="@string/show_more" />

        <View
            android:id="@+id/view21"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/button2"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginEnd="10dp"
            android:enabled="@{enabled}"
            android:onClick="@{ctaOnClick}"
            android:padding="6dp"
            android:visibility="@{ctaVisible ? View.VISIBLE : View.GONE}"
            app:icon="@{ctaIcon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck"
            tools:srcCompat="@drawable/ic_icon_call" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
