<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="section"
            type="com.eatapp.clementine.internal.ReservationSection" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:orientation="vertical">

        <View
            android:id="@+id/background_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:alpha="0.10"
            android:background="@color/chart_blue"
            app:layout_constraintBottom_toTopOf="@+id/bottom_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_cont" />

        <LinearLayout
            android:id="@+id/main_cont"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="?selectableItemBackground"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/inter_medium"
                android:text="@{section.title}"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_13"
                tools:text="Bruce Lee" />

            <ImageView
                android:id="@+id/iv_chevron"
                android:layout_marginStart="@dimen/margin_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <View
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/ic_icon_cutlery"
                tools:srcCompat="@drawable/ic_icon_cutlery" />

            <TextView
                android:id="@+id/resCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/inter_medium"
                android:text="@{Integer.toString(section.resCount)}"
                android:textColor="@color/colorDark100"
                android:textSize="13sp"
                tools:text="3" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView2"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/ic_icon_people"
                tools:srcCompat="@drawable/ic_icon_people" />

            <TextView
                android:id="@+id/coversCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:fontFamily="@font/inter_medium"
                android:text="@{Integer.toString(section.coversCount)}"
                android:textColor="@color/colorDark100"
                android:textSize="13sp"
                tools:text="14" />

        </LinearLayout>

        <View
            android:id="@+id/bottom_line"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:alpha="0.5"
            android:background="@color/chart_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>