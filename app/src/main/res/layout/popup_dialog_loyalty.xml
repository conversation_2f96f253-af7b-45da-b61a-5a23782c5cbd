<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="com.eatapp.clementine.R" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/margin_20">

        <LinearLayout
            android:id="@+id/container_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:text="@string/loyalty_popup_title"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_20" />

            <ImageButton
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_icon_close"
                app:tint="@color/grey800" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_current_state"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginTop="36dp"
            android:layout_marginEnd="@dimen/margin_16"
            android:background="@drawable/shape_rounded_btn_bcg_grey_outline_50"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingStart="@dimen/margin_6"
            android:paddingEnd="@dimen/margin_6"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_title">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_warning" />

            <TextView
                android:id="@+id/tv_current_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_8"
                android:ellipsize="middle"
                android:fontFamily="@font/inter_medium"
                android:maxLines="1"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_14"
                tools:text="John currently has 300 points" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_points_input"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_current_state">

            <EditText
                android:id="@+id/et_points"
                android:layout_width="94dp"
                android:layout_height="wrap_content"
                android:background="@null"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center"
                android:inputType="number"
                android:maxLines="1"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_24"
                tools:text="320" />

            <View
                android:layout_width="94dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/margin_8"
                android:background="@color/grey300" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_8"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/points_label"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_12" />

        </LinearLayout>

        <HorizontalScrollView
            android:scrollbars="none"
            android:id="@+id/container_increments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_points_input">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/container_increments_plus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_20"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/divider"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:operator="minus"
                        app:value="25" />

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_6"
                        app:operator="minus"
                        app:value="10" />

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_marginStart="@dimen/margin_6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:operator="minus"
                        app:value="5" />

                </LinearLayout>

                <View
                    android:id="@+id/divider"
                    android:layout_width="1dp"
                    android:layout_height="28dp"
                    android:background="@color/grey300"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/container_increments_minus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_20"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/divider"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:operator="plus"
                        app:value="5" />

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_6"
                        app:operator="plus"
                        app:value="10" />

                    <com.eatapp.clementine.views.LoyaltyModificationView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_6"
                        app:operator="plus"
                        app:value="25" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </HorizontalScrollView>


        <LinearLayout
            android:id="@+id/container_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:animateLayoutChanges="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_increments">

            <TextView
                android:id="@+id/tv_modification_info"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginEnd="@dimen/margin_10"
                android:background="@drawable/shape_dotted_background_selected"
                android:gravity="center"
                android:paddingStart="@dimen/margin_16"
                android:paddingEnd="@dimen/margin_16"
                android:visibility="gone"
                tools:text="100 will be added" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_action"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:progressBarColor="@color/white"
                app:tintColor="@{R.color.white}" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>