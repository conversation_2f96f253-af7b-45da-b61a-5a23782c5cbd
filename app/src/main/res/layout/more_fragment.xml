<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.more.MoreViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.more.MoreFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/nav_item_more"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout3">

            <LinearLayout
                android:id="@+id/linearLayout4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true"
                android:background="@color/mainBcg"
                android:paddingBottom="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/restaurant"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/view14"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator" />

                <include
                    android:id="@+id/restaurant"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[0].arrow}"
                    app:icon="@{viewmodel.settings[0].icon}"
                    app:title="@{viewmodel.settings[0].title}" />

                <include
                    android:id="@+id/omnisearch"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[7].arrow}"
                    app:icon="@{viewmodel.settings[7].icon}"
                    app:title="@{viewmodel.settings[7].title}"
                    android:enabled="@{!viewmodel.isFreemium}"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/label_guests_activity"
                    android:visibility="@{viewmodel.isTablet() ? View.GONE : View.VISIBLE}"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/view14wd"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:visibility="@{viewmodel.isTablet() ? View.GONE : View.VISIBLE}"
                    android:background="@color/colorSeparator" />

                <include
                    android:id="@+id/guest_management"
                    layout="@layout/settings_entry"
                    android:visibility="@{viewmodel.isTablet() ? View.GONE : View.VISIBLE}"
                    app:arrow="@{viewmodel.settings[1].arrow}"
                    app:icon="@{viewmodel.settings[1].icon}"
                    app:title="@{viewmodel.settings[1].title}" />

                <TextView
                    android:id="@+id/textView1a3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/settings"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/view1dda4"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator" />

                <include
                    android:id="@+id/print_settings"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[5].arrow}"
                    app:icon="@{viewmodel.settings[5].icon}"
                    app:title="@{viewmodel.settings[5].title}" />

                <include
                    android:id="@+id/reservation_list_mode"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[6].arrow}"
                    app:icon="@{viewmodel.settings[6].icon}"
                    app:title="@{viewmodel.settings[6].title}" />

                <TextView
                    android:id="@+id/textView1sa3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/support"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/view1dd4"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator" />

                <include
                    android:id="@+id/support"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[2].arrow}"
                    app:icon="@{viewmodel.settings[2].icon}"
                    app:title="@{viewmodel.settings[2].title}" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/profile"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/view1sd4"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator" />

                <include
                    android:id="@+id/redeem"
                    layout="@layout/settings_entry"
                    android:visibility="@{viewmodel.showRedeemVoucherEntry ? View.VISIBLE : View.GONE}"
                    app:arrow="@{viewmodel.settings[3].arrow}"
                    app:icon="@{viewmodel.settings[3].icon}"
                    app:title="@{viewmodel.settings[3].title}" />

                <include
                    android:id="@+id/logout"
                    layout="@layout/settings_entry"
                    app:arrow="@{viewmodel.settings[4].arrow}"
                    app:icon="@{viewmodel.settings[4].icon}"
                    app:title="@{viewmodel.settings[4].title}" />

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>