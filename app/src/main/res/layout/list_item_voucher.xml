<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_8"
        android:paddingStart="@dimen/margin_16"
        android:paddingTop="@dimen/margin_8"
        android:paddingEnd="@dimen/margin_16"
        android:paddingBottom="@dimen/margin_8"
        tools:background="@drawable/shape_dotted_background">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="22dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_voucher"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_16"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_code_required"
            android:layout_marginEnd="@dimen/margin_8"
            tools:text="25% off" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            tools:text="Some cool description" />

        <TextView
            android:id="@+id/tv_code_required"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_pill_voucher_code_required"
            android:fontFamily="@font/inter_regular"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/margin_8"
            android:paddingTop="@dimen/margin_4"
            android:paddingEnd="@dimen/margin_8"
            android:paddingBottom="@dimen/margin_4"
            android:text="@string/code_required_label"
            android:textColor="@color/orange800"
            android:textSize="@dimen/text_size_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>