<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.pos.ReservationPosViewModel" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        tools:context=".ui.guest.GuestActivity">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_pos"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="@{viewmodel.posActive &amp; viewmodel.posRecord != null ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:orientation="vertical"
                android:paddingBottom="@dimen/global_margin_16">

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/global_margin_16"
                    android:layout_marginTop="@dimen/section_title_margin_top"
                    android:layout_marginBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/pos_details"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="@dimen/section_title_size" />

                <include
                    layout="@layout/input_regular"
                    bind:enabled="@{false}"
                    bind:inputType="@{InputType.TYPE_TEXT_VARIATION_NORMAL}"
                    bind:key="@{@string/ticket_id}"
                    bind:value="@{viewmodel.posRecord.ticketId}" />

                <include
                    layout="@layout/input_regular"
                    bind:enabled="@{false}"
                    bind:inputType="@{InputType.TYPE_TEXT_VARIATION_NORMAL}"
                    bind:key="@{@string/ticket_opened_at}"
                    bind:value="@{viewmodel.posRecord.ticketOpenedAtS}" />

                <include
                    layout="@layout/input_regular"
                    bind:enabled="@{false}"
                    bind:inputType="@{InputType.TYPE_TEXT_VARIATION_NORMAL}"
                    bind:key="@{@string/ticket_closed_at}"
                    bind:value="@{viewmodel.posRecord.ticketClosedAtS}" />

                <include
                    layout="@layout/input_regular"
                    bind:enabled="@{false}"
                    bind:inputType="@{InputType.TYPE_TEXT_VARIATION_NORMAL}"
                    bind:key="@{@string/server_name}"
                    bind:value="@{viewmodel.posRecord.serverName}" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="@{viewmodel.posRecord.menuItems.size() > 0 ? View.VISIBLE : View.GONE}">

                    <TextView
                        android:id="@+id/textView6"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/global_margin_16"
                        android:layout_marginTop="@dimen/section_title_margin_top"
                        android:layout_marginBottom="@dimen/section_title_margin_bottom"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/menu_items"
                        android:textAllCaps="false"
                        android:textColor="@color/colorDark50"
                        android:textSize="@dimen/section_title_size" />

                    <View
                        android:id="@+id/view15"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:weightSum="11">

                        <TextView
                            android:id="@+id/textView22"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="6"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/name"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/textView23"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="2"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/quantity"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/textView24"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="2"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/price"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view15z"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/menuItemsList"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/pos_record_item">

                    </androidx.recyclerview.widget.RecyclerView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:weightSum="11">

                        <TextView
                            android:id="@+id/textView22c"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_marginEnd="@dimen/global_margin_16"
                            android:layout_weight="8"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/total"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/commercialTotal"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="3"
                            android:fontFamily="@font/inter_bold"
                            android:text='@{String.format("%s %s", viewmodel.posRecord.commercialTotal.toString(), viewmodel.currency)}'
                            android:textAllCaps="false"
                            android:textColor="@color/colorDark100"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view1cc"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="@{viewmodel.posRecord.voidedItems.size() > 0 ? View.VISIBLE : View.GONE}">

                    <TextView
                        android:id="@+id/textView6c"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/global_margin_16"
                        android:layout_marginTop="@dimen/section_title_margin_top"
                        android:layout_marginBottom="@dimen/section_title_margin_bottom"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/voided_items"
                        android:textAllCaps="false"
                        android:textColor="@color/colorDark50"
                        android:textSize="@dimen/section_title_size" />

                    <View
                        android:id="@+id/viewc15"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:weightSum="11">

                        <TextView
                            android:id="@+id/textView2t2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="6"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/name"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/textView2t3"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="2"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/quantity"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />

                        <TextView
                            android:id="@+id/textView2t4"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/global_margin_16"
                            android:layout_weight="2"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/price"
                            android:textAllCaps="false"
                            android:textColor="@color/colorGrey250"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <View
                        android:id="@+id/view1x5"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/voidedItemsList"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/pos_record_item" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <include
            android:id="@+id/pos_active_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            bind:imageId="@{R.drawable.ic_graphic_pos}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:subtitle="@{@string/pos_inactive_subtitle}"
            bind:title="@{@string/pos_inactive_title}"
            android:visibility="@{viewmodel.posActive ? View.GONE : View.VISIBLE}"
            tools:visibility="gone" />

        <include
            android:id="@+id/pos_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            bind:imageId="@{R.drawable.ic_graphic_pos}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:title="@{@string/pos_unmatched}"
            android:visibility="@{viewmodel.posActive &amp; viewmodel.reservation.relationships.posRecord.data == null ? View.VISIBLE : View.GONE}"
            tools:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
