<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge>

        <LinearLayout android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/upcoming"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_rounded_btn_bcg_grey_outline"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/margin_2"
                android:layout_marginBottom="-6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/upcoming_status"
                    android:layout_width="12dp"
                    android:layout_height="13dp"
                    android:layout_marginStart="@dimen/margin_2"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_icon_status_not_confirmed"
                    tools:tint="@color/blue500" />

                <TextView
                    android:id="@+id/upcoming_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:layout_marginStart="@dimen/margin_2"
                    android:layout_marginEnd="@dimen/margin_3"
                    android:textColor="@color/grey800"
                    android:textSize="9sp"
                    android:text="02:00"
                    tools:ignore="SmallSp" />

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_cont"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:elevation="-3dp">

                <TextView
                    android:id="@+id/emoji"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:autoSizeTextType="uniform"
                    android:textAlignment="center"
                    android:textColor="@color/grey900"
                    tools:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="R" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/table_shape"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.github.rongi.rotate_layout.layout.RotateLayout
                        android:id="@+id/shape_cont"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.eatapp.clementine.views.Shape
                            android:id="@+id/shape"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:background="@android:color/transparent" />

                    </com.github.rongi.rotate_layout.layout.RotateLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="@+id/shape_cont"
                        app:layout_constraintEnd_toEndOf="@+id/shape_cont"
                        app:layout_constraintStart_toStartOf="@+id/shape_cont"
                        app:layout_constraintTop_toTopOf="@+id/shape_cont">

                        <ImageView
                            android:id="@+id/icon"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:importantForAccessibility="no"
                            tools:srcCompat="@drawable/ic_icon_appetizer"
                            tools:tint="@color/blue500" />

                        <TextView
                            android:id="@+id/title"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/table_title_height"
                            android:layout_marginTop="-1dp"
                            android:fontFamily="@font/inter_semibold"
                            android:gravity="center"
                            android:paddingStart="3dp"
                            android:paddingEnd="3dp"
                            android:textColor="@color/grey800"
                            android:textSize="10sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            tools:ignore="SmallSp"
                            tools:text="23" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </merge>

</layout>