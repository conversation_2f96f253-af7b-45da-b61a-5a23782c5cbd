<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bcg"
    android:layout_width="wrap_content"
    android:layout_height="32dp"
    android:animateLayoutChanges="true"
    android:background="@drawable/shape_pill_unselected"
    android:clipToPadding="false"
    android:gravity="center"
    android:paddingLeft="12dp"
    android:paddingRight="12dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon_left"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="-8dp"
        android:cropToPadding="false"
        app:tint="@color/colorDark50"
        app:srcCompat="@drawable/ic_icon_group_by"
        tools:srcCompat="@drawable/ic_icon_group_by" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_medium"
        android:textColor="@color/colorDark50"
        android:textSize="12sp"
        tools:text="Lifecycle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon_right"
        android:layout_width="0dp"
        android:layout_height="24dp"
        android:layout_marginEnd="-8dp"
        android:layout_weight="1"
        android:cropToPadding="false"
        app:srcCompat="@drawable/ic_icon_arrow_drop_down"
        app:tint="@color/colorDark50"
        tools:srcCompat="@drawable/ic_icon_arrow_drop_down" />

</LinearLayout>