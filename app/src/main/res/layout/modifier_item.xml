<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable name="currency"
            type="String"/>

        <variable name="item"
            type="com.eatapp.clementine.data.network.response.pos.MenuItem"/>

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:gravity="top"
            android:orientation="horizontal"
            android:weightSum="11"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/textView22"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_weight="6"
                android:fontFamily="@font/inter_medium"
                android:text="@{item.name}"
                android:textAllCaps="false"
                android:textColor="@color/colorGrey250"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/textView23"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="2"
                android:fontFamily="@font/inter_medium"
                android:text='@{String.format("x%s", item.quantity)}'
                android:textAllCaps="false"
                android:textColor="@color/colorGrey250"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/textView24"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_weight="3"
                android:fontFamily="@font/inter_medium"
                android:text='@{String.format("%s %s", item.price, currency)}'
                android:textAllCaps="false"
                android:textColor="@color/colorGrey250"
                android:textSize="13sp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>