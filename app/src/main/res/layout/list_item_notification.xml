<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="notification"
            type="com.eatapp.clementine.data.network.response.notification.Notification" />

        <variable
            name="paymentsActive"
            type="Boolean" />

        <variable
            name="showSource"
            type="Boolean" />

        <variable
            name="showActionsRow"
            type="Boolean" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_8"
        android:paddingEnd="@dimen/margin_8"
        android:paddingTop="@dimen/margin_4"
        android:paddingBottom="@dimen/margin_4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_rounded_btn_bcg_grey_outline_12">

            <LinearLayout
                android:id="@+id/time_cont"
                android:layout_width="65dp"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toTopOf="@+id/action_row"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/text_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:textColor="@color/grey800"
                    android:textSize="15sp"
                    app:layout_constraintStart_toEndOf="@+id/time_separator"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="12:45"
                    android:text="@{notification.reservation.timeHourS}"/>

                <TextView
                    android:id="@+id/text_time_format"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:textColor="@color/grey700"
                    android:textSize="11sp"
                    app:layout_constraintStart_toEndOf="@+id/time_separator"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="PM"
                    android:text="@{notification.reservation.timeMarkerS}"/>

                <TextView
                    android:id="@+id/text_timer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:textColor="@color/grey500"
                    tools:text="23 Feb"
                    android:textSize="12sp"
                    app:layout_constraintStart_toEndOf="@+id/time_separator"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible"
                    android:text='@{notification.reservation.dateS}'/>

            </LinearLayout>

            <View
                android:id="@+id/view7"
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:background="@color/grey300"
                app:layout_constraintBottom_toTopOf="@id/action_row"
                app:layout_constraintStart_toEndOf="@+id/time_cont"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/linearLayout12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:orientation="vertical"
                android:paddingStart="@dimen/margin_16"
                android:paddingTop="@dimen/margin_16"
                android:paddingBottom="@dimen/margin_16"
                app:layout_constraintBottom_toTopOf="@+id/action_row"
                app:layout_constraintEnd_toStartOf="@+id/status_cont"
                app:layout_constraintStart_toEndOf="@+id/view7"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/online_icon"
                        android:layout_width="16dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/margin_4"
                        android:visibility="@{showSource ? View.VISIBLE : View.GONE}"
                        tools:srcCompat="@drawable/ic_icon_whatsapp_logo" />

                    <TextView
                        android:id="@+id/text_guest_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:text="@{notification.reservation.guestNameS}"
                        android:textColor="@color/grey800"
                        android:textSize="15sp"
                        tools:text="Bruce Lee" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_10"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/visits_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_semibold"
                        android:text="@{String.valueOf(notification.reservation.totalVisitsCount)}"
                        android:textColor="@color/grey600"
                        android:textSize="12sp"
                        tools:text="2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_3"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/total_visits"
                        android:textColor="@color/grey500"
                        android:textSize="12sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:text=" • "
                        android:textColor="@color/grey500"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/no_shows_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_semibold"
                        android:text="@{String.valueOf(notification.reservation.noShowCount)}"
                        android:textColor="@color/grey600"
                        android:textSize="12sp"
                        tools:text="2" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_3"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/no_shows"
                        android:textColor="@color/grey500"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/image_covers"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        app:srcCompat="@drawable/ic_icon_people" />

                    <TextView
                        android:id="@+id/text_covers"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="-1dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="@{String.valueOf(notification.reservation.covers)}"
                        android:textColor="@color/grey700"
                        android:textSize="13sp"
                        tools:text="2" />

                    <ImageView
                        android:id="@+id/image_table"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="12dp"
                        app:srcCompat="@drawable/ic_icon_table" />

                    <TextView
                        android:id="@+id/text_table"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="-1dp"
                        android:ellipsize="end"
                        android:fontFamily="@font/inter_semibold"
                        android:maxWidth="100dp"
                        android:maxLines="1"
                        android:text="@{notification.reservation.tablesS}"
                        android:textColor="@color/grey700"
                        android:textSize="13sp"
                        tools:text="13B" />

                    <LinearLayout
                        android:id="@+id/container_payments_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginEnd="4dp"
                        android:orientation="horizontal"
                        android:visibility="@{paymentsActive ? View.VISIBLE : View.GONE}">

                        <ImageView
                            android:layout_width="@dimen/global_icon_size_16"
                            android:layout_height="@dimen/global_icon_size_16"
                            app:srcCompat="@drawable/ic_payment" />

                        <ImageView
                            android:id="@+id/payments_icon"
                            android:layout_width="@dimen/global_icon_size_16"
                            android:layout_height="@dimen/global_icon_size_16"
                            android:layout_marginStart="-5dp"
                            tools:src="@drawable/ic_payment_status_refunded" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/status_cont"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="12dp"
                android:alpha="0.4"
                android:background="@drawable/shape_rounded_bcg_status"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:statusColor="@{notification.reservation.statusColor}">

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/image_status"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:background="@drawable/shape_status_icon_bcg"
                app:icon="@{notification.reservation.statusIcon}"
                app:layout_constraintBottom_toBottomOf="@+id/status_cont"
                app:layout_constraintEnd_toEndOf="@+id/status_cont"
                app:layout_constraintStart_toStartOf="@+id/status_cont"
                app:layout_constraintTop_toTopOf="@+id/status_cont"
                app:statusColor="@{notification.reservation.statusColor}"
                tools:srcCompat="@drawable/ic_icon_status_appetizer" />

            <TextView
                android:id="@+id/notified_at"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="12dp"
                android:text="@{notification.notifiedAtS}"
                android:textColor="@color/grey700"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/status_cont"
                tools:text="5m" />

            <View
                android:id="@+id/view8"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/grey300"
                android:visibility="@{showActionsRow ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toTopOf="@id/action_row"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <LinearLayout
                android:id="@+id/action_row"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="8dp"
                android:visibility="@{showActionsRow ? View.VISIBLE : View.GONE}"
                app:layout_constraintStart_toStartOf="parent"
                android:animateLayoutChanges="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginTop="8dp">

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/confirm_btn"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="1.5"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/shape_rounded_btn_bcg_green"
                    app:progressBarColor="@color/white"
                    app:tintColor="@{R.color.white}"
                    app:title="@string/confirm" />

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/decline_btn"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_weight="1"
                    android:visibility="visible"
                    android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                    app:progressBarColor="@color/green500"
                    app:tintColor="@{R.color.green500}"
                    app:title="@string/decline"
                    app:strokeWidth="2dp" />

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/waitlist_btn"
                    android:layout_width="0dp"
                    android:layout_height="36dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:visibility="visible"
                    android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                    app:progressBarColor="@color/green500"
                    app:tintColor="@{R.color.green500}"
                    app:title="@string/waitlist"
                    app:strokeWidth="2dp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout> 