<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
                name="viewmodel"
                type="com.eatapp.clementine.ui.common.hubspot.HubspotViewModel"/>

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginTop="10dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@+id/webView"
            app:layout_constraintEnd_toEndOf="@+id/webView"
            app:layout_constraintStart_toStartOf="@+id/webView"
            app:layout_constraintTop_toTopOf="@+id/webView" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>