<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/shape_pill_unselected"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginEnd="@dimen/margin_8"
        android:paddingStart="@dimen/margin_10"
        android:paddingEnd="@dimen/margin_8">

        <ImageView
            android:layout_marginEnd="@dimen/margin_4"
            android:visibility="gone"
            android:id="@+id/iv_left"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_icon_check"
            app:tint="@color/green500" />

        <TextView
            android:includeFontPadding="false"
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_14"
            tools:text="25% off" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/margin_4"
            tools:src="@drawable/ic_voucher" />

    </LinearLayout>
</layout>