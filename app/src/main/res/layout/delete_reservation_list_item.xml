<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="isFreemium"
            type="Boolean" />

        <variable
            name="deleteButtonVisible"
            type="Boolean" />

        <variable
            name="permissionReservation"
            type="com.eatapp.clementine.data.network.response.user.Permission" />

        <import type="com.eatapp.clementine.R" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:background="@color/mainBcg"
        android:layout_height="wrap_content">

        <com.eatapp.clementine.views.LoadingButton
            android:layout_marginTop="@dimen/margin_32"
            android:id="@+id/btn_delete"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@{isFreemium || permissionReservation.boolValue ? @drawable/shape_rounded_btn_bcg_grey_outline_200 : @drawable/shape_rounded_btn_bcg_red_outline}"
            android:enabled="@{!isFreemium}"
            app:progressBarColor="@color/colorRed"
            app:title="@string/delete_reservation"
            app:tintColor="@{isFreemium || permissionReservation.boolValue ? R.color.colorGrey200 : R.color.colorRed}"
            app:visible_or_gone="@{deleteButtonVisible}" />

    </LinearLayout>
</layout>