<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.launch.SignUpViewModel" />

        <import type="android.util.Pair" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:elevation="4dp"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <EditText
                android:id="@+id/email"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height"
                android:background="@null"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/email_hint"
                android:inputType="textEmailAddress"
                android:paddingStart="@dimen/register_input_padding"
                android:paddingEnd="@dimen/register_input_padding"
                android:text="@={viewmodel.email}"
                android:textColor="@color/colorDark50"
                android:textColorHint="@color/colorGrey200"
                android:textSize="13sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <com.scottyab.showhidepasswordedittext.ShowHidePasswordEditText
                android:id="@+id/password"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height"
                android:background="@null"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/password_hint"
                android:inputType="textPassword"
                android:paddingStart="@dimen/register_input_padding"
                android:paddingEnd="@dimen/register_input_padding"
                android:text="@={viewmodel.password}"
                android:textColor="@color/colorDark50"
                android:textColorHint="@color/colorGrey200"
                android:textSize="13sp"
                app:tint_color="@color/colorGrey200" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <EditText
                android:id="@+id/name"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height"
                android:background="@null"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/venue_name_hint"
                android:inputType="textPersonName|textCapWords"
                android:paddingStart="@dimen/register_input_padding"
                android:paddingEnd="@dimen/register_input_padding"
                android:text="@={viewmodel.name}"
                android:textColor="@color/colorDark50"
                android:textColorHint="@color/colorGrey200"
                android:textSize="13sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/zone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height">

                <TextView
                    android:id="@+id/textView132"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@null"
                    android:ems="10"
                    android:fontFamily="@font/inter_regular"
                    android:gravity="center_vertical"
                    android:hint="@string/time_zone_hint"
                    android:paddingStart="@dimen/register_input_padding"
                    android:paddingEnd="@dimen/register_input_padding"
                    android:text="@{viewmodel.zone.first}"
                    android:textColor="@color/colorDark50"
                    android:textColorHint="@color/colorGrey200"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.39" />

                <ImageView
                    android:id="@+id/imageView73"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_icon_arrow_down"
                    app:tint="@color/colorDark100" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height">

                <LinearLayout
                    android:id="@+id/phonePrefixCont"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/phonePrefix"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:fontFamily="@font/inter_regular"
                        android:gravity="center_vertical"
                        android:paddingLeft="@dimen/register_input_padding"
                        android:text='@{viewmodel.phonePrefix.equals("CODE") ? viewmodel.phonePrefix : String.format("%s%s", "+", viewmodel.phonePrefix)}'
                        android:textColor="@color/colorDark50"
                        android:textColorHint="@color/colorGrey200"
                        android:textSize="13sp"
                        tools:text="+971" />

                    <ImageView
                        android:id="@+id/imageView7"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="4dp"
                        android:layout_marginLeft="4dp"
                        android:layout_marginTop="2dp"
                        android:layout_marginEnd="4dp"
                        app:srcCompat="@drawable/ic_icon_arrow_down"
                        app:tint="@color/colorDark100" />

                </LinearLayout>

                <View
                    android:id="@+id/view9"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/phonePrefixCont"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/phoneNumber"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@null"
                    android:ems="10"
                    android:fontFamily="@font/inter_regular"
                    android:hint="@string/venue_phone_number_hint"
                    android:inputType="phone"
                    android:paddingStart="@dimen/register_input_padding"
                    android:paddingEnd="@dimen/register_input_padding"
                    android:text="@={viewmodel.phoneNumber}"
                    android:textColor="@color/colorDark50"
                    android:textColorHint="@color/colorGrey200"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/view9"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/btnRegister"
            android:layout_width="match_parent"
            android:layout_height="@dimen/register_button_height"
            android:background="@drawable/shape_rounded_bottom_btn_bcg_green"
            android:onClick="@{() -> viewmodel.signUpBtnClick()}"
            app:progressBarColor="@color/white"
            app:title="@string/register"
            app:tintColor="@{R.color.white}" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <CheckBox
                android:buttonTint="@color/white"
                android:id="@+id/cb_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="@={viewmodel.signInEnabled}" />

            <TextView
                android:id="@+id/tv_privacy"
                android:layout_marginStart="@dimen/margin_4"
                android:textColorLink="@color/white"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/privacy_policy_link" />
        </LinearLayout>

    </LinearLayout>

</layout>