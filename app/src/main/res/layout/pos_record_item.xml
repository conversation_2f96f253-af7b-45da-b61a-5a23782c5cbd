<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable name="currency"
            type="String"/>

        <variable name="item"
            type="com.eatapp.clementine.data.network.response.pos.MenuItem"/>

        <import type="android.view.View" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/linearLayout6"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:weightSum="11">

            <TextView
                android:id="@+id/textView22"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_weight="6"
                android:fontFamily="@font/inter_bold"
                android:text="@{item.name}"
                android:textAllCaps="false"
                android:textColor="@color/colorDark100"
                android:textSize="13sp"
                tools:text="Item record" />

            <TextView
                android:id="@+id/textView23"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_weight="2"
                android:fontFamily="@font/inter_bold"
                android:text='@{String.format("x%s", item.quantity)}'
                android:textAllCaps="false"
                android:textColor="@color/colorDark100"
                android:textSize="13sp"
                tools:text="x2" />

            <TextView
                android:id="@+id/textView24"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_weight="3"
                android:fontFamily="@font/inter_bold"
                android:text='@{String.format("%s %s", item.price, currency)}'
                android:textAllCaps="false"
                android:textColor="@color/colorDark100"
                android:textSize="13sp"
                tools:text="12 AED" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/modifiersList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/modifier_item" />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="@color/colorSeparator" />

    </LinearLayout>

</layout>