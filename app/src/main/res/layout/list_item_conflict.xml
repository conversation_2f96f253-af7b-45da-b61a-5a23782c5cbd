<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="permission"
            type="com.eatapp.clementine.data.network.response.user.Permission" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_4"
        android:background="@drawable/shape_rounded_bcg_white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/margin_12"
        android:paddingTop="@dimen/margin_8"
        android:paddingEnd="@dimen/margin_12"
        android:paddingBottom="@dimen/margin_8"
        bind:backgroundTint="@{permission.value.equals(OptionType.Error.type) ? R.color.red50 : R.color.orange50}">

        <ImageView
            android:id="@+id/icon_error"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_icon_info"
            bind:tintColor="@{permission.value.equals(OptionType.Error.type) ? R.color.red800 : R.color.orange600}" />

        <TextView
            android:id="@+id/text_error"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_8"
            android:layout_weight="1"
            android:text="@{permission.errorMessage}"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_13"
            tools:text="There is another reservation on this table at the sdsd selected time" />

        <ImageView
            android:id="@+id/icon_close"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="@dimen/margin_8"
            android:src="@drawable/ic_icon_close" />

    </LinearLayout>
</layout>