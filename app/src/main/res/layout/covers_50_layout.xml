<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/global_margin_16">

        <TextView
            android:id="@+id/textView16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:fontFamily="@font/inter_semibold"
            android:text="@string/popup_upgrade_package"
            android:textColor="@color/colorDark50"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/textView15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:fontFamily="@font/inter_regular"
            android:text="@string/covers_50_text_1"
            android:textAlignment="textStart"
            android:textColor="@color/colorGrey700"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textView15asd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:fontFamily="@font/inter_regular"
            android:text="@string/covers_50_text_2"
            android:textAlignment="textStart"
            android:textColor="@color/colorGrey700"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textView15asasdd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:fontFamily="@font/inter_regular"
            android:text="@string/covers_50_text_3"
            android:textAlignment="textStart"
            android:textColor="@color/colorGrey700"
            android:textSize="14sp" />

    </LinearLayout>

</merge>
