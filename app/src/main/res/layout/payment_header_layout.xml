<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/margin_16">

        <TextView
            android:id="@+id/amount_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:text="@string/amount_label"
            android:textColor="@color/grey700"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/amount_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:fontFamily="@font/inter_medium"
            tools:text="9000 AED"
            android:textColor="@color/grey900"
            android:textSize="@dimen/text_size_15"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/container_internal_notes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:orientation="vertical">

            <TextView
                android:id="@+id/internal_notes_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/payment_internal_notes_label"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/internal_notes_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_8"
                android:fontFamily="@font/inter_medium"
                tools:text="Testing internal notes"
                android:textColor="@color/grey900"
                android:textSize="@dimen/text_size_15"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>

        <TextView
            android:id="@+id/description_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:fontFamily="@font/inter_regular"
            android:text="@string/payment_description_label"
            android:textColor="@color/grey700"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/description_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:fontFamily="@font/inter_medium"
            tools:text="Testing description value"
            android:textColor="@color/grey900"
            android:textSize="@dimen/text_size_15"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>
</layout>