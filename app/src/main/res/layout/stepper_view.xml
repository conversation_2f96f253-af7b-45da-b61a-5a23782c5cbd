<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="bottom"
            android:orientation="vertical"
            tools:context="views.StepperView">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/input_padding_side"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="@dimen/input_spacing"
                android:fontFamily="@font/inter_regular"
                android:lines="1"
                android:maxLines="1"
                android:textAllCaps="true"
                android:textColor="@color/colorGrey200"
                android:textSize="@dimen/input_key_size"
                app:layout_constraintBottom_toTopOf="@+id/value"
                app:layout_constraintEnd_toStartOf="@+id/minus"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="KEY" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/input_padding_side"
                android:layout_marginEnd="-8dp"
                android:layout_marginBottom="@dimen/input_padding_bottom"
                android:background="@android:color/transparent"
                android:ellipsize="marquee"
                android:fontFamily="@font/inter_medium"
                android:lines="1"
                android:maxLines="1"
                android:textColor="@color/grey800"
                android:textSize="@dimen/input_value_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/minus"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="06:15 PM" />

            <ImageButton
                android:id="@+id/minus"
                android:layout_width="48dp"
                android:layout_height="0dp"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_icon_minus_filled"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/plus"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                tools:ignore="ContentDescription" />

            <ImageButton
                android:id="@+id/plus"
                android:layout_width="48dp"
                android:layout_height="0dp"
                android:layout_marginEnd="6dp"
                android:background="?attr/selectableItemBackground"
                android:src="@drawable/ic_icon_plus_filled"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <View
                android:id="@+id/disable"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="gone" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_conflicts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:paddingBottom="8dp"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_conflict" />

        <View
            android:id="@+id/separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:visibility="visible"
            android:background="@color/colorSeparator" />

    </LinearLayout>
</layout>