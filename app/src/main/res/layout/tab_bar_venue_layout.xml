<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_rounded_bcg_grey800">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_restaurant_selector"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/margin_12"
            app:layout_constraintBottom_toTopOf="@id/container_restaurants"
            app:layout_constraintStart_toStartOf="parent">

            <View
                android:id="@+id/separator"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/grey900"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/container_restaurant_short_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginTop="@dimen/margin_12"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/shape_reserve_btn_bcg_orange"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/green100" />

                <TextView
                    android:id="@+id/tv_restaurant_short_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:textColor="@color/grey900"
                    android:textSize="@dimen/text_size_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="PKC" />

                <LinearLayout
                    android:id="@+id/container_icon_arrow"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:background="@drawable/shape_rounded_bcg_grey"
                    android:backgroundTint="@color/white"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_icon_arrow_drop_down" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/container_restaurant_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginEnd="@dimen/margin_4"
                android:orientation="vertical"
                app:layout_constraintEnd_toStartOf="@id/iv_chevron"
                app:layout_constraintStart_toEndOf="@id/container_restaurant_short_name"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_restaurant_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_semibold"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_16"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Eat demo restaurant" />

                <TextView
                    android:id="@+id/tv_restaurant_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_regular"
                    android:maxLines="1"
                    android:textColor="@color/grey300"
                    android:textSize="@dimen/text_size_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="<EMAIL>" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_chevron"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_12"
                android:rotation="180"
                android:src="@drawable/ic_icon_arrow_drop_down"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/container_restaurants"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <EditText
                android:id="@+id/et_search_restaurant"
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:background="@color/search_bar_color"
                android:drawableStart="@drawable/ic_icon_search_grey500"
                android:drawablePadding="@dimen/margin_12"
                android:fontFamily="@font/inter_medium"
                android:hint="@string/search_label"
                android:paddingStart="@dimen/margin_12"
                android:paddingEnd="@dimen/margin_12"
                android:textColor="@color/white"
                android:textColorHint="@color/grey500"
                android:textCursorDrawable="@null"
                android:textSize="@dimen/text_size_16" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_restaurants"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/list_item_navigation_restaurant" />

            <LinearLayout
                android:id="@+id/container_logout"
                android:layout_width="match_parent"
                android:clickable="true"
                android:focusable="true"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/grey600" />

                <Button
                    android:layout_width="wrap_content"
                    android:clickable="false"
                    android:layout_height="64dp"
                    android:layout_marginStart="@dimen/margin_12"
                    android:background="@android:color/transparent"
                    android:drawableStart="@drawable/ic_navigation_logout"
                    android:fontFamily="@font/inter_semibold"
                    android:text="@string/log_out"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_14" />
            </LinearLayout>

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>