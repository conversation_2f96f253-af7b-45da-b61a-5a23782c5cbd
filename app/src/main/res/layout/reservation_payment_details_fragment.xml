<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.payments.details.ReservationPaymentDetailsViewModel" />

        <import type="com.eatapp.clementine.R" />

        <import type="com.eatapp.clementine.data.repository.PaymentActionType" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_gateway_actions"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="-1dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constrainedHeight="true"
            app:layout_constraintBottom_toTopOf="@+id/container_buttons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/list_item_reservation_payment" />

        <com.eatapp.clementine.views.ReservationPaymentListItem
            android:id="@+id/payment_list_item"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/grey50"
            android:orientation="horizontal"
            android:paddingStart="@dimen/margin_8"
            android:paddingEnd="@dimen/margin_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_edit"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginStart="@dimen/margin_8"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_grey_outline_700"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/grey700"
                app:tintColor="@{R.color.grey700}"
                app:title="@string/edit_label"
                tools:visibility="visible" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_void"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_red_outline"
                android:onClick="@{(v) -> viewmodel.updatePayment(PaymentActionType.VOID, v)}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_refund"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/red800"
                app:tintColor="@{R.color.red800}"
                app:title="@string/void_label"
                tools:visibility="visible" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_refund"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_red_outline"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_cancel"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/red800"
                app:tintColor="@{R.color.red800}"
                app:title="@string/refund_label"
                tools:visibility="visible" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_red_outline"
                android:onClick="@{(v) -> viewmodel.updatePayment(PaymentActionType.CANCEL, v)}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_capture"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/red800"
                app:tintColor="@{R.color.red800}"
                app:title="@string/cancel_label" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_capture"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                android:onClick="@{(v) -> viewmodel.updatePayment(PaymentActionType.CAPTURE, v)}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_send_reminder"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/green500"
                app:tintColor="@{R.color.green500}"
                app:title="@string/capture_label" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_send_reminder"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_12"
                android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/green500"
                app:tintColor="@{R.color.green500}"
                app:title="@string/send_reminder_label" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>