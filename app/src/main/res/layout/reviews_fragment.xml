<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.reports.ReviewsViewModel" />

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        tools:context=".ui.home.reports.ReviewsFragment">


        <View
            android:id="@+id/separator_top"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/reviewsCountCont"
            android:layout_width="match_parent"
            android:layout_height="74dp"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/separator_top">

            <ImageButton
                android:id="@+id/imageButton4"
                android:layout_width="@dimen/toolbar_icon_size"
                android:layout_height="@dimen/toolbar_icon_size"
                android:layout_marginStart="4dp"
                android:layout_marginTop="1dp"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> viewmodel.plusDay()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_icon_arrow_right"
                app:tint="@color/grey700"
                tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck" />

            <ImageButton
                android:id="@+id/imageButton3"
                android:layout_width="@dimen/toolbar_icon_size"
                android:layout_height="@dimen/toolbar_icon_size"
                android:layout_marginTop="1dp"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> viewmodel.minusDay()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/date"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_icon_arrow_left"
                app:tint="@color/grey700"
                tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

            <TextView
                android:id="@+id/date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/grey700"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/imageButton4"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Thu, 13 Jul" />

            <ProgressBar
                android:id="@+id/progressReviews"
                style="?android:attr/progressBarStyle"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_weight="1"
                android:indeterminateTint="@color/green500"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/imageButton4"
                app:layout_constraintStart_toEndOf="@+id/imageButton3"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/totalReviewsMain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_bold"
                android:textColor="@color/colorPrimary"
                android:textSize="29sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="0" />

            <TextView
                android:id="@+id/textView12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_8"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/reviews"
                android:textColor="@color/colorDark100"
                android:layout_marginTop="2dp"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/totalReviewsMain"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/separator_middle"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="@+id/reviewsCountCont"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <EditText
            android:id="@+id/searchEditText"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:ems="10"
            android:fontFamily="@font/inter_regular"
            android:hint="@string/review_search_hint"
            android:inputType="textPersonName"
            android:onTextChanged="@{viewmodel::onTextChanged}"
            android:paddingStart="15dp"
            android:textColor="@color/colorDark50"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/reviewsCountCont"
            tools:visibility="visible" />

        <View
            android:id="@+id/list_separator"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/searchEditText"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/reviews_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/list_separator"
            tools:context=".ui.home.overview.ReservationsFragment"
            tools:listitem="@layout/list_item_review" />

        <include
            android:id="@+id/empty_list"
            layout="@layout/empty_reviews_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="70dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/list_separator" />

        <include
            android:id="@+id/empty_search_list"
            layout="@layout/empty_reviews_search_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/searchEditText"
            tools:visibility="gone" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_reviews"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>