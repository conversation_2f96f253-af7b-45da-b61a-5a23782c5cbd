<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/reservationTagsCont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:layout_marginEnd="@dimen/margin_16"
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginBottom="@dimen/section_title_margin_bottom"
            android:fontFamily="@font/inter_regular"
            android:textAllCaps="false"
            android:textColor="@color/grey500"
            android:textSize="@dimen/section_title_size" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_choices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_8"
            android:clipToPadding="false"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_multiple_choice" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator" />

    </LinearLayout>
</layout>
