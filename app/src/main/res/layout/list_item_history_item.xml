<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="editClickListener"
            type="android.view.View.OnClickListener"/>

        <variable
            name="item"
            type="com.eatapp.clementine.data.network.response.reservation.StatusHistory" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="android.graphics.Color" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/grey800"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Alison Wonderland" />

        <TextView
            android:id="@+id/date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/grey700"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/title"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:text="03:36 PM, Sun 03 March" />

        <ImageView
            android:id="@+id/icon"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="24dp"
            android:background="@{item.last ? @drawable/shape_status_stroke_green : null}"
            android:padding="1dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/shape_status_stroke_green"
            tools:src="@drawable/ic_icon_status_canceled" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>