<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.selector.RestaurantsViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        tools:context=".ui.common.selector.RestaurantsFragment">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/search_layout"
            android:layout_width="0dp"
            android:layout_height="@dimen/tab_height"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            android:visibility="@{viewmodel.restaurants.size() > 5 ? View.VISIBLE : View.GONE, default=visible}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/white"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/restaurants_search_hint"
                android:inputType="textPersonName"
                android:paddingStart="15dp"
                android:paddingEnd="0dp"
                android:text="@={viewmodel.search}"
                android:textColor="@color/colorDark50"
                android:textSize="16sp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/cancelIcon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/cancelIcon"
                android:layout_width="54dp"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                android:contentDescription="Search Icon"
                android:src="@{viewmodel.search.isEmpty() ? @drawable/ic_icon_search : @drawable/ic_icon_cancel, default=@drawable/ic_icon_search}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:tint="@color/colorDark50" />

            <View
                android:id="@+id/view3"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/restaurants_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/bottomButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/search_layout"
            app:layout_constraintVertical_bias="0.0"
            tools:context=".ui.home.guests.GuestFragment"
            tools:listitem="@layout/list_item_guest" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/changeBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                android:visibility="@{viewmodel.preSelectedRestaurant == viewmodel.selectedRestaurant ? View.GONE : View.VISIBLE, default=visible}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:title="@string/change_restaurant"
                app:tintColor="@{R.color.white}"
                bind:layout_constraintEnd_toEndOf="parent"
                bind:layout_constraintStart_toStartOf="parent"
                bind:progressBarColor="@color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>