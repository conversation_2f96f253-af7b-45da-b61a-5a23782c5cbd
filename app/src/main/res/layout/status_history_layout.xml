<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_16"
        android:layout_marginEnd="@dimen/margin_16"
        android:background="@drawable/shape_rounded_bcg_white"
        app:cardCornerRadius="@dimen/margin_12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="24dp"
                android:fontFamily="@font/inter_medium"
                android:text="@string/activity"
                android:textColor="@color/grey800"
                android:textSize="20sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_history_items"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingBottom="24dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>