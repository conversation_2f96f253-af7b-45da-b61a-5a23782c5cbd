<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
                name="clickListener"
                type="android.view.View.OnClickListener"/>

        <variable name="item"
                  type="com.eatapp.clementine.data.network.response.restaurant.Restaurant"/>

        <import type="android.view.View" />

    </data>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{clickListener}"
            android:orientation="vertical"
            android:background="@color/white">

        <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:gravity="center_vertical"
                android:layout_weight="1" android:paddingLeft="@dimen/input_padding_side">

            <TextView
                    android:text='@{item.name}'
                    android:layout_width="0dp"
                    android:ellipsize="end"
                    android:layout_height="wrap_content"
                    android:id="@+id/title"
                    tools:text="Bruce Lee Bruce Lee Bruce Lee Bruce LeeBruce Lee"
                    android:fontFamily="@font/inter_regular" android:textColor="@color/colorDark200"
                    android:textSize="15sp" android:layout_weight="1" android:layout_marginTop="12dp"
                    android:layout_marginBottom="13dp"/>

            <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_icon_radio_on"
                    tools:src="@drawable/ic_icon_radio_on"
                    tools:layout_editor_absoluteY="-42dp" tools:layout_editor_absoluteX="-46dp"
                    android:id="@+id/imageView5" android:layout_marginStart="16dp" android:layout_marginEnd="16dp"
                    app:tint="@color/colorPrimary"
                    android:visibility="@{item.isSelected ? View.VISIBLE : View.INVISIBLE}"
                    tools:tint="@color/colorPrimary"/>

        </LinearLayout>

        <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:id="@+id/bottom_separator"
                android:background="@color/colorSeparator"/>

    </LinearLayout>

</layout>