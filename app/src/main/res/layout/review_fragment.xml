<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.reports.ReviewViewModel"/>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/guestName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/inter_medium"
            android:text="@{viewmodel.reservation.guestName}"
            android:textColor="@color/colorDark100"
            android:textSize="15sp"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Bruce Lee" />

        <ImageView
            android:id="@+id/imageView8"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guestName"
            app:srcCompat="@drawable/ic_icon_time" />

        <TextView
            android:id="@+id/createdAt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:fontFamily="@font/inter_medium"
            android:text='@{String.format("%s %s", viewmodel.reservation.timeHourS, viewmodel.reservation.timeMarkerS)}'
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView8"
            app:layout_constraintStart_toEndOf="@+id/imageView8"
            app:layout_constraintTop_toTopOf="@+id/imageView8"
            tools:text="01:00PM" />

        <ImageView
            android:id="@+id/imageView18"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toEndOf="@+id/createdAt"
            app:layout_constraintTop_toBottomOf="@+id/guestName"
            app:srcCompat="@drawable/ic_icon_people" />

        <TextView
            android:id="@+id/covers"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:fontFamily="@font/inter_medium"
            android:text="@{viewmodel.reservation.coversS}"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView18"
            app:layout_constraintStart_toEndOf="@+id/imageView18"
            app:layout_constraintTop_toTopOf="@+id/imageView18"
            tools:text="2" />

        <ImageView
            android:id="@+id/imageView19"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toEndOf="@+id/covers"
            app:layout_constraintTop_toBottomOf="@+id/guestName"
            app:srcCompat="@drawable/ic_icon_table" />

        <TextView
            android:id="@+id/table"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:ellipsize="end"
            android:fontFamily="@font/inter_medium"
            android:maxWidth="100dp"
            android:maxLines="1"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text="@{viewmodel.reservation.tablesS}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView19"
            app:layout_constraintStart_toEndOf="@+id/imageView19"
            app:layout_constraintTop_toTopOf="@+id/imageView19"
            tools:text="13B" />

        <TextView
            android:id="@+id/textViewe2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/overall"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/createdAt" />

        <TextView
            android:id="@+id/overall"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text='@{String.format("%s", viewmodel.reservation.review.rating)}'
            app:layout_constraintStart_toEndOf="@+id/textViewe2"
            app:layout_constraintTop_toBottomOf="@+id/createdAt"
            tools:text="4.0" />

        <TextView
            android:id="@+id/textViewe"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/food"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintStart_toEndOf="@+id/overall"
            app:layout_constraintTop_toBottomOf="@+id/createdAt" />

        <TextView
            android:id="@+id/food"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text="@{viewmodel.reservation.review.food}"
            app:layout_constraintStart_toEndOf="@+id/textViewe"
            app:layout_constraintTop_toBottomOf="@+id/createdAt"
            tools:text="4.0" />

        <TextView
            android:id="@+id/textViewe4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/service"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintStart_toEndOf="@+id/food"
            app:layout_constraintTop_toBottomOf="@+id/createdAt" />

        <TextView
            android:id="@+id/service"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text="@{viewmodel.reservation.review.service}"
            app:layout_constraintStart_toEndOf="@+id/textViewe4"
            app:layout_constraintTop_toBottomOf="@+id/createdAt"
            tools:text="4.0" />

        <TextView
            android:id="@+id/ambience"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="14dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/ambience"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintStart_toEndOf="@+id/service"
            app:layout_constraintTop_toBottomOf="@+id/createdAt" />

        <TextView
            android:id="@+id/guestName5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text="@{viewmodel.reservation.review.ambience}"
            app:layout_constraintStart_toEndOf="@+id/ambience"
            app:layout_constraintTop_toBottomOf="@+id/createdAt"
            tools:text="4.0" />

        <View
            android:id="@+id/middle_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:background="@color/colorSeparator"
            android:visibility="@{TextUtils.isEmpty(viewmodel.reservation.review.comment) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textViewe2" />

        <TextView
            android:id="@+id/comment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:fontFamily="@font/inter_regular"
            android:text="@{viewmodel.reservation.review.comment}"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:visibility="@{TextUtils.isEmpty(viewmodel.reservation.review.comment) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/middle_separator"
            tools:text="Here’s an awesome review for this restaurant. The food was great, wine list was amazing. Here’s an awesome review for this restaurant. The food was gr Here’s an awesome review for this restaurant. The food was great, wine list was amazing." />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/comment" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>