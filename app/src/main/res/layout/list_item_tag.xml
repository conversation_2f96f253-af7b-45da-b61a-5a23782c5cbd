<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="tag"
            type="com.eatapp.clementine.internal.SelectorItem" />

        <variable
            name="editable"
            type="Boolean" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginEnd="@dimen/margin_8"
        android:background="@{tag.isAddItem ? (editable ? @drawable/shape_pill_selected_green : @drawable/shape_pill_selected) : @drawable/shape_tag_bcg, default=@drawable/shape_tag_bcg}"
        android:onClick="@{clickListener}">


        <FrameLayout
            android:id="@+id/tag_bcg_white"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="1dp"
            android:layout_marginTop="1dp"
            android:layout_marginEnd="1dp"
            android:layout_marginBottom="1dp"
            android:background="@drawable/shape_tag_bcg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tintColor="@{R.color.white}"
            app:visible_or_gone="@{!tag.isAddItem}" />

        <View
            android:id="@+id/tag_bcg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visible_or_gone="@{!tag.isAddItem}"
            android:alpha="0.3"
            android:background="@drawable/shape_tag_bcg" />

        <ImageView
            android:id="@+id/tag_icon"
            android:layout_width="@dimen/global_icon_size_16"
            android:layout_height="@dimen/global_icon_size_16"
            android:layout_marginStart="@dimen/margin_12"
            android:tagIcon="@{tag}"
            android:visibility="@{tag.icon == null ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@drawable/fish_icon"
            tools:visibility="visible"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tag_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_5"
            android:layout_marginEnd="@dimen/margin_4"
            android:fontFamily="@font/inter_medium"
            android:gravity="center_vertical"
            android:text='@{tag.name}'
            android:textColor="@{tag.isAddItem ? (editable ? @color/white : @color/colorGrey200) : @color/colorDark200}"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/icon_remove_tag"
            app:layout_constraintStart_toEndOf="@id/tag_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/margin_12"
            app:layout_goneMarginStart="@dimen/margin_12"
            tools:text="Tag One" />

        <ImageView
            android:id="@+id/icon_remove_tag"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="@dimen/margin_8"
            android:visibility="@{tag.isAddItem ? View.GONE : editable ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tag_name"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_cancel"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>