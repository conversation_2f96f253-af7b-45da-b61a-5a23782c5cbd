<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.payments.create.CreatePaymentViewModel" />

        <import type="com.eatapp.clementine.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@id/container_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_16">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/payment_description_title"
                    android:textColor="@color/grey800"
                    android:textSize="@dimen/text_size_13" />

                <EditText
                    android:id="@+id/text_payment_description"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/edittext_height"
                    android:layout_marginTop="@dimen/margin_4"
                    android:background="@drawable/shape_rounded_edittext_bcg"
                    android:hint="@string/payment_description_hint"
                    android:paddingStart="@dimen/margin_16"
                    android:paddingEnd="@dimen/margin_8"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey800"
                    android:textColorHint="@color/grey500"
                    android:textSize="@dimen/text_size_14"
                    android:text="@={viewmodel.paymentDescription}"
                    tools:hint="A payment is required to hold this slot" />

                <com.eatapp.clementine.views.CreatePaymentListItem
                    android:id="@+id/container_payment_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12"
                    app:childrenClickable="@{false}"
                    app:editable="@{false}"
                    app:title="@{@string/payment_type_label}" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12">

                    <com.eatapp.clementine.views.CreatePaymentListItem
                        android:id="@+id/container_payment_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_8"
                        app:layout_constraintEnd_toStartOf="@id/guideline"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:showChevron="@{false}"
                        app:title="@{@string/payment_amount_title}" />

                    <com.eatapp.clementine.views.CreatePaymentListItem
                        android:id="@+id/container_payment_rules"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_8"
                        android:visibility="gone"
                        app:childrenClickable="@{false}"
                        app:editable="@{false}"
                        app:layout_constraintEnd_toStartOf="@id/guideline"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:title="@{@string/payment_package_label}" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />

                    <com.eatapp.clementine.views.CreatePaymentListItem
                        android:id="@+id/container_payment_total"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_8"
                        app:editable="@{false}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/guideline"
                        app:layout_constraintTop_toTopOf="parent"
                        app:showChevron="@{false}" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.eatapp.clementine.views.CreatePaymentListItem
                    android:id="@+id/container_payment_auto_cancellation_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12"
                    app:childrenClickable="@{false}"
                    app:editable="@{false}"
                    app:hint="@{@string/payment_amount_auto_cancellation_date_hint}"
                    app:title="@{@string/payment_amount_auto_cancellation_date_title}" />

                <com.eatapp.clementine.views.CreatePaymentListItem
                    android:id="@+id/container_payment_auto_cancellation_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12"
                    app:childrenClickable="@{false}"
                    app:editable="@{false}"
                    app:hint="@{@string/payment_amount_auto_cancellation_date_hint}"
                    app:title="@{@string/payment_amount_auto_cancellation_time_title}" />

                <com.eatapp.clementine.views.CreatePaymentListItem
                    android:id="@+id/container_payment_charge_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12"
                    app:childrenClickable="@{false}"
                    app:editable="@{false}"
                    app:title="@{@string/payment_amount_charge_type_title}" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_12">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:padding="2dp"
                        android:src="@drawable/ic_icon_info"
                        app:tint="@color/grey800" />

                    <TextView
                        android:id="@+id/text_charge_type_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_regular"
                        android:textColor="@color/grey700"
                        android:textSize="@dimen/text_size_11" />
                </LinearLayout>

                <com.eatapp.clementine.views.CreatePaymentListItem
                    android:id="@+id/container_payment_internal_notes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_16"
                    app:showChevron="@{false}"
                    app:title="@{@string/payment_internal_notes_label}" />

            </LinearLayout>

        </ScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_create_payment"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:layout_marginBottom="@dimen/global_margin_16"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:loading="@{viewmodel.loading}"
                app:progressBarColor="@color/white"
                app:title="@{viewmodel.isInEditMode() ? @string/edit_label : @string/create_payment_label}"
                app:tintColor="@{R.color.white}"
                tools:title="@string/create_payment" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>