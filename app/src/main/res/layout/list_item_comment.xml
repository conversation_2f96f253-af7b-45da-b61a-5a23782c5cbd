<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="editClickListener"
            type="android.view.View.OnClickListener"/>

        <variable
            name="permission"
            type="com.eatapp.clementine.data.network.response.user.Permission"/>

        <variable
            name="comment"
            type="com.eatapp.clementine.data.network.response.comment.Comment" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="android.graphics.Color" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="9dp"
            android:fontFamily="sans-serif-medium"
            android:textColor="@{Color.parseColor(comment.color)}"
            android:text="@{comment.name}"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Alison Wonderland" />

        <EditText
            android:id="@+id/com"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:background="@{comment.isInEditMode ? @color/colorGreen05 : @android:color/transparent}"
            android:fontFamily="@font/inter_regular"
            android:padding="@{comment.isInEditMode ? 15 : 0}"
            android:text="@{comment.comment}"
            android:enabled="@{comment.isInEditMode}"
            android:textColor="@color/colorDark200"
            android:textSize="15sp"
            app:layout_constraintEnd_toStartOf="@+id/edit_icon"
            app:layout_constraintStart_toStartOf="@+id/name"
            app:layout_constraintTop_toBottomOf="@+id/name"
            tools:text="Yeah, I’ve already made sure of that and her gluten intolerance." />

        <TextView
            android:id="@+id/date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/inter_regular"
            android:text="@{comment.dateS}"
            android:textColor="@color/colorGrey200"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/name"
            app:layout_constraintTop_toBottomOf="@+id/com"
            tools:text="03:36 PM, Sun 03 March" />

        <ImageView
            android:id="@+id/edit_icon"
            android:layout_width="@dimen/global_icon_size_24"
            android:layout_height="@dimen/global_icon_size_24"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:onClick="@{editClickListener}"
            android:src="@{comment.isInEditMode ? @drawable/ic_icon_check : @drawable/ic_edit_icon}"
            android:visibility="@{comment.editable ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>