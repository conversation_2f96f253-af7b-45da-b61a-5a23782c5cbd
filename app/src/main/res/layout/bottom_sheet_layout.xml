<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>
        <import type="com.eatapp.clementine.R" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_rounded_btn_bcg_grey" />

        </LinearLayout>

        <View
            android:visibility="gone"
            android:id="@+id/separator"
            android:background="@color/grey200"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>

        <LinearLayout
            android:visibility="gone"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:paddingTop="@dimen/margin_8"
            android:paddingBottom="@dimen/margin_8"
            android:background="@drawable/shape_rounded_bcg_grey"
            android:id="@+id/container_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_icon_info"
                app:tint="@color/grey700" />

            <TextView
                android:id="@+id/text_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="Only users with refund permission are listed below" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:context=".views.BottomSheetView"
            tools:listitem="@layout/list_item_selector">

        </androidx.recyclerview.widget.RecyclerView>

        <FrameLayout
            android:id="@+id/actionCont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/view10"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toTopOf="@+id/bottomButtons" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/applyBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                android:textAllCaps="false"
                app:tintColor="@{R.color.white}"
                android:textSize="15sp" />

        </FrameLayout>

    </LinearLayout>

</layout>
