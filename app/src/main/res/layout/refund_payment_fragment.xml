<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.eatapp.clementine.R" />

        <import type="android.text.InputType" />

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.payments.refund.RefundPaymentViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <TextView
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_16"
            android:id="@+id/text_refund"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:text="@string/refund_type_label"
            android:textColor="@color/grey700"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RadioGroup
            android:layout_marginStart="@dimen/margin_16"
            android:id="@+id/group_amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_refund">

            <RadioButton
                android:id="@+id/radio_full_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:fontFamily="@font/inter_regular"
                android:textSize="@dimen/text_size_13"
                tools:text="Full amount (800 AED)" />

            <RadioButton
                android:id="@+id/radio_partial_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/partial_amount_label"
                android:textSize="@dimen/text_size_13" />
        </RadioGroup>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_marginStart="@dimen/margin_16"
            android:id="@+id/container_amount"
            android:layout_width="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/margin_16"
            android:layout_height="44dp"
            android:layout_marginTop="@dimen/margin_12"
            android:background="@{viewmodel.validationError ?  @drawable/shape_rounded_btn_bcg_red_outline : @drawable/shape_rounded_btn_bcg_grey_outline}"
            android:orientation="horizontal"
            android:padding="@dimen/margin_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/group_amount">

            <EditText
                android:id="@+id/edit_text_amount"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@null"
                android:enabled="@{!viewmodel.isFullAmount()}"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/enter_amount_to_be_refunded_label"
                android:inputType="@{viewmodel.isFullAmount() ? InputType.TYPE_NULL : (InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL)}"
                android:onTextChanged="@{(text, start, before, count) -> viewmodel.validateAmount(text.toString())}"
                android:textColor="@color/grey900"
                android:textColorHint="@color/grey500"
                android:textSize="@dimen/text_size_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/image_amount_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_warning"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/red800"
                app:visible_or_gone="@{viewmodel.validationError &amp;&amp; !viewmodel.isFullAmount}"
                tools:ignore="ContentDescription" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/text_error_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_24"
            android:fontFamily="@font/inter_regular"
            android:text="@string/refund_error_label"
            android:textColor="@color/red800"
            android:textSize="@dimen/text_size_11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_amount"
            app:visible_or_gone="@{viewmodel.validationError &amp;&amp; !viewmodel.isFullAmount}" />

        <LinearLayout
            android:orientation="vertical"
            android:background="@color/grey50"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_refund"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:layout_marginBottom="@dimen/global_margin_16"
                app:loading="@{viewmodel.loading}"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:progressBarColor="@color/white"
                app:title="@string/refund_label"
                app:tintColor="@{R.color.white}" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>