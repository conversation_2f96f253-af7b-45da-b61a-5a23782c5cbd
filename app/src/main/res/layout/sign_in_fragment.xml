<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.launch.SignInViewModel" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:elevation="4dp"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <EditText
                android:id="@+id/email"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height"
                android:background="@null"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/email_hint"
                android:inputType="textEmailAddress"
                android:paddingStart="@dimen/register_input_padding"
                android:paddingEnd="@dimen/register_input_padding"
                android:text="@={viewmodel.email}"
                android:textColor="@color/colorDark50"
                android:textColorHint="@color/colorGrey200"
                android:textSize="13sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <com.scottyab.showhidepasswordedittext.ShowHidePasswordEditText
                android:id="@+id/password"
                android:layout_width="match_parent"
                android:layout_height="@dimen/register_input_height"
                android:background="@null"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/password_hint"
                android:inputType="textPassword"
                android:paddingStart="@dimen/register_input_padding"
                android:paddingEnd="@dimen/register_input_padding"
                android:text="@={viewmodel.password}"
                android:textColor="@color/colorDark50"
                android:textColorHint="@color/colorGrey200"
                android:textSize="13sp"
                app:tint_color="@color/colorGrey200" />

        </LinearLayout>

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/register_button_height"
            android:background="@drawable/shape_rounded_bottom_btn_bcg_green"
            android:onClick="@{() -> viewmodel.signInBtnClick()}"
            app:progressBarColor="@color/white"
            app:title="@string/sign_in"
            app:tintColor="@{R.color.white}" />

        <TextView
            android:layout_marginTop="@dimen/margin_16"
            android:id="@+id/tv_privacy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_4"
            android:fontFamily="@font/inter_regular"
            android:text="@string/privacy_policy_label"
            android:textColor="@color/white"
            android:textColorLink="@color/white" />

    </LinearLayout>

</layout>