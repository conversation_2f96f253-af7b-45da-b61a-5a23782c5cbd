<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.eatapp.clementine.R" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="@dimen/margin_20">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:text="@string/vouchers_label"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_20" />

            <ImageButton
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_icon_close"
                app:tint="@color/grey800" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_vouchers"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/margin_20"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_voucher" />

        <EditText
            android:visibility="gone"
            android:id="@+id/et_voucher_code"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/margin_4"
            android:background="@drawable/shape_rounded_edittext_bcg"
            android:ellipsize="end"
            android:fontFamily="@font/inter_medium"
            android:hint="@string/voucher_code_redeem_hint"
            android:maxLines="1"
            android:paddingStart="@dimen/margin_16"
            android:paddingEnd="@dimen/margin_8"
            android:textColor="@color/grey800"
            android:textColorHint="@color/grey500"
            android:textSize="@dimen/text_size_14" />

        <TextView
            android:visibility="gone"
            android:id="@+id/tv_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_4"
            android:fontFamily="@font/inter_regular"
            android:text="@string/invalid_voucher_code_label"
            android:textColor="@color/red500"
            android:textSize="@dimen/text_size_12" />

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/btn_action"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/margin_20"
            android:background="@drawable/shape_rounded_btn_bcg_green"
            app:progressBarColor="@color/white"
            app:tintColor="@{R.color.white}"
            tools:title="Assign voucher" />

    </LinearLayout>
</layout>