<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="text"
            type="String" />

        <variable
            name="type"
            type="OptionType" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:id="@+id/cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:gravity="bottom"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_rounded_bcg_white"
            android:orientation="horizontal"
            android:padding="@dimen/section_title_margin_bottom"
            app:backgroundTint="@{type == OptionType.Error ? R.color.colorRed10 : R.color.colorOrange10}"
            tools:background="@color/colorRed20">

            <ImageView
                android:id="@+id/imageView11"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginTop="1dp"
                app:srcCompat="@drawable/ic_icon_info"
                app:tintColor="@{type == OptionType.Error ? R.color.colorRed : R.color.colorOrange}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/inter_regular"
                android:text="@={text}"
                android:textColor="@color/colorDark50"
                android:textSize="13sp"
                tools:text="Permission error message goes here" />

        </LinearLayout>

    </LinearLayout>

</layout>

