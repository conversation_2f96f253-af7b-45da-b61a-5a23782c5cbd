<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.guest.GuestViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        tools:context=".ui.guest.GuestActivity"
        android:alpha="@{viewmodel.createMode == false &amp;&amp; viewmodel.guest == null ? 0.2f : 1.0f}">

        <View
            android:id="@+id/view16"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@+id/view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />

        <include
            android:id="@+id/include2"
            layout="@layout/info_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:visibility="@{viewmodel.permission.boolValue ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:type="@{OptionType.Error}"
            bind:text="@{viewmodel.permission.errorMessage}" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayoutGF"
            android:layout_width="0dp"
            android:layout_height="@dimen/tab_height"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2"
            app:tabGravity="center"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabMode="auto"
            app:tabPaddingEnd="@dimen/global_margin_16"
            app:tabPaddingStart="@dimen/global_margin_16"
            app:tabRippleColor="@color/colorGreen05"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/EatTab"
            app:tabTextColor="@color/colorDark50" />

        <View
            android:id="@+id/view"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/viewPagerGF"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabLayoutGF" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPagerGF"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view"
            tools:ignore="SpeakableTextPresentCheck" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
