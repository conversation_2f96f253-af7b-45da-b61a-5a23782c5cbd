<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="guest"
            type="com.eatapp.clementine.data.network.response.guest.Guest" />

        <variable
            name="permission"
            type="com.eatapp.clementine.data.network.response.user.Permission" />

        <variable
            name="tagsAvailable"
            type="Boolean" />

        <variable
            name="onPhoneGuest"
            type="android.view.View.OnClickListener"/>

        <variable
            name="isFreemium"
            type="Boolean" />

        <variable
            name="isMarketingVisible"
            type="Boolean" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="@dimen/section_title_margin_top"
            android:layout_marginBottom="@dimen/section_title_margin_bottom"
            android:fontFamily="@font/inter_medium"
            android:text="@string/personal_details"
            android:textAllCaps="false"
            android:textColor="@color/colorDark50"
            android:textSize="@dimen/section_title_size" />

        <View
            android:id="@+id/bottom_separator5"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator" />

        <include
            layout="@layout/input_regular"
            bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
            bind:hint="@{@string/first_name_hint}"
            app:keyLabelCaps="@{false}"
            bind:inputType="@{InputType.TYPE_TEXT_VARIATION_PERSON_NAME|InputType.TYPE_TEXT_FLAG_CAP_WORDS}"
            bind:key="@{@string/first_name}"
            bind:value="@={guest.firstName}" />

        <include
            layout="@layout/input_regular"
            bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
            bind:hint="@{@string/last_name_hint}"
            app:keyLabelCaps="@{false}"
            bind:inputType="@{InputType.TYPE_TEXT_VARIATION_PERSON_NAME|InputType.TYPE_TEXT_FLAG_CAP_WORDS}"
            bind:key="@{@string/last_name}"
            bind:value="@={guest.lastName}" />

        <include
            layout="@layout/input_regular"
            app:keyLabelCaps="@{false}"
            bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
            bind:hint="@{@string/guest_phone_hint}"
            bind:inputType="@{InputType.TYPE_CLASS_PHONE}"
            bind:key="@{@string/phone}"
            bind:value="@={guest.phone}"
            bind:ctaVisible="@{guest.phone != null &amp;&amp; !guest.phone.empty}"
            bind:ctaIcon="@{R.drawable.ic_icon_call}"
            bind:ctaOnClick="@{onPhoneGuest}"/>

        <include
            layout="@layout/input_regular"
            bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
            bind:hint="@{@string/guest_email_hint}"
            bind:inputType="@{InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS}"
            bind:key="@{@string/email}"
            app:keyLabelCaps="@{false}"
            bind:value="@={guest.email}" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_vouchers"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_8"
                android:fontFamily="@font/inter_regular"
                android:text="@string/vouchers_label"
                android:textAllCaps="false"
                android:textColor="@color/grey500"
                android:textSize="@dimen/section_title_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_voucher_assignments"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_8"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginEnd="@dimen/margin_16"
                android:layout_marginBottom="@dimen/margin_8"
                android:clipToPadding="false"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/iv_add_voucher"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                tools:listitem="@layout/list_item_voucher_assignment" />

            <ImageView
                bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
                app:layout_goneMarginBottom="@dimen/margin_8"
                android:id="@+id/iv_add_voucher"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_16"
                android:src="@drawable/ic_icon_plus_green"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginBottom="@dimen/margin_8"
                app:tintColor="@{(!permission.boolValue || isFreemium) ? R.color.grey500 : R.color.grey800}"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.eatapp.clementine.views.ExpandableInputRegular
            android:id="@+id/guest_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/tagsCont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:visibility="@{tagsAvailable || guest.taggings.size() > 0 ? View.VISIBLE : View.GONE}">

            <TextView
                android:id="@+id/tagsTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginBottom="@dimen/section_title_margin_bottom"
                android:fontFamily="@font/inter_medium"
                android:text="@string/guest_tags"
                android:textAllCaps="false"
                android:textColor="@color/grey500"
                android:textSize="@dimen/section_title_size" />

            <androidx.recyclerview.widget.RecyclerView
                android:layout_marginStart="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_16"
                android:layout_marginBottom="@dimen/margin_8"
                android:id="@+id/tagsList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:context=".ui.guest.GuestProfileFragment"
                tools:listitem="@layout/list_item_tag" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

        </LinearLayout>

        <TextView
            android:id="@+id/textView6"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="@dimen/section_title_margin_top"
            android:layout_marginBottom="@dimen/section_title_margin_bottom"
            android:fontFamily="@font/inter_medium"
            android:text="@string/more_details"
            android:textAllCaps="false"
            android:textColor="@color/colorDark50"
            android:textSize="@dimen/section_title_size" />

        <View
            android:id="@+id/bottom_separator6"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator" />

        <include
            android:id="@+id/birthday"
            layout="@layout/input_select"
            app:keyLabelCaps="@{false}"
            bind:disabled="@{!permission.boolValue || isFreemium}"
            bind:hint="@{@string/birthday_hint}"
            bind:key="@{@string/birthday}"
            bind:showIcon="@{false}"
            bind:value="@{guest.birthdayS}" />

        <include
            android:id="@+id/anniversary"
            layout="@layout/input_select"
            app:keyLabelCaps="@{false}"
            bind:disabled="@{!permission.boolValue || isFreemium}"
            bind:hint="@{@string/anniversary_hint}"
            bind:key="@{@string/anniversary}"
            bind:showIcon="@{false}"
            bind:value="@{guest.anniversaryS}" />

        <include
            layout="@layout/toggle_list_item"
            android:id="@+id/marketing_opt_in"
            android:visibility="@{isMarketingVisible ? View.VISIBLE : View.GONE}"
            bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
            bind:key="@{@string/marketing_opt_in}"
            bind:value="@{guest.marketingAccepted}" />

    </LinearLayout>

</layout>