<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_header"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/grey700"
            android:layout_width="0dp"
            android:layout_height="38dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_12"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_13"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Seated" />

            <ImageView
                app:tint="@color/white"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/title"
                android:layout_marginStart="@dimen/margin_4"
                android:src="@drawable/ic_icon_arrow_down"
                android:id="@+id/iv_chevron"
                android:layout_width="24dp"
                android:layout_height="24dp"/>

            <androidx.appcompat.widget.AppCompatImageView
                app:tint="@color/white"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/text_reservation_count"
                android:id="@+id/icon_reservation_count"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/ic_icon_cutlery"
                tools:srcCompat="@drawable/ic_icon_cutlery" />

            <TextView
                android:id="@+id/text_reservation_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/white"
                app:layout_constraintEnd_toStartOf="@id/icon_covers_count"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textSize="13sp"
                tools:text="3" />

            <androidx.appcompat.widget.AppCompatImageView
                app:tint="@color/white"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/text_covers_count"
                android:id="@+id/icon_covers_count"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                app:srcCompat="@drawable/ic_icon_people"
                tools:srcCompat="@drawable/ic_icon_people" />

            <TextView
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_goneMarginEnd="@dimen/margin_12"
                android:id="@+id/text_covers_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/white"
                android:textSize="13sp"
                tools:text="14" />

            <View
                android:id="@+id/bottom_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/grey800"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_reservations"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_header" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>