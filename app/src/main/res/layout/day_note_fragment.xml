<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overflow.DayNoteViewModel" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        android:paddingTop="6dp">

        <include
            android:id="@+id/include2"
            layout="@layout/info_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="@{viewmodel.permission.boolValue ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:type="@{OptionType.Error}"
            bind:text="@{viewmodel.permission.errorMessage}" />

        <View
            android:id="@+id/view14"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/note" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/today_note"
            android:textAllCaps="false"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2" />

        <EditText
            android:id="@+id/note"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            android:cursorVisible="false"
            android:ems="10"
            android:enabled="@{viewmodel.permission.boolValue}"
            android:fontFamily="@font/inter_regular"
            android:hint="@string/daily_note_hint"
            android:inputType="textMultiLine"
            android:lineSpacingExtra="4sp"
            android:padding="16dp"
            android:text="@={viewmodel.note}"
            android:textColor="@color/colorDark200"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2"
            tools:text="We have a special tasting menu in the main dining room -- 180aed/pp, special wine list 100aed/pp, 4 hour time slot.  Specials include roasted branzino with ratatouille, lobster bisque, and tagliatelle alla ragu.  Patio is closed until 4pm" />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/colorSeparator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/note" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/mainBcg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/updateBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_margin="16dp"
                android:background="@{!viewmodel.permission.boolValue ? @drawable/shape_rounded_btn_bcg_grey200 : @drawable/shape_rounded_btn_bcg_green}"
                android:clickable="@{viewmodel.permission.boolValue}"
                android:onClick="@{() -> viewmodel.onUpdateNotes()}"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tintColor="@{!viewmodel.permission.boolValue ? R.color.colorGrey200 : R.color.white}"
                bind:layout_constraintEnd_toEndOf="parent"
                bind:layout_constraintStart_toStartOf="parent"
                bind:progressBarColor="@color/white"
                bind:title="@string/update_notes"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>