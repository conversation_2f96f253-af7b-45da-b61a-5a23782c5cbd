<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grey50"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:paddingBottom="@dimen/margin_8">

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progress"
                style="@style/Widget.MaterialComponents.LinearProgressIndicator"
                android:layout_width="0dp"
                android:layout_height="2dp"
                android:indeterminate="true"
                android:padding="0dp"
                android:visibility="gone"
                app:indicatorColor="@color/green300"
                app:indicatorInset="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:trackColor="@color/green100" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_vertical_phone_emil"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.6" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_vertical_names"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.6" />

            <LinearLayout
                android:id="@+id/iv_flag_cont"
                android:layout_width="@dimen/edittext_height"
                android:layout_height="@dimen/edittext_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_12"
                android:background="@drawable/shape_rounded_edittext_bcg"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/iv_flag"
                    android:layout_width="24dp"
                    android:layout_height="18dp"
                    tools:src="@drawable/flag_ba" />

            </LinearLayout>

            <EditText
                android:id="@+id/et_phone"
                android:layout_width="0dp"
                android:layout_height="@dimen/edittext_height"
                android:layout_marginStart="@dimen/margin_6"
                android:layout_marginTop="@dimen/margin_12"
                android:layout_marginEnd="@dimen/margin_3"
                android:layout_weight="1"
                android:autofillHints="phone"
                android:background="@drawable/shape_rounded_edittext_bcg"
                android:fontFamily="@font/inter_medium"
                android:hint="@string/phone"
                android:inputType="phone"
                android:maxLines="1"
                android:paddingStart="@dimen/margin_8"
                android:paddingEnd="@dimen/margin_8"
                android:textColor="@color/grey800"
                android:textColorHint="@color/grey500"
                android:textSize="@dimen/text_size_14"
                app:layout_constraintEnd_toStartOf="@id/guideline_vertical_phone_emil"
                app:layout_constraintStart_toEndOf="@+id/iv_flag_cont"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="@dimen/global_margin_16" />

            <EditText
                android:id="@+id/et_email"
                android:layout_width="0dp"
                android:layout_height="@dimen/edittext_height"
                android:layout_marginStart="@dimen/margin_3"
                android:layout_marginTop="@dimen/margin_12"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:autofillHints="phone"
                android:background="@drawable/shape_rounded_edittext_bcg"
                android:fontFamily="@font/inter_medium"
                android:hint="@string/email"
                android:inputType="textEmailAddress"
                android:maxLines="1"
                android:paddingStart="@dimen/margin_8"
                android:paddingEnd="@dimen/margin_8"
                android:textColor="@color/grey800"
                android:textColorHint="@color/grey500"
                android:textSize="@dimen/text_size_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@id/guideline_vertical_phone_emil"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_first_name"
                android:layout_width="0dp"
                android:layout_height="@dimen/edittext_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_6"
                android:layout_marginEnd="@dimen/margin_3"
                android:autofillHints="phone"
                android:background="@drawable/shape_rounded_edittext_bcg"
                android:fontFamily="@font/inter_medium"
                android:hint="@string/first_name"
                android:inputType="text|textCapWords"
                android:maxLines="1"
                android:paddingStart="@dimen/margin_8"
                android:paddingEnd="@dimen/margin_8"
                android:textColor="@color/grey800"
                android:textColorHint="@color/grey500"
                android:textSize="@dimen/text_size_14"
                app:layout_constraintEnd_toStartOf="@id/guideline_vertical_names"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et_email" />

            <EditText
                android:id="@+id/et_last_name"
                android:layout_width="0dp"
                android:layout_height="@dimen/edittext_height"
                android:layout_marginStart="@dimen/margin_3"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:autofillHints="phone"
                android:background="@drawable/shape_rounded_edittext_bcg"
                android:fontFamily="@font/inter_medium"
                android:hint="@string/last_name"
                android:inputType="text|textCapWords"
                android:maxLines="1"
                android:paddingStart="@dimen/margin_8"
                android:paddingEnd="@dimen/margin_8"
                android:textColor="@color/grey800"
                android:textColorHint="@color/grey500"
                android:textSize="@dimen/text_size_14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/guideline_vertical_names"
                app:layout_constraintTop_toTopOf="@id/et_first_name" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/container_additional_text"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_marginStart="@dimen/margin_12"
            android:layout_marginEnd="@dimen/margin_12"
            android:layout_marginBottom="@dimen/margin_8"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/iv_chevron"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_icon_arrow_down"
                app:tint="@color/green600" />

            <TextView
                android:id="@+id/tv_additional_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_6"
                android:fontFamily="@font/inter_semibold"
                android:textColor="@color/green600"
                android:textSize="@dimen/text_size_14"
                tools:text="19 guests found" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_guests"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:visibility="gone" />

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="@dimen/margin_12"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:layout_marginBottom="@dimen/global_margin_16"
            android:background="@drawable/shape_rounded_btn_bcg_green_100"
            android:visibility="gone"
            app:progressBarColor="@color/grey700"
            app:title="Save"
            tools:visibility="gone" />

        <FrameLayout
            android:id="@+id/bottom_line"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="@color/grey200"/>

    </LinearLayout>

</layout>