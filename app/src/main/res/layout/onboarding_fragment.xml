<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.launch.OnboardingViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/green_gradient_bcg"
        tools:context=".ui.launch.OnboardingFragment">

        <ImageView
            android:id="@+id/topShape"
            android:layout_width="0dp"
            android:layout_height="239dp"
            android:alpha="0"
            android:scaleType="fitXY"
            android:src="@drawable/ic_onboarding_top_shape"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1" />

        <ScrollView
            android:id="@+id/onboardingScrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0"
            android:fillViewport="false"
            android:scrollIndicators="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imageView9"
                    android:layout_width="51dp"
                    android:layout_height="26dp"
                    android:layout_margin="30dp"
                    android:src="@drawable/ic_logo"
                    app:tint="@color/white" />

                <LinearLayout
                    android:id="@+id/carousel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/carouselPager"
                        android:layout_width="match_parent"
                        android:layout_height="265dp"
                        android:layout_marginStart="@dimen/input_padding_side"
                        android:layout_marginEnd="@dimen/input_padding_side"
                        android:layout_marginBottom="30dp" />

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/carouselTabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="6dp"
                        android:layout_marginTop="5dp"
                        app:tabBackground="@drawable/carousel_tab_selector"
                        app:tabGravity="center"
                        app:tabIndicator="@null"
                        app:tabPaddingEnd="8dp"
                        app:tabPaddingStart="8dp" />
                </LinearLayout>

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/formTabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/input_padding_side"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="@dimen/input_padding_side"
                    android:background="@drawable/shape_rounded_tabs_bcg"
                    app:tabIndicator="@null"
                    app:tabPaddingEnd="0dp"
                    app:tabPaddingStart="0dp"
                    app:tabRippleColor="@color/colorGreen05"
                    app:tabSelectedTextColor="@color/colorPrimary"
                    app:tabTextColor="@color/colorDark50" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/formPager"
                    android:layout_marginBottom="@dimen/margin_8"
                    android:layout_width="match_parent"
                    android:layout_height="350dp"
                    android:layout_marginStart="@dimen/input_padding_side"
                    android:layout_marginEnd="@dimen/input_padding_side" />

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>