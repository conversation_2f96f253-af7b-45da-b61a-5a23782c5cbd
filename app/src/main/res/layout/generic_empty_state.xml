<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="imageId"
            type="Integer" />

        <variable
            name="title"
            type="String" />

        <variable
            name="subtitle"
            type="String" />
    </data>

    <LinearLayout
        android:id="@+id/container_empty_reservation_payments"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorWhite50"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-24dp"
            app:icon="@{imageId}"
            tools:ignore="ContentDescription"
            tools:src="@drawable/icon_no_reservation_payments" />

        <TextView
            android:id="@+id/text_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_48"
            android:layout_marginTop="@dimen/margin_12"
            android:layout_marginEnd="@dimen/margin_48"
            android:fontFamily="@font/inter_medium"
            android:gravity="center_horizontal"
            android:text="@{title}"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_18"
            tools:text="@string/reservation_payment_empty_state_payment_title" />

        <TextView
            android:id="@+id/text_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_32"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginEnd="@dimen/margin_32"
            android:fontFamily="@font/inter_regular"
            android:gravity="center"
            android:text="@{subtitle}"
            android:textColor="@color/grey600"
            android:textSize="@dimen/text_size_13"
            tools:text="@string/omnisearch_empty_state_subtitle" />
    </LinearLayout>
</layout>
