<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="title"
            type="String" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@android:color/transparent">

        <FrameLayout
            android:id="@+id/bcg"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:background="@drawable/shape_rounded_bcg_date_white"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/headerName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_semibold"
                android:text='@{title}'
                android:textColor="@color/grey800"
                android:textSize="12sp"
                android:layout_gravity="center"
                tools:text="Yesterday"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout_editor_absoluteX="44dp" />

        </FrameLayout>



    </FrameLayout>

</layout>