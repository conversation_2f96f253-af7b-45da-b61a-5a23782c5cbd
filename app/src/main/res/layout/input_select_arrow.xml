<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="key"
            type="String" />

        <variable
            name="hint"
            type="String" />

        <variable
            name="value"
            type="String" />

        <variable
            name="disabled"
            type="Boolean" />

        <variable
            name="counter"
            type="Integer" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View"/>

    </data>

    <LinearLayout
        android:background="@color/white"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="bottom"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/input_padding_side"
                android:layout_marginTop="@dimen/input_padding_bottom"
                android:layout_marginEnd="@dimen/input_padding_side"
                android:layout_marginBottom="@dimen/input_spacing"
                android:fontFamily="@font/inter_regular"
                android:text="@{key}"
                android:textAllCaps="true"
                android:textColor="@color/colorGrey200"
                android:textSize="@dimen/input_key_size"
                app:layout_constraintBottom_toTopOf="@+id/tv_value"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="KEY" />

            <TextView
                android:id="@+id/tv_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/input_padding_side"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="@dimen/input_padding_bottom"
                android:background="@android:color/transparent"
                android:ellipsize="end"
                android:ems="10"
                android:fontFamily="@font/inter_medium"
                android:hint="@{hint}"
                android:maxLines="1"
                android:text="@{value}"
                android:textColor="@{disabled ? @color/grey500 : @color/grey800}"
                android:textColorHint="@color/grey400"
                android:textSize="@dimen/input_value_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/textView10"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="value" />

            <TextView
                android:id="@+id/textView10"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/shape_rounded_bcg_blue"
                android:ems="10"
                android:fontFamily="@font/inter_medium"
                android:gravity="center"
                android:singleLine="true"
                android:text="@{counter.toString()}"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:visibility="@{counter > 0 ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/imageView2"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="12" />

            <ImageView
                android:id="@+id/imageView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_icon_arrow_right" />

            <View
                android:id="@+id/disable"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="@{disabled}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_conflicts"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:paddingBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_conflict" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator" />
    </LinearLayout>

</layout>

