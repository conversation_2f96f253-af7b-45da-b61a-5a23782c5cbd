<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="isFreemium"
            type="Boolean" />

        <variable
            name="isMarketingVisible"
            type="Boolean" />

        <variable
            name="reservation"
            type="com.eatapp.clementine.data.network.response.reservation.Reservation" />

        <variable
            name="posActive"
            type="Boolean" />


        <variable
            name="loyaltyActive"
            type="Boolean" />

        <variable
            name="currency"
            type="String" />

        <variable
            name="permission"
            type="com.eatapp.clementine.data.network.response.user.Permission" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

        <import type="android.text.TextUtils" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey200"
        android:orientation="vertical"
        android:paddingBottom="@dimen/margin_4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/searchLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                bind:layout_constraintHorizontal_bias="0"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                bind:layout_constraintEnd_toStartOf="@id/btnGoogle"
                bind:layout_constrainedWidth="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >

                <TextView
                    android:id="@+id/searchEditText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:fontFamily="@font/inter_medium"
                    android:layout_marginStart="@dimen/margin_16"
                    android:textColor="@color/grey800"
                    android:textSize="16sp"
                    android:visibility="visible"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/tv_example"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_8"
                    android:layout_weight="0"
                    android:background="@drawable/shape_rounded_btn_bcg_orange_100"
                    android:fontFamily="@font/inter_semibold"
                    android:paddingStart="@dimen/margin_4"
                    android:paddingEnd="@dimen/margin_4"
                    android:paddingBottom="1dp"
                    android:text="@string/example_label"
                    android:textColor="@color/grey900"
                    android:textSize="@dimen/text_size_12" />
            </LinearLayout>


            <ImageView
                android:id="@+id/btnGoogle"
                android:layout_width="44dp"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/margin_4"
                android:background="@android:color/transparent"
                android:padding="@dimen/margin_11"
                android:src="@drawable/ic_icon_google"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnChangeGuest"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnChangeGuest"
                android:layout_width="44dp"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/margin_4"
                android:background="@android:color/transparent"
                android:contentDescription="@string/guests_search_hint"
                android:padding="@dimen/margin_8"
                android:src="@{(reservation.guest == null &amp; TextUtils.isEmpty(reservation.tempName)) ? @drawable/ic_icon_search : @drawable/ic_icon_edit_guest, default=@drawable/ic_icon_edit_guest}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnExpand"
                app:layout_constraintTop_toTopOf="parent"
                bind:tint="@color/grey700" />

            <ImageView
                android:id="@+id/btnExpand"
                android:layout_width="44dp"
                android:layout_height="0dp"
                android:layout_marginEnd="12dp"
                android:padding="@dimen/margin_10"
                android:src="@drawable/ic_icon_arrow_down"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/disableSearchLayout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="@{isFreemium}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                tools:visibility="gone" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/separator3"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/searchLayout" />

        <LinearLayout
            android:id="@+id/guestDetails"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="@{reservation.guest == null ? View.GONE : View.VISIBLE}"
            app:layout_constraintTop_toBottomOf="@id/separator3">

            <FrameLayout
                android:id="@+id/ltvLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/grey50">

                <include
                    layout="@layout/guest_ltv_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    bind:loyaltyActive="@{loyaltyActive}"
                    bind:guest="@{reservation.guest}"
                    bind:posActive="@{posActive}"
                    bind:createMode="@{reservation.guest == null}"
                    bind:currency="@{currency}" />

            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <TextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/input_padding_side"
                    android:layout_marginTop="@dimen/margin_8"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/phone"
                    android:textAllCaps="false"
                    android:textColor="@color/colorGrey200"
                    android:textSize="@dimen/input_key_size"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/phoneNumber"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/input_padding_side"
                    android:layout_marginTop="@dimen/input_spacing"
                    android:layout_marginBottom="@dimen/margin_8"
                    android:background="@android:color/transparent"
                    android:ellipsize="end"
                    android:ems="10"
                    android:fontFamily="@font/inter_medium"
                    android:maxLines="1"
                    android:text='@{(reservation.guest.phone == null || reservation.guest.phone.isEmpty()) ? "--" : reservation.guest.phone}'
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/input_value_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView9"
                    app:layout_goneMarginBottom="@dimen/margin_8"
                    bind:layout_constraintEnd_toStartOf="@+id/btn_whatsapp"
                    tools:text="+97150123456" />

                <ImageButton
                    android:id="@+id/btn_whatsapp"
                    android:layout_width="44dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="4dp"
                    android:background="@android:color/transparent"
                    android:src="@drawable/ic_icon_whatsapp_logo"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:layout_constraintEnd_toStartOf="@+id/btnPhone" />

                <ImageButton
                    android:id="@+id/btnPhone"
                    android:layout_width="44dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/margin_8"
                    android:background="@android:color/transparent"
                    android:src="@drawable/ic_icon_call"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:layout_constraintEnd_toStartOf="@+id/phoneDivider"
                    bind:tint="@color/grey700" />

                <View
                    android:id="@+id/phoneDivider"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/margin_4"
                    android:layout_marginEnd="@dimen/margin_12"
                    android:layout_marginBottom="@dimen/margin_4"
                    android:background="@color/colorSeparator"
                    bind:layout_constraintBottom_toBottomOf="parent"
                    bind:layout_constraintEnd_toStartOf="@+id/visitsCount"
                    bind:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/visitsCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/margin_16"
                    android:gravity="center"
                    android:orientation="vertical"
                    bind:layout_constraintBottom_toBottomOf="parent"
                    bind:layout_constraintEnd_toEndOf="parent"
                    bind:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:ellipsize="none"
                        android:fontFamily="@font/inter_medium"
                        android:text="@{String.valueOf(reservation.guest.visitCount)}"
                        android:textColor="@color/colorDark100"
                        android:textSize="@dimen/input_value_size"
                        tools:text="1821" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/total_visits"
                        android:textAllCaps="false"
                        android:textColor="@color/colorGrey200"
                        android:textSize="13sp" />

                </LinearLayout>


                <View
                    android:id="@+id/view5"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <View
                    android:id="@+id/disableGuestDetails"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:clickable="@{isFreemium}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:layout_constraintHorizontal_bias="0.0"
                    bind:layout_constraintVertical_bias="0.0"
                    tools:visibility="gone" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/guestTagsCont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView100">

                <TextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/input_padding_side"
                    android:layout_marginTop="@dimen/margin_8"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/guest_tags"
                    android:textAllCaps="false"
                    android:textColor="@color/colorGrey200"
                    android:textSize="@dimen/section_title_size" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/guestTagsList"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_16"
                    android:layout_marginTop="@dimen/margin_8"
                    android:layout_marginEnd="@dimen/margin_16"
                    android:layout_marginBottom="@dimen/margin_8"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:context=".ui.guest.GuestProfileFragment"
                    tools:listitem="@layout/list_item_tag" />

            </LinearLayout>

            <View
                android:id="@+id/separator4"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                bind:layout_constraintEnd_toEndOf="parent"
                bind:layout_constraintStart_toStartOf="parent"
                bind:layout_constraintTop_toBottomOf="@id/searchLayout" />

            <include
                android:id="@+id/marketing_opt_in"
                layout="@layout/toggle_list_item"
                android:visibility="@{reservation.guest != null &amp;&amp; isMarketingVisible ? View.VISIBLE : View.GONE}"
                bind:enabled="@{permission.boolValue &amp;&amp; !isFreemium}"
                bind:key="@{@string/marketing_opt_in}"
                bind:value="@{reservation.guest.marketingAccepted}" />

        </LinearLayout>

    </LinearLayout>
</layout>