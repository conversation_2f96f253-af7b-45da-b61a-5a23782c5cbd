<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.messages.MessagesViewModel" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grey100"
        tools:context=".ui.guest.GuestActivity">

        <ImageView
            android:id="@+id/imageView14"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            android:src="@drawable/chat_view_bcg"
            app:layout_constraintBottom_toTopOf="@+id/frameLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/messages_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="-12dp"
            android:paddingBottom="60dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/frameLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="58dp"
            tools:listitem="@layout/list_item_message" />

        <View
            android:id="@+id/view22"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/grey300"
            app:layout_constraintBottom_toBottomOf="@+id/messages_list"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/secure_messages"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:gravity="center"
            android:visibility="@{viewmodel.messages.size() == 0 &amp;&amp; !viewmodel.loading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imageView14"
            tools:ignore="UseCompoundDrawables"
            tools:visibility="visible">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_icon_lock"
                android:tint="@color/grey700"
                tools:ignore="UseAppTint" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/secure_messages"
                android:textSize="12sp" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/no_messages"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_rounded_lbl_bcg_grey200"
            android:visibility="@{viewmodel.messages.size() == 0 &amp;&amp; !viewmodel.loading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginEnd="@dimen/margin_12"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/no_conversations_yet"
                android:textSize="12sp" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/no_active_conversations"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_rounded_lbl_bcg_grey200"
            android:visibility="@{!viewmodel.canSendWhatsapp &amp;&amp; viewmodel.messages.size() > 0 &amp;&amp; !viewmodel.loading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginEnd="@dimen/margin_12"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/no_active_conversations"
                android:textSize="12sp" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/frameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/grey50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/action_buttons_cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/global_margin_16"
                android:paddingTop="12dp"
                android:paddingEnd="@dimen/global_margin_16"
                android:paddingBottom="@dimen/global_margin_16"
                android:visibility="gone"
                tools:visibility="visible">

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/whatsapp_button"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/shape_rounded_btn_bcg_green"
                    app:leftIcon="@drawable/ic_icon_whatsapp"
                    app:rightIcon="@drawable/ic_warning"
                    app:tintColor="@{R.color.white}"
                    app:title="@string/whatsapp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:orientation="horizontal">

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/sms_button"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height"
                        android:layout_marginEnd="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                        app:leftIcon="@drawable/ic_icon_sms"
                        app:tintColor="@{R.color.green500}"
                        app:title="@string/sms" />

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/email_button"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_rounded_btn_bcg_green_outline"
                        app:leftIcon="@drawable/ic_icon_email"
                        app:tintColor="@{R.color.green500}"
                        app:title="@string/email" />

                </LinearLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/send_message_cont"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingTop="10dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp"
                android:visibility="gone"
                tools:visibility="gone">

                <ImageButton
                    android:id="@+id/channel_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_icon_whatsapp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/message_input"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/selector_rounded_edittext_bcg"
                    android:fontFamily="@font/inter_medium"
                    android:hint="@string/type_your_message"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingStart="16dp"
                    android:paddingEnd="50dp"
                    android:singleLine="true"
                    android:textColorHint="@color/grey500"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/send_button"
                    app:layout_constraintStart_toEndOf="@+id/channel_button"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/template_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/send"
                    android:cropToPadding="true"
                    android:src="@drawable/ic_icon_template"
                    app:layout_constraintBottom_toBottomOf="@+id/message_input"
                    app:layout_constraintEnd_toEndOf="@+id/message_input"
                    app:layout_constraintTop_toTopOf="@+id/message_input"
                    app:tint="@color/grey800" />

                <ImageButton
                    android:id="@+id/send_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/shape_rounded_btn_bcg_green_20"
                    android:contentDescription="@string/send"
                    android:cropToPadding="true"
                    android:padding="12dp"
                    android:src="@drawable/ic_icon_send"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/white" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
