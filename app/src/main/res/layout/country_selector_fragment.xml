<?xml version="1.0" encoding="utf-8"?>
<layout>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <EditText
        android:layout_marginTop="@dimen/margin_16"
        android:layout_marginStart="@dimen/margin_16"
        android:layout_marginEnd="@dimen/margin_16"
        android:id="@+id/et_search"
        android:layout_width="0dp"
        android:layout_height="@dimen/edittext_height"
        android:background="@drawable/shape_rounded_edittext_bcg"
        android:fontFamily="@font/inter_semibold"
        android:hint="@string/search_label"
        android:paddingStart="@dimen/margin_16"
        android:paddingEnd="@dimen/margin_16"
        android:textColor="@color/grey800"
        android:textColorHint="@color/grey500"
        android:textSize="@dimen/text_size_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:layout_marginTop="@dimen/margin_8"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:id="@+id/rv_countries"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_search" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>