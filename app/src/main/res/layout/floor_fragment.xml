<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.floor.FloorViewModel" />

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.floor.FloorFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/totalCoversCont"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:visibility="gone"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/imageView17"
                            android:layout_width="@dimen/global_icon_size_24"
                            android:layout_height="@dimen/global_icon_size_24"
                            app:srcCompat="@drawable/ic_icon_people"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/totalCovers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:fontFamily="@font/inter_medium"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="123" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="24dp"
                        android:fontFamily="@font/inter_medium"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Thu, 13 Jul" />

                    <ImageButton
                        android:id="@+id/imageButton"
                        android:layout_width="@dimen/toolbar_icon_size"
                        android:layout_height="@dimen/toolbar_icon_size"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="4dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:onClick="@{() -> viewmodel.minusDay()}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/date"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_icon_arrow_left"
                        app:tint="@color/white" />

                    <ImageButton
                        android:id="@+id/imageButton2"
                        android:layout_width="@dimen/toolbar_icon_size"
                        android:layout_height="@dimen/toolbar_icon_size"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="1dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:onClick="@{() -> viewmodel.plusDay()}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/date"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_icon_arrow_right"
                        app:tint="@color/white" />

                    <ProgressBar
                        android:id="@+id/progressMain"
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:indeterminateTint="@color/white"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>


        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout">

            <View
                android:id="@+id/view16"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toTopOf="@+id/view"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayoutFF"
                android:layout_width="wrap_content"
                app:tabMode="scrollable"
                android:layout_height="@dimen/tab_height"
                android:background="@color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabGravity="start"
                app:tabIndicatorColor="@color/colorPrimary"
                app:tabPaddingEnd="@dimen/global_margin_16"
                app:tabPaddingStart="@dimen/global_margin_16"
                app:tabRippleColor="@color/colorGreen05"
                app:tabSelectedTextColor="@color/colorPrimary"
                app:tabTextAppearance="@style/EatTab"
                app:tabTextColor="@color/colorDark50" />

            <View
                android:id="@+id/view"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toTopOf="@+id/roomScrollView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tabLayoutFF" />

            <HorizontalScrollView
                android:id="@+id/roomScrollView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/roomViewCont"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent">

                    <com.eatapp.clementine.views.RoomView
                        android:id="@+id/roomView"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </HorizontalScrollView>

            <TextView
                android:id="@+id/textView20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:fontFamily="@font/inter_regular"
                android:text="@string/no_rooms_title"
                android:textColor="@color/colorGrey800"
                android:textSize="15sp"
                android:visibility="@{viewmodel.rooms().size() == 0 ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="@+id/roomScrollView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/roomScrollView"
                app:layout_constraintTop_toBottomOf="@+id/view16" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>