<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="template"
            type="com.eatapp.clementine.data.network.response.templates.Template" />

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grey50"
        android:clickable="true"
        android:focusable="true"
        android:layoutDirection="ltr"
        android:alpha="@{template.disabled ? 0.5f : 1.0f}"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@drawable/shape_rounded_message_content_bcg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="8dp"
                android:layoutDirection="ltr"
                android:orientation="horizontal"
                tools:ignore="UseCompoundDrawables">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center_vertical"
                    android:text="@{template.name}"
                    android:textColor="@color/grey900"
                    android:textSize="15sp"
                    tools:text="John Doe" />

                <ImageView
                    android:id="@+id/preview_button"
                    android:layout_width="24dp"
                    android:layout_height="match_parent"
                    app:srcCompat="@drawable/ic_icon_arrow_down" />

            </LinearLayout>

            <TextView
                android:id="@+id/body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/inter_regular"
                android:text="@{template.body}"
                android:textColor="@color/grey800"
                android:textSize="13sp"
                tools:text="Template body text that might span multiple lines depending on the length of the content." />

            <WebView
                android:id="@+id/email_preview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:background="@drawable/shape_rounded_bcg_grey"
                android:clipToOutline="true"
                android:visibility="gone"
                tools:ignore="WebViewLayout"
                tools:targetApi="s" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/send_button"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/button_height"
                android:layout_weight="1"
                android:layout_marginTop="4dp"
                android:background="@{template.disabled ? @drawable/shape_rounded_btn_bcg_grey_outline_200 : @drawable/shape_rounded_btn_bcg_green_outline}"
                android:layoutDirection="rtl"
                app:leftIcon="@drawable/ic_icon_send"
                app:tintColor="@{template.disabled ? R.color.grey500 : R.color.green500}"
                android:onClick="@{clickListener}"
                android:enabled="@{!template.disabled}"
                app:title="@string/send"
                tools:ignore="InefficientWeight" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>