<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_12"
        android:paddingTop="@dimen/margin_4"
        android:paddingEnd="@dimen/margin_12"
        android:paddingBottom="@dimen/margin_4">

        <ImageView
            android:id="@+id/iv_flag"
            android:layout_width="24dp"
            android:layout_height="18dp"
            android:layout_marginStart="@dimen/margin_8"
            android:layout_marginTop="@dimen/margin_8"
            android:src="@drawable/flag_ba"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_phone_prefix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/green900"
            android:textSize="@dimen/text_size_16"
            app:layout_constraintBottom_toBottomOf="@id/iv_flag"
            app:layout_constraintStart_toEndOf="@id/iv_flag"
            app:layout_constraintTop_toTopOf="@id/iv_flag"
            tools:text="+387" />

        <TextView
            android:id="@+id/tv_country_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginEnd="@dimen/margin_8"
            android:layout_marginBottom="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/grey600"
            android:textSize="@dimen/text_size_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_phone_prefix"
            app:layout_constraintTop_toBottomOf="@id/iv_flag"
            tools:text="United kingdom" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>