<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/reservationTagsCont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:text="@string/vouchers_label"
            android:textAllCaps="false"
            android:textColor="@color/grey500"
            android:textSize="@dimen/section_title_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_voucher_assignments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_8"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginEnd="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_8"
            android:clipToPadding="false"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/iv_add_voucher"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:listitem="@layout/list_item_voucher_assignment" />

        <ImageView
            app:layout_goneMarginBottom="@dimen/margin_8"
            android:id="@+id/iv_add_voucher"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:src="@drawable/ic_icon_plus_green"
            app:layout_constraintBottom_toBottomOf="@id/rv_voucher_assignments"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/rv_voucher_assignments"
            app:tint="@color/grey800" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
