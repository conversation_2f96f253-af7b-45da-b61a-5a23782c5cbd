<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content" android:background="@color/white"
              android:animateLayoutChanges="true">


    <LinearLayout
        android:id="@+id/chartItemsCont"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginStart="@dimen/input_padding_side"
        android:layout_marginTop="@dimen/input_padding_side"
        android:layout_marginEnd="@dimen/input_padding_side"
        android:animateLayoutChanges="true"
        android:background="@drawable/shape_rounded_chart_placeholder_bcg"
        android:orientation="horizontal"
        android:weightSum="100" />

    <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side" android:paddingBottom="@dimen/input_padding_side"
            android:id="@+id/desItemsCont"/>
    <View
            android:layout_width="match_parent"
            android:layout_height="1dp" android:id="@+id/view14" android:background="@color/colorSeparator"/>

</LinearLayout>