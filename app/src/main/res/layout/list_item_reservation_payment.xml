<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="@dimen/margin_16">

        <TextView
            android:id="@+id/amount_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:text="@string/amount_label_caps"
            android:textAllCaps="true"
            android:textColor="@color/grey700"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/amount_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_4"
            android:fontFamily="@font/inter_medium"
            android:textAllCaps="true"
            android:textColor="@color/grey900"
            android:textSize="@dimen/text_size_15"
            app:layout_constraintBottom_toBottomOf="@id/amount_label"
            app:layout_constraintStart_toEndOf="@id/amount_label"
            app:layout_constraintTop_toTopOf="@id/amount_label"
            tools:text="AED 2500" />

        <TextView
            android:id="@+id/status_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_regular"
            android:text="@string/status_label_caps"
            android:textAllCaps="true"
            android:textColor="@color/grey700"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintBottom_toBottomOf="@id/image_status"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/image_status" />

        <ImageView
            android:id="@+id/image_status"
            android:layout_width="@dimen/global_icon_size_24"
            android:layout_height="@dimen/global_icon_size_24"
            android:layout_marginStart="@dimen/margin_4"
            android:layout_marginTop="@dimen/margin_12"
            android:contentDescription="@string/payment_status_icon_hint"
            app:layout_constraintStart_toEndOf="@id/status_label"
            app:layout_constraintTop_toBottomOf="@id/amount_value"
            tools:src="@drawable/ic_payment_status_authorised" />

        <TextView
            android:id="@+id/status_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_4"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/grey900"
            android:textSize="@dimen/text_size_15"
            app:layout_constraintBottom_toBottomOf="@id/image_status"
            app:layout_constraintStart_toEndOf="@id/image_status"
            app:layout_constraintTop_toTopOf="@id/image_status"
            app:layout_goneMarginStart="@dimen/margin_4"
            tools:text="Authorised" />

        <ImageView
            android:id="@+id/image_chevron"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_icon_arrow_right"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/container_dates"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_12"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_status">

            <LinearLayout
                android:id="@+id/container_created"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/created_label"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_11" />

                <TextView
                    android:id="@+id/text_created"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_11"
                    tools:text="03 Mar, 05:54PM" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_updated"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_12"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/updated_label"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_11" />

                <TextView
                    android:id="@+id/text_updated"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_11"
                    tools:text="03 Mar, 05:54PM" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_expires"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_12"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/expires_label"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_11" />

                <TextView
                    android:id="@+id/text_expires"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_11"
                    tools:text="03 Mar, 05:54PM" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_payment_link"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_dates">

            <TextView
                android:id="@+id/label_payment_link"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/payment_link_label"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="@dimen/margin_8">

                <TextView
                    android:id="@+id/text_payment_link"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/green500"
                    android:textSize="@dimen/text_size_15"
                    tools:text="https://pay.staging.eatapp.co/J6JCYR" />

                <ImageView
                    android:id="@+id/image_copy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_8"
                    android:src="@drawable/ic_copy"
                    tools:ignore="ContentDescription" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_bank_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_16"
            android:background="@color/colorWhite50"
            android:orientation="vertical"
            android:padding="@dimen/margin_16"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_dates">

            <TextView
                android:id="@+id/text_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_bold"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="CODE ********" />

            <TextView
                android:id="@+id/text_bank_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_4"
                android:fontFamily="@font/inter_regular"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="Here’s a generic message from the bank" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>