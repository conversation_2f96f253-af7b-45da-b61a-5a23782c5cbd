<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="key"
            type="String" />

        <variable
            name="value"
            type="Boolean" />

        <variable
            name="enabled"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginBottom="1dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:text="@{key}"
                android:textColor="@color/grey800"
                android:textSize="15sp"
                tools:text="@string/marketing_opt_in" />

            <CheckBox
                android:id="@+id/cb_toggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:checked="@{value}"
                android:enabled="@{enabled}"
                tools:src="@drawable/ic_icon_radio_on"
                tools:tint="@color/colorPrimary" />

        </LinearLayout>

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
