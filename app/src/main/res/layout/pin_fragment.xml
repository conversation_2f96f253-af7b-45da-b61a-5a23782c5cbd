<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <TextView
            android:id="@+id/wrong_pin_textview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:ellipsize="none"
            android:visibility="gone"
            android:fontFamily="@font/inter_medium"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/wrong_pin"
            android:textColor="@color/colorRed"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view17" />

        <TextView
            android:id="@+id/textView33"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="100dp"
            android:ellipsize="none"
            android:fontFamily="@font/inter_medium"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/enter_pin"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/pin_edittext"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:background="@android:color/transparent"
            android:ems="10"
            android:gravity="start"
            android:inputType="numberPassword"
            android:letterSpacing="0.6"
            android:maxLength="4"
            android:textSize="37sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView33"
            tools:text="1234" />

        <View
            android:id="@+id/view17"
            android:layout_width="0dp"
            android:layout_height="4dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_dashed_underline_bcg"
            app:layout_constraintEnd_toEndOf="@+id/pin_edittext"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="@+id/pin_edittext"
            app:layout_constraintTop_toBottomOf="@+id/pin_edittext" />

        <TextView
            android:id="@+id/tv_select_editor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_64"
            android:fontFamily="@font/inter_regular"
            android:text="@string/select_different_editor_label"
            android:textColor="@color/green500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view17" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>