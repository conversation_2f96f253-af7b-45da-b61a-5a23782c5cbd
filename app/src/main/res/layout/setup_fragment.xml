<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.setup.SetupViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/linearLayout2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@+id/main_action"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/main_alert_graphic"
                android:layout_width="283dp"
                android:layout_height="180dp"
                android:layout_gravity="center"
                android:layout_marginTop="45dp"
                app:srcCompat="@drawable/ic_graphic_setup_onboarding"
                tools:srcCompat="@drawable/ic_graphic_setup_onboarding" />

            <TextView
                android:id="@+id/main_alert_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="35dp"
                android:layout_marginEnd="30dp"
                android:fontFamily="@font/inter_semibold"
                android:text="@string/setup_onboarding_title"
                android:textAlignment="center"
                android:textColor="@color/colorGrey800"
                android:textSize="24sp"
                tools:text="@string/setup_onboarding_title" />

            <TextView
                android:id="@+id/main_alert_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="35dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="35dp"
                android:fontFamily="@font/inter_regular"
                android:text="@string/setup_onboarding_description"
                android:textAlignment="center"
                android:textColor="@color/colorDark50"
                android:textSize="15sp"
                tools:text="@string/setup_onboarding_description" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/main_alert_text2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="45dp"
                    android:layout_marginEnd="45dp"
                    android:layout_marginBottom="22dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@{viewmodel.onboardingCompleted ? @string/setup_onboarding_loading_completed : @string/setup_onboarding_loading_in_progress}"
                    android:textAlignment="center"
                    android:textColor="@color/colorDark50"
                    android:textSize="15sp"
                    tools:text="@string/setup_onboarding_loading_in_progress" />

                <ProgressBar
                    android:id="@+id/progressBar3"
                    android:layout_width="@dimen/progress_size"
                    android:layout_height="@dimen/progress_size"
                    android:layout_gravity="center"
                    android:visibility="@{viewmodel.onboardingCompleted ? View.GONE : View.VISIBLE}"/>

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="@drawable/shape_reserve_btn_bcg_orange"
                    android:visibility="@{viewmodel.onboardingCompleted ? View.VISIBLE : View.GONE}"
                    tools:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/global_icon_size_24"
                        android:layout_height="@dimen/global_icon_size_24"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_icon_check"
                        app:tint="@color/white" />

                </FrameLayout>

            </LinearLayout>

        </LinearLayout>

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/main_action"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="bottom"
            android:layout_margin="16dp"
            android:background="@drawable/shape_rounded_btn_bcg_green"
            android:fontFamily="@font/inter_semibold"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="15sp"
            app:disabled="@{!viewmodel.onboardingCompleted}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:tintColor="@{R.color.white}"
            bind:title="@string/continue_setup"
            tools:text="@string/continue_setup" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>