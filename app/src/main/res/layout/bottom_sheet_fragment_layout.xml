<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="true"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_rounded_btn_bcg_grey" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/titleCont"
            android:layout_width="match_parent"
            android:layout_height="@dimen/bottom_sheet_nav_height"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center"
                android:textColor="@color/colorDark50"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Title"
                tools:visibility="visible" />

            <ImageButton
                android:id="@+id/close_button"
                android:layout_width="@dimen/global_icon_size_24"
                android:layout_height="@dimen/global_icon_size_24"
                android:layout_marginStart="12dp"
                android:background="@color/white"
                android:src="@drawable/ic_icon_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator" />

        <FrameLayout
            android:id="@+id/sheet_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:context=".views.BottomSheetView">

            <FrameLayout
                android:id="@+id/sheet_container_inner"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:context=".views.BottomSheetView">

            </FrameLayout>

        </FrameLayout>

    </LinearLayout>
</layout>

