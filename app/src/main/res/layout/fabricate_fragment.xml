<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
                name="viewmodel"
                type="com.eatapp.clementine.ui.launch.FabricateViewModel"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            tools:context=".ui.launch.SignInFragment"
            android:background="@drawable/green_gradient_bcg">

        <androidx.constraintlayout.widget.Group
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintEnd_toEndOf="parent"
                app:constraint_referenced_ids="progressBar,progressBarText"
                android:id="@+id/progress"
                android:visibility="visible"/>

        <ProgressBar
                style="?android:attr/progressBarStyle"
                android:layout_width="@dimen/progress_size"
                android:layout_height="@dimen/progress_size"
                android:id="@+id/progressBar" app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintHorizontal_bias="0.5" app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="20dp" android:indeterminateTint="@color/white"/>

        <TextView
                android:text="@string/loading_restaurants"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" android:id="@+id/progressBarText"
                android:layout_marginTop="15dp"
                app:layout_constraintTop_toBottomOf="@+id/progressBar" app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintHorizontal_bias="0.5" app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/white" android:fontFamily="@font/inter_medium"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>