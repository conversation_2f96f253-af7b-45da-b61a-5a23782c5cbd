<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">


    <ImageView
        android:layout_width="50dp"
        android:layout_height="50dp" app:srcCompat="@drawable/ic_icon_list"
        android:id="@+id/imageView"
        app:tint="@color/colorDark50" app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_bias="0.5" app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:text="@string/reviews_empty_search_list"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:id="@+id/textView" android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@+id/imageView" android:textColor="@color/colorDark50"
        app:layout_constraintStart_toStartOf="parent" app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintEnd_toEndOf="parent" android:textAlignment="center"
        android:fontFamily="@font/inter_medium"/>

</androidx.constraintlayout.widget.ConstraintLayout>