<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="reservation"
            type="com.eatapp.clementine.data.network.response.reservation.Reservation" />

        <variable
            name="posRecord"
            type="com.eatapp.clementine.data.network.response.pos.PosRecord" />

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:foreground="?attr/selectableItemBackground"
        android:minHeight="@dimen/reservation_item_height"
        android:onClick="@{clickListener}">

        <ImageView
            android:id="@+id/imageView13"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toEndOf="@+id/ticket_number"
            app:layout_constraintTop_toBottomOf="@+id/guest_name"
            app:srcCompat="@drawable/ic_icon_money" />

        <ImageView
            android:id="@+id/imageView12"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="8dp"
            app:layout_constraintStart_toStartOf="@+id/guest_name"
            app:layout_constraintTop_toBottomOf="@+id/guest_name"
            app:srcCompat="@drawable/ic_icon_pos" />

        <TextView
            android:id="@+id/commercial_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:ellipsize="end"
            android:fontFamily="@font/inter_medium"
            android:maxWidth="100dp"
            android:maxLines="1"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            android:text="@{posRecord.commercialTotal.toString()}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView13"
            app:layout_constraintStart_toEndOf="@+id/imageView13"
            app:layout_constraintTop_toTopOf="@+id/imageView13"
            tools:text="674.65" />

        <TextView
            android:id="@+id/ticket_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:fontFamily="@font/inter_medium"
            android:text="@{posRecord.ticketId}"
            android:textColor="@color/colorDark50"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView12"
            app:layout_constraintStart_toEndOf="@+id/imageView12"
            app:layout_constraintTop_toTopOf="@+id/imageView12"
            tools:text="2342346578" />

        <TextView
            android:id="@+id/guest_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="14dp"
            android:fontFamily="@font/inter_medium"
            android:text="@{reservation.guestName}"
            android:textColor="@color/colorDark100"
            android:textSize="15sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Bruce Lee" />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guest_name"
            app:layout_constraintTop_toBottomOf="@+id/imageView12">

            <ImageView
                android:id="@+id/notes_icon"
                android:layout_width="@dimen/global_icon_size_16"
                android:layout_height="@dimen/global_icon_size_16"
                android:layout_marginEnd="12dp"
                android:visibility="@{TextUtils.isEmpty(reservation.notes) ? View.GONE : View.VISIBLE}"
                app:srcCompat="@drawable/ic_icon_notes" />

            <GridLayout
                android:id="@+id/tags_cont"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:columnCount="16"
                android:orientation="horizontal"
                android:visibility="@{reservation.taggings.size() + reservation.guest.taggings.size() > 0 ? View.VISIBLE : View.GONE}" />

            <TextView
                android:id="@+id/table2"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_marginStart="4dp"
                android:ellipsize="end"
                android:fontFamily="@font/inter_medium"
                android:maxWidth="100dp"
                android:maxLines="1"
                android:text='@{String.format("+%s", (reservation.taggings.size() + reservation.guest.taggings.size())-6)}'
                android:textColor="@color/colorDark50"
                android:textSize="12sp"
                android:visibility="@{reservation.taggings.size() + reservation.guest.taggings.size() > 6 ? View.VISIBLE : View.GONE}"
                tools:text="+6" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>