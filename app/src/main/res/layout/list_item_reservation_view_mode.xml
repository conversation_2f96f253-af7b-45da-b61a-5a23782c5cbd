<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewMode"
            type="com.eatapp.clementine.ui.home.more.ReservationViewMode" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_12"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="@dimen/margin_12"
            android:textColor="@color/colorGrey800"
            android:textSize="16sp"
            android:text="@{viewMode.getTitle}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Compact view" />

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_12"
            android:layout_marginTop="@dimen/margin_12"
            android:layout_marginEnd="@dimen/margin_12"
            app:cardCornerRadius="@dimen/cardview_default_radius"
            app:cardElevation="@dimen/cardview_default_elevation"
            app:cardUseCompatPadding="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:text="7:45"
                    android:textColor="@color/colorGrey800"
                    android:textSize="@dimen/text_size_14"
                    app:layout_constraintBottom_toTopOf="@id/tv_period"
                    app:layout_constraintEnd_toStartOf="@id/time_separator"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_period"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="PM"
                    android:textColor="@color/colorGrey800"
                    android:textSize="@dimen/text_size_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/time_separator"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_time" />

                <View
                    android:id="@+id/time_separator"
                    android:layout_width="1dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="56dp"
                    android:background="@color/colorGrey100"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_guest_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_12"
                    android:layout_marginTop="@dimen/margin_12"
                    android:fontFamily="@font/inter_medium"
                    android:text="Jane Doe"
                    android:textColor="@color/colorGrey800"
                    android:textSize="@dimen/text_size_16"
                    app:layout_constraintStart_toEndOf="@id/time_separator"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/layout_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="@dimen/margin_8"
                    android:gravity="center_vertical"
                    app:layout_constraintBottom_toTopOf="@id/layout_status_icons"
                    app:layout_constraintStart_toStartOf="@id/tv_guest_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_guest_name"
                    app:layout_goneMarginBottom="@dimen/margin_12">

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_icon_people" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_4"
                        android:fontFamily="@font/inter_medium"
                        android:text="4"
                        android:textColor="@color/colorGrey700"
                        android:textSize="@dimen/text_size_14" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="@dimen/margin_12"
                        android:src="@drawable/ic_icon_table" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_4"
                        android:fontFamily="@font/inter_medium"
                        android:text="11"
                        android:textColor="@color/colorGrey700"
                        android:textSize="@dimen/text_size_14" />

                    <ImageView
                        android:id="@+id/iv_tags"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_12"
                        android:src="@drawable/ic_icon_tags" />

                </LinearLayout>


                <ImageView
                    android:id="@+id/iv_status"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/margin_12"
                    android:layout_marginEnd="@dimen/margin_12"
                    android:background="@drawable/shape_rounded_chart_placeholder_bcg"
                    android:backgroundTint="@color/colorIndigo100"
                    android:scaleType="center"
                    android:src="@drawable/ic_icon_status_appetizer"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/layout_status_icons"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_12"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="@{viewMode.isCompactView?View.GONE:View.VISIBLE}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/layout_status"
                    app:layout_constraintTop_toBottomOf="@id/layout_status">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_icon_payment_captured" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_4"
                        android:src="@drawable/ic_icon_no_pos" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="@dimen/margin_4"
                        android:src="@drawable/ic_icon_online"
                        app:tint="@color/colorPrimary" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_4"
                        android:src="@drawable/booth_icon" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin_4"
                        android:src="@drawable/vip_icon" />

                </LinearLayout>

                <TextView
                    android:id="@+id/duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/shape_rounded_btn_bcg_orange_10"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:text="1h"
                    android:textColor="@color/orange500"
                    android:visibility="@{viewMode.isDetailWithTimeView?View.VISIBLE:View.GONE}"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="@id/iv_status"
                    app:layout_constraintStart_toStartOf="@id/br"
                    app:layout_constraintTop_toBottomOf="@id/iv_status" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/br"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="start"
                    app:constraint_referenced_ids="iv_status,duration" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>