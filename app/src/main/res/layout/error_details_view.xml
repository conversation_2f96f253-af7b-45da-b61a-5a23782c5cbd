<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="message"
            type="com.eatapp.clementine.data.network.response.message.Message"/>

        <import type="android.view.View" />

    </data>

    <FrameLayout
        android:background="@android:color/transparent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="@dimen/margin_12">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/errorIcon"
                    android:layout_width="@dimen/global_icon_size_24"
                    android:layout_height="@dimen/global_icon_size_24"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="20dp"
                    app:tint="@color/red500"
                    android:src="@drawable/ic_warning"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/errorTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:fontFamily="@font/inter_medium"
                    android:textSize="20sp"
                    android:textColor="@color/red500"
                    android:text="@string/message_error"
                    app:layout_constraintBottom_toBottomOf="@+id/errorIcon"
                    app:layout_constraintEnd_toStartOf="@+id/closeButton"
                    app:layout_constraintStart_toEndOf="@+id/errorIcon"
                    app:layout_constraintTop_toTopOf="@+id/errorIcon" />

                <ImageView
                    android:id="@+id/closeButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/ic_icon_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/errorTitle" />

                <TextView
                    android:id="@+id/errorMessage"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="20dp"
                    android:fontFamily="@font/inter_regular"
                    android:text="@{message.errorDetails.tooltip}"
                    android:textColor="@color/grey800"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toTopOf="@id/suggestionBox"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/errorTitle"
                    app:layout_goneMarginBottom="20dp"
                    tools:text="An unexpected WhatsApp error occurred. This is on WhatsApp's side, not Eat App." />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/suggestionBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginBottom="21dp"
                    android:background="@drawable/shape_rounded_suggestion_bcg"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp"
                    app:layout_constraintBottom_toTopOf="@+id/linearLayout11"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/errorMessage"
                    app:layout_goneMarginBottom="20dp"
                    app:visible_or_gone="@{message.errorDetails.recommendedAction != null &amp;&amp; !message.errorDetails.recommendedAction.empty}">

                    <ImageView
                        android:id="@+id/suggestionIcon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="8dp"
                        android:src="@drawable/ic_icon_bulb"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/amber400" />

                    <TextView
                        android:id="@+id/suggestionTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:textColor="@color/amber900"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/message_suggestion"
                        android:textSize="16sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/suggestionIcon"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Suggestion" />

                    <TextView
                        android:id="@+id/suggestionText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="14dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/inter_regular"
                        android:text="@{message.errorDetails.recommendedAction}"
                        android:textColor="@color/grey800"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="@+id/suggestionTitle"
                        app:layout_constraintTop_toBottomOf="@id/suggestionTitle"
                        tools:text="Wait a minute and tap ⟳ Retry. If it keeps failing, contact Eat App support." />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/linearLayout11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="20dp"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/suggestionBox"
                    app:visible_or_gone="@{message.errorDetails.link != null &amp;&amp; !message.errorDetails.link.empty}">

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/meta_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/button_height"
                        android:background="@android:color/transparent"
                        app:title="@string/see_more_info"
                        app:visible_or_gone="@{message.errorDetails.link != null &amp;&amp; !message.errorDetails.link.empty}" />

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/retry_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/button_height"
                        android:visibility="gone"
                        app:title="@string/retry" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

    </FrameLayout>

</layout>