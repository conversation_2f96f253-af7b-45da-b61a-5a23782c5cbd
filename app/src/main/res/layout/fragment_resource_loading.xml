<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
        name="viewmodel"
        type="com.eatapp.clementine.ui.launch.ResourceLoaderViewModel" />

    </data>

    <RelativeLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        tools:context=".ui.launch.ResourceLoadingFragment"
        android:background="@drawable/gradient_green_bcg">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="25dp"
            app:srcCompat="@drawable/ic_logo_white" />

    </RelativeLayout>

</layout>