<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="reservation"
            type="com.eatapp.clementine.data.network.response.reservation.Reservation" />

        <variable
            name="type"
            type="com.eatapp.clementine.adapter.ReservationAdapter.ListType" />

        <variable
            name="posActive"
            type="Boolean" />

        <variable
            name="paymentsActive"
            type="Boolean" />

        <variable
            name="showSource"
            type="Boolean" />

        <variable
            name="timeIndicator"
            type="com.eatapp.clementine.adapter.TimeIndicator" />

        <import type="android.text.TextUtils" />

        <import type="com.eatapp.clementine.adapter.TimeIndicator" />

        <import type="com.eatapp.clementine.internal.Status" />

        <import type="com.eatapp.clementine.internal.managers.TimerType" />

        <import type="com.eatapp.clementine.adapter.ReservationAdapter.ListType" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:background="@{reservation.showUpdateInterval ? @color/highlight_color : @color/white}"
        android:id="@+id/main_cont"
        android:minHeight="@dimen/reservation_item_height"
        android:onClick="@{clickListener}">

        <FrameLayout
            android:id="@+id/highlight_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/green50"
            android:alpha="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_marginTop="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_16"
            android:id="@+id/time_cont"
            android:layout_width="65dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:animateLayoutChanges="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="@dimen/global_icon_size_16"
                android:layout_height="@dimen/global_icon_size_16"
                android:layout_marginBottom="4dp"
                android:visibility="@{reservation.guest.unreadMessagesCount == 0 ? View.GONE : View.VISIBLE}"
                android:src="@drawable/ic_icon_message_notification"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/tv_wait_list_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_4"
                android:background="@drawable/shape_rounded_btn_bcg_green_100"
                android:fontFamily="@font/inter_regular"
                android:padding="4dp"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_12"
                tools:text="#1"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/text_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_medium"
                android:gravity="center"
                android:text="@{reservation.timeHourS}"
                android:textColor="@color/grey800"
                android:textSize="15sp"
                app:layout_constraintStart_toEndOf="@+id/time_separator"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="12:45" />

            <TextView
                android:id="@+id/text_time_format"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_semibold"
                android:gravity="center"
                android:text="@{reservation.timeMarkerS}"
                android:textColor="@color/grey700"
                android:textSize="11sp"
                app:layout_constraintStart_toEndOf="@+id/time_separator"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="PM" />

            <TextView
                android:id="@+id/text_timer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/inter_medium"
                android:gravity="center"
                android:text='@{String.format("%s", reservation.waitlistInterval)}'
                android:textColor="@{reservation.waitlistTimerError ? @color/colorRed : @color/colorDark200}"
                android:textSize="13sp"
                android:visibility='@{!reservation.status.equals("wait_list") || reservation.timerPermission.optionValue.equals(TimerType.Hide.type) ? View.GONE : View.VISIBLE}'
                app:layout_constraintStart_toEndOf="@+id/time_separator"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="00:45"
                tools:visibility="visible" />

        </LinearLayout>

        <View
            android:id="@+id/time_separator"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/time_cont"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/linearLayout12"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:orientation="vertical"
            android:paddingStart="@dimen/margin_16"
            android:paddingTop="@dimen/margin_16"
            android:paddingBottom="@dimen/margin_16"
            app:layout_constraintEnd_toStartOf="@id/br_end_column"
            app:layout_constraintStart_toEndOf="@id/time_separator"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/text_guest_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_64"
                android:fontFamily="@font/inter_medium"
                android:text="@{type == ListType.OVERVIEW ? reservation.guestName : reservation.dateShortS}"
                android:textColor="@color/grey800"
                android:textSize="15sp"
                tools:text="Bruce Lee" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_64"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/image_covers"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    app:srcCompat="@drawable/ic_icon_people" />

                <TextView
                    android:id="@+id/text_covers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="-1dp"
                    android:fontFamily="@font/inter_semibold"
                    android:text="@{reservation.coversS}"
                    android:textColor="@color/grey700"
                    android:textSize="13sp"
                    tools:text="2" />

                <ImageView
                    android:id="@+id/image_table"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="12dp"
                    app:srcCompat="@drawable/ic_icon_table" />

                <TextView
                    android:id="@+id/text_table"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="-1dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_semibold"
                    android:maxWidth="100dp"
                    android:maxLines="1"
                    android:text="@{reservation.tablesS}"
                    android:textColor="@color/grey700"
                    android:textSize="13sp"
                    tools:text="13B" />

                <ImageView
                    android:id="@+id/iv_compact_view_tags"
                    android:layout_width="20dp"
                    android:layout_height="@dimen/global_margin_16"
                    android:layout_marginStart="@dimen/margin_12"
                    android:src="@drawable/ic_icon_tags" />

                <TextView
                    android:layout_marginStart="@dimen/margin_12"
                    android:background="@drawable/shape_rounded_btn_bcg_orange_100"
                    android:paddingStart="@dimen/margin_4"
                    android:paddingEnd="@dimen/margin_4"
                    android:paddingTop="1dp"
                    android:paddingBottom="1dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:text="@string/example_label"
                    android:textColor="@color/green900"
                    android:textSize="@dimen/text_size_11"
                    android:visibility="@{reservation.demo ? View.VISIBLE : View.GONE}" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/layout_statuses"
                android:layout_width="match_parent"
                android:layout_height="@dimen/global_icon_size_16"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="@dimen/margin_64"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/online_icon"
                    android:layout_width="@dimen/global_icon_size_16"
                    android:layout_height="@dimen/global_icon_size_16"
                    android:layout_marginEnd="6dp"
                    android:padding="1dp"
                    android:scaleType="centerCrop"
                    android:visibility="@{showSource ? View.VISIBLE : View.GONE}" />

                <LinearLayout
                    android:id="@+id/container_payments_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:orientation="horizontal"
                    android:visibility="@{paymentsActive ? View.VISIBLE : View.GONE}">

                    <ImageView
                        android:layout_width="@dimen/global_icon_size_16"
                        android:layout_height="@dimen/global_icon_size_16"
                        app:srcCompat="@drawable/ic_payment" />

                    <ImageView
                        android:id="@+id/payments_icon"
                        android:layout_width="@dimen/global_icon_size_16"
                        android:layout_height="@dimen/global_icon_size_16"
                        android:layout_marginStart="-5dp"
                        tools:src="@drawable/ic_payment_status_refunded" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/pos_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/icon_pos_static"
                        android:layout_width="@dimen/global_icon_size_16"
                        android:layout_height="@dimen/global_icon_size_16"
                        app:srcCompat="@drawable/ic_icon_pos" />

                    <ImageView
                        android:id="@+id/pos_status"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/global_icon_size_16"
                        android:layout_marginStart="-5dp"
                        android:layout_weight="1"
                        app:icon="@{reservation.relationships.posRecord.data == null ? R.drawable.ic_icon_pos_unmatched : R.drawable.ic_icon_pos_matched}"
                        tools:src="@drawable/ic_icon_pos_matched" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/preference_icon"
                    android:layout_width="@dimen/global_icon_size_16"
                    android:layout_height="@dimen/global_icon_size_16"
                    android:layout_marginEnd="6dp"
                    android:visibility="@{TextUtils.isEmpty(reservation.channel.displayName) ? View.GONE : View.VISIBLE}"
                    app:srcCompat="@drawable/ic_icon_preference" />

                <ImageView
                    android:id="@+id/comments_icon"
                    android:layout_width="@dimen/global_icon_size_16"
                    android:layout_height="@dimen/global_icon_size_16"
                    android:layout_marginEnd="6dp"
                    app:tint="@color/blue500"
                    android:visibility="@{reservation.comments.size() > 0 ? View.VISIBLE : View.GONE}"
                    app:srcCompat="@drawable/ic_icon_comment" />

                <View
                    android:layout_width="8dp"
                    android:layout_height="16dp"
                    android:visibility="@{reservation.relationships.posRecord.data != null || paymentsActive || reservation.online || !TextUtils.isEmpty(reservation.channel.displayName) || reservation.comments.size() > 0 ? View.VISIBLE : View.GONE}" />

                <GridLayout
                    android:id="@+id/tags_cont"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:columnCount="16"
                    android:orientation="horizontal"
                    android:visibility="@{reservation.taggings.size() + reservation.guest.taggings.size() > 0 ? View.VISIBLE : View.GONE}" />

                <TextView
                    android:id="@+id/text_more_tags"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_medium"
                    android:maxWidth="100dp"
                    android:maxLines="1"
                    android:text='@{String.format("+%s", (reservation.taggings.size() + reservation.guest.taggings.size())-4)}'
                    android:textColor="@color/colorDark50"
                    android:textSize="12sp"
                    android:visibility="@{reservation.taggings.size() + reservation.guest.taggings.size() > 4 ? View.VISIBLE : View.GONE}"
                    tools:text="+6" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_reservation_notes"
                android:layout_width="match_parent"
                android:layout_height="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_8"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:layout_width="@dimen/global_icon_size_16"
                    android:layout_height="@dimen/global_icon_size_16"
                    android:src="@drawable/ic_icon_notes" />

                <TextView
                    android:id="@+id/tv_notes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/margin_4"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_regular"
                    android:maxLines="1"
                    android:textColor="@color/grey800"
                    android:textSize="@dimen/text_size_12" />

            </LinearLayout>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/status_cont"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:alpha="0.4"
            android:background="@drawable/shape_rounded_bcg_status"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:statusColor="@{reservation.statusColor}">

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/image_status"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="@drawable/shape_status_icon_bcg"
            app:icon="@{reservation.statusIcon}"
            app:layout_constraintBottom_toBottomOf="@+id/status_cont"
            app:layout_constraintEnd_toEndOf="@+id/status_cont"
            app:layout_constraintStart_toStartOf="@+id/status_cont"
            app:layout_constraintTop_toTopOf="@+id/status_cont"
            app:statusColor="@{reservation.statusColor}"
            tools:srcCompat="@drawable/ic_icon_status_appetizer" />

        <ImageView
            android:id="@+id/image_waitlist_confirmed"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="14dp"
            android:layout_marginBottom="14dp"
            app:icon='@{reservation.waitlistConfirmedIcon}'
            app:layout_constraintBottom_toBottomOf="@+id/image_status"
            app:layout_constraintStart_toStartOf="@+id/image_status"
            tools:srcCompat="@drawable/ic_icon_pos_matched" />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/shape_rounded_btn_bcg_orange_10"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:visibility="@{timeIndicator != null ? View.VISIBLE : View.GONE}"
            tools:text="1h"
            android:textSize="@dimen/text_size_12"
            bind:backgroundTint="@{timeIndicator != null ? timeIndicator.backgroundColor: -1}"
            bind:textColor="@{timeIndicator != null ? timeIndicator.textColor: -1}"
            android:text="@{timeIndicator != null ? timeIndicator.text: null}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@id/status_cont"
            app:layout_constraintStart_toStartOf="@id/br_status"
            app:layout_constraintTop_toBottomOf="@id/status_cont" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="status_cont,duration" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/updated_at_cont"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:layout_marginTop="11dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="14dp"
            android:background="@drawable/shape_rounded_bcg_blue100"
            android:visibility="@{reservation.showUpdateInterval ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/duration"
            app:layout_constraintVertical_bias="0"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/imageView3"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_marginStart="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_edit_icon"
                app:tint="@color/blue700" />

            <TextView
                android:id="@+id/text_updated_at"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="4dp"
                android:ellipsize="end"
                android:fontFamily="@font/inter_bold"
                android:maxWidth="100dp"
                android:maxLines="1"
                android:text='@{String.format("%s m ago", reservation.updatedAtInterval/60)}'
                android:textColor="@color/blue700"
                android:textSize="9sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView3"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="3 m ago" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_end_column"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="status_cont,duration,updated_at_cont" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>