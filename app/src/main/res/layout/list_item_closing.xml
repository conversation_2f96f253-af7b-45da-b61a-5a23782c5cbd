<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="deleteClickListener"
            type="android.view.View.OnClickListener"/>

        <variable
            name="permission"
            type="com.eatapp.clementine.data.network.response.user.Permission"/>

        <variable
            name="closing"
            type="com.eatapp.clementine.data.network.response.closing.Closing" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/range"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/colorDark100"
            android:textSize="15sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Bruce Lee" />

        <TextView
            android:id="@+id/types"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/colorGrey250"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/progressBar"
            app:layout_constraintStart_toStartOf="@+id/range"
            app:layout_constraintTop_toBottomOf="@+id/range"
            app:layout_constraintVertical_bias="0.0"
            app:lineHeight="18dp"
            tools:text="sdfsd fsdfs dfsf sdfsd sdf sdf  sdfs dfsf dgsgdfg df afsdf sdfsdfsdf sdfgs fd fsdf sd" />

        <LinearLayout
            android:id="@+id/linearLayout7"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/shape_rounded_bcg_white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/section_title_margin_bottom"
            android:visibility="@{closing.longerRange ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/types"
            bind:backgroundTint="@{R.color.colorWhite50}"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/imageView11"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginTop="1dp"
                bind:srcCompat="@drawable/ic_icon_info"
                bind:tintColor="@{R.color.colorDark50}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/inter_regular"
                android:text="@string/longer_range"
                android:textColor="@color/colorDark50"
                android:textSize="13sp"
                tools:text="@string/longer_range" />

        </LinearLayout>

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:indeterminateTint="@color/colorPrimary"
            android:visibility="@{closing.deleting ? View.VISIBLE : View.INVISIBLE}"
            app:layout_constraintBottom_toBottomOf="@+id/imageView21"
            app:layout_constraintEnd_toEndOf="@+id/imageView21"
            app:layout_constraintStart_toStartOf="@+id/imageView21"
            app:layout_constraintTop_toTopOf="@+id/imageView21"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/imageView21"
            android:layout_width="@dimen/global_icon_size_24"
            android:layout_height="@dimen/global_icon_size_24"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="4dp"
            android:onClick="@{deleteClickListener}"
            android:visibility="@{closing.deleting ? View.INVISIBLE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@+id/types"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/range"
            app:srcCompat="@drawable/ic_icon_delete"
            app:tint="@color/colorRed"
            app:tintColor="@{closing.longerRange || !permission.boolValue ? R.color.colorGrey100 : R.color.colorRed}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>