<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.more.guests.GuestsViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.more.guests.GuestsFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_medium"
                        android:text="@string/nav_item_guests"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appBarLayout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/searchLayout"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:animateLayoutChanges="true"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <EditText
                    android:id="@+id/searchEditText"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/white"
                    android:ems="10"
                    android:fontFamily="@font/inter_regular"
                    android:hint="@string/guests_search_hint"
                    android:inputType="textPersonName"
                    android:paddingStart="15dp"
                    android:paddingEnd="0dp"
                    android:text="@={viewmodel.search}"
                    android:textColor="@color/colorDark50"
                    android:textSize="15sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/cancelIcon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/cancelIcon"
                    android:layout_width="44dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/margin_4"
                    android:background="@android:color/transparent"
                    android:contentDescription="Search Icon"
                    android:src="@{viewmodel.search.isEmpty() ? @drawable/ic_icon_search : @drawable/ic_icon_cancel, default=@drawable/ic_icon_search}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:tint="@color/colorDark50" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/view3"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toTopOf="@+id/addLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/searchLayout" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/addLayout"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view3">

                <ImageButton
                    android:id="@+id/addIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginStart="12dp"
                    android:background="@android:color/transparent"
                    android:contentDescription="Add Icon"
                    android:src="@drawable/ic_icon_add"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:tintColor="@{viewmodel.permission.boolValue ? R.color.colorPrimary : R.color.colorGrey100}" />

                <TextView
                    android:id="@+id/addGuestTextView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="8dp"
                    android:background="@color/white"
                    android:ellipsize="end"
                    android:ems="10"
                    android:fontFamily="@font/inter_regular"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:paddingEnd="10dp"
                    android:text="@string/add_guest"
                    android:textColor="@{viewmodel.permission.boolValue ? @color/colorDark50 : @color/colorGrey200}"
                    android:textSize="15sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/addIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/view4"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toTopOf="@+id/guests_list"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/addLayout" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/guests_list"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/mainBcg"
                android:visibility="@{viewmodel.guests.size() == 0 ? View.INVISIBLE : View.VISIBLE}"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="1.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view4"
                app:layout_constraintVertical_bias="0.0"
                tools:context=".ui.home.guests.GuestFragment"
                tools:listitem="@layout/list_item_guest" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                app:constraint_referenced_ids="progressBar,progressBarText"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/searchLayout" />

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyle"
                android:layout_width="@dimen/progress_size"
                android:layout_height="@dimen/progress_size"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp"
                android:indeterminateTint="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/searchLayout" />

            <TextView
                android:id="@+id/progressBarText"
                android:layout_width="wrap_content"
                android:layout_height="34dp"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/inter_medium"
                android:text="@string/loading_guests"
                android:textColor="@color/colorDark50"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/progressBar" />

            <TextView
                app:visible_or_gone="@{viewmodel.empty}"
                android:fontFamily="@font/inter_medium"
                android:textSize="@dimen/text_size_16"
                android:textColor="@color/grey600"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="@string/label_empty_guests"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>