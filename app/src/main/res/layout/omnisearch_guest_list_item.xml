<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_5"
        android:background="@drawable/background_shadow"
        android:padding="@dimen/margin_16">

        <TextView
            android:id="@+id/text_guest_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Samir <PERSON>" />

        <LinearLayout
            android:id="@+id/container_visits"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_guest_name">

            <TextView
                android:id="@+id/text_total_visits_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/total_visits"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13" />

            <TextView
                android:id="@+id/text_total_visits_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_bold"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="12" />

            <TextView
                android:id="@+id/text_cancellations_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_16"
                android:fontFamily="@font/inter_regular"
                android:text="@string/cancellations"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13" />

            <TextView
                android:id="@+id/text_cancellations_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_bold"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="12" />

            <TextView
                android:id="@+id/text_no_shows_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_16"
                android:fontFamily="@font/inter_regular"
                android:text="@string/no_shows"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13" />

            <TextView
                android:id="@+id/text_no_shows_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_bold"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="12" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_notes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_visits"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/image_notes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_icon_notes"
                app:tint="@color/grey800" />

            <TextView
                android:id="@+id/text_notes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_4"
                android:fontFamily="@font/inter_regular"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13"
                tools:text="Loves beer and stuff" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_notes"
            tools:listitem="@layout/list_item_tag" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>