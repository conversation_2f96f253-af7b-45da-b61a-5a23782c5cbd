<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overflow.BlockHoursViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.text.TextUtils" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/colorWhite50"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorWhite50"
                android:paddingTop="6dp">

                <include
                    android:id="@+id/include2"
                    layout="@layout/info_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="@{viewmodel.permission.boolValue ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:type="@{OptionType.Error}"
                    bind:text="@{viewmodel.permission.errorMessage}"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/textView28"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/block_new_reservation"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="13sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/include2" />

                <View
                    android:id="@+id/bottom_separator9"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintTop_toBottomOf="@+id/textView28"
                    tools:layout_editor_absoluteX="32dp" />

                <View
                    android:id="@+id/bottom_separator10"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout8" />

                <com.eatapp.clementine.views.StepperView
                    android:id="@+id/start_block_time"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/input_height"
                    app:disabled="@{!viewmodel.permission.boolValue}"
                    app:hint="@{@string/select_time}"
                    app:key="@string/start_block_time"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/bottom_separator9"
                    app:value="@{viewmodel.closingBody.startTime}" />

                <com.eatapp.clementine.views.StepperView
                    android:id="@+id/end_block_time"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/input_height"
                    app:disabled="@{!viewmodel.permission.boolValue}"
                    app:hint="@{@string/select_time}"
                    app:key="@string/end_block_time"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/start_block_time"
                    app:value="@{viewmodel.closingBody.endTime}" />

                <LinearLayout
                    android:id="@+id/linearLayout5"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/input_padding_side"
                    android:paddingTop="8dp"
                    android:paddingEnd="@dimen/input_padding_side"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/end_block_time">

                    <CheckBox
                        android:id="@+id/online"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewmodel.permission.boolValue}"
                        android:onCheckedChanged="@{(compoundButton, checked) -> viewmodel.onOnlineChanged(checked)}"
                        tools:layout_editor_absoluteX="-46dp"
                        tools:layout_editor_absoluteY="-42dp"
                        tools:src="@drawable/ic_icon_radio_on"
                        tools:tint="@color/colorPrimary" />

                    <TextView
                        android:id="@+id/title2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="24dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/online"
                        android:textSize="15sp" />

                    <CheckBox
                        android:id="@+id/in_house"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewmodel.permission.boolValue}"
                        android:onCheckedChanged="@{(compoundButton, checked) -> viewmodel.onInHouseChanged(checked)}"
                        tools:layout_editor_absoluteX="-46dp"
                        tools:layout_editor_absoluteY="-42dp"
                        tools:src="@drawable/ic_icon_radio_on"
                        tools:tint="@color/colorPrimary" />

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="24dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/in_house"
                        android:textSize="15sp" />

                    <CheckBox
                        android:id="@+id/menus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewmodel.permission.boolValue}"
                        android:onCheckedChanged="@{(compoundButton, checked) -> viewmodel.onMenusChanged(checked)}"
                        tools:layout_editor_absoluteX="-46dp"
                        tools:layout_editor_absoluteY="-42dp"
                        tools:src="@drawable/ic_icon_radio_on"
                        tools:tint="@color/colorPrimary"
                        android:visibility="@{viewmodel.ordersActive ? View.VISIBLE : View.GONE}" />

                    <TextView
                        android:id="@+id/title3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/menus"
                        android:textSize="15sp"
                        android:visibility="@{viewmodel.ordersActive ? View.VISIBLE : View.GONE}"/>

                </LinearLayout>

                <TextView
                    android:id="@+id/textView29"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/currently_blocked"
                    android:textAllCaps="false"
                    android:textColor="@color/colorDark50"
                    android:textSize="13sp"
                    android:visibility="@{viewmodel.closings.size() > 0 ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout8" />

                <LinearLayout
                    android:id="@+id/linearLayout8"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toTopOf="@+id/textView29"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/linearLayout5">

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/createBtn"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/button_height"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="12dp"
                        android:background="@drawable/shape_rounded_btn_bcg_green"
                        android:onClick="@{() -> viewmodel.onCreateClosing()}"
                        android:visibility="visible"
                        app:disabled="@{TextUtils.isEmpty(viewmodel.closingBody.startTime) || TextUtils.isEmpty(viewmodel.closingBody.endTime) || viewmodel.closingBody.closingTypes.size() == 0}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:progressBarColor="@color/white"
                        app:tintColor="@{R.color.white}"
                        bind:title="@string/create_closing"
                        tools:visibility="visible" />

                </LinearLayout>

                <View
                    android:id="@+id/bottom_separator11"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="8dp"
                    android:background="@color/colorSeparator"
                    android:visibility="@{viewmodel.closings.size() > 0 ? View.VISIBLE : View.GONE}"
                    app:layout_constraintTop_toBottomOf="@+id/textView29"
                    tools:layout_editor_absoluteX="0dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/closings_list"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/bottom_separator11"
                    tools:listitem="@layout/list_item_closing" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>