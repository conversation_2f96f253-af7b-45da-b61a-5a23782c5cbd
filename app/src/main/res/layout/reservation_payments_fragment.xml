<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.payments.ReservationPaymentsViewModel" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_payments"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/container_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/list_item_reservation_payment"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/container_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/grey50"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/btn_create_payment"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="@dimen/global_margin_16"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:progressBarColor="@color/white"
                app:title="@string/create_payment"
                app:tintColor="@{R.color.white}" />

        </LinearLayout>

        <include
            android:id="@+id/guest_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            app:imageId="@{R.drawable.icon_guest_empty_state}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:subtitle="@{@string/reservation_payment_empty_state_guest_subtitle}"
            app:title="@{@string/reservation_payment_empty_state_guest_title}"
            tools:visibility="gone" />

        <include
            android:id="@+id/payment_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            app:imageId="@{R.drawable.icon_no_reservation_payments}"
            app:layout_constraintBottom_toTopOf="@id/container_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:subtitle="@{@string/reservation_payment_empty_state_payment_subtitle}"
            app:title="@{@string/reservation_payment_empty_state_payment_title}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>