<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        app:behavior_hideable="false"
        app:behavior_peekHeight="136dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <View
            android:background="@color/colorSeparator"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>

        <LinearLayout
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_rounded_btn_bcg_grey" />

        </LinearLayout>

        <TextView
            android:text="@string/selected_table_label"
            android:textAlignment="center"
            android:textColor="@color/grey500"
            android:layout_marginStart="@dimen/margin_16"
            android:fontFamily="@font/inter_regular"
            android:textSize="13sp"
            android:textAllCaps="true"
            android:layout_width="wrap_content"
            android:layout_height="18dp"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_selected_tables"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:background="@color/white"
            android:nestedScrollingEnabled="false"
            tools:listitem="@layout/list_item_selected_table" />

        <TextView
            android:id="@+id/label_no_tables"
            android:fontFamily="@font/inter_regular"
            android:gravity="center"
            android:textColor="@color/grey400"
            android:layout_marginStart="@dimen/margin_16"
            android:text="@string/no_tables_selected_label"
            android:layout_width="wrap_content"
            android:layout_height="48dp"/>

        <View
            android:background="@color/colorSeparator"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            android:background="@color/white"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabMode="scrollable"
            app:tabPaddingEnd="@dimen/global_margin_16"
            app:tabPaddingStart="@dimen/global_margin_16"
            app:tabRippleColor="@color/colorGreen05"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/EatTab"
            app:tabTextColor="@color/colorDark50" />

        <View
            android:id="@+id/separatorBottom"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            tools:layout_editor_absoluteX="32dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_selector">

        </androidx.recyclerview.widget.RecyclerView>

    </LinearLayout>

</layout>