<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="title"
            type="String" />

        <variable
            name="hint"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/item_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/inter_regular"
            android:maxLines="1"
            android:text="@{title}"
            android:textColor="@color/grey800"
            android:textSize="@dimen/text_size_13"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Payment type" />

        <EditText
            android:id="@+id/edit_item"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="@dimen/margin_4"
            android:background="@drawable/shape_rounded_edittext_bcg"
            android:drawableEnd="@drawable/ic_icon_arrow_down"
            android:ellipsize="end"
            android:fontFamily="@font/inter_medium"
            android:hint="@{hint}"
            android:maxLines="2"
            android:paddingStart="@dimen/margin_16"
            android:paddingEnd="@dimen/margin_8"
            android:textColor="@color/grey800"
            android:textColorHint="@color/grey500"
            android:textSize="@dimen/text_size_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_title"
            tools:hint="A payment is required to hold this slot" />

        <TextView
            android:textColor="@color/grey500"
            tools:text="AED"
            app:layout_constraintTop_toTopOf="@id/edit_item"
            app:layout_constraintBottom_toBottomOf="@id/edit_item"
            app:layout_constraintEnd_toEndOf="@id/edit_item"
            android:layout_marginEnd="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:textSize="@dimen/text_size_14"
            android:textAllCaps="true"
            android:id="@+id/text_currency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
