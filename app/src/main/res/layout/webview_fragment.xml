<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <com.google.android.material.appbar.AppBarLayout
            android:visibility="gone"
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_icon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_icon_search_grey500"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/tv_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_8"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/nav_item_guests"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_16" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <WebView
            android:id="@+id/webViewUrl"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie_loading"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:scaleType="center"
            android:scaleX="1.6"
            android:scaleY="1.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/skeleton_small"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>