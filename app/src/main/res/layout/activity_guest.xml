<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBcg"
    tools:context=".ui.guest.GuestActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/grey800"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingLeft="-8dp">

                <ImageButton
                    android:id="@+id/createReservation"
                    android:layout_width="44dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/margin_8"
                    android:background="@android:color/transparent"
                    android:contentDescription="Search Icon"
                    android:paddingTop="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:srcCompat="@drawable/ic_icon_add"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/backBtnG"
                    android:layout_width="@dimen/toolbar_icon_size"
                    android:layout_height="@dimen/toolbar_icon_size"
                    android:layout_marginTop="2dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:tint="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_icon_back" />

                <TextView
                    android:id="@+id/guestNameG"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="16dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_medium"
                    android:lines="1"
                    android:text="@string/label_guest_activity"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/createReservation"
                    app:layout_constraintStart_toEndOf="@+id/backBtnG"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>


    </com.google.android.material.appbar.AppBarLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBarLayout"
        app:navGraph="@navigation/guest_nav_graph" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>