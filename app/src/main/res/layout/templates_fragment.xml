<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.templates.TemplatesViewModel" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grey50"
        tools:context=".ui.guest.GuestActivity">

        <include
            android:id="@+id/comments_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="@{viewmodel.templates.size() == 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:imageId="@{R.drawable.ic_graphic_templates}"
            bind:subtitle="@{@string/empty_template_message}"
            bind:title="@{@string/empty_template_title}"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/templates_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="-12dp"
            android:paddingBottom="48dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="66dp"
            tools:listitem="@layout/list_item_template" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
