<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overview.GuestSpendViewModel" />

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.overview.ReservationsFragment">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sort_layout"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@color/white"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/pos_search_hint"
                android:inputType="textPersonName"
                android:onTextChanged="@{viewmodel::onTextChanged}"
                android:paddingStart="15dp"
                android:textColor="@color/colorDark50"
                android:textSize="16sp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/separator2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/separator2"
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:layout_marginEnd="12dp"
                android:background="@color/colorSeparator"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/sort_by_title"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

            <TextView
                android:id="@+id/sort_by_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:fontFamily="@font/inter_regular"
                android:textColor="@color/colorGrey200"
                android:textSize="13sp"
                tools:text="Sort: high to low"
                android:text='@{viewmodel.sortHighToLow ? R.string.sort_high_to_low : R.string.sort_low_to_high}'
                android:onClick="@{() -> viewmodel.sortPosRecords()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/sort_by_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.517" />

            <ImageView
                android:id="@+id/sort_by_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                app:icon='@{viewmodel.sortHighToLow ? R.drawable.ic_icon_sort_high : R.drawable.ic_icon_sort_low}'
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:srcCompat="@drawable/ic_icon_sort_high" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/view2"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/pos_records_list"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sort_layout"
            android:visibility="visible"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/pos_records_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view2"
            tools:context=".ui.home.overview.ReservationsFragment"
            tools:listitem="@layout/list_item_pos_record" />

        <include
            android:id="@+id/empty_list"
            layout="@layout/empty_pos_records_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_pos_data"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>