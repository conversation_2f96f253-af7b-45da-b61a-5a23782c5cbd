<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="message"
            type="com.eatapp.clementine.data.network.response.message.Message" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="android.graphics.Color" />

        <import type="com.eatapp.clementine.data.network.response.message.ChannelType" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layoutDirection="@{message.inbound ? View.LAYOUT_DIRECTION_LTR : View.LAYOUT_DIRECTION_RTL}"
        android:paddingStart="16dp"
        android:paddingTop="24dp"
        android:paddingEnd="16dp">

        <FrameLayout
            android:id="@+id/channel_icon_cont"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/shape_rounded_message_channel_bcg"
            android:contentDescription="@string/account"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/channel_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                tools:srcCompat="@drawable/ic_icon_whatsapp" />

        </FrameLayout>

        <View
            android:id="@+id/arrow_view"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginTop="13dp"
            android:layout_marginEnd="-18dp"
            android:background="@drawable/shape_rounded_message_arrow_bcg"
            android:rotation="45"
            app:layout_constraintEnd_toEndOf="@+id/channel_icon_cont"
            app:layout_constraintTop_toTopOf="@+id/channel_icon_cont" />

        <LinearLayout
            android:id="@+id/message_container_large"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/shape_rounded_message_content_bcg"
            android:orientation="vertical"
            android:padding="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/channel_icon_cont"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="800dp"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:layoutDirection="ltr"
                android:orientation="horizontal"
                android:visibility="@{message.messageTemplateName == null &amp;&amp; message.channel != ChannelType.EMAIL ? View.GONE : View.VISIBLE}"
                tools:ignore="UseCompoundDrawables">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center_vertical"
                    android:text="@{message.messageTemplateName}"
                    android:textColor="@color/grey900"
                    android:textSize="15sp"
                    tools:text="John Doe" />

                <ImageView
                    android:id="@+id/preview_button"
                    android:layout_width="24dp"
                    android:layout_height="match_parent"
                    app:srcCompat="@drawable/ic_icon_arrow_down" />

            </LinearLayout>

            <TextView
                android:id="@+id/body_large"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:fontFamily="@font/inter_regular"
                android:text="@{message.content}"
                android:textColor="@color/grey800"
                android:textDirection="ltr"
                android:textSize="13sp"
                tools:text="This is a sample message text that might span multiple lines depending on the length of the message content." />

            <WebView
                android:id="@+id/email_preview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="6dp"
                android:background="@drawable/shape_rounded_bcg_grey"
                android:clipToOutline="true"
                android:visibility="gone"
                tools:ignore="WebViewLayout"
                tools:targetApi="s" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layoutDirection="ltr"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/date_large"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/inter_regular"
                    android:textAlignment="viewEnd"
                    android:textColor="@color/grey600"
                    android:textSize="10sp"
                    tools:text="15 Jun 2023, 14:30" />

                <ImageView
                    android:id="@+id/fail_large"
                    android:layout_width="16dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    app:srcCompat="@drawable/ic_warning"
                    app:tint="@color/red500" />

                <ImageView
                    android:id="@+id/check_large"
                    android:layout_width="16dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    tools:srcCompat="@drawable/ic_message_two_check" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/message_container_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/shape_rounded_message_content_bcg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/channel_icon_cont"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layoutDirection="ltr"
                android:orientation="horizontal"
                tools:ignore="UselessParent">

                <TextView
                    android:id="@+id/body_small"
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:layout_marginEnd="8dp"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:text="@{message.content}"
                    android:textColor="@color/grey800"
                    android:textSize="14sp"
                    tools:ignore="InefficientWeight"
                    tools:text="This is a smal sdassd" />

                <TextView
                    android:id="@+id/date_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_regular"
                    android:gravity="center"
                    android:textColor="@color/grey600"
                    android:textSize="10sp"
                    tools:text="15 Jun 2023, 14:30" />

                <ImageView
                    android:id="@+id/fail_small"
                    android:layout_width="16dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="4dp"
                    app:srcCompat="@drawable/ic_warning"
                    app:tint="@color/red500" />

                <ImageView
                    android:id="@+id/check_small"
                    android:layout_width="16dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="4dp"
                    tools:srcCompat="@drawable/ic_message_two_check" />
            </LinearLayout>


        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>