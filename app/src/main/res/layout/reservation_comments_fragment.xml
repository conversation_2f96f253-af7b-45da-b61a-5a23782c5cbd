<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
                name="viewmodel"
                type="com.eatapp.clementine.ui.common.comments.ReservationCommentsViewModel"/>

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

        <import type="android.graphics.Color" />

        <import type="android.text.TextUtils" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        tools:context=".ui.common.voucher.RedeemVoucherFragment">

        <LinearLayout
            android:id="@+id/linearLayout7"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/shape_rounded_bcg_grey"
            android:orientation="horizontal"
            android:padding="@dimen/section_title_margin_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageView11"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginTop="1dp"
                bind:srcCompat="@drawable/ic_icon_info"
                bind:tintColor="@{R.color.colorDark100}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:fontFamily="@font/inter_regular"
                android:text="@string/comment_info_message"
                android:textColor="@color/colorDark50"
                android:textSize="13sp"
                tools:text="@string/comment_info_message" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/comments_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="1dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="1dp"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/view3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout7"
            app:stackFromEnd="true"
            tools:context=".ui.common.comments.ReservationCommentsFragment"
            tools:listitem="@layout/list_item_comment" />

        <View
            android:id="@+id/view3"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/bottomButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/comments_list" />

        <include
            android:id="@+id/comments_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="true"
            android:focusable="true"
            android:visibility="@{viewmodel.comments.size() == 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:imageId="@{R.drawable.ic_graphic_empty_state}"
            bind:subtitle="@{@string/empty_comment_message}"
            bind:title="@{@string/empty_comment_title}"
            tools:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorWhite50"
                android:visibility="@{viewmodel.taker == null ? View.GONE : View.VISIBLE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/shape_rounded_txt_bcg"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/sendButton"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/staffName"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="16dp"
                        android:ellipsize="end"
                        android:fontFamily="@font/inter_medium"
                        android:gravity="center_vertical"
                        android:maxWidth="95dp"
                        android:singleLine="true"
                        android:text="@{viewmodel.taker}"
                        android:textColor="@{Color.parseColor(viewmodel.color(viewmodel.taker))}"
                        android:textSize="13sp"
                        tools:text="Allison Wonders Wondy" />

                    <TextView
                        android:id="@+id/textView32"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:fontFamily="@font/inter_regular"
                        android:gravity="center_vertical"
                        android:text=":" />

                    <EditText
                        android:id="@+id/commentText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:ems="10"
                        android:fontFamily="@font/inter_regular"
                        android:hint="@string/enter_comment_hint"
                        android:inputType="textPersonName"
                        android:text="@={viewmodel.commentText}"
                        android:textSize="13sp"
                        tools:ignore="TouchTargetSizeCheck" />

                </LinearLayout>

                <com.eatapp.clementine.views.LoadingButton
                    android:id="@+id/sendButton"
                    android:layout_width="@dimen/button_height"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/shape_rounded_btn_bcg_green"
                    android:onClick="@{() -> viewmodel.postNewComment()}"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tintColor="@{R.color.white}"
                    bind:layout_constraintEnd_toEndOf="parent"
                    bind:progressBarColor="@color/white">

                    <ImageView
                        android:id="@+id/imageView20"
                        android:layout_width="@dimen/global_icon_size_24"
                        android:layout_height="@dimen/global_icon_size_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_icon_send"
                        app:tint="@color/white" />

                </com.eatapp.clementine.views.LoadingButton>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/commentBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                android:visibility="@{(viewmodel.taker == null || TextUtils.isEmpty(viewmodel.taker)) ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tintColor="@{R.color.white}"
                bind:layout_constraintEnd_toEndOf="parent"
                bind:layout_constraintStart_toStartOf="parent"
                bind:progressBarColor="@color/white"
                bind:title="@string/comment"
                tools:visibility="gone" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>