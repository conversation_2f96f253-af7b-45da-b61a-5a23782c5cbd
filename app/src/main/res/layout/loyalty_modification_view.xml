<?xml version="1.0" encoding="utf-8"?>
<layout>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="40dp"
    android:layout_height="40dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:foreground="?attr/selectableItemBackground"
    android:background="@drawable/shape_rounded_btn_bcg_green_outline_8">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_operator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_semibold"
            tools:text="+"
            android:textColor="@color/green500"
            android:textSize="@dimen/text_size_14" />

        <TextView
            android:id="@+id/tv_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_semibold"
            tools:text="5"
            android:textColor="@color/green500"
            android:textSize="@dimen/text_size_14" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>