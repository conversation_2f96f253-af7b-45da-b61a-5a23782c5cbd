<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="checkedListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="item"
            type="com.eatapp.clementine.internal.ItemsGroup" />

        <variable
            name="title"
            type="String" />

        <variable
            name="showSeparator"
            type="boolean" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/mainBcg"
        android:onClick="@{clickListener}">

        <View
            android:id="@+id/bcg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            android:visibility="@{showSeparator ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/headerName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:fontFamily="@font/inter_bold"
            android:text='@{title}'
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/expandIcon"
            app:layout_constraintStart_toEndOf="@+id/headerIcon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/expandIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_icon_arrow_down" />

        <ImageView
            android:id="@+id/headerIcon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/global_margin_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_tag_icon" />

        <CheckBox
            android:id="@+id/checkBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:visibility="@{item.checkable ? View.VISIBLE : View.GONE}"
            android:checked="@={item.checked}"
            android:onClick="@{checkedListener}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>