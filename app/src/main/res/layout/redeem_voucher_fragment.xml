<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
                name="viewmodel"
                type="com.eatapp.clementine.ui.common.voucher.RedeemVoucherViewModel"/>

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:context=".ui.common.voucher.RedeemVoucherFragment">

        <TextView
                android:id="@+id/textView14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/voucher_code"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/textLayout"
                android:layout_width="0dp"
                android:layout_height="@dimen/tab_height"
                android:layout_marginTop="16dp"
                android:animateLayoutChanges="false"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView14">

            <EditText
                    android:id="@+id/searchEditText"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@color/white"
                    android:ems="10"
                    android:fontFamily="@font/inter_regular"
                    android:hint="@string/voucher_code_hint"
                    android:inputType="textPersonName"
                    android:paddingStart="15dp"
                    android:paddingEnd="0dp"
                    android:text="@={viewmodel.code}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            <View
                    android:id="@+id/view11"
                    android:layout_width="wrap_content"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintTop_toTopOf="parent"
                    bind:layout_constraintEnd_toEndOf="parent"
                    bind:layout_constraintStart_toStartOf="parent" />

            <View
                    android:id="@+id/view3"
                    android:layout_width="wrap_content"
                    android:layout_height="1dp"
                    android:background="@color/colorSeparator"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottomButtons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textLayout">

            <com.eatapp.clementine.views.LoadingButton
                    android:onClick="@{() -> viewmodel.redeemVoucher()}"
                    android:id="@+id/redeemBtn"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:background="@drawable/shape_rounded_btn_bcg_green"
                    bind:title="@string/redeem_voucher"
                    app:tintColor="@{R.color.white}"
                    bind:progressBarColor="@color/white"
                    bind:layout_constraintStart_toStartOf="parent"
                    bind:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginStart="16dp" android:layout_marginBottom="16dp"
                    app:layout_constraintBottom_toBottomOf="parent" android:layout_marginEnd="16dp"
                    app:layout_constraintTop_toTopOf="parent" android:layout_marginTop="16dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>