<?xml version="1.0" encoding="utf-8"?>
<layout>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".ui.launch.CarouselFragment" android:orientation="vertical">

    <ImageView
            android:src="@drawable/carousel_two"
            android:layout_width="match_parent"
            android:id="@+id/carouselImage" android:layout_height="170dp"/>
    <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Hello" android:fontFamily="@font/inter_bold" android:textSize="20sp"
            android:layout_marginStart="@dimen/input_padding_side" android:layout_marginTop="28dp"
            android:layout_marginBottom="4dp"
            android:layout_marginEnd="@dimen/input_padding_side" android:id="@+id/carouselTitle"
            android:textColor="@color/white"
            android:textAllCaps="true"/>
    <TextView
            android:text="TextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" android:id="@+id/carouselDescription"
            android:layout_marginStart="@dimen/input_padding_side"
            android:layout_marginEnd="@dimen/input_padding_side" android:fontFamily="@font/inter_regular"
            android:textSize="13sp"
            android:textColor="@color/white"/>

</LinearLayout>
</layout>