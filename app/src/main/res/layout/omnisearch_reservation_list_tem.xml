<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_5"
        android:background="@drawable/background_shadow">

        <LinearLayout
            android:id="@+id/container_restaurant"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/grey100"
            android:gravity="center_vertical"
            android:padding="@dimen/margin_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_restaurant"
                android:layout_width="40dp"
                android:layout_height="40dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_16"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_restaurant_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey800"
                    android:textSize="@dimen/text_size_15"
                    tools:text="The Cavendish Restaurant and Terrace" />

                <TextView
                    android:id="@+id/text_restaurant_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_2"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_regular"
                    android:maxLines="1"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_13"
                    tools:text="The Bonnington Hotel, Jumeirah Lake towers, Firs Street" />
            </LinearLayout>

        </LinearLayout>

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/grey300"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/container_restaurant" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_reservation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/margin_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/separator">

            <TextView
                android:id="@+id/text_guest_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/grey800"
                android:textSize="@dimen/text_size_15"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Samir Spahic" />

            <LinearLayout
                android:id="@+id/container_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_8"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_guest_name">

                <ImageView
                    android:id="@+id/image_date"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_calendar" />

                <TextView
                    android:id="@+id/text_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_13"
                    tools:text="Wed, 7 Nov 2019" />

                <ImageView
                    android:id="@+id/image_time"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginStart="@dimen/margin_12"
                    android:src="@drawable/ic_icon_time" />

                <TextView
                    android:id="@+id/text_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_13"
                    tools:text="04:21 PM" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_details"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_8"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/container_date">

                <ImageView
                    android:id="@+id/image_covers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_icon_people"
                    app:tint="@color/grey800" />

                <TextView
                    android:id="@+id/text_covers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_13"
                    tools:text="12" />

                <ImageView
                    android:id="@+id/image_eat_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_12"
                    android:src="@drawable/ic_eat"
                    app:tint="@color/grey800" />

                <TextView
                    android:id="@+id/text_eat_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_medium"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_13"
                    tools:text="12" />
            </LinearLayout>

            <ImageView
                android:id="@+id/image_status"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_icon_status_appetizer"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>