<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
                name="viewmodel"
                type="com.eatapp.clementine.ui.home.reports.PerformanceViewModel"/>

        <import type="android.view.View"/>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:context=".ui.home.reports.PerformanceFragment">

        <LinearLayout
            android:id="@+id/cont"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="74dp"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="@dimen/input_padding_side"
                android:paddingEnd="@dimen/input_padding_side">

                <TextView
                    android:id="@+id/totalCoversMain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="2dp"
                    android:fontFamily="@font/inter_bold"
                    android:textColor="@color/colorPrimary"
                    android:textSize="29sp"
                    tools:text="43" />

                <TextView
                    android:id="@+id/textView12"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/inter_semibold"
                    android:text="@string/total_covers"
                    android:textColor="@color/colorDark100"
                    android:textSize="16sp" />

            </LinearLayout>

            <View
                android:id="@+id/view14e"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator" />

            <com.eatapp.clementine.views.ChartView
                android:id="@+id/shift"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.eatapp.clementine.views.ChartView
                android:id="@+id/source"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.eatapp.clementine.views.ChartView
                android:id="@+id/type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <com.eatapp.clementine.views.ChartView
                android:id="@+id/status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_reports"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>