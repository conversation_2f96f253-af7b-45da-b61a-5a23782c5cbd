<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:layout_marginBottom="@dimen/margin_12"
        android:paddingTop="@dimen/margin_8"
        tools:background="@color/grey800">

        <LinearLayout
            android:id="@+id/container_data"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="@dimen/margin_8"
                android:src="@drawable/ic_icon_people"
                app:tint="@color/white" />

            <ImageView
                android:visibility="gone"
                android:id="@+id/iv_icon_collapsed"
                android:layout_width="@dimen/margin_16"
                android:layout_height="@dimen/margin_16"
                android:src="@drawable/ic_icon_arrow_drop_down"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/tv_title"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_8"
                android:layout_marginEnd="@dimen/margin_4"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_14"
                tools:text="Testi sadad ad asd asd asd asd asd asd asd asd asd a ng" />

            <ImageView
                android:id="@+id/iv_chevron"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/margin_8"
                android:src="@drawable/ic_icon_arrow_down"
                app:tint="@color/white" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_subcategories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_8"
            app:layout_goneMarginTop="0dp"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/container_data" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>