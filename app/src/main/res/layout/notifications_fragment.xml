<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overflow.NotificationsViewModel" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grey50"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            tools:listitem="@layout/list_item_notification" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progress2"
            style="@style/Widget.MaterialComponents.LinearProgressIndicator"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:indeterminate="true"
            android:padding="0dp"
            android:visibility="@{viewmodel.paginationInProgress ? View.VISIBLE : View.GONE}"
            app:indicatorColor="@color/green300"
            app:indicatorInset="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:trackColor="@color/green100" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewmodel.loading &amp;&amp; viewmodel.items.size() == 0 ? View.VISIBLE : View.GONE}"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/recyclerView" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_notifications"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>