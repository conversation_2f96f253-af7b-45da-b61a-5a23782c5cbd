<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
                name="clickListener"
                type="android.view.View.OnClickListener"/>

        <variable name="item"
                  type="com.eatapp.clementine.internal.ChitPrintConfigItem"/>

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:onClick="@{clickListener}"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/input_padding_side">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="@dimen/global_icon_size_20"
                android:layout_height="@dimen/global_icon_size_20"
                android:layout_marginEnd="10dp"
                android:alpha="@{item.enabled ? 1.0f : 0.3f}"
                android:src="@{item.selected ? @drawable/ic_icon_checkbox_on : @drawable/ic_icon_checkbox_off}"
                app:srcCompat="@drawable/ic_icon_checkbox_on"
                bind:tintColor="@{item.enabled ? R.color.colorPrimary : R.color.colorDark50}" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/inter_regular"
                android:text='@{item.title}'
                android:textSize="14sp"
                tools:text="Bruce Lee" />

            <LinearLayout
                android:id="@+id/iconsCont"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/global_margin_16"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:orientation="horizontal" />

        </LinearLayout>

    </LinearLayout>

</layout>