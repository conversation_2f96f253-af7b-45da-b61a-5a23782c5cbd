<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.overview.WaitlistFragment">

    <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Waitlist" android:textAlignment="center" app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" tools:layout_editor_absoluteX="16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>