<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="section"
            type="String" />

        <variable
            name="showSeparator"
            type="Boolean" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/colorWhite50">

        <TextView
            android:id="@+id/header_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="9dp"
            android:fontFamily="@font/inter_medium"
            tools:text="@string/logout_title"
            android:textAllCaps="false"
            android:textColor="@color/colorDark50"
            android:textSize="@dimen/section_title_size"
            android:text='@{section}'
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/bottom_separator8"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:visibility="@{showSeparator ? View.VISIBLE : View.INVISIBLE}"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>