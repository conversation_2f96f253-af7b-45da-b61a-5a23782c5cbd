<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="arrow"
            type="Boolean" />

        <variable
            name="icon"
            type="int" />

        <variable
            name="title"
            type="String" />

        <variable
            name="showNew"
            type="Boolean" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cont"
        android:layout_width="match_parent"
        android:layout_height="@dimen/input_height"
        android:background="@color/white"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="bottom"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageView4"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:visibility="@{icon == 0 ? View.GONE : View.VISIBLE}"
            app:icon="@{icon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/colorGrey200"
            tools:srcCompat="@drawable/ic_icon_status_main" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="1dp"
            android:ellipsize="end"
            android:fontFamily="@font/inter_regular"
            android:maxLines="1"
            android:text="@{title}"
            android:textColor="@color/grey800"
            android:textSize="@dimen/input_value_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/imageView2"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/imageView4"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Entry title" />

        <TextView
            android:paddingBottom="1dp"
            android:visibility="@{safeUnbox(showNew) ? View.VISIBLE : View.GONE}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_8"
            android:background="@drawable/shape_rounded_btn_bcg_grey"
            android:backgroundTint="@color/green100"
            android:fontFamily="@font/inter_regular"
            android:paddingStart="@dimen/margin_4"
            android:paddingEnd="@dimen/margin_4"
            android:text="@string/new_label"
            android:textColor="@color/grey900"
            android:textSize="@dimen/text_size_14"
            app:layout_constraintBottom_toBottomOf="@id/textView4"
            app:layout_constraintStart_toEndOf="@id/textView4"
            app:layout_constraintTop_toTopOf="@id/textView4" />

        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:visibility="@{safeUnbox(arrow) ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_icon_arrow_right" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>

