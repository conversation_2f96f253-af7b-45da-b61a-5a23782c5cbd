<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overview.ReservationsViewModel" />

        <import type="com.eatapp.clementine.ui.home.overview.ReservationsViewModel.FilterType" />

        <import type="com.eatapp.clementine.enums.Configuration" />

        <import type="com.eatapp.clementine.ui.home.overview.ReservationsViewModel.StatusType" />

        <import type="android.view.View"/>

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.overview.ReservationsFragment">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sort_layout"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            android:visibility="@{viewmodel.type == StatusType.RESERVATION ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <HorizontalScrollView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/white"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/filtersIcon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0">

                <LinearLayout
                    android:id="@+id/statusFilterView"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@color/white"
                    android:gravity="start"
                    android:orientation="horizontal">

                    <com.eatapp.clementine.views.StatusFilterView
                        android:id="@+id/seated"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:gravity="center"
                        android:onClick="@{() -> viewmodel.statusFilterChange(FilterType.SEATED)}"
                        app:iconLeft="@{0}"
                        app:iconRight="@{0}"
                        app:selected="@{viewmodel.filterType == FilterType.SEATED || viewmodel.filterType == FilterType.SEATED_UPCOMING}"
                        app:text="@string/filter_seated" />

                    <com.eatapp.clementine.views.StatusFilterView
                        android:id="@+id/upcoming"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:gravity="center"
                        android:onClick="@{() -> viewmodel.statusFilterChange(FilterType.UPCOMING)}"
                        app:iconLeft="@{0}"
                        app:iconRight="@{0}"
                        app:selected="@{viewmodel.filterType == FilterType.UPCOMING || viewmodel.filterType == FilterType.SEATED_UPCOMING}"
                        app:text="@string/filter_upcoming" />

                    <com.eatapp.clementine.views.StatusFilterView
                        android:id="@+id/shifts_filter"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:gravity="center"
                        app:iconLeft="@{0}"
                        app:iconRight="@{@drawable/ic_icon_arrow_drop_down}"
                        app:selected="@{false}"
                        app:text="@string/all_shifts">

                    </com.eatapp.clementine.views.StatusFilterView>

                </LinearLayout>


            </HorizontalScrollView>

            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@color/white"
                android:ems="10"
                android:fontFamily="@font/inter_regular"
                android:hint="@string/reservations_search_hint"
                android:inputType="textPersonName"
                android:onTextChanged="@{viewmodel::onTextChanged}"
                android:paddingStart="15dp"
                android:textColor="@color/colorDark50"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/separator"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/separator"
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:background="@color/colorSeparator"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/filtersIcon"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/searchIcon"
                android:layout_width="50dp"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                android:contentDescription="Search Icon"
                android:onClick="@{() -> viewmodel.searchBtnClick()}"
                android:src="@drawable/ic_icon_search"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:tint="@color/colorDark50" />

            <View
                android:id="@+id/separator2"
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:background="@color/colorSeparator"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/searchIcon"
                app:layout_constraintStart_toEndOf="@+id/filtersIcon"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/filtersIcon"
                android:layout_width="50dp"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                android:contentDescription="Search Icon"
                android:src="@drawable/ic_icon_filters"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/searchIcon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:tint="@color/colorDark50" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/view2"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/reservations_list"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sort_layout"
            android:visibility="@{viewmodel.type == StatusType.RESERVATION ? View.VISIBLE : View.GONE}"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/reservations_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/bottomButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view2"
            tools:context=".ui.home.overview.ReservationsFragment"
            tools:listitem="@layout/list_item_reservation" />

        <include
            android:id="@+id/empty_list"
            layout="@layout/empty_reservations_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:title="@{@string/reservations_empty_list}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/progress_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="progressBar,progressBarText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/progressBarText"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginTop="15dp"
            android:fontFamily="@font/inter_medium"
            android:text="@string/loading_reservations"
            android:textColor="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/progressBar" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/createBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible"
                android:visibility="@{viewmodel.type == StatusType.FLOOR ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/white"
                app:title="@string/create_reservation"
                app:tintColor="@{R.color.white}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>