<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M320,0h320v480H320z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,0h320v480H0z"
      android:fillColor="#ffe000"/>
  <path
      android:pathData="m464.95,190.14 l3.11,0.73 4.92,0.81 2.27,0.25 -0.38,9.6s-0.56,6.34 -1.5,9.39c-0.69,2.25 -3.01,6.37 -3.01,6.37l-1.48,2.25 -8.72,-7.55s1.5,-2.11 2.04,-3.26c0.67,-1.44 1.48,-4.57 1.48,-4.57l0.84,-4.99 0.46,-6.28zM454.01,219.23 L461.96,226.14s-1.61,1.42 -2.57,1.86a14.21,14.21 0,0 1,-6.07 1.4,11.14 11.14,0 0,1 -4.28,-1.19c-3.01,-1.38 -6.34,-2.82 -8.29,-5.51 -0.69,-0.96 -0.96,-2.23 -0.94,-3.4 0.04,-1.34 1.21,-3.82 1.21,-3.82s0.46,1.67 0.98,2.3a9.41,9.41 0,0 0,3.38 2.38,8.26 8.26,0 0,0 6.95,0.21c0.63,-0.25 1.71,-1.15 1.71,-1.15z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M441.47,168.52s0.61,1.27 0.81,1.94q0.56,1.73 0.58,3.57a15.17,15.17 0,0 1,-0.61 3.57,32.83 32.83,117.88 0,1 -1.98,5.05c-0.5,1.04 -1.15,1.98 -1.65,3.03 -0.79,1.67 -1.34,3.46 -2.04,5.15 -0.58,1.44 -1.32,2.82 -1.77,4.3 -0.58,1.96 -0.86,4.01 -1.15,6.03a44.16,44.16 87.24,0 0,-0.44 5.28c0,2.11 0.06,4.22 0.44,6.26s0.96,4.07 1.88,5.95a15.17,15.17 0,0 0,6.11 6.68c1.06,0.65 2.28,1.02 3.46,1.5 1.59,0.63 4.88,1.79 4.88,1.79s-4.53,-2.23 -6.11,-4.11a13.06,13.06 0,0 1,-2.46 -5.55,21.5 21.5,0 0,1 -0.23,-7.03 37.63,37.63 96.71,0 1,1.77 -7.35c0.81,-2.53 1.92,-4.97 2.96,-7.41 0.75,-1.79 1.59,-3.49 2.3,-5.28q1.32,-3.15 2.38,-6.37c0.4,-1.32 0.92,-2.63 1.04,-4.01a14.59,14.59 0,0 0,-0.42 -4.49,17.09 17.09,0 0,0 -2.27,-5.88 7.87,7.87 0,0 0,-2.84 -2.53c-1.09,-0.56 -2.4,-1.04 -3.63,-0.79 -0.38,0.08 -1,0.71 -1,0.71"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M451.63,181.56s0.84,-3.46 1,-5.22q0.15,-2.27 -0.12,-4.55c-0.19,-1.67 -0.29,-3.42 -0.94,-4.97a13.06,13.06 0,0 0,-7.33 -7.1,17.66 17.66,0 0,0 -5.38,-0.92 24.96,24.96 0,0 0,-7.3 0.77c-0.77,0.27 -2.25,1.09 -2.25,1.09l5.76,9.41s1.79,-1.08 2.78,-1.34a10.56,10.56 0,0 1,3.26 -0.23c1.11,0.08 2.25,0.19 3.26,0.63a10.94,10.94 0,0 1,3.38 2.55,14.21 14.21,0 0,1 2.88,4.22c0.58,1.44 0.67,3.07 0.88,4.65 0.06,0.33 0.1,1.02 0.1,1.02"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M452.66,174.09s-1.02,-3.19 -1.98,-4.51a12.29,12.29 0,0 0,-4.28 -3.8c-1.8,-0.9 -5.95,-1.19 -5.95,-1.19l2.19,4.03 -3.84,-0.08 -1.98,-3.34 -3.71,1.67 -1.82,-3.01 3.92,-1.8 -1.8,-2.92 3.92,-0.38 1.71,2.82s3.05,0 4.53,0.33a11.14,11.14 0,0 1,5.28 2.76,13.06 13.06,0 0,1 2.3,2.73c0.58,0.98 1.25,3.21 1.25,3.21z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m426.69,162.24 l6.11,8.97m-3.46,-10.52 l-2.88,1.69 -2.75,2.11 5.95,9.04 2.63,-2.04 2.71,-1.57zM440.53,198.89 L443.97,191.79 447.63,193.27 449.13,189.62 445.48,187.89 447.36,183.8 444.68,182.59 442.64,186.53 438.96,184.99 437.34,188.83 440.91,190.31 437.61,197.36zM468.02,196.76 L471.5,196.68l0,4.03l3.38,0.17 -0.58,4.11 -3.15,-0.19s-0.52,2.4 -0.98,3.53c-0.48,1.19 -1.86,3.34 -1.86,3.34l-3.72,-0.92s1.34,-2.11 1.86,-3.26c0.46,-1.04 1.09,-3.19 1.09,-3.19l-3.84,-0.48 0.67,-3.88 3.48,0.23z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m494.87,190.14l-3.11,0.73 -4.92,0.81 -2.27,0.25 0.38,9.6s0.56,6.34 1.5,9.39c0.69,2.25 3.01,6.37 3.01,6.37l1.48,2.25 8.72,-7.55s-1.5,-2.11 -2.04,-3.26c-0.67,-1.44 -1.48,-4.57 -1.48,-4.57l-0.84,-4.99 -0.46,-6.28zM505.81,219.23L497.86,226.14s1.61,1.42 2.57,1.86a14.21,14.21 0,0 0,6.07 1.4,11.14 11.14,0 0,0 4.28,-1.19c3.01,-1.38 6.34,-2.82 8.29,-5.51 0.69,-0.96 0.96,-2.23 0.94,-3.4 -0.04,-1.34 -1.21,-3.82 -1.21,-3.82s-0.46,1.67 -0.98,2.3a9.41,9.41 0,0 1,-3.38 2.38,8.26 8.26,0 0,1 -6.95,0.21c-0.63,-0.25 -1.71,-1.15 -1.71,-1.15z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M518.35,168.52s-0.61,1.27 -0.81,1.94q-0.56,1.73 -0.58,3.57a15.17,15.17 0,0 0,0.61 3.57,32.83 32.83,51.83 0,0 1.98,5.05c0.5,1.04 1.15,1.98 1.65,3.03 0.79,1.67 1.34,3.46 2.04,5.15 0.58,1.44 1.32,2.82 1.77,4.3 0.58,1.96 0.86,4.01 1.15,6.03a44.16,44.16 0,0 1,0.44 5.28c-0,2.11 -0.06,4.22 -0.44,6.26s-0.96,4.07 -1.88,5.95a15.17,15.17 0,0 1,-6.11 6.68c-1.06,0.65 -2.28,1.02 -3.46,1.5 -1.59,0.63 -4.88,1.79 -4.88,1.79s4.53,-2.23 6.11,-4.11a13.06,13.06 0,0 0,2.46 -5.55,21.5 21.5,0 0,0 0.23,-7.03 37.63,37.63 0,0 0,-1.77 -7.35c-0.81,-2.53 -1.92,-4.97 -2.96,-7.41 -0.75,-1.79 -1.59,-3.49 -2.3,-5.28q-1.32,-3.15 -2.38,-6.37c-0.4,-1.32 -0.92,-2.63 -1.04,-4.01a14.59,14.59 0,0 1,0.42 -4.49,17.09 17.09,0 0,1 2.27,-5.88 7.87,7.87 0,0 1,2.84 -2.53c1.09,-0.56 2.4,-1.04 3.63,-0.79 0.38,0.08 1,0.71 1,0.71"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M508.19,181.56s-0.84,-3.46 -1,-5.22q-0.15,-2.27 0.12,-4.55c0.19,-1.67 0.29,-3.42 0.94,-4.97a13.06,13.06 105.76,0 1,7.33 -7.1,17.66 17.66,78.53 0,1 5.38,-0.92 24.96,24.96 108.64,0 1,7.3 0.77c0.77,0.27 2.25,1.09 2.25,1.09l-5.76,9.41s-1.79,-1.08 -2.78,-1.34a10.56,10.56 0,0 0,-3.26 -0.23c-1.11,0.08 -2.25,0.19 -3.26,0.63a10.94,10.94 0,0 0,-3.38 2.55,14.21 14.21,62.59 0,0 -2.88,4.22c-0.58,1.44 -0.67,3.07 -0.88,4.65 -0.06,0.33 -0.1,1.02 -0.1,1.02"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M507.15,174.09s1.02,-3.19 1.98,-4.51a12.29,12.29 0,0 1,4.28 -3.8c1.8,-0.9 5.95,-1.19 5.95,-1.19l-2.19,4.03 3.84,-0.08 1.98,-3.34 3.71,1.67 1.82,-3.01 -3.92,-1.8 1.8,-2.92 -3.92,-0.38 -1.71,2.82s-3.05,0 -4.53,0.33a11.14,11.14 0,0 0,-5.28 2.76,13.06 13.06,61.66 0,0 -2.3,2.73c-0.58,0.98 -1.25,3.21 -1.25,3.21z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m533.13,162.24l-6.11,8.97m3.46,-10.52l2.88,1.69 2.75,2.11 -5.95,9.04 -2.63,-2.04 -2.71,-1.57zM519.29,198.89L515.85,191.79 512.18,193.27 510.69,189.62 514.33,187.89 512.45,183.8 515.14,182.59 517.18,186.53 520.86,184.99 522.48,188.83 518.9,190.31 522.21,197.36zM491.79,196.76L488.32,196.68l-0,4.03l-3.38,0.17 0.58,4.11 3.15,-0.19s0.52,2.4 0.98,3.53c0.48,1.19 1.86,3.34 1.86,3.34l3.72,-0.92s-1.34,-2.11 -1.86,-3.26c-0.46,-1.04 -1.09,-3.19 -1.09,-3.19l3.84,-0.48 -0.67,-3.88 -3.48,0.23z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m460.29,141.24 l-5.28,5.64 3.26,9.79 1.84,6.87 -5.32,4.95 9.73,13.63 -3.34,3.82a2.3,2.3 0,0 0,-0.4 1.65q0.1,0.83 0.69,1.4c0.86,0.79 2.17,0.98 3.32,1.29 4.03,1.08 8.24,1.54 12.4,1.86 0.9,0.08 2.69,0 2.69,0s1.8,0.08 2.71,0c4.19,-0.31 8.37,-0.79 12.42,-1.86 1.15,-0.31 2.44,-0.5 3.32,-1.29a2.3,2.3 0,0 0,0.27 -3.05l-3.13,-6.91 9.95,-10.64 -3.78,-3.44 -0.1,-8.29 3.26,-9.79 -4.9,-4.93 -4.42,-11.75s-0.9,-3.28 -1.69,-4.8c-0.46,-0.88 -1.11,-1.67 -1.77,-2.46 -0.77,-0.94 -1.54,-1.9 -2.46,-2.67a16.32,16.32 0,0 0,-2.8 -1.73c-1.06,-0.56 -2.11,-1.15 -3.24,-1.44 -1.17,-0.29 -3.65,-0.29 -3.65,-0.29s-2.44,0 -3.63,0.29c-1.15,0.31 -2.19,0.88 -3.23,1.44a16.32,16.32 0,0 0,-2.8 1.73c-0.94,0.77 -1.71,1.73 -2.48,2.67 -0.63,0.77 -1.29,1.57 -1.75,2.48 -0.79,1.5 -1.71,4.78 -1.71,4.78z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M499.95,179.16s-0.61,-0.54 -0.96,-0.77a12.48,12.48 0,0 0,-2.3 -1.15c-1.06,-0.42 -2.21,-0.63 -3.32,-0.9a37.82,37.82 74.8,0 0,-8.51 -1.09c-1.65,0 -4.95,0.25 -4.95,0.25s-3.3,-0.25 -4.95,-0.25a37.44,37.44 0,0 0,-8.51 1.09c-1.13,0.27 -2.27,0.48 -3.34,0.9a12.48,12.48 0,0 0,-2.3 1.15c-0.33,0.23 -0.96,0.77 -0.96,0.77m42.01,-8.2s-1,-1 -1.65,-1.13a1.73,1.73 0,0 0,-1.27 0.29,2.5 2.5,0 0,0 -0.77,2.92c0.15,0.38 0.38,0.83 0.79,1 0.92,0.36 2.96,-0.38 2.96,-0.38zM502.83,170.27c1.11,0.04 1.54,0.61 1.82,1.25m-46.69,-0.56 l-0.06,2.69s2.02,0.75 2.94,0.38c0.38,-0.17 0.65,-0.6 0.81,-1a2.5,2.5 0,0 0,-0.77 -2.92,1.73 1.73,0 0,0 -1.27,-0.29c-0.67,0.13 -1.65,1.15 -1.65,1.15zM456.96,170.27c-1.11,0.04 -1.54,0.61 -1.8,1.25m38.04,0.27 l-1.61,-0.19m-24.96,0.19 l1.61,-0.19m14.59,0.15c-0.56,-0.08 -1.46,-0.81 -1.46,-0.81m-4.38,0.81c0.54,-0.08 1.46,-0.81 1.46,-0.81m27.07,-3.71s-0.83,0.63 -1.42,0.73l-0.23,0.13s-0.38,1.09 -0.69,1.57a8.64,8.64 0,0 1,-1.25 1.19s-0.92,-0.79 -1.48,-0.96q-0.61,-0.15 -1.15,0.06a2.11,2.11 0,0 0,-0.92 0.83c-0.27,0.38 -0.52,0.88 -0.5,1.34 0.02,0.58 0.75,1.59 0.75,1.59l-0.5,0.86 -0.71,0.44a17.28,17.28 0,0 1,-3.65 -1.44,3.65 3.65,0 0,1 -0.84,-1.73s0.86,-0.52 1.08,-0.96c0.23,-0.48 0.31,-1.15 0.04,-1.61 -0.31,-0.5 -1.02,-0.77 -1.61,-0.77a1.73,1.73 0,0 0,-1.21 0.52c-0.31,0.35 -0.58,0.86 -0.48,1.34 0.08,0.46 0.9,1.09 0.9,1.09s-0.35,0.88 -0.73,1.06c-1.15,0.48 -1.92,0.77 -2.94,0.92a7.49,7.49 0,0 1,-3.13 0,3.26 3.26,0 0,1 -1.73,-1.06c-0.12,-0.17 -0.15,-0.58 -0.15,-0.58s1.25,-0.33 1.73,-0.71c0.5,-0.38 1.02,-0.88 1.13,-1.5 0.1,-0.61 -0.23,-1.27 -0.58,-1.77a3.46,3.46 0,0 0,-1.46 -1.15c-0.25,-0.12 -0.83,-0.13 -0.83,-0.13l-0.19,0.17a2.88,2.88 0,0 1,-0.9 0.73l-0.02,0l-0.04,0.02 -0.21,0.12 -0.15,0.02 -0.46,0.15s0.73,0.23 0.96,0.54q0.17,0.27 0.19,0.63l0.04,0.38q0,0.35 -0.13,0.65a1.73,1.73 0,0 1,-0.33 0.67q-0.13,0.17 -0.31,0.27 0,0 0,0l-0.04,0.02q-0.31,0.19 -0.63,0.29c-0.27,0.08 -0.81,0.02 -0.83,0.02s1.06,0 1.46,-0.31l0.04,-0.02a2.11,2.11 0,0 0,0.73 -1.38l0,-0.58q-0.08,-0.48 -0.42,-0.81c-0.46,-0.4 -1.79,-0.46 -1.8,-0.46s-1.34,0.06 -1.8,0.46a1.54,1.54 0,0 0,-0.44 0.81l0,0.6l0.1,0.44q0.19,0.58 0.63,0.94l0.04,0.02c0.42,0.31 1.46,0.31 1.48,0.31 -0.02,0 -0.58,0.06 -0.83,0q-0.35,-0.12 -0.65,-0.31l-0.02,0l0,-0.02q-0.19,-0.1 -0.33,-0.27a1.73,1.73 0,0 1,-0.33 -0.67q-0.1,-0.31 -0.13,-0.63l0.04,-0.4q0.06,-0.36 0.21,-0.63c0.21,-0.31 0.96,-0.54 0.96,-0.54l-0.48,-0.15 -0.13,-0.02 -0.23,-0.12l-0.02,0l-0.02,-0.02a2.88,2.88 0,0 1,-0.92 -0.73l-0.19,-0.17s-0.58,0 -0.81,0.13a3.46,3.46 0,0 0,-1.46 1.15c-0.35,0.5 -0.67,1.15 -0.58,1.77s0.63,1.11 1.11,1.5c0.5,0.38 1.73,0.71 1.73,0.71s-0.04,0.4 -0.15,0.58a3.26,3.26 0,0 1,-1.73 1.06,7.49 7.49,0 0,1 -3.13,0 11.33,11.33 0,0 1,-2.92 -0.92c-0.38,-0.19 -0.75,-1.06 -0.75,-1.06s0.84,-0.63 0.92,-1.11c0.08,-0.46 -0.19,-0.96 -0.5,-1.32a1.73,1.73 0,0 0,-1.19 -0.54c-0.6,0 -1.31,0.29 -1.61,0.79 -0.29,0.46 -0.19,1.13 0.04,1.61 0.19,0.44 1.08,0.96 1.08,0.96a3.65,3.65 0,0 1,-0.86 1.73,17.09 17.09,0 0,1 -3.65,1.44l-0.69,-0.44 -0.5,-0.86s0.71,-1 0.73,-1.57c0.04,-0.48 -0.21,-0.96 -0.48,-1.36a2.11,2.11 0,0 0,-0.92 -0.83q-0.54,-0.21 -1.15,-0.04c-0.58,0.15 -1.48,0.94 -1.48,0.94s-0.92,-0.73 -1.25,-1.19a9.41,9.41 0,0 1,-0.71 -1.57l-0.23,-0.13c-0.58,-0.1 -1.4,-0.73 -1.4,-0.73l-0.84,0.19s-0.79,0.4 -0.81,0.77c0,0.54 0.73,0.77 1.06,1.15q0.4,0.54 0.79,1.09c0.23,0.35 0.56,0.65 0.67,1.06 0.12,0.44 -0.19,0.96 0,1.38 0.23,0.52 0.71,1.04 1.29,1.15 0.5,0.1 1.46,-0.5 1.46,-0.5l-0.04,3.82 1.98,1.79 1.86,3.9 -0.73,1 0.21,1.92c1.44,-1.84 3.94,-2.65 6.18,-3.3 3.01,-0.86 8.56,-0.92 11.14,-0.9l1.4,0.02s8.51,-0.27 12.52,0.88c2.25,0.65 4.74,1.46 6.18,3.3l0.23,-1.92 -0.75,-1 1.86,-3.9 1.98,-1.79 -0.02,-3.82s0.96,0.6 1.46,0.5c0.58,-0.12 1.06,-0.63 1.29,-1.15 0.19,-0.42 -0.13,-0.94 0,-1.38 0.12,-0.38 0.42,-0.71 0.65,-1.06q0.38,-0.58 0.81,-1.08c0.33,-0.4 1.06,-0.63 1.04,-1.15 0,-0.38 -0.79,-0.81 -0.79,-0.81zM477.03,166.73c0.19,0.19 0.58,0.54 0.92,0.73l0.04,0.02q0.15,0.08 0.38,0.13c0.54,0.15 1.5,0.06 1.54,0.06l0,0c0.02,0 0.98,0.1 1.54,-0.06l0.38,-0.13 0.04,-0.02c0.33,-0.19 0.71,-0.56 0.92,-0.73q0.15,-0.19 0.25,-0.42c0.21,-0.52 0.19,-1.17 -0.04,-1.67 -0.31,-0.58 -1,-0.86 -1.56,-1.21 -0.48,-0.29 -1.54,-0.71 -1.54,-0.71a13.25,13.25 0,0 0,-1.54 0.71c-0.58,0.35 -1.27,0.63 -1.56,1.21 -0.25,0.5 -0.27,1.15 -0.04,1.67q0.1,0.23 0.25,0.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M503.31,159.32s-2.11,-0.96 -3.19,-1.32c-2,-0.63 -4.09,-1 -6.14,-1.4 -2.4,-0.46 -4.84,-0.58 -7.22,-1.15 -0.75,-0.15 -1.44,-0.52 -2.21,-0.67 -1.04,-0.21 -2.11,-0.56 -3.15,-0.33 -0.58,0.12 -1.5,0.88 -1.5,0.88s-0.94,-0.77 -1.5,-0.88c-1.04,-0.23 -2.13,0.12 -3.17,0.33 -0.77,0.15 -1.46,0.52 -2.19,0.69 -2.38,0.56 -4.84,0.67 -7.24,1.15 -2.05,0.38 -4.13,0.75 -6.14,1.38 -1.08,0.35 -3.17,1.32 -3.17,1.32m37.9,-8.66s1.04,-0.56 1.4,-0.96a1.92,1.92 0,0 0,0.52 -1.02,1.73 1.73,0 0,0 -0.44,-1.25 1.73,1.73 0,0 0,-1.19 -0.42q-0.58,0 -1.02,0.35a1.73,1.73 0,0 0,-0.58 1.08c-0.06,0.35 0.1,0.71 0.25,1.02a6.34,6.34 0,0 0,1.06 1.21m-28.99,0s0.79,-0.73 1.04,-1.21a1.73,1.73 0,0 0,0.27 -1.02,1.73 1.73,0 0,0 -0.58,-1.08 1.54,1.54 0,0 0,-1.02 -0.35,1.73 1.73,0 0,0 -1.21,0.42 1.73,1.73 0,0 0,-0.42 1.25c0.02,0.38 0.25,0.71 0.5,1 0.38,0.42 1.4,0.96 1.4,0.96zM457.5,154.12 L455.22,157.11s0,0.77 0.15,1.11a4.61,4.61 0,0 0,1.13 1.09l0.15,4.11 -2.17,3.76 -0.19,0.04 1.4,0.73 0.19,0.02c0.84,-0.06 1.34,-1 2.09,-1.42q1.4,-0.77 2.92,-1.38a46.08,46.08 80.56,0 1,9.6 -2.9,27.26 27.26,59.46 0,1 5.66,-0.23c1.27,0.1 3.72,0.65 3.74,0.65 0,0 2.48,-0.58 3.74,-0.65a27.07,27.07 0,0 1,11.1 1.59,46.08 46.08,64.04 0,1 7.08,2.92c0.73,0.42 1.25,1.34 2.07,1.42l0.19,0l1.4,-0.77 -0.19,-0.02 -2.17,-3.76 0.17,-4.11s0.88,-0.63 1.11,-1.09c0.19,-0.33 0.15,-1.11 0.15,-1.11l-2.28,-3s-1.15,1.15 -1.9,1.4q-0.61,0.17 -1.25,0c-0.48,-0.15 -0.84,-0.56 -1.25,-0.86a16.13,16.13 83.4,0 1,-2.48 -2.32,13.44 13.44,98.78 0,1 -1.02,-1.67s-2.48,1.94 -3.96,2.32c-1.46,0.38 -3.07,0.31 -4.51,-0.12 -0.36,-0.12 -0.96,-0.58 -0.96,-0.58a8.06,8.06 0,0 0,-5.07 -1.54,8.06 8.06,0 0,0 -5.07,1.54s-0.61,0.46 -0.96,0.58a8.64,8.64 0,0 1,-4.53 0.12,15.74 15.74,0 0,1 -3.96,-2.32s-0.6,1.15 -1.02,1.67a16.13,16.13 0,0 1,-2.48 2.32c-0.38,0.31 -0.77,0.71 -1.25,0.86q-0.61,0.17 -1.23,0a7.1,7.1 0,0 1,-1.92 -1.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M504.29,152.91s0.08,-1.09 -0.04,-1.63a4.03,4.03 0,0 0,-0.6 -1.34c-0.29,-0.44 -1.09,-1.15 -1.09,-1.15m-0.27,5.34s0.65,0.02 0.94,-0.12c0.5,-0.25 1.15,-1.21 1.15,-1.21s1.29,0.88 2,0.77c0.35,-0.04 0.61,-0.35 0.81,-0.65a1.54,1.54 0,0 0,0.23 -1.09c-0.06,-0.29 -0.31,-0.5 -0.54,-0.69 -0.44,-0.38 -1.23,-0.33 -1.52,-0.81 -0.23,-0.38 -0.1,-0.94 0.04,-1.36 0.27,-0.92 0.9,-1.67 1.44,-2.46 0.33,-0.5 1.02,-0.84 1.08,-1.44 0.04,-0.31 -0.38,-0.88 -0.38,-0.88s-1,-0.02 -1.48,0.12c-0.77,0.23 -1.59,0.58 -2.15,1.17 -0.46,0.5 -0.58,1.23 -0.83,1.86 -0.19,0.48 -0.48,1.48 -0.48,1.48s-1.15,0 -1.67,0.23a3.26,3.26 0,0 0,-1.67 1.48c-0.23,0.52 -0.19,1.19 0,1.73 0.21,0.58 0.67,1.02 1.17,1.34 0.56,0.35 1.86,0.54 1.86,0.54M457.23,148.8s-0.79,0.69 -1.08,1.11a4.03,4.03 0,0 0,-0.61 1.36c-0.12,0.54 -0.02,1.63 -0.02,1.63m-3.26,-8.74s-0.42,0.58 -0.38,0.88c0.08,0.6 0.77,0.96 1.09,1.44 0.52,0.79 1.15,1.54 1.44,2.46 0.12,0.42 0.25,0.96 0.02,1.34 -0.27,0.5 -1.08,0.46 -1.5,0.83 -0.23,0.19 -0.48,0.4 -0.56,0.69a1.54,1.54 0,0 0,0.23 1.09c0.19,0.31 0.48,0.61 0.83,0.65 0.69,0.12 1.98,-0.77 1.98,-0.77s0.67,0.96 1.15,1.21c0.29,0.13 0.96,0.12 0.96,0.12s1.31,-0.19 1.86,-0.56c0.48,-0.31 0.96,-0.77 1.15,-1.31 0.19,-0.56 0.25,-1.23 0,-1.75a3.26,3.26 0,0 0,-1.67 -1.48c-0.5,-0.25 -1.67,-0.23 -1.67,-0.23s-0.27,-1 -0.46,-1.48c-0.25,-0.63 -0.38,-1.36 -0.84,-1.86a4.8,4.8 0,0 0,-2.15 -1.17c-0.48,-0.13 -1.48,-0.12 -1.48,-0.12m26.04,2.75 l1.61,-0.46 1.59,0.46m-2.78,3.84s-1.25,-1.11 -1.48,-1.88a1.54,1.54 0,0 1,0.08 -1.06c0.23,-0.42 1.13,-0.9 1.13,-0.9s-1.36,-0.65 -1.73,-1.27a1.73,1.73 0,0 1,-0.1 -1.21,3.26 3.26,0 0,1 1.11,-1.54c0.61,-0.52 2.17,-1.04 2.17,-1.04s1.54,0.52 2.15,1.02a3.26,3.26 0,0 1,1.13 1.56c0.1,0.38 0.08,0.84 -0.12,1.21 -0.36,0.61 -1.73,1.27 -1.73,1.27s0.92,0.48 1.15,0.9c0.15,0.31 0.17,0.71 0.06,1.06 -0.23,0.77 -1.48,1.9 -1.48,1.9l-1.17,0.08zM483.08,145.61s2.57,1.09 3.46,2.13c0.23,0.29 0.44,0.67 0.42,1.06 -0.1,1.34 -1.98,3.49 -1.98,3.49l-3.84,-1.59l-2.48,0l-3.84,1.59s-1.88,-2.17 -1.96,-3.49c-0.04,-0.38 0.19,-0.77 0.42,-1.06 0.86,-1.04 3.46,-2.13 3.46,-2.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m477.12,128.49 l1.54,-1.19 1.25,0.04 -1.27,-0.04s-0.69,-0.46 -0.88,-0.81a1.73,1.73 0,0 1,-0.19 -1.15c0.1,-0.52 0.38,-1.06 0.84,-1.32 0.44,-0.25 1.5,-0.1 1.5,-0.1s1.06,-0.15 1.48,0.1c0.46,0.27 0.75,0.81 0.86,1.31 0.08,0.38 0,0.83 -0.19,1.15 -0.19,0.36 -0.88,0.83 -0.88,0.83l-1.29,0.04 1.25,-0.04 1.54,1.19m10.12,0.98 l-0.52,0.9m6.26,-3.84 l-1.44,0.19m1.63,1.69 l-2.21,0.12m-0.29,-2.65c0.29,0.19 0.81,0.56 0.88,0.88a1.92,1.92 0,0 1,-0.6 1.77c-0.46,0.38 -1.79,0.29 -1.79,0.29m-34.89,6.45 l3.26,-0.81c1.54,-0.38 3.07,-0.81 4.65,-1.09q2.69,-0.46 5.38,-0.67c2.27,-0.17 6.78,-0.12 6.78,-0.12s4.51,-0.06 6.76,0.12q2.69,0.19 5.38,0.67c1.57,0.29 3.11,0.73 4.67,1.09l3.26,0.81m-32.95,-5.8 l0.52,0.9m-6.28,-3.84 l1.44,0.19m-1.63,1.69 l2.21,0.12m-1.23,3.07s2.4,-0.61 3.61,-0.86q2.02,-0.42 4.03,-0.71 2.3,-0.27 4.67,-0.38c1.84,-0.08 5.55,-0.06 5.55,-0.06s3.69,0 5.55,0.08a54.53,54.53 53.63,0 1,8.68 1.08c1.21,0.25 3.63,0.86 3.63,0.86M465.07,128.83s-1.31,0.1 -1.77,-0.29c-0.48,-0.38 -0.77,-1.15 -0.61,-1.77 0.13,-0.46 1.08,-1 1.08,-1m12.12,14.21c-2.3,0.15 -4.61,0.29 -6.87,0.71a42.24,42.24 96.1,0 0,-6.95 1.86c-2.13,0.77 -4.4,4.34 -6.14,2.92 -0.38,-0.31 0.13,-0.96 0.31,-1.42a8.26,8.26 0,0 1,1.5 -2.42c0.46,-0.52 1.54,-0.58 1.63,-1.27 0.1,-0.5 -0.65,-0.77 -0.83,-1.27a3.26,3.26 0,0 1,-0.1 -1.92c0.23,-0.79 1.25,-1.19 1.48,-1.98 0.19,-0.69 -0.48,-1.5 -0.17,-2.15 0.42,-0.81 2.19,-0.52 2.3,-1.42 0.08,-0.44 -0.73,-0.58 -0.86,-0.98 -0.12,-0.31 0.06,-0.63 0.06,-0.96 0,-0.4 0.08,-0.86 -0.12,-1.25 -0.17,-0.33 -0.71,-0.4 -0.83,-0.77a1.15,1.15 0,0 1,0.21 -1.04c0.19,-0.19 0.71,0.08 0.83,-0.17 0.27,-0.5 -0.48,-1.02 -0.71,-1.54 -0.27,-0.61 -0.61,-1.21 -0.77,-1.86 -0.12,-0.52 -0.58,-1.34 -0.12,-1.59 0.54,-0.31 1.06,0.61 1.54,0.98 0.65,0.48 1.38,0.92 1.82,1.59 0.33,0.48 0.13,1.25 0.58,1.65 0.5,0.48 1.48,0.1 2,0.56 0.46,0.38 0.86,1.06 0.73,1.63s-1.46,0.56 -1.34,1.11c0.17,0.65 1.34,0.61 2,0.48 0.38,-0.06 0.67,-0.42 0.98,-0.65 0.27,-0.19 0.46,-0.42 0.77,-0.54 0.31,-0.15 1.02,-0.19 1.02,-0.19l-0.4,-0.1s-0.61,-0.48 -0.67,-0.83a1.15,1.15 52.58,0 1,0.31 -0.96,1.54 1.54,0 0,1 1.06,-0.42c0.33,0 0.67,0.13 0.88,0.36a1.15,1.15 0,0 1,0.33 0.92,1.15 1.15,62.65 0,1 -0.44,0.71 2.5,2.5 90.92,0 1,-0.88 0.29l0.44,0.12 1.17,1.63 5.11,-0.29s0.63,-0.63 0.5,-1q-0.19,-0.52 -1.09,-0.48c-1.63,0.08 -1.98,-1.31 -1.92,-2.3 0.06,-0.84 1.06,-1.4 1.82,-1.73 0.77,-0.29 2.07,0.71 2.42,-0.02 0.29,-0.63 -1.11,-0.96 -1.34,-1.63 -0.1,-0.29 -0.1,-0.65 0.04,-0.92 0.35,-0.69 1.09,-1.15 1.8,-1.48 0.31,-0.13 0.96,-0.15 0.96,-0.15s0.69,0.02 0.98,0.15c0.71,0.33 1.46,0.79 1.8,1.48 0.13,0.27 0.13,0.63 0.04,0.92 -0.21,0.67 -1.63,1 -1.34,1.63 0.35,0.73 1.67,-0.27 2.42,0.04s1.77,0.86 1.82,1.71c0.06,1 -0.27,2.38 -1.92,2.3q-0.9,-0.04 -1.09,0.48c-0.12,0.36 0.52,1 0.52,1l5.09,0.29 1.17,-1.63 0.44,-0.12s-0.63,-0.1 -0.86,-0.29a1.15,1.15 128.41,0 1,-0.44 -0.71c-0.04,-0.33 0.1,-0.69 0.31,-0.92s0.58,-0.36 0.88,-0.36q0.61,0 1.06,0.42c0.23,0.23 0.38,0.61 0.31,0.96s-0.67,0.83 -0.67,0.83l-0.4,0.1s0.71,0.04 1.04,0.19c0.29,0.12 0.5,0.36 0.75,0.54 0.33,0.23 0.6,0.58 0.98,0.65 0.67,0.13 1.84,0.19 1.98,-0.48 0.13,-0.58 -1.17,-0.56 -1.31,-1.11 -0.13,-0.58 0.25,-1.25 0.71,-1.63 0.52,-0.46 1.52,-0.08 2.02,-0.56 0.42,-0.38 0.25,-1.15 0.58,-1.65 0.44,-0.67 1.15,-1.11 1.8,-1.59 0.48,-0.36 1.02,-1.29 1.54,-0.98 0.46,0.27 0,1.08 -0.12,1.59 -0.15,0.65 -0.5,1.25 -0.77,1.86 -0.23,0.52 -0.96,1.04 -0.71,1.54 0.13,0.25 0.63,-0.04 0.83,0.17a1.15,1.15 0,0 1,0.21 1.04c-0.12,0.36 -0.65,0.44 -0.83,0.77 -0.19,0.38 -0.1,0.84 -0.1,1.27 0,0.31 0.15,0.63 0.06,0.94 -0.15,0.4 -0.96,0.54 -0.88,0.98 0.13,0.9 1.9,0.61 2.3,1.42 0.33,0.65 -0.36,1.46 -0.17,2.15 0.23,0.79 1.25,1.19 1.5,1.98 0.19,0.61 0.08,1.31 -0.12,1.92 -0.15,0.48 -0.9,0.77 -0.83,1.27 0.12,0.69 1.19,0.75 1.65,1.27q0.94,1.06 1.48,2.42c0.19,0.44 0.71,1.11 0.33,1.42 -1.77,1.42 -4.03,-2.13 -6.14,-2.92a42.24,42.24 100.36,0 0,-13.82 -2.57c-1.34,-0.08 -4.03,-0.08 -4.03,-0.08s-2.69,0 -4.03,0.08m5.97,-25.67 l-1.94,0.02 -1.96,-0.02m3.26,-6.51c0.5,-1.08 -0.38,-1.86 -1.31,-2.65 -0.92,0.77 -1.8,1.57 -1.31,2.65m2.71,8.99c0.75,0.04 1.65,0.81 2.23,0.33 0.25,-0.23 0.13,-0.71 0,-1.02 -0.31,-0.73 -1.61,-0.9 -1.67,-1.69 -0.06,-0.67 0.98,-1 1.21,-1.63q0.15,-0.52 0,-1.02c-0.54,-1.27 -2.78,-1.34 -3.19,-2.63 -0.17,-0.58 1.15,-1.25 1.31,-1.32 0.84,-0.36 1.86,0.52 2.69,0.15 0.58,-0.23 1.17,-0.79 1.15,-1.4 0,-0.65 -0.69,-1.21 -1.31,-1.48 -1.17,-0.5 -3.11,1.15 -3.84,0.1 -0.65,-0.98 1.67,-2.13 1.34,-3.26 -0.13,-0.56 -1.34,-1.02 -1.34,-1.02s-1.17,0.46 -1.32,1.02c-0.31,1.13 2.02,2.28 1.34,3.26 -0.71,1.06 -2.65,-0.6 -3.84,-0.1 -0.6,0.27 -1.31,0.83 -1.31,1.48 0,0.61 0.6,1.15 1.15,1.4 0.83,0.36 1.86,-0.52 2.69,-0.15 0.17,0.08 1.48,0.77 1.31,1.32 -0.38,1.29 -2.63,1.34 -3.17,2.63a1.54,1.54 0,0 0,0 1.02c0.21,0.63 1.27,0.96 1.21,1.63 -0.06,0.79 -1.38,0.96 -1.67,1.69 -0.13,0.31 -0.27,0.79 0,1.02 0.58,0.48 1.48,-0.29 2.23,-0.33 0.46,-0.04 1.4,0 1.4,0s0.92,-0.04 1.4,0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M498.11,183.07s-1.36,-0.58 -2.07,-0.83a49.54,49.54 0,0 0,-3.84 -1.25c-1.27,-0.35 -2.53,-0.71 -3.84,-0.86 -2.8,-0.31 -8.45,-0.04 -8.45,-0.04s-5.64,-0.27 -8.45,0.04c-1.31,0.15 -2.59,0.52 -3.84,0.86a49.54,49.54 100.2,0 0,-5.93 2.07M500.47,140.35s-2.3,-0.92 -3.48,-1.32q-1.46,-0.52 -2.98,-0.92 -1.84,-0.44 -3.72,-0.67a74.3,74.3 74.05,0 0,-10.39 -0.65s-3.3,0.02 -4.95,0.12a72.38,72.38 113.76,0 0,-9.18 1.21q-1.5,0.38 -2.96,0.92c-1.17,0.4 -3.46,1.32 -3.46,1.32m-2.73,23.12s3.36,-1.15 5.07,-1.67c1.75,-0.54 3.49,-1.15 5.28,-1.54 2.15,-0.48 4.34,-0.71 6.53,-0.96 2.11,-0.21 6.39,-0.44 6.39,-0.44s4.26,0.23 6.39,0.46c2.17,0.23 4.38,0.46 6.53,0.92 1.79,0.38 3.51,1.02 5.28,1.56l5.07,1.67"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m417.39,173.57 l19.43,16.67s-1,2.15 -1.4,3.26a27.46,27.46 0,0 0,-1.79 7.87l-22.66,-20.74c0.33,-4.74 3,-6.43 6.41,-7.07z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m414.65,183.97 l-4.67,5.22 2.92,2.8 -4.51,4.38 -2.92,-2.9 -4.9,5.22 -0.04,6.47 11.14,8.74 3.86,-2.86 -0.21,-6.2 2.96,1.65 2.98,-1.8 0.15,5.59 -2.42,-1.75 -2.63,3.46 -0.27,6.14 8.39,7.49 10.18,-9.04 -0.77,-2.71 -0.42,-2.44 -0.25,-4.82 0.38,-5.28z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m409.98,189.2 l-0.12,5.76 3,-2.94z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m424.82,218.96 l-0.36,6.59m-3.57,-26.82 l0.12,6.11m-2.84,-3.32 l0.17,4.95m-2.78,-6.97 l0.12,5.57m-4.11,2.92 l0.06,5.88m4.74,-1.86 l8.45,7.1 8.66,-7.6m-32.83,-12.84 l11.06,9.45 3.63,-3.24 -2.88,-2.42 3.21,-3.01 2.48,2.13 2.78,-2.84 3.65,3.51 -3.3,2.57 3.07,2.8 -2.92,2.69"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m488.05,236.03 l9.35,8.56 2.05,6.05 -12.02,-1.29 -6.85,-6.78 -7.3,-6.76 -31.49,-27.28s0.67,-2.52 1.08,-3.74q0.96,-2.67 2.04,-5.28l0.58,-1.31 35.14,31.16z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M496,237.31c5.18,7.3 13.96,14.15 16.61,17.45 2.67,3.3 4.38,6.43 0.5,10.66s-8.26,1.82 -11.73,-0.5 -9.52,-9.85 -18.2,-15.61z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m492.93,249.56 l-6.76,-3.07m7.87,-7.33c0.46,1.54 0.1,3.23 0.69,4.68 0.58,1.46 2.69,3.84 2.69,3.84s-1.34,0.19 -1.98,0.42c-0.9,0.36 -2.3,0.52 -2.52,1.48 -0.19,0.83 0.77,1.54 1.34,2.15q0.9,0.86 2,1.46c1.17,0.69 2.38,1.46 3.72,1.71 1.67,0.31 4.82,-2.07 5.11,-0.38 0.12,0.69 -1.42,0.25 -2.05,0.58 -1.5,0.73 -2.23,2.3 -0.92,3.42 0.84,0.71 2.17,0.46 3.26,0.56 1.06,0.08 2.25,-0.54 3.17,0 1.08,0.61 2.44,2.11 1.86,3.23 -0.58,1.15 -2.78,1.11 -3.84,0.38 -0.77,-0.58 -0.56,-1.82 -0.67,-2.76l0,-0.84"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M536.7,284.18m-24.9,0a24.9,24.9 0,1 1,49.8 0a24.9,24.9 0,1 1,-49.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M527.37,285.25a7.78,7.78 0,1 0,7.78 7.76l2.69,-0.12a7.78,7.78 0,1 0,7.78 -7.78l0.12,-1.52a7.78,7.78 0,1 0,-7.78 -7.78l-2.69,-0.23a7.78,7.78 0,1 0,-7.78 7.78z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M536.7,277.65m-2.8,0a2.8,2.8 45,1 1,5.61 0a2.8,2.8 45,1 1,-5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M543.36,284.18m-2.8,0a2.8,2.8 45,1 1,5.61 0a2.8,2.8 45,1 1,-5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M536.82,290.15m-2.8,0a2.8,2.8 45,1 1,5.61 0a2.8,2.8 45,1 1,-5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M529.91,284.18m-2.8,0a2.8,2.8 45,1 1,5.61 0a2.8,2.8 45,1 1,-5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M536.22,299.21c-1.02,2.11 -2.42,3.07 -4.2,4.22a12.1,12.1 0,0 0,8.41 0,10.37 10.37,0 0,1 -4.2,-4.22zM521.53,283.81c-2.11,-1.02 -3.07,-2.42 -4.22,-4.22a12.1,12.1 0,0 0,0 8.45,10.18 10.18,0 0,1 4.22,-4.22zM552.1,284.51c2.11,-1.02 3.07,-2.42 4.22,-4.2a12.1,12.1 0,0 1,0 8.43c-0.9,-1.79 -2.3,-3.07 -4.22,-4.22zM537.05,268.99c-1.02,-2.11 -2.42,-3.07 -4.22,-4.22a12.1,12.1 0,0 1,8.45 0,10.18 10.18,0 0,0 -4.22,4.22z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M417.43,173.53s0.13,-0.96 0,-1.42a4.42,4.42 0,0 0,-1.27 -1.96,3.84 3.84,0 0,0 -1.77,-0.88 4.8,4.8 0,0 0,-3.4 0.81l-2.88,1.19 -1.06,3.4s-0.61,1.08 -0.77,1.67a4.42,4.42 0,0 0,-0.1 1.94c0.19,0.75 0.61,1.46 1.21,1.96a3.65,3.65 0,0 0,1.98 0.77,5.38 5.38,0 0,0 1.59,-0.36c0.31,-3.57 1.9,-6.3 6.43,-7.08z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m411.06,170.04 l-7.45,-3.26 -0.6,0.58 4.03,7.33s1.15,-1.67 1.8,-2.44c0.69,-0.79 2.21,-2.21 2.21,-2.21z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M402.8,166.14m-3.07,0a3.07,3.07 0,1 1,6.14 0a3.07,3.07 0,1 1,-6.14 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.71,179c0.65,5.38 1.4,10.73 3.07,16.09l5.95,5.47c-1.25,-5.63 -3.53,-11.23 -3.51,-16.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m432.7,186.7 l-16.4,-1.23 5.22,4.8 14.98,0.63 0.23,-0.77zM452.93,204.86a11.9,11.9 0,0 0,-7.28 7.1c-1.23,0.13 -2.36,0.13 -2.76,-0.77a11.33,11.33 0,0 1,6.85 -9.18c2.88,-0.54 2.34,1.71 3.19,2.82zM458.21,209.82a11.9,11.9 0,0 0,-7.28 7.14c-1.21,0.1 -2.36,0.12 -2.76,-0.79a11.33,11.33 0,0 1,6.87 -9.18c2.88,-0.54 2.32,1.71 3.17,2.82zM518.27,260.1a13.82,13.82 0,0 0,-2.9 0.29c0.06,1.54 -0.58,3.19 -2.27,5.03a6.34,6.34 0,0 1,-4.55 2.3s-0.54,3.4 0.5,4.47c1.21,1.27 5.2,0.75 5.2,0.75l0.35,-0.21a24.96,24.96 0,0 1,6.12 -7.64l0.06,-0.25s0.58,-3.17 -0.4,-4.13c-0.46,-0.44 -1.27,-0.58 -2.11,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M555.86,264.08m-3.4,0a3.4,3.4 0,1 1,6.8 0a3.4,3.4 0,1 1,-6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M557.04,303.36m-3.4,0a3.4,3.4 0,1 1,6.8 0a3.4,3.4 0,1 1,-6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M518.92,305.59m-3.4,0a3.4,3.4 0,1 1,6.8 0a3.4,3.4 0,1 1,-6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m542.61,173.57l-19.43,16.67s1,2.15 1.4,3.26a27.46,27.46 0,0 1,1.79 7.87l22.66,-20.74c-0.33,-4.74 -3,-6.43 -6.41,-7.07z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m545.35,183.97l4.67,5.22 -2.92,2.8 4.51,4.38 2.92,-2.9 4.9,5.22 0.04,6.47 -11.14,8.74 -3.86,-2.86 0.21,-6.2 -2.96,1.65 -2.98,-1.8 -0.15,5.59 2.42,-1.75 2.63,3.46 0.27,6.14 -8.39,7.49 -10.18,-9.04 0.77,-2.71 0.42,-2.44 0.25,-4.82 -0.38,-5.28z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m550.02,189.2l0.12,5.76 -3,-2.94z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m535.18,218.96l0.36,6.59m3.57,-26.82l-0.12,6.11m2.84,-3.32l-0.17,4.95m2.78,-6.97l-0.12,5.57m4.11,2.92l-0.06,5.88m-4.74,-1.86l-8.45,7.1 -8.66,-7.6m32.83,-12.84l-11.06,9.45 -3.63,-3.24 2.88,-2.42 -3.21,-3.01 -2.48,2.13 -2.78,-2.84 -3.65,3.51 3.3,2.57 -3.07,2.8 2.92,2.69"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m471.95,236.03l-9.35,8.56 -2.05,6.05 12.02,-1.29 6.85,-6.78 7.3,-6.76 31.49,-27.28s-0.67,-2.52 -1.08,-3.74q-0.96,-2.67 -2.04,-5.28l-0.58,-1.31 -35.14,31.16z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M464,237.31c-5.18,7.3 -13.96,14.15 -16.61,17.45 -2.67,3.3 -4.38,6.43 -0.5,10.66s8.26,1.82 11.73,-0.5 9.52,-9.85 18.2,-15.61z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="m467.07,249.56l6.76,-3.07m-7.87,-7.33c-0.46,1.54 -0.1,3.23 -0.69,4.68 -0.58,1.46 -2.69,3.84 -2.69,3.84s1.34,0.19 1.98,0.42c0.9,0.36 2.3,0.52 2.52,1.48 0.19,0.83 -0.77,1.54 -1.34,2.15q-0.9,0.86 -2,1.46c-1.17,0.69 -2.38,1.46 -3.72,1.71 -1.67,0.31 -4.82,-2.07 -5.11,-0.38 -0.12,0.69 1.42,0.25 2.05,0.58 1.5,0.73 2.23,2.3 0.92,3.42 -0.84,0.71 -2.17,0.46 -3.26,0.56 -1.06,0.08 -2.25,-0.54 -3.17,0 -1.08,0.61 -2.44,2.11 -1.86,3.23 0.58,1.15 2.78,1.11 3.84,0.38 0.77,-0.58 0.56,-1.82 0.67,-2.76l-0,-0.84"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.3,284.18m24.9,0a24.9,24.9 0,1 0,-49.8 0a24.9,24.9 0,1 0,49.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M432.63,285.25a7.78,7.78 0,1 1,-7.78 7.76l-2.69,-0.12a7.78,7.78 0,1 1,-7.78 -7.78l-0.12,-1.52a7.78,7.78 0,1 1,7.78 -7.78l2.69,-0.23a7.78,7.78 0,1 1,7.78 7.78z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.3,277.65m2.8,0a2.8,2.8 0,1 0,-5.61 0a2.8,2.8 0,1 0,5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M416.64,284.18m2.8,0a2.8,2.8 0,1 0,-5.61 0a2.8,2.8 0,1 0,5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.18,290.15m2.8,0a2.8,2.8 0,1 0,-5.61 0a2.8,2.8 0,1 0,5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M430.09,284.18m2.8,0a2.8,2.8 0,1 0,-5.61 0a2.8,2.8 0,1 0,5.61 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ccc"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.78,299.21c1.02,2.11 2.42,3.07 4.2,4.22a12.1,12.1 0,0 1,-8.41 0,10.37 10.37,0 0,0 4.2,-4.22zM438.47,283.81c2.11,-1.02 3.07,-2.42 4.22,-4.22a12.1,12.1 0,0 1,-0 8.45,10.18 10.18,0 0,0 -4.22,-4.22zM407.9,284.51c-2.11,-1.02 -3.07,-2.42 -4.22,-4.2a12.1,12.1 0,0 0,-0 8.43c0.9,-1.79 2.3,-3.07 4.22,-4.22zM422.95,268.99c1.02,-2.11 2.42,-3.07 4.22,-4.22a12.1,12.1 0,0 0,-8.45 0,10.18 10.18,0 0,1 4.22,4.22z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M542.57,173.53s-0.13,-0.96 -0,-1.42a4.42,4.42 0,0 1,1.27 -1.96,3.84 3.84,0 0,1 1.77,-0.88 4.8,4.8 0,0 1,3.4 0.81l2.88,1.19 1.06,3.4s0.61,1.08 0.77,1.67a4.42,4.42 0,0 1,0.1 1.94c-0.19,0.75 -0.61,1.46 -1.21,1.96a3.65,3.65 0,0 1,-1.98 0.77,5.38 5.38,0 0,1 -1.59,-0.36c-0.31,-3.57 -1.9,-6.3 -6.43,-7.08z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m548.94,170.04l7.45,-3.26 0.6,0.58 -4.03,7.33s-1.15,-1.67 -1.8,-2.44c-0.69,-0.79 -2.21,-2.21 -2.21,-2.21z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M557.2,166.14m3.07,0a3.07,3.07 0,1 0,-6.14 0a3.07,3.07 0,1 0,6.14 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M536.29,179c-0.65,5.38 -1.4,10.73 -3.07,16.09l-5.95,5.47c1.25,-5.63 3.53,-11.23 3.51,-16.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m527.3,186.7l16.4,-1.23 -5.22,4.8 -14.98,0.63 -0.23,-0.77zM507.07,204.86a11.9,11.9 0,0 1,7.28 7.1c1.23,0.13 2.36,0.13 2.76,-0.77a11.33,11.33 0,0 0,-6.85 -9.18c-2.88,-0.54 -2.34,1.71 -3.19,2.82zM501.79,209.82a11.9,11.9 0,0 1,7.28 7.14c1.21,0.1 2.36,0.12 2.76,-0.79a11.33,11.33 0,0 0,-6.87 -9.18c-2.88,-0.54 -2.32,1.71 -3.17,2.82zM441.73,260.1a13.82,13.82 0,0 1,2.9 0.29c-0.06,1.54 0.58,3.19 2.27,5.03a6.34,6.34 0,0 0,4.55 2.3s0.54,3.4 -0.5,4.47c-1.21,1.27 -5.2,0.75 -5.2,0.75l-0.35,-0.21a24.96,24.96 0,0 0,-6.12 -7.64l-0.06,-0.25s-0.58,-3.17 0.4,-4.13c0.46,-0.44 1.27,-0.58 2.11,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M404.14,264.08m3.4,0a3.4,3.4 0,1 0,-6.8 0a3.4,3.4 0,1 0,6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M402.96,303.36m3.4,0a3.4,3.4 0,1 0,-6.8 0a3.4,3.4 0,1 0,6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M441.08,305.59m3.4,0a3.4,3.4 0,1 0,-6.8 0a3.4,3.4 0,1 0,6.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M481.89,225.75c0,1.04 -0.86,1.42 -2.55,1.42q-2.48,0.02 -2.44,-1.52c0.04,-1.54 0.77,-1.29 2.44,-1.29 1.69,0 2.55,0.35 2.55,1.38z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M483.12,228.5c0,1.04 -0.86,1.42 -2.55,1.42q-2.48,0.02 -2.44,-1.52c0.04,-1.54 0.77,-1.29 2.44,-1.29 1.69,0 2.55,0.35 2.55,1.38z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M483.44,232.07c-0.33,0.96 -1.29,1.08 -2.88,0.52 -1.57,-0.58 -2.15,-1.27 -1.8,-2.25s1.15,-0.96 2.75,-0.4c1.57,0.56 2.3,1.15 1.94,2.11z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M483.48,234.72c-0.19,1.02 -1.13,1.23 -2.76,0.9q-2.44,-0.48 -2.11,-1.98c0.19,-1.02 1.02,-1.11 2.65,-0.77s2.44,0.83 2.23,1.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.25,237.81c-0.19,1.02 -1.13,1.23 -2.76,0.9q-2.44,-0.48 -2.11,-1.98c0.19,-1.02 1.02,-1.11 2.65,-0.77s2.44,0.83 2.23,1.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M491.97,233.51c-0.5,-0.13 -0.44,-1.02 -0.31,-1.52 0.23,-0.77 1.61,-1.75 1.61,-1.75s0.35,1.08 0.19,1.57c-0.23,0.71 -0.77,1.88 -1.5,1.69z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M490.16,235.08c-0.54,-0.12 -0.61,-1.06 -0.48,-1.59 0.23,-0.83 1.86,-1.79 1.86,-1.79s0.29,1.15 0.1,1.69c-0.25,0.71 -0.75,1.86 -1.48,1.69zM488.09,238.08c-0.83,-0.21 -0.73,-1.69 -0.5,-2.5 0.19,-0.69 0.71,-1.77 1.38,-1.59 0.81,0.19 0.81,1.59 0.6,2.4 -0.17,0.73 -0.77,1.88 -1.48,1.69z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M485.63,240.42c-0.81,-0.19 -1.15,-1.46 -0.98,-2.28s1.02,-2 1.86,-1.8c0.86,0.19 1.15,1.59 0.96,2.46 -0.19,0.79 -1.04,1.8 -1.84,1.63zM485.21,241.84c-0.19,0.96 -1.44,1.31 -2.21,1.06s-1.82,-1.36 -1.61,-2.4c0.23,-1.04 1.57,-1.31 2.36,-1 0.75,0.29 1.67,1.38 1.46,2.34zM468.89,232.38s0.98,0.48 1.4,0.92c0.44,0.46 0.98,0.63 0.71,1.32s-1.11,0.46 -1.56,-0.04c-0.4,-0.44 -0.58,-2.21 -0.58,-2.21zM473.75,234.05c1.02,0.21 0.98,1.38 0.48,2.11 -0.54,0.73 -2.09,1.71 -3.15,1.5s-0.92,-1.5 -0.35,-2.25c0.56,-0.69 2.04,-1.54 3.01,-1.34z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.11,236.26c0.92,0.42 0.65,1.56 0,2.15 -0.69,0.61 -2.42,1.21 -3.4,0.77 -1,-0.42 -0.58,-1.65 0.15,-2.25 0.69,-0.58 2.3,-1.08 3.24,-0.67z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M479.79,238.31c0.92,0.42 0.65,1.56 0,2.15 -0.69,0.61 -2.42,1.21 -3.4,0.77 -1,-0.42 -0.58,-1.65 0.15,-2.25 0.69,-0.58 2.3,-1.08 3.24,-0.67z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M480.7,240.13c0.92,0.13 1.15,1.42 0.88,2.25 -0.31,0.86 -1.44,2.04 -2.4,1.88s-1.15,-1.54 -0.81,-2.42c0.31,-0.81 1.4,-1.86 2.3,-1.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M483.02,242.94c0.77,0.44 0.58,1.84 0.08,2.61 -0.52,0.79 -1.88,1.65 -2.69,1.19 -0.81,-0.48 -0.52,-1.98 0.04,-2.76 0.54,-0.73 1.82,-1.46 2.57,-1.04zM466.49,235.28c0.83,-0.06 1.38,1.15 1.36,2.04 0,0.94 -0.63,2.38 -1.48,2.46s-1.4,-1.29 -1.34,-2.23c0.02,-0.88 0.65,-2.19 1.46,-2.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M469.18,237.64c0.77,0.17 0.96,1.5 0.67,2.34 -0.31,0.88 -1.34,2.07 -2.17,1.88s-0.96,-1.65 -0.61,-2.53c0.31,-0.83 1.31,-1.88 2.11,-1.71z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M471.63,239.27c0.96,0.23 1.15,1.88 0.81,2.94 -0.35,1.09 -1.57,2.57 -2.57,2.34s-1.11,-2.05 -0.71,-3.17c0.36,-1.02 1.54,-2.32 2.48,-2.11z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M473.63,241.31c0.96,-0.12 1.73,1.34 1.79,2.46 0.06,1.15 -0.58,2.96 -1.57,3.07 -1.02,0.13 -1.77,-1.52 -1.79,-2.69 -0.02,-1.08 0.63,-2.73 1.57,-2.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M476.01,243.01c0.86,-0.12 1.57,1.11 1.61,2.02 0.06,0.96 -0.52,2.46 -1.42,2.57 -0.94,0.12 -1.61,-1.27 -1.63,-2.23 0,-0.9 0.58,-2.27 1.44,-2.36z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M478.47,245.01c0.86,-0.12 1.57,1.11 1.61,2.02 0.06,0.96 -0.52,2.46 -1.42,2.57 -0.94,0.12 -1.61,-1.27 -1.63,-2.23 0,-0.9 0.58,-2.27 1.44,-2.36z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M493.43,240.17c-0.63,0.54 -1.86,0.06 -2.42,-0.56 -0.6,-0.63 -1.02,-2.02 -0.35,-2.59 0.65,-0.58 1.96,0.04 2.53,0.69 0.52,0.63 0.84,1.92 0.23,2.46z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M492.08,242.63c-0.63,0.54 -1.86,0.06 -2.42,-0.56 -0.6,-0.63 -1.02,-2.02 -0.35,-2.59 0.65,-0.58 1.96,0.04 2.53,0.69 0.52,0.63 0.84,1.92 0.23,2.46z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M489.68,244.68c-0.63,0.54 -1.86,0.06 -2.42,-0.56 -0.6,-0.63 -1.02,-2.02 -0.35,-2.59 0.65,-0.58 1.96,0.04 2.53,0.69 0.52,0.63 0.84,1.92 0.23,2.46z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M488.8,245.99c-0.21,0.79 -1.54,1.08 -2.32,0.86 -0.84,-0.19 -1.96,-1.11 -1.73,-1.96 0.25,-0.84 1.67,-1.08 2.5,-0.83 0.81,0.23 1.77,1.13 1.54,1.92z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M485.34,245.41c0.69,0.48 0.54,2.02 0.08,2.86 -0.5,0.86 -1.73,1.82 -2.48,1.31s-0.48,-2.17 0.04,-3.03c0.48,-0.81 1.67,-1.61 2.36,-1.15z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M481.96,247.2c0.77,0.46 0.58,1.84 0.08,2.61 -0.52,0.79 -1.88,1.65 -2.69,1.19 -0.81,-0.48 -0.52,-1.98 0.04,-2.76 0.54,-0.73 1.82,-1.46 2.57,-1.04z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.61,252.1c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M473.21,254.75c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M472.59,257.18c-0.83,0.35 -0.92,1.96 -0.58,2.94 0.35,1.02 1.52,2.28 2.4,1.92 0.88,-0.35 0.9,-2.11 0.5,-3.13 -0.38,-0.94 -1.5,-2.05 -2.3,-1.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M470.58,260.04c-0.83,0.35 -0.92,1.96 -0.58,2.94 0.35,1.02 1.52,2.28 2.4,1.92 0.88,-0.35 0.9,-2.11 0.5,-3.13 -0.38,-0.94 -1.5,-2.05 -2.3,-1.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M469.12,263.23c-0.88,0.19 -1.25,1.79 -1.06,2.8 0.19,1.06 1.15,2.5 2.07,2.28s1.23,-1.96 1,-3.01c-0.23,-0.98 -1.15,-2.27 -2.02,-2.07z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.18,266.28c-0.88,0.19 -1.25,1.79 -1.06,2.8 0.19,1.06 1.15,2.5 2.07,2.28s1.23,-1.96 1,-3.01c-0.23,-0.98 -1.15,-2.27 -2.02,-2.07z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M465.53,269.43c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M463.55,273.12c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M461.23,276.54c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M458.83,279.48c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M456.35,282.2c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M453.62,284.43c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M451.01,286.83c-0.88,0.04 -1.54,1.54 -1.54,2.55 0,1.08 0.67,2.67 1.61,2.63s1.57,-1.69 1.54,-2.78c-0.04,-1.02 -0.71,-2.44 -1.59,-2.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="m448.02,288.04 l-1.02,3.96s0.58,1.4 1.15,1.34c1.06,-0.12 1.34,-1.73 1.54,-2.78 0.19,-1.61 -1.67,-2.52 -1.67,-2.52zM510.3,290.55c0.31,1.15 2.94,2.17 2.94,2.17l-1.29,-5.34c-1.09,0.04 -1.82,2.38 -1.65,3.15z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M510.07,286.27c-0.83,-0.35 -2.02,0.77 -2.44,1.71 -0.44,0.98 -0.46,2.69 0.42,3.05 0.88,0.35 2.11,-0.92 2.52,-1.92 0.38,-0.96 0.33,-2.52 -0.5,-2.86z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M507.15,283.91c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M504.58,281.28c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M502.18,278.3c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M499.72,275.79c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M497.75,272.33c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M495.58,269.24c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M493.6,265.96c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M491.83,262.16c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M489.91,258.72c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M488.34,254.63c-0.9,-0.06 -1.67,1.38 -1.77,2.4 -0.1,1.08 0.42,2.71 1.36,2.76 0.96,0.04 1.73,-1.54 1.79,-2.63 0.06,-1.02 -0.5,-2.5 -1.38,-2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.69,250.48s-0.31,5.11 1.54,5.43c1.17,0.21 2.09,-2.86 2.09,-2.86z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M480.5,309.27c-0.56,0.69 0.19,2.15 0.96,2.82 0.83,0.69 2.48,1.19 3.05,0.44s-0.29,-2.28 -1.15,-2.96c-0.77,-0.61 -2.3,-1.02 -2.86,-0.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.02,308.85c-0.56,0.69 0.19,2.15 0.96,2.82 0.83,0.69 2.48,1.19 3.05,0.44s-0.29,-2.28 -1.15,-2.96c-0.77,-0.61 -2.3,-1.02 -2.86,-0.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M487.57,308.16c-0.56,0.69 0.19,2.15 0.96,2.82 0.83,0.69 2.48,1.19 3.05,0.44s-0.29,-2.28 -1.15,-2.96c-0.77,-0.61 -2.3,-1.02 -2.86,-0.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M490.91,307.93c-0.44,0.77 0.5,2.11 1.36,2.65 0.92,0.58 2.63,0.83 3.07,0 0.48,-0.84 -0.61,-2.23 -1.56,-2.76 -0.88,-0.5 -2.46,-0.65 -2.88,0.12z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M494.75,307.32c-0.44,0.77 0.5,2.11 1.36,2.65 0.92,0.58 2.63,0.83 3.07,0 0.48,-0.84 -0.61,-2.23 -1.56,-2.76 -0.88,-0.5 -2.46,-0.65 -2.88,0.12z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M498.07,306.32c-0.38,0.83 0.69,2.05 1.61,2.52 0.96,0.48 2.69,0.58 3.07,-0.29s-0.84,-2.15 -1.82,-2.59c-0.92,-0.42 -2.5,-0.44 -2.88,0.38z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M501.7,305.24c-0.38,0.83 0.69,2.05 1.61,2.52 0.96,0.48 2.69,0.58 3.07,-0.29s-0.84,-2.15 -1.82,-2.59c-0.92,-0.42 -2.5,-0.44 -2.88,0.38z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M505.33,304.11c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M509.02,302.92c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M512.57,301.42c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M516.37,299.85c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M520.21,298.62c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M520.67,295.6c-0.33,0.84 0.81,2.02 1.75,2.4 1,0.42 2.73,0.42 3.05,-0.46 0.33,-0.9 -0.96,-2.11 -1.98,-2.5 -0.96,-0.35 -2.52,-0.27 -2.82,0.56z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M519.81,294.7s2.61,0.38 2.84,-0.56c0.31,-1.25 -2.96,-2.44 -2.96,-2.44z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M449.71,302.52c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M453.34,304.01c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M456.81,304.55c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460.61,305.49c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M464.05,305.99c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.43,306.68c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M470.9,307.64c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.34,308.26c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.85,308.79c-0.77,0.48 -0.58,2.09 -0.08,2.98 0.52,0.94 1.88,2 2.69,1.5s0.54,-2.25 -0.04,-3.17c-0.52,-0.86 -1.8,-1.77 -2.57,-1.31z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M439.45,291.36c-0.81,-0.4 -2.11,0.58 -2.61,1.46 -0.52,0.96 -0.73,2.65 0.12,3.07 0.84,0.44 2.21,-0.69 2.69,-1.65 0.48,-0.9 0.58,-2.48 -0.19,-2.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M438.26,294.6c-0.84,-0.31 -2,0.84 -2.38,1.8 -0.38,1 -0.38,2.71 0.52,3.03 0.9,0.31 2.09,-1 2.46,-2.02 0.35,-0.96 0.25,-2.52 -0.58,-2.82z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M439.36,297.6c-0.86,-0.23 -1.92,1.02 -2.21,2 -0.31,1.04 -0.12,2.75 0.81,2.98s1.98,-1.17 2.27,-2.23c0.25,-0.98 0,-2.53 -0.86,-2.76z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M442.18,298.81c-0.86,-0.25 -1.96,0.96 -2.28,1.92 -0.33,1.04 -0.19,2.75 0.71,3.01s2.02,-1.11 2.32,-2.15c0.29,-0.96 0.1,-2.53 -0.77,-2.78z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M444.02,299.29c-0.88,0.21 -1.21,1.8 -1,2.82 0.21,1.06 1.19,2.46 2.11,2.23s1.19,-1.98 0.92,-3.03c-0.23,-0.96 -1.19,-2.23 -2.05,-2.02z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M447.25,300.48c-0.88,0.21 -1.21,1.8 -1,2.82 0.21,1.06 1.19,2.46 2.11,2.23s1.19,-1.98 0.92,-3.03c-0.23,-0.96 -1.19,-2.23 -2.05,-2.02z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M476.26,249.95c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.51,253.34c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.55,257.22c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.51,261.29c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.68,265.27c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.03,268.93c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.51,273.04c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.03,276.36c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.51,279.65c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.51,283.56c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.26,287.4c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.32,291.09c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.45,295.01c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.45,298.81c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.32,302.61c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.09,306.47c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.09,310.68c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.97,315.05c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.97,319.16c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.97,322.94c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.86,327.23c-0.65,0.6 -0.13,2.15 0.54,2.92 0.71,0.81 2.27,1.56 2.96,0.9s0.06,-2.3 -0.69,-3.09c-0.71,-0.75 -2.15,-1.34 -2.8,-0.75z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.38,250.21c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.9,253.31c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.84,256.76c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.79,260.28c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.73,263.48c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.9,266.94c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.67,270.26c0.48,0.71 -0.25,2.07 -0.98,2.69 -0.77,0.61 -2.3,1.04 -2.78,0.29 -0.5,-0.77 0.36,-2.19 1.15,-2.8 0.77,-0.58 2.15,-0.88 2.61,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.59,273.79c0.5,0.75 -0.29,2.17 -1.09,2.78 -0.86,0.65 -2.53,1.09 -3.07,0.31s0.38,-2.27 1.27,-2.88c0.84,-0.6 2.38,-0.92 2.88,-0.19z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.34,277.17c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.46,281.09c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.52,284.95c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.4,289.09c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.52,292.95c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.4,296.87c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.46,300.79c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.46,304.76c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.29,308.74c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.52,312.36c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.52,316.17c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.4,320.26c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M484.4,324.29c0.67,0.61 0.15,2.17 -0.52,2.94 -0.71,0.81 -2.27,1.54 -2.94,0.9 -0.69,-0.67 -0.06,-2.3 0.69,-3.11 0.69,-0.73 2.13,-1.34 2.78,-0.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M478.12,354.87a14.78,14.78 0,0 0,9.27 0.08l-3.36,-5.38"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="m485.55,341.03 l-6.09,-1.57s0.08,-6.76 3.92,-6.16c3.19,0.48 2.3,7.74 2.17,7.74z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M482.6,330.82m-2.48,0a2.48,2.48 0,1 1,4.95 0a2.48,2.48 0,1 1,-4.95 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M485,344.04c1.34,3.42 2.17,6.37 0.9,10.23m1.46,0.67c-1.96,-1.59 -7.03,-1.13 -9.22,-0.08 -0.84,0 -3.15,-15.97 4.93,-15.59 7.45,0.38 4.8,15.74 4.28,15.67z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M471.52,358.33a9.02,9.02 0,0 0,10.41 0.1l-7.72,-8.06"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
  <path
      android:pathData="M476.93,333.75m-2.48,0a2.48,2.48 0,1 1,4.95 0a2.48,2.48 0,1 1,-4.95 0"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="m479.99,341.76 l-6.28,-0.12s-0.81,-5.11 3.11,-5.34c3.24,-0.19 3.3,5.45 3.17,5.45z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#ce9d09"
      android:strokeColor="#000"/>
  <path
      android:pathData="M476.97,357.25c-1.21,-2.67 -1.32,-5.95 -1,-9.5m-1.8,9.68c-1.48,-2.53 -1.75,-5.76 -0.58,-9.83m8.33,10.87c-0.96,-1.19 -7.74,-1.69 -10.39,-0.08 -0.96,0 -3.57,-18.01 5.57,-17.57 8.37,0.4 5.38,17.72 4.8,17.64z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.61"
      android:fillColor="#fa0204"
      android:strokeColor="#000"/>
</vector>
