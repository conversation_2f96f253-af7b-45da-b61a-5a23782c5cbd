<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M426.7,0H640v480H426.7z"
      android:fillColor="#ce1126"/>
  <path
      android:pathData="M213.3,0h213.4v480H213.3z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,0h213.3v480H0z"
      android:fillColor="#006847"/>
  <path
      android:pathData="m355.8,289.4 l0.2,4.5 1.7,-1.1 -1.3,-3.7z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M355.6,288.2m-1.4,0a1.4,1.4 0,1 1,2.8 0a1.4,1.4 0,1 1,-2.8 0"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m361.1,296.4 l-3.2,-3.1 -1.5,1.2 4.5,2.6z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M360.9,298.2q-0.7,-1 0.3,-2 1.2,-0.7 2,0.2 0.7,1 -0.3,2 -1.2,0.7 -2,-0.2z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m386.3,249.6 l3.4,3.3 0.4,-1.7 -3.1,-2z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M385.9,248.7m-1.4,0a1.4,1.4 0,1 1,2.8 0a1.4,1.4 0,1 1,-2.8 0"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M395.2,251.6 L390,253l0.5,-1.7 4.4,-0.4z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M396,250.8m-1.4,0a1.4,1.4 0,1 1,2.8 0a1.4,1.4 0,1 1,-2.8 0"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m378,276.8 l-3.2,-4.8 0.5,-0.3 3.5,4.2z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M374.5,270.8m-1.4,0a1.4,1.4 0,1 1,2.8 0a1.4,1.4 0,1 1,-2.8 0"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m378.1,277 l4,0.7 0.1,-0.5 -3.3,-1.4z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M383.3,277.7m-1.4,0a1.4,1.4 0,1 1,2.8 0a1.4,1.4 0,1 1,-2.8 0"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M284.6,288q-0.1,1.2 -1,1.2 -1.2,0 -1,-1.2c0.2,-1.2 0.5,-1.3 1,-1.3s1,0.7 1,1.4z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m284.6,290.3 l1,5 -1.3,-0.5 -0.4,-4.3z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M285.7,288.6c0.6,0.7 -0.4,1.9 -1.4,2.2s-2.4,-0.2 -2.4,-1.2 1.6,-0.5 2,-0.6c0.6,-0.2 1.2,-1.2 1.8,-0.4z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M275.4,296.3a1.6,1.1 0,1 0,3.2 0a1.6,1.1 0,1 0,-3.2 0z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m279.6,296 l4.8,-0.2 -0.8,-1 -4,0.4z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M280,295.4c0.5,1.3 0.5,3 -0.9,2.7 -1.4,-0.1 -1,-1.4 -1.2,-1.8 -0.2,-0.9 -1,-1.7 -0.2,-2.5s2,0.4 2.3,1.6z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M263.5,269.2a0.9,1.4 0,1 0,1.8 0a0.9,1.4 0,1 0,-1.8 0z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m264.4,272.4 l0.1,4.6 -1.2,-1v-3.8z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M266.2,271c0.3,1 -1.3,1.6 -2.4,1.4s-1.9,-0.7 -1.7,-1.7 1.5,-0.8 2,-0.5 1.8,-0.8 2.1,0.7z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M254.6,276.5a1.6,0.7 0,1 0,3.2 0a1.6,0.7 0,1 0,-3.2 0z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m259.1,276.5 l3.6,-0.3 1.6,1.2 -5.3,-0.2z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M257.8,274.5c1,0 1.6,1.1 1.5,2.3q-0.4,1.8 -2,2c-0.9,-0.2 -0.8,-1 -0.8,-1.2 0,-0.3 0.5,-0.7 0.6,-1s-0.3,-1.3 -0.1,-1.6q0,-0.5 0.8,-0.5zM254.8,246.2c-0.4,0.6 -1.2,1.1 -1.6,0.9s-0.2,-1.2 0.2,-1.8q0.7,-1 1.4,-0.8 0.6,0.6 0,1.7z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m250.7,253.5 l2,-4.8 -0.2,-0.3 -2.4,3.4z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M252.4,248.7q-1.4,-1 -1,-2.3c0.4,-0.7 1.2,-0.2 1.2,-0.2l0.8,0.7c0.4,0.2 1,0 1.4,0.6 0.5,0.6 0.2,1.2 -0.1,1.4s-1.5,0.4 -2.3,-0.2zM243.9,248.1q1.1,0.7 0.9,1.5 -0.5,0.7 -1.8,0 -1,-0.6 -0.8,-1.5 0.5,-0.7 1.7,0z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m246,250.2 l3.8,2.2 -0.1,1.8 -4.1,-3.5z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M246,250.6q-1.2,1.3 -2.3,0.7c-0.7,-0.5 -0.1,-1.2 -0.1,-1.2l0.8,-0.7c0.2,-0.4 0,-1 0.7,-1.4s1.2,0 1.3,0.3c0.1,0.4 0.3,1.5 -0.4,2.3z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m356.6,289.8 l-0.4,0.4 -0.3,1v-1.5l0.5,-0.3zM356.8,288.9s-0.3,0.5 -1.1,0.5 -1.2,-0.4 -1.3,-0.8q-0.2,-0.5 0.2,-1.2c0.2,-0.5 -0.4,0.4 -0.4,0.4v1l0.6,0.6 0.5,0.2h0.4l0.7,-0.2 0.4,-0.3zM361,296.2s-0.4,0.2 -0.7,0.1c-1.9,-0.6 -3.5,-1.8 -3.5,-1.8l3.8,2.4zM363.4,297.3s0,0.7 -0.8,1q-1,0.4 -1.5,-0.1c-0.5,-0.5 -0.4,-0.6 -0.4,-1.2l-0.1,0.6 0.4,0.8 0.8,0.4 0.5,-0.1 0.5,-0.2 0.4,-0.5 0.3,-0.5zM387.7,249.7 L387.6,250.1v0.7l-0.9,-1 0.4,-0.5zM387.2,248.5c0,0.7 -0.9,1.6 -1.6,1.3q-1.2,-0.5 -0.9,-1.8l-0.2,0.3 0.1,1.1 0.7,0.6h0.9l0.6,-0.3 0.4,-0.7zM397.2,250.3s-0.1,-0.4 0,0 -0.6,1.7 -1.4,1.6q-1,-0.3 -1,-1.4c0,-0.6 -0.1,0.2 -0.1,0.2v0.8l0.5,0.4 0.6,0.4 0.6,-0.2 0.7,-0.3 0.2,-0.6v-0.9z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M393.8,251s0,0.4 -0.4,0.7l-1.3,0.7 2.8,-0.8 -0.2,-0.7zM376.7,273.5 L376.6,274v0.8l-1.6,-2.5 0.5,-0.3zM375.8,271.1c-0.2,0.7 -1.4,1 -2,0.8 -0.5,-0.4 -1,-1.6 -0.3,-2l-0.4,0.2 -0.1,0.7 0.2,0.7 0.6,0.7 0.5,0.1 0.8,-0.2 0.6,-0.4s0.4,-1.3 0.1,-0.6m5.6,5.7s0,0.3 -0.5,0.3h-1.6l2.6,0.6 0.2,-0.6z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M384.6,277.5c0.3,0.8 -0.5,1.7 -1.5,1.4s-1.3,-1.3 -1,-1.8l-0.2,0.6 0.2,0.8 0.6,0.6h1.3l0.6,-0.7 0.2,-0.7 -0.1,-0.2zM285.6,288.6c0.3,0.3 -0.1,1.5 -1.2,2q-1.7,0.4 -2,-0.4 -0.4,-0.7 -0.3,-0.8l-0.2,0.2 0.2,0.6 0.5,0.5 1,0.2 0.8,-0.1 0.7,-0.5 0.4,-0.3 0.3,-0.5v-0.6z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="m284.6,291.1 l-0.3,0.4 -0.3,0.8v-1.4l0.5,-0.3zM280.8,295.1s0,0.3 0.2,0.5l1,0.3h-1.8l-0.2,-0.7h0.8zM280.2,296.3v0.2l-0.1,0.5q-0.1,0.9 -1,0.9a1,1 0,0 1,-1 -1.1q-0.1,-0.6 -0.2,-0.6v0.6l0.4,1 0.7,0.3h0.5l0.5,-0.2 0.2,-0.6 0.1,-0.5zM275.5,296.2s0.2,0.8 1.2,0.8 1.2,-0.2 1.2,-0.2v0.3l-0.2,0.2L276,297.3l-0.3,-0.3 -0.3,-0.5v-0.3zM266.1,270.8v0.4c-0.1,0.3 -0.7,1 -1.8,1q-1.4,0 -1.8,-0.7t-0.4,-0.8v0.5l0.4,0.7 1.1,0.5h1l1,-0.3 0.5,-0.5 0.1,-0.6zM264.4,273.1s-0.4,0 -0.6,0.2l-0.4,0.4v-1.3h1zM259.7,276.5 L260.2,276.8c0.5,0.3 3.1,0.5 3.1,0.5h-4.1l0.1,-0.8zM259.2,276.5 L259,277.1c-0.2,0.5 -0.7,1.6 -1.6,1.4 -0.9,-0.1 -0.7,-0.8 -0.7,-0.8v-0.4l-0.2,0.7 0.2,0.4 0.4,0.2 0.7,0.1 0.4,-0.2 0.6,-0.5 0.2,-0.5 0.2,-0.6zM257,276.7s0,0.2 -0.7,0.2 -1.7,-0.3 -1.7,-0.3l0.4,0.4 0.5,0.2h1.3zM255,247.7s0,0.5 -0.3,0.8c-0.4,0.3 -1.3,0.5 -1.8,0.2s-1.5,-1.3 -1.5,-1.3l0.4,0.8 0.7,0.6 1,0.3 1,-0.2 0.4,-0.4 0.2,-0.3z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M252,249.1v0.6l-0.2,1.1 0.9,-2 -0.4,-0.2zM246.6,250.6 L246.8,251.1c0.2,0.5 2.3,2.6 2.3,2.6l-3.3,-2.9 0.4,-0.5zM245.6,250.9s-0.5,0.3 -1,0.3c-0.3,0 -1,0 -1,-0.4q0,-0.7 0.1,-0.7l-0.3,0.6 0.1,0.4 0.5,0.3h0.7l0.6,-0.3 0.2,-0.2zM244.1,249.5h-0.5c-0.7,0 -1.4,-0.8 -1.4,-0.8l0.5,0.8 1,0.5z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M399.9,240.2c-0.3,3.8 -4.1,5.8 -6.1,7.2s-3,3.2 -3,3.2l-0.6,2.2 -0.3,1.5 -0.1,0.5q0.2,0.6 0.3,2l-0.1,4.3 2.7,-1.9 1.9,-0.7 0.6,-0.1s-2.2,2.1 -3,4.4 -2.4,7.2 -5.3,8.8c-3,1.6 -4.8,1 -5.7,1.8s-1,1 -1,1l-1.3,1.8 -1,1.2 -0.9,0.7 -0.6,0.4 -0.2,1.7 -0.7,2.4s0.6,-0.4 1.3,-0.5h1.3s-0.5,0.6 -0.7,1.3c-0.2,0.6 0.1,4.6 -3.6,7 -3.8,2.3 -13.4,2 -13.4,2l-1.8,0.7 -1.7,1.3 -1.6,1.7v0.4s-1.3,1.5 -2.1,2l-2.8,1.9 2.2,0.1 3.5,1.5s-2.2,0 -3.6,0.5c-1.5,0.6 -8.2,4.4 -11.4,4.3s-8,-4.9 -8,-4.9l-2,-1.4 -3.5,-0.7 -4.3,-0.2v-0.6l0.1,-0.7s1.5,-0.2 3.8,0.1c2,0.2 2.7,1 4.4,1.1q2.6,0.2 3.5,-0.3c0.7,-0.3 5.9,-4.7 5.9,-4.7l5.8,-2 2.3,0.3 1,0.2 0.9,0.3 -1,1 -1.4,1.1 0.7,0.6 3.7,-0.6 1.2,0.3 0.3,0.2q0,-0.5 0.6,-1.3c0.4,-0.6 2.5,-2.3 3.4,-2.9l1.4,-1c0.3,-0.3 1.5,-3.3 1.5,-3.3l0.1,-1.6 4.1,-4 2.8,-2.9 1.2,-3.3 -0.2,-0.7s1,1 0.9,3c-0.2,2.2 -0.7,2.9 -0.7,2.9s2.8,-2 4.3,-2.6q1.8,-0.6 2.4,-0.5c0.4,-0.2 1.5,-0.9 2.1,-1.8 0.8,-1.2 1,-1.5 1,-2l0.2,-2.8 -0.3,-7 2.4,-5.4 3.5,-3.1 0.7,-0.4 -0.4,1.2v1.5s1.8,-2.5 2.7,-2.9l0.5,-0.3 0.8,-2 0.3,-3v-3l-0.5,-2.3 -1.7,-4.3v-5.8l-1.2,-1.6s1.1,0 2.5,2a14,14 0,0 1,1.9 5l3.3,-10.3s0.9,1.2 1.5,3.3l0.8,3.3 1.4,-2.8 0.1,1c0.2,1 2.3,2.1 2,6m-67.3,65s-0.7,-1.6 -3.9,-3.2c-3.1,-1.5 -5.5,-1.8 -5.5,-1.8v1.1l5,2.2 2.4,2z"
      android:fillColor="#9ca168"/>
  <path
      android:pathData="M355.4,295.6c0.3,-0.4 3,-2.8 4.4,-3.5s3.2,-0.8 3.2,-0.8c1.9,-0.4 2.2,-0.1 6.6,-2.5s5.3,-5.2 6.2,-5.9c0.8,-0.7 2.3,-0.8 2.3,-0.8l-3.4,3.9a29,29 0,0 1,-5.8 4.5,18 18,0 0,1 -7.6,1.8 8,8 0,0 0,-4 2c-1.3,1 -2,2.2 -2,2.2s-0.2,-0.5 0,-0.9zM367.4,282.9c-4,3.7 -5.3,7.2 -5.3,7.2l5.4,-4.9c1.9,-1.8 4.5,-2.6 5.6,-3.7 1.2,-1.1 1.3,-2 2,-2.7l1.3,-1s-1.8,-1.4 -9,5.1m-8.5,8.6s1.7,-0.8 2.4,-2.3c0.5,-1.2 0.2,-1.5 1,-2.8 0,0 4.2,-4 5.7,-5.8 2.6,-3.1 1.4,-5.7 1.4,-5.7s0.3,1.3 -0.8,2.7c-1,1.4 -6.5,5 -7.2,7.2 -0.6,2.2 -0.3,2.3 -0.6,3.4 -0.6,2.6 -1.9,3.3 -1.9,3.3m-2.8,10.5s-2.1,-1.4 -4.5,-1.3c-4.9,0.4 -9,3.4 -12.2,3.4 -3.1,0.1 -4.4,-1.5 -6.9,-3.2 -2.6,-1.8 -9.2,-1.5 -9.2,-1.5v0.3s4.4,0 6.4,0.6c3.6,1 5.6,4.8 9.7,4.6 5.5,-0.3 9.9,-3.6 12,-3.6 3.8,0 4.7,0.7 4.7,0.7m-24,-2.4s3.7,0.6 6.6,-1.5c3,-2.1 6.4,-5 8.3,-5.2 2,0 4,0.4 4,0.4s-2.5,-1 -4.4,-1q-3,-0.2 -5.8,1.3c-1.8,1.1 -2.7,3 -4.6,4.3a13,13 0,0 1,-4.1 1.7m-0.7,5q-0.9,0.1 -1.3,0.3c-0.2,0 -1.3,-1.2 -3,-2 -1.6,-1 -4,-1.4 -4,-1.4s-0.3,0 0.3,0.2l3.8,1.5a9,9 0,0 1,2.6 2.1c0.1,0.3 0.6,0.9 1.5,0.8q1.4,-0.1 1.2,-1 -0.2,-0.5 -1.1,-0.5m2.6,-3.6s1.5,0.8 4,0.8c5.2,-0.2 8.6,-3.8 12,-5 3.4,-1.4 5.2,-0.3 5.2,-0.3s0.1,-0.2 0,-0.2a7,7 0,0 0,-3.9 -1.2c-5.7,0 -11.2,3.7 -13.5,4.7q-3.6,1.3 -3.8,1.2m56,-39.9c-1.8,1.9 -4.5,7.7 -5.6,9.3 -1.2,1.6 -2.7,2 -3.3,2.7 -0.6,0.6 -2.7,3.5 -3.2,4.1 -0.5,0.7 -0.6,0.5 -1,0.8q-0.6,0.4 0.1,0.2c0.6,-0.1 0.8,-0.5 1.6,-1.5 1,-1 0.9,-1.4 2.3,-2.7 1.4,-1.2 3.9,-2.6 5.1,-4.2 1.3,-1.5 3.8,-7.5 5.2,-9s4,-2.4 4,-2.4 -2,-0.6 -5.3,2.7zM381,272.2s-0.1,-1 1.2,-2.6 1.7,-1.3 2.8,-3.6q1.3,-3.4 2.3,-6.9c0.9,-2.4 2.3,-4.8 2.3,-4.8s-1.3,0.2 -2.7,2a35,35 0,0 0,-4.8 8.7c-1.1,3.4 -1.1,7.2 -1.1,7.2m-1.7,2.2s0.5,-0.2 0.5,-3.2c0.1,-2.9 -0.1,-7 0.7,-9 0.9,-2 5.4,-7 5.4,-7s-1.9,0.5 -4.5,2.5 -3.7,4.7 -3.6,6.7 1.3,4.2 1.4,6.1l0.1,4zM389.8,254.3s1.1,-4 1.6,-5 0.5,-1.5 2.7,-4c1.3,-1.5 2.6,-2.4 3.1,-4.4s0.6,-7.7 0.6,-7.7 -0.6,0.5 -1.1,1.5 -0.1,4.7 -1,6.4 -2.7,5.6 -3.7,6.5c0,0 -0.2,-2.9 0.2,-5.7 0.3,-3 1.3,-3.8 1.7,-5.7s0.2,-6.7 0.2,-6.7 -1.5,1.5 -2.3,3.4 -1.2,5 -1.2,7.5 0.5,4.5 0.6,5.9 0.2,2 -0.4,3.6l-0.6,2 -0.7,2.6zM386.3,232.7s1.4,1.7 1.2,3.6c-0.3,2 -1,4.5 -0.2,6.5 0.7,2 1.9,2.2 2.2,3.4s0.3,3.4 0.3,3.4 0.6,-4.3 0.2,-5.5c-0.5,-1.1 -0.7,-0.6 -1.3,-1.8s0,-4.7 -0.4,-6.7 -2,-2.9 -2,-2.9"
      android:fillColor="#717732"/>
  <path
      android:pathData="M306.7,304.8s0.4,-1 3.1,-2.4a34,34 0,0 1,7.3 -3c0.3,0.3 -0.3,1.8 -0.3,1.8l-2.6,0.8 -2.1,1.2 -2.5,1.6z"
      android:fillColor="#9ca168"/>
  <path
      android:pathData="M313.6,297.7c-3.4,0.1 -5.3,1 -6,1 -0.2,0.1 -0.7,0.5 -1.2,0.3a5,5 0,0 1,-1.5 -1.5l-0.7,-0.7 -0.2,2.5 -5,-4.4 -0.6,3 -0.7,1.3 -5,-4.8 0.2,3.7 -1.4,0.1 -3.6,-2.5 -1,0.4 1.2,2.2 -4.6,0.3 -1,0.8 -1,1v0.4h1.9c0.4,-0.2 0.6,-0.7 0.7,-0.5 0.2,0.2 0.3,1.2 0.9,1.1s2.7,-1.7 4.2,-1c1.7,0.8 -2.2,2 -1.8,3.1s3.2,0.7 4,0.3c0.6,-0.3 2.7,-3 3.6,-2 1.2,1.4 -2.5,2.5 -1.8,3.9s2.7,1 3.7,0.4 3.4,-4.1 3.9,-3.4c0.9,1.4 -2,2.7 -1.3,3.7 0.8,1 2.3,0 3.4,-0.8s1.5,-2.5 3,-3.3c1.4,-0.8 1.3,-0.5 1.7,-0.5s1.5,-1.1 1.5,-1.1l3.5,-1.5 1.6,0.2 0.9,0.4 1.7,0.2 0.2,-1.4s-2,-1 -3.3,-1zM286.2,297.7 L286.9,296.4 286.5,295.8s-1.2,-0.7 -2.3,-1.8a7,7 0,0 0,-2 -1.6L280,291l-0.8,-2.8 -0.5,-0.9 -1.3,0.6 -1.7,-6.1 -0.3,-1.1h-0.7l-1.5,3.6 -1.5,-2.3 -0.4,-5 -1,1 -1.3,1.4 -2.3,-4.6s-0.1,-0.1 -0.3,0.3c0,0.3 -0.3,1.8 -0.1,2.6a26,26 0,0 1,-6 -5.2l-1,-2.5 1,-2 0.5,-2.6 -2.2,0.9 -0.4,-5.7 -0.3,-1.8 -2.7,3.8 -1,-1.7v-3.6l-0.7,-0.2 -1,2s-1.1,-2 -1.6,-2.5c0.1,-0.8 0.2,-2.9 -0.3,-4.5 -0.6,-2 -1.3,-3.1 -1,-4.8 0.2,-1.6 0.9,-1.8 0.8,-2.5 0,-0.8 -1.2,0.5 -0.8,-0.5s3.8,-3.2 3.1,-4.4c-0.7,-1.1 -4,1.9 -3.3,-0.1s4,-2 4.2,-4.9c0.1,-1.7 -3,1.1 -3.3,0.2 -0.3,-0.8 2,-2.6 1.9,-3.8 0,-1 0.4,-1.2 -0.2,-1.8s-2.6,2.3 -2.6,2.3l-2,-0.7 -0.8,3 -0.6,2.5 -2.6,-1.5 0.7,3.1 0.6,3 -2.7,-0.7 1.4,2.6 1.9,2 1.1,1.5 0.9,0.4 1,1.1 0.5,1.7 0.6,1.9 0.1,1.8v2.4l-0.1,0.4v1.1c-0.5,0 -1.4,-0.8 -1.7,-0.3s1.6,2 1.2,2.4c-0.3,0.4 -3,-0.5 -3.2,0.3 -0.2,1 0.2,2.2 1.8,2.6s5.3,1.2 4.7,2c-0.6,0.7 -4.7,-2.3 -4.5,-0.1a4,4 0,0 0,2.6 3.4c1,0.4 5.1,0 4.9,1 -0.3,0.9 -3.3,-0.4 -3.6,1s2.1,1.6 2.6,1.6 2.2,-0.1 2.9,0.4l4.3,4.1 4.6,3.4c-0.8,0 -2.4,-0.3 -2.6,0.3s6,3 4.2,3.8 -3.8,-2 -4.3,-0.4 1.2,3 2.2,3.6 6.7,0.1 5.6,1.4 -5.3,-0.8 -5.3,0.7 2.7,4 4.2,3.9c1.4,0 3,-2.4 3.6,-1.1s-1,1.7 0.3,2 2.3,-1.4 4,-1c1.5,0.3 4,1.3 5.4,2.5z"
      android:fillColor="#9ca168"/>
  <path
      android:pathData="M308.4,304.1c1,0 1,0.6 1.2,0.6s1.8,-1.5 3.3,-2.3a18,18 0,0 1,4 -1.4l0.1,0.2s-2.8,0.7 -4.2,1.7l-3,2.1q-0.3,0.5 -1.7,0.7c-1,0 -1.4,-0.6 -1.4,-0.9 0,-0.2 0.7,-0.7 1.7,-0.7m8.5,-4.3s-0.9,0.1 -1.3,-0.3a4,4 0,0 0,-2.6 -0.8,7 7,0 0,0 -4,1.8q0.1,0.2 -1.4,1.3 0.6,0 2,-1a7,7 0,0 1,3.6 -1.4c1,-0.2 1.7,0.4 2.2,0.7 0.5,0.4 1.4,0.3 1.4,0.3zM266.4,279.2v-0.7l-2.2,-1.1c-1.6,-1 -5,-3.8 -5,-3.8l2.8,2.7c1.4,1.3 4,2.9 4,2.9zM302,304.4 L303.5,302.3q1.5,-2 1.9,-2.2 0,-0.5 -0.7,-1.4l-0.2,-1.2s0.4,0.8 1,1.3l1.1,1s1.4,-0.2 1.4,-0.5q0,-0.3 -0.2,-0.5c-0.3,-0.1 -0.5,0.2 -1.1,0 -1.3,-0.6 -1.8,-2.6 -2.6,-2.6 -0.8,-0.1 -0.2,2 -0.6,2 -1.1,0.2 -2,-4.3 -5,-4.5 -2,0 -2.3,0.3 -2.4,0.7 0,0.5 2.5,3.1 1.6,3.5s-3.5,-4.2 -5.5,-4.2c-1.9,0 -2,0.7 -2,1.1 0.2,0.4 2.2,0.8 1.8,2.2s-2.5,-2 -4.3,-1.9 -2,0.3 -2,0.9c-0.1,0.5 0.6,1.2 0.3,1.4s-1.4,0.1 -2.3,0.7 -2.1,2.4 -2.1,2.4 1.2,-1.7 2.7,-1.9h5.6l-1.1,-0.8c-0.6,-0.5 -1.1,-1.6 -1.1,-1.6l1.7,1.5c0.8,0.7 1.9,1.2 1.9,1.2s1.7,0.2 1.8,0.4q0.3,0.3 -0.8,1.3l-1.7,1.7 2,-1.6 1.5,-1.2 1.4,0.2q0.3,0 -0.8,-1.7l-1.6,-2.5s1,0.9 2,2.3c1.1,1.3 1,2 1.4,2q0.7,0.1 1.4,0.1t-0.4,1.7q-1.4,2.5 -1.2,2.5c0.2,0 0.8,-1.2 1.6,-2 0.7,-0.8 1.1,-1.7 1.5,-1.8h1.5l-0.8,-1.6c-0.6,-1 -0.8,-2.6 -0.8,-2.6s0.6,1.4 1.4,2.5l1.3,1.9 1.4,-0.1 0.3,0.1c0,0.5 -0.2,0.7 -0.7,1.8zM280.5,292q1.7,0.6 1.7,0.5c0,-0.1 -1.7,-1 -2.1,-2.4s0,-4.4 -0.8,-4.3c-0.7,0 -1.1,2.1 -1.8,1.7 -0.8,-0.4 0.2,-4.4 -0.6,-6 -0.8,-1.7 -2.6,-2.9 -3,-2.3s-0.3,4.2 -1.5,3.5 0.1,-4 -0.2,-5.3c-0.4,-1.5 -1.2,-2.5 -1.9,-2.1s0.3,3 -0.7,3 -0.9,-1.9 -1.3,-2 -0.6,0.3 -0.9,0c-0.2,-0.4 0,-1.7 -0.6,-1.6s-0.5,1.2 -0.3,1.7q0.3,1 0.7,1.8c0.2,0.4 1,0.9 1,1.2q-0.2,0.5 -1,0.6h-1.9s1.4,0.4 1.9,0.4 1.4,-0.2 1.7,0.2c0.2,0.5 1.4,2 1.4,2s0.3,-0.8 0.4,-2.2 0,-2.7 0,-2.7 0.5,1.6 0.4,2.7c0,1 -0.4,3 -0.4,3s1.1,0.8 0.8,1c-0.3,0.3 -1.5,0.3 -2.8,0.3 -1.4,0 -3.3,-0.5 -3.3,-0.5a14,14 0,0 0,6.7 1.3l1.8,2s0.9,-1.5 1,-3v-3l0.4,2.8c0,1.3 -0.4,3.6 -0.6,3.8a5,5 0,0 1,-1.9 1l-3.5,0.5s2.2,0.3 3.8,0c1.5,-0.4 1.7,-0.9 2.2,-0.6l0.8,0.7 1.3,1c0.1,0.1 -0.7,0.4 -1,0.7l-1.5,0.7 2.2,-0.7 1.1,-0.4 0.5,0.2 -0.1,-1.2 -0.3,-1.8s0.5,0.8 0.7,1.6l0.1,1.6s0.2,0.2 1.4,0.6m-31.1,-35.6s0.3,-1.7 0.3,-3.6a14,14 0,0 0,-1.9 -5.8l1,-1.4 -1.2,1 -1.2,-0.5 -0.9,-1 1.2,0.8q0.8,0.2 0.7,0.1l-0.5,-2 -1.9,-1c-1.3,-0.6 -2.5,-1.8 -2.5,-1.8l2.8,1.5q1.5,0.5 1.6,0.4l1.6,-0.6 1.7,-1.1s-1,0.6 -1.8,0.7l-1.6,0.3 -0.3,-2.4 -1.1,-1.1c-0.9,-1.2 -1.6,-2.9 -1.6,-2.9s1,1.2 1.7,1.7 1.4,1.5 1.4,1.5l1.6,-1 2.8,-2 -2.7,1.3 -1.6,0.7s-0.2,-1 0,-1.5 0.8,-1.2 0.7,-1.6c0,-0.5 -0.4,-0.3 -0.5,-0.7 -0.2,-0.3 0.4,-2.6 0.4,-2.6l0.1,1.2c0,0.5 -0.2,1 0.4,1s3.2,-2.7 3.6,-3.2 0.8,-1.8 -0.6,-1.3 -1,1.8 -2.2,1.7c-0.4,0 -0.8,-1.5 -1.2,-1.2s-1.4,1.3 -1.6,2.4 0.2,2.6 -0.4,3.1 -1,-1.8 -2.2,-1.5 -1.5,2 -1.3,2.6 2.7,3.5 2,4c-0.9,0.3 -2.9,-2.6 -4,-0.6 -1,1.9 3.4,4 4,4.4 0.5,0.5 0,0.6 0.8,1.6s1.8,1.2 2.4,1.8a13,13 0,0 1,2.1 5.9zM261.1,273.5s-1,-1.2 -0.7,-3.3 1.8,-5.7 1.3,-6.2 -2,2.2 -2.4,1.6 0.9,-4.3 0.2,-5.8 -0.7,-2.5 -2,-2.3c-1.3,0.1 -1.8,4.4 -2.5,3.6 -0.6,-0.7 0.4,-2.6 0,-3.7q-0.6,-1.4 -1.3,-1.2c-0.7,0.2 -1,2.2 -1.4,2.1 -0.5,0 -1.2,-2.3 -2.2,-2 -0.9,0.3 2.3,4.5 2.3,4.5s0.5,-0.5 0.8,-1.2l0.6,-1.5s0.3,1.3 0,1.9 -0.3,1.7 -0.3,1.7 -0.8,0.3 -2.1,0l-2.3,-0.7s0.8,0.6 2.1,1l2.7,0.7 2,2.7 0.8,-2.1 1.3,-3.5 -0.6,3.9 -0.7,3s-1,0 -2.6,-0.3l-3.6,-0.8 3.6,1.2 2.8,0.7 1.4,1.9 1,-1.5 1,-1.8s-0.3,1.4 -0.7,2.3l-0.7,1.4 -1.8,0.2h-2.5a20,20 0,0 0,4.5 0.9s0,0.5 0.7,1.3z"
      android:fillColor="#717732"/>
  <path
      android:pathData="M314.4,310.8s-0.6,-0.6 -1.4,-1q-1.3,-0.6 -1.2,-0.4l-0.4,-0.1 0.8,-2 5.5,-5.3 0.9,-5.3h3.4v7.2l1,0.5 6.2,3.7v1.6l-1,0.4 -0.7,0.4 -2.3,0.2 -4,-3 -1.8,-1.7 -3,4z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m318.8,296.7 l-0.1,1c0,0.9 0,5.2 -0.2,5.8s-3.2,1.8 -4.9,3.3 -1.8,2.6 -1.8,2.6 -0.5,0 -1.6,0.4c-1,0.4 -1.5,1 -1.5,1s0.6,-2.6 3.4,-5c2.7,-2.6 4,-2.8 4.4,-3.2 0.3,-0.4 0,-5 0,-5.5q0.3,-0.5 0.7,-0.4zM325.8,312.4 L326.6,311.3 327.6,310.5 326.1,309.7c-1.5,-0.6 -2.3,-0.4 -3.3,-1.2l-2.2,-2 -1.5,1.2 1.7,2c0.9,0.8 2.7,1.1 3.5,1.6l1.6,1.1z"
      android:fillColor="#016848"/>
  <path
      android:pathData="M321.5,296.6s1.7,0 1.9,0.2l0.2,0.6c0,0.1 0,4 -0.2,5.3a8,8 0,0 1,-1.4 3l-4,3.7c-1.2,1.2 -2.4,2.8 -2.4,2.8l-0.8,-1q-0.6,-0.5 -0.6,-0.9a10,10 0,0 1,3.3 -3.4c2,-1.3 3.6,-2.8 4,-4.9 0.3,-2 0,-5.4 0,-5.4"
      android:fillColor="#cd202a"/>
  <path
      android:pathData="M332.5,310s-0.4,-0.4 -1.8,-0.4l-1.5,0.1s-0.8,-1 -2,-1.7c-1.2,-0.8 -2.1,-0.7 -3.5,-1.6 -1.3,-0.8 -2,-2.1 -2,-2.1l1,-2s1.2,1.5 2.4,2.4 3.7,1.7 4.6,2.4z"
      android:fillColor="#cd202a"/>
  <path
      android:pathData="M345,286.4s-3.6,-1.4 -3.4,-2.7c0.3,-1.2 8.3,-3.3 8.3,-3.3l0.1,-2.1s-1.2,-0.1 -3.2,0.5 -5.2,1.6 -8.6,1.6l-34.4,-2.2c-3.7,-0.3 -6,-6 -6,-6l-1.8,0.4s0.5,2.7 -0.4,3a65,65 0,0 1,-13.9 -6l-0.6,2.6s7.3,4 7.1,5.5c-0.2,1.4 -2.4,1.3 -2.4,1.3l1,1.8c0.3,0 12,0.6 12.3,4.1 0,1.5 -3,2.3 -3,2.3l1,1.2v0.6s6.7,0 8.5,1 2.7,2.6 5,3.8c2.2,1.3 17.3,1 19.6,0.3 2.7,-0.7 4.3,-3.7 8.7,-5 4.3,-1.4 5.7,-1.4 5.7,-1.4z"
      android:strokeWidth=".5"
      android:fillColor="#30c2dc"
      android:strokeColor="#0872a7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M284.5,280.7m-2.1,0a2.1,2.1 0,1 1,4.2 0a2.1,2.1 0,1 1,-4.2 0"
      android:fillColor="#fff"
      android:strokeColor="#0872a7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M296.4,270.9m-2.1,0a2.1,2.1 0,1 1,4.2 0a2.1,2.1 0,1 1,-4.2 0"
      android:fillColor="#fff"
      android:strokeColor="#0872a7"/>
  <path
      android:strokeWidth="1"
      android:pathData="M346.5,286.6m-2.1,0a2.1,2.1 0,1 1,4.2 0a2.1,2.1 0,1 1,-4.2 0"
      android:fillColor="#fff"
      android:strokeColor="#0872a7"/>
  <path
      android:pathData="M275.1,267.8c-0.7,1.6 2,4.6 4,5q2.6,0.3 3.2,-1.1a3,3 0,0 0,-0.4 -2.5c-1.2,-1.7 -6,-3 -6.8,-1.4"
      android:fillColor="#f8c83c"/>
  <path
      android:pathData="M281,270.2c0,-1 -1.6,-1.9 -2.6,-1.9s-2,0 -1.8,0.5 2.7,1.3 2.8,1.6 -0.8,0.6 -0.4,1q0.8,0.4 1.4,-0.2 0.6,-0.3 0.7,-1z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M297.7,288.3c0.4,1.4 -0.7,2 -2,2.7 -1.5,0.6 -4.3,0.2 -5,-0.9 -0.6,-1.1 1,-3 2.8,-3.4s3.8,0.3 4.2,1.6"
      android:fillColor="#f8c83c"/>
  <path
      android:pathData="M294.8,289c0.5,0 0.6,1 1,0.8 0.5,0 1,-0.8 0.8,-1.3s-1,-1.3 -1.8,-1.2c-0.8,0 -3,2 -2.8,2.5q0.4,0.7 1.2,0.3c0.4,-0.2 1,-1 1.6,-1z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M349.2,281c1.3,1.7 3.6,0.2 4.2,-0.5s2.4,-1.7 1.7,-2.8c-0.8,-1.1 -2,-1 -3,-1 -0.8,0 -2.6,1.4 -2.9,2 -0.3,0.5 -0.6,1.6 0,2.4z"
      android:fillColor="#f8c83c"/>
  <path
      android:pathData="M349.6,280s0.1,-1.9 1.8,-2c1,0 1,0.3 1.8,0.7 0.7,0.4 1,-0.5 1,-0.5s0,1.3 -1.1,1.3 -0.8,-0.5 -2,-0.7c-1,-0.2 -1.5,1.1 -1.5,1.1z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m321.9,276.4 l-0.9,-0.6h-2.8l-3.4,0.1 3.4,9.9 4,5.8 1.6,0.6 3.1,-0.2 0.6,-1.6 -1.2,-9.5z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#f9aa51"
      android:strokeColor="#953220"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m310.6,277 l0.2,-1.1 1.8,-0.1 2.2,0.1s2.3,2.9 3.1,4.7 1.7,4.8 2.7,6.6c1,1.9 3.2,5 3.2,5h-4l-2,-0.6 -5.6,-9z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#f9aa51"
      android:strokeColor="#953220"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M308.2,275.8h2.6a14,14 0,0 1,3.9 5.4c1,2.8 0.5,3.2 1.8,6.2 1.4,2.9 3.2,4.9 3.2,4.9s-3.9,0.2 -6,-0.2c-2.3,-0.4 -3,-0.3 -3.6,-1l-1.7,-1.3h-2.2l0.5,-3.1 -0.6,-6.9 0.1,-3.5zM331.6,277.6 L327.9,276.1 320.9,275.7 321.5,279.3a30,30 0,0 0,2.6 7.7c1.1,2 2.2,4.4 2.8,5s4.6,-1.1 4.6,-1.1l2.6,-0.4 -0.3,-2.8 -0.4,-1 0.6,-8 -0.6,-1.2z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#f9aa51"
      android:strokeColor="#953220"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M307.1,277.7c0,0.8 1,0.9 1.2,0.8s1,-0.3 1,-1.5 -0.7,-2.1 -2.2,-2.2c-1.4,0 -2.5,1.7 -2.5,3 0,1.2 1.2,1.8 1.2,2.3 0,0 -1.2,1.1 -1.1,3.1s1.6,3.6 1.6,3.6 -1.7,1.3 -1.7,2.7 1.3,2.3 2.6,2.3c1.2,0 2.8,-0.8 2.8,-1.8s-1,-1.7 -1.7,-1.7q-1.1,0.2 -1.1,0.8m25.6,-10.9c0,0.9 -0.6,1 -1,1s-1.2,-0.4 -1.2,-1.5c0,-1 1.4,-1.6 2.4,-1.6s2.4,1.2 2.4,2.7 -1,2.6 -1,2.6 0.6,0.3 0.6,2.2 -1.2,3.3 -1.2,3.3 1.6,0.6 1.6,2.6 -1.4,2.6 -2.3,2.6c-1,0 -2.6,-0.5 -2.6,-1.9s0.8,-1.8 1.5,-1.8q1.1,0.2 1.3,1.5"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#f9aa51"
      android:strokeColor="#953220"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m309.5,288.6 l0.4,0.6q0.2,0.7 0.8,1.4c0.8,0.7 7.2,1 9.2,1s7.8,0.2 8.9,-0.4c1,-0.7 1.2,-1.8 1.9,-2.3l0.9,-0.4 -0.9,0.7v1.4l0.5,0.8s-0.1,0.4 -0.8,0.8q-1,0.5 -2.3,0.6c-1,0.1 -13.2,0.1 -15.2,-0.2s-1.8,-0.3 -2.4,-0.7l-1,-1 0.3,-0.8 -0.3,-1.4zM329.5,276.8 L330.5,277.3s-0.9,0.3 -1.6,2.3 -0.1,2.5 -0.6,2.7 -5.8,0 -5.8,0l-1,-2.1z"
      android:fillColor="#953220"/>
  <path
      android:pathData="M346.4,276s-0.9,-0.3 -1.3,-1q-0.8,-1.4 -0.5,-2c0.4,-0.1 1,0.5 1,1.1s0.8,2 0.8,2zM356.8,273.2s0.7,1.2 1,1.3l1.2,0.6s-1.5,0 -2,-0.4l-1,-1.5z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M360,274.2s-1,0.5 -1.6,0.2q-1.4,-0.5 -1,-1t0.8,0.2c0.4,0.4 1.8,0.6 1.8,0.6m5.3,-4.5s-0.8,0.6 -1.3,0.6 -1.5,-0.2 -1.5,-0.4 1.3,-0.4 1.6,-0.3zM357.3,259.2s-1.1,0.4 -1.5,1 -0.3,1.2 0.2,1.2 0.6,-0.7 0.6,-1zM352.1,262.1s-0.9,0.5 -1,1c-0.3,0.5 -0.5,1.6 0,1.6s0.7,-0.8 0.7,-1.2c0,-0.5 0.3,-1.4 0.3,-1.4"
      android:fillColor="#231f20"/>
  <path
      android:pathData="m342.8,268.4 l-2.9,3s6.6,3.7 11.7,3.5c5,-0.1 10.8,-4 11.2,-5 0.4,-0.7 0,-4.3 -0.6,-5.4s-4.2,-3.8 -5.5,-3.7 -3.7,1.9 -5.8,4c-2.2,2.1 -2,3.9 -5,3.7z"
      android:strokeWidth=".5"
      android:fillColor="#8cbebf"
      android:strokeColor="#04534e"/>
  <path
      android:pathData="M342,269.6s7,1 9.1,-0.8 4.3,-5.3 5.7,-6c1.4,-0.9 2,-0.9 2,-0.9l1.6,1.1 1.8,1.5 0.6,3.4v1.8l-2.2,1.8 -4,1.9 -4,1.2 -3.2,-0.1 -6.7,-2 -1.7,-1.1 -0.4,-0.4z"
      android:fillColor="#0c8489"/>
  <path
      android:pathData="M352.8,265.5q0,-0.7 1,-0.6c1,0.1 1,0.2 1,0.8s-0.5,1.4 -1,1.4 -1,-1 -1,-1.6m2.8,1.8q0,-0.9 1,-0.8c0.7,0 1.3,0.7 1.3,1.2s-0.5,1 -1.1,1 -1.2,-0.9 -1.2,-1.4m-1,4.2q0.1,-1 1.1,-0.8 1,0 1,1a1.2,1.2 0,0 1,-1.1 1.2q-1,-0.2 -1,-1.4m-3.7,-1.3c0,-0.7 0.7,-1 1.3,-1q1,0.2 1,1.4c0,1.2 -0.4,1.3 -1,1.3s-1.3,-1 -1.3,-1.7m-5.9,0.7a1.4,1.4 0,1 1,3 0,1.4 1.4,0 0,1 -3,0"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M355.6,267.3c0,-0.5 0.7,-0.8 1,-0.8q0.8,0.1 0.8,0.8 0,1 -0.7,1 -0.8,-0.1 -1,-1zM354.6,271.3q0.2,-0.8 1.2,-0.8c1,0 1,0.3 1,0.8s-0.5,1 -1,1q-1,0 -1.1,-1zM350.9,270.1c0,-0.6 1,-0.9 1.5,-0.9q0.4,0 0.3,1 0,1.1 -0.7,1.2c-0.7,0.1 -1,-0.7 -1,-1.3zM352.7,265.4q0.1,-0.6 0.8,-0.5c0.7,0.1 0.6,0.1 0.6,0.7s-0.3,1 -0.6,1q-0.7,-0.2 -0.8,-1.2m-7.7,5.1q0.1,-1.1 1.4,-1.3 1.8,-0.2 1.7,1c0,0.6 -1,1.8 -1.8,1.8s-1.3,-0.8 -1.3,-1.5"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M347.7,269.4s-1,0.1 -1.5,0.6 -0.5,1 -0.3,1.2 0.7,-0.3 0.8,-0.6c0,-0.2 1,-1.2 1,-1.2m4.7,-1s-1,0.6 -1,1.3q-0.1,1 0.2,1c0.3,0 0.5,-0.9 0.4,-1.2s0.4,-1.2 0.4,-1.2z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M353.2,269.3s-0.6,0 -1,0.4q-0.9,0.9 -0.6,1 0.6,0 0.8,-0.5c0,-0.2 0.8,-0.9 0.8,-0.9m1,-5.4 l-0.8,0.6c-0.2,0.2 -0.5,1 -0.2,1q0.4,0.3 0.8,-0.5 0.2,-1 0.2,-1.1m3.5,2.3s-0.9,0 -1.2,0.5q-0.6,0.7 0,0.8 0.4,0 0.6,-0.5c0.1,-0.4 0.6,-0.8 0.6,-0.8m-0.5,4.1s-0.2,0.7 -0.8,1q-0.7,0.6 -1,0.1 0,-0.6 0.4,-0.7z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M362.8,267.5s0.4,2 -1.3,3 -6.4,4.2 -10.9,3.8 -9.6,-3 -9.6,-3l-0.7,0.4 1.2,0.5 3.4,1.4 4,1.2 2.5,0.1 2.1,-0.2 4,-1.3 3.3,-1.7 1.8,-1.4 0.4,-0.6v-1.9z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M274.9,242.2s1,0.9 1,1.6q-0.3,1.2 -0.7,1c-0.3,-0.2 -0.3,-1 -0.3,-1.3zM282.6,249.3s-0.3,1.4 -0.9,1.5c-0.5,0.1 -0.9,-0.4 -0.8,-0.7q0.3,-0.1 0.8,-0.2zM265.8,250.3s0.4,0.8 1,1q1.1,-0.2 1,-0.6 -0.2,-0.2 -0.8,-0.2zM268.8,256.3s0.7,0.6 1.5,0.7a1,1 0,0 0,1.3 -0.7c0,-0.2 -1,-0.2 -1.3,0s-1.5,0 -1.5,0m5.3,4.4s1.4,-0.2 1.6,-0.6 0.6,-1 0.3,-1.3 -0.5,0.5 -0.7,0.9c-0.1,0.3 -1.2,1 -1.2,1"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M282.6,257.1s1,-6 -4,-10.4c-5,-4.3 -8.1,-3.2 -9.8,-1.6s-3.2,6.7 2,11.3c5.3,4.5 11.7,3 11.7,3z"
      android:strokeWidth=".5"
      android:fillColor="#8cbebf"
      android:strokeColor="#04534e"/>
  <path
      android:pathData="M267.6,250.7s-0.1,-3.4 1.5,-4.4c1.5,-1 6.8,-1.8 9.3,3.1s2.7,7.4 2,10h-2.7l-4.3,-1.8 -3.5,-2.6z"
      android:fillColor="#0c8489"/>
  <path
      android:pathData="M282,258s-0.9,1 -2.7,1c-1.7,-0.2 -10,-2.3 -12,-9.8v1.7l0.1,0.5 0.6,1.5 1.7,2.5 2.2,1.9 2.8,1.4 2,0.6 2.5,0.4h1.5z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M277.7,255.9q0,-0.9 0.8,-0.7c0.8,0.2 1,-0.2 1,0.4s-0.4,1.6 -1,1.6q-0.7,-0.2 -0.8,-1.3m0.3,-4q0,-0.7 1,-0.8 0.8,0 0.9,0.9 0,0.7 -1,0.8 -0.8,-0.1 -0.9,-0.8zM274.3,256.3q0,-0.6 1,-0.7c1,-0.1 1,0.3 1,0.7s-1,0.8 -1.5,0.8 -0.5,-0.5 -0.5,-0.8m-0.4,-4.6c0,-0.6 0.3,-0.4 0.8,-0.4s1.2,0.1 1.2,0.7 -0.7,1.4 -1.2,1.4 -0.8,-1.2 -0.8,-1.7m-3.4,0.7q0,-1 0.9,-1.1c0.9,-0.1 1.3,0.6 1.3,1.1q-0.1,0.9 -1.1,1 -1,-0.1 -1.1,-1m0.6,-4q0,-0.7 1,-0.5 1,0 1.1,0.7c0,0.5 -0.7,1.2 -1.4,1.2s-0.7,-1 -0.7,-1.5zM274.8,247.9q0.1,-0.5 1,-0.3 1,0 1.1,1c0.1,1 -0.7,1 -1.3,1s-0.8,-1.2 -0.8,-1.6z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M271,248.3q0.2,-0.6 0.8,-0.7 0.8,-0.1 0.9,0.7 -0.1,0.8 -0.8,0.9 -1,-0.1 -0.8,-1zM270.4,252.3q0,-0.9 0.7,-1 1,-0.2 1,0.5 -0.1,1 -1,1.1c-0.9,0.1 -0.7,-0.1 -0.7,-0.6m4.4,-4.3q0,-0.5 0.8,-0.4 1,0 0.8,0.5 -0.1,0.7 -0.8,0.7 -1,-0.1 -0.8,-0.8m-0.9,3.9q0.1,0.9 1,1 1.2,0 1,-1c0,-0.6 -0.6,-1 -1,-1s-1,0.4 -1,1m4.3,-0.6q0,0.8 1,0.9 0.8,0 0.9,-0.9c0.1,-0.9 -0.5,-0.9 -1,-0.9s-1,0.4 -1,1zM274.1,256.3q0.2,-0.8 0.9,-0.9 1,0 0.9,0.7c0,0.4 -0.8,1 -1.2,1q-0.7,0 -0.6,-0.9zM277.7,255.6q0,-0.7 1,-0.6 0.8,0 0.8,0.6 -0.1,1 -1,1c-0.9,0 -0.8,-0.5 -0.8,-1"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M278.7,249.4s0.5,0.7 0.6,1.3v1.2q-0.5,0 -0.7,-0.9c-0.1,-0.5 0,-1.6 0,-1.6zM275.5,246.1s0.4,0.5 0.4,1 0,1 -0.3,1 -0.3,-0.4 -0.3,-0.8zM271.8,246.6s0.4,0.5 0.4,1 0,1 -0.3,1 -0.3,-0.4 -0.3,-0.8z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="m270.5,247.3 l1,0.4q0.5,0.7 0.3,1 -0.3,0.2 -0.6,-0.4zM273.3,251.1s0.7,0 1.1,0.5q0.8,0.4 0.5,0.9 -0.4,0.1 -1,-0.5zM269.9,251.1 L270.9,251.4q0.5,0.5 0.2,0.8 -0.2,0.2 -0.6,-0.4zM278.2,253.6 L278.4,255q0.1,0.9 -0.2,1c-0.3,0.1 -0.3,-0.7 -0.3,-1 0,-0.5 0.3,-1.4 0.3,-1.4"
      android:fillColor="#231f20"/>
  <path
      android:pathData="m277.3,254.9 l0.8,0.5q0.4,0.4 0.1,0.6l-0.4,-0.5zM273.6,255.1 L274.5,255.4c0.2,0.2 0.3,1 0.2,1.1q-0.1,0.1 -0.6,-0.3z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M272.1,243.8s-3.2,0.7 -3.8,2.9c-0.6,2 -0.7,4.4 -0.7,4.4l-0.4,-2.7 0.6,-1.7 1,-1.6 1.4,-1 1.3,-0.4zM282.3,256.3s0.4,-0.8 -0.6,-3.4 -3,-6 -3,-6l1.6,1.8 1.2,2 0.6,1.8 0.5,1.7v1.6l-0.3,0.6z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M285.6,252.2s1.2,0.7 1.3,1.1q0.3,0.6 -0.1,0.7 -0.6,0 -1,-0.7zM280.8,265s0.6,-1 1.3,-1.3q1,-0.6 1.4,-0.1 0.1,0.4 -1,0.9zM288.9,268.1s0.8,-0.4 1.3,-1q0.7,-0.8 0.4,-1 -0.5,-0.4 -1.1,0.5c-0.3,0.4 -0.6,1.5 -0.6,1.5m-0.9,-0.7s0.3,-1 0.6,-1.4q0.4,-0.5 1,-0.3 0.2,0.4 -0.2,0.9z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M301.5,261.2s-5,-7.2 -12.8,-7.6a7.4,7.4 0,0 0,-8 7c0.2,1.6 2.6,5.2 10.9,5.8s9.6,-3.9 10,-4.1c0.3,-0.2 -0.1,-1 -0.1,-1z"
      android:strokeWidth=".5"
      android:fillColor="#8cbebf"
      android:strokeColor="#04534e"/>
  <path
      android:pathData="M295.6,256.3s2,1.5 3,2.8q1.2,2 0.5,2.3c-0.5,0.2 -5,-4 -8.8,-4.9 -3.7,-0.9 -6.9,0 -7.6,1.7s-0.4,4.5 1.2,5.6c1.8,1 9.7,2.3 9.7,2.3l3.7,-0.5 2.3,-1.3 0.5,-1.3 0.6,-1.6 0.2,-0.7 -1.9,-2z"
      android:fillColor="#0c8489"/>
  <path
      android:pathData="M289.2,257.3q-0.1,0.6 -1,0.7c-0.9,0.1 -0.9,-0.3 -0.9,-0.7q0,-0.8 1,-0.9c1,-0.1 1,0.4 1,0.9zM284.6,258.9q0.2,-0.6 1,-0.4 1,0 1,0.8c0,0.8 -0.8,1.1 -1.3,0.9q-0.8,-0.5 -0.7,-1.3m5,2.7q0,-1 1,-1 1.1,0 1.2,1c0.1,1 -0.8,1.2 -1.5,1.2s-0.7,-0.6 -0.7,-1.2"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M285.8,262.7q0.2,-0.7 1,-0.6t1,1c0.1,0.9 -0.5,0.9 -1,0.9s-1,-0.8 -1,-1.3"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M292.5,264.5q0,-0.7 0.7,-0.8 0.8,0 1,0.7 -0.1,0.8 -1,0.9a1,1 0,0 1,-0.7 -0.8m3,-2.3q0,-0.6 0.8,-0.4 1.1,0.1 1,0.8 -0.3,1 -1.1,0.8 -0.7,-0.2 -0.7,-1.2"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M289,256.9q0,0.6 -0.7,0.6 -1,0 -0.9,-0.6t0.6,-0.6q1,0 1,0.6m-2.5,1.9c0,0.5 -0.7,0.7 -1,0.7q-0.8,0 -0.8,-0.7t0.8,-0.6q1.1,0 1,0.6m1,3.8q0.1,0.6 -0.5,0.7 -1,0 -1,-0.7 -0.1,-0.6 0.5,-0.7 1,0 1.1,0.7zM291.5,261.3c0,0.4 -0.7,1 -1.2,1s-0.6,-0.9 -0.6,-1.3 0.4,-0.7 0.8,-0.7 1,0.6 1,1m5.8,1.3c0,0.4 -0.7,0.4 -1,0.4q-0.8,-0.1 -0.8,-1 -0.2,-0.7 1,-0.7c0.5,0 0.8,0.8 0.8,1.3m-3.4,1.7q0,0.8 -0.7,0.8a1,1 0,0 1,-0.8 -0.8q0.1,-0.6 0.8,-0.6 0.9,0 0.7,0.6"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M294.4,262s0.8,-0.3 1.5,-0.3q0.8,0.1 0.6,0.4t-0.7,0.2l-1.4,-0.4zM291.1,264.8s0.6,-0.4 1.1,-0.5q0.8,-0.2 0.8,0.4 0,0.4 -0.6,0.3zM288.9,259.8s1.4,0.6 1.7,1.1q0.5,0.8 0.1,0.9t-0.8,-0.5z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="m288.5,261.6 l1.6,-0.3q0.7,0 0.7,0.2t-0.5,0.3zM285.1,261.6s1.2,0.2 1.5,0.5q0.4,0.5 0.1,0.7c0,0.2 -0.6,0.1 -0.8,0z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M285,263.3s0.4,-0.7 0.8,-0.8 1,-0.1 1,0.1c0.1,0.2 -0.4,0.5 -0.7,0.6zM284,258.2 L285.2,258.5q0.6,0.5 0.4,0.7t-0.8,0c-0.5,-0.4 -0.7,-1 -0.7,-1zM286.6,255.2s1.2,0.4 1.6,1q0.5,0.8 0.1,0.9l-0.9,-0.4c-0.3,-0.3 -0.8,-1.4 -0.8,-1.4z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M300,263.7s-2.3,2 -7.7,1.9 -10.4,-2.5 -11.6,-5l0.5,1.2 1.2,1.6 3.8,2 4.3,0.9 3.3,0.2 3.2,-0.6 2.5,-1.1 0.4,-0.7zM300.8,261.2s-0.9,-1.2 -2.2,-2.3c-1.2,-1.2 -6.2,-4.4 -6.2,-4.4l4,1.8 2.7,2.2 1.8,2z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M301,256.4s1.4,0.5 1.6,0.8l0.5,0.6s-0.3,1 -0.6,0.6l-0.8,-0.9zM305,254.2s1.3,0.5 1.8,1.1q0.6,1.1 0.2,1.3c-0.2,0 -1,-0.7 -1.2,-1l-0.7,-1.4z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M306.4,254s1.2,0.8 1.4,1.2 0.5,1 0.1,1c-0.3,0.2 -0.9,-0.4 -1,-0.8l-0.6,-1.5zM313.2,255.5s1,0.4 1,1.2v1.3l-0.7,-0.7v-0.7l-0.3,-1zM315,256.2s0.8,0.7 0.9,1.2 0.2,0.8 -0.3,1q-0.6,-0.1 -0.6,-1zM302.7,271.2s0.6,0.2 1.2,0q0.9,-0.4 0.9,-1 -0.2,-0.8 -0.8,-0.2c-0.4,0.4 -0.2,0.6 -0.5,0.8zM309.5,273s0.9,-0.2 1.5,-0.9q1,-0.9 0.5,-1.2c-0.4,-0.2 -1,0 -1,0.5zM315,273.1 L316.3,272.2q0.8,-0.6 0.4,-1c-0.3,-0.5 -0.6,-0.2 -0.8,0l-0.2,0.7z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M316.7,273.1s0.8,-0.9 1,-1.4q0.3,-0.9 -0.1,-1 -0.7,0.1 -0.8,0.9z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M299.9,264.1c0,-2.4 3.5,-8 7.5,-8 1.6,0 4.9,1 7.5,2.1 2.2,1 3,2 4.4,2.4s3.7,0.1 3.7,0.1l5,-0.5 -4,5.7 -1.4,1.2s-1.2,2.4 -4.7,3.8c-3.6,1.4 -9.5,0.4 -12.2,-0.3s-6,-3.2 -5.8,-6.5z"
      android:strokeWidth=".5"
      android:fillColor="#8cbebf"
      android:strokeColor="#04534e"/>
  <path
      android:pathData="M299.9,264.1h0.2q0,-0.8 0.6,-2.1a12,12 0,0 1,2.7 -3.8q1.7,-1.7 3.9,-1.8 1.3,0 3.4,0.6l4.1,1.4q1.5,0.8 2.4,1.5 1,0.6 2,1l2,0.2 1.8,-0.1 4.4,-0.5 -3.6,5.2 -1.4,1.2v0.1a9,9 0,0 1,-4.6 3.7q-2,0.6 -4.5,0.6c-2.8,0 -5.9,-0.5 -7.6,-1a8,8 0,0 1,-3.8 -2.1,6 6,0 0,1 -1.8,-4h-0.5q0.1,2.6 2,4.4a9,9 0,0 0,4 2.3c1.7,0.4 4.8,1 7.7,1a14,14 0,0 0,4.7 -0.7,9 9,0 0,0 4.9,-4h-0.3l0.2,0.1 1.4,-1.3 4.3,-6.2 -5.6,0.7h-0.1l-3.4,-0.1a6,6 0,0 1,-1.9 -1q-0.9,-0.7 -2.5,-1.4l-4.2,-1.5q-2,-0.6 -3.4,-0.6h-0.1q-1.5,0 -3,1a11,11 0,0 0,-3.3 3.5,8 8,0 0,0 -1.4,3.7z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M324,264.9q-0.7,0.4 -1.4,0.3c-0.5,0 -11.6,-6.7 -15,-6.4s-6.8,1.7 -6.9,5.3c0,3.5 0.8,3.5 1.5,4.5s7.5,2.5 7.5,2.5h4.1l3.3,-0.4 3,-1.6 1.5,-1.3 1.1,-1.3z"
      android:fillColor="#0c8489"/>
  <path
      android:pathData="M306.5,263q0,1.3 -1,1.3t-1.1,-1c-0.1,-1 0.4,-1 1,-1q1,-0.1 1,0.8zM307.6,260q0.2,-0.9 1.2,-0.8 1.1,0 1.2,0.8 0,1 -1.2,1 -1,0 -1.2,-1m6.7,1.8q0,-0.8 1,-0.9 0.8,0 0.9,1c0.1,1 -0.4,0.8 -1,0.8a1,1 0,0 1,-1 -0.9zM315.6,265.8q0.1,-1 1,-1t1.2,0.8q0.2,1.3 -0.8,1.3c-0.6,0 -1.4,-0.4 -1.4,-1zM312.4,268.5q0.1,-1 1,-1 1.1,0 1.2,1 -0.1,0.8 -1,0.9 -1.1,0 -1.2,-1zM306.4,267.5q0,-1 0.9,-1a1,1 0,1 1,0 1.9,1 1,0 0,1 -1,-1z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M306.4,263q0,0.9 -1,1a1,1 0,0 1,-1 -1q0,-0.9 1,-0.8c1,0.1 1,0.2 1,0.7zM309.7,259.8q0,0.8 -0.8,0.9c-0.5,0 -1.3,-0.4 -1.3,-0.9s0.5,-1 1,-1q1,0.1 1,1zM316.1,261.5q0,0.8 -1,0.9c-1,0.1 -0.9,-0.4 -0.9,-0.9q0,-0.7 1,-0.8c1,-0.1 0.9,0.4 0.9,0.8"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M312.7,264.2q0,1.1 -1,1.2t-1.1,-1c-0.1,-1 0.3,-1 1,-1s1,0.2 1,0.8z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M308,267.2q0,0.9 -1,1c-1,0.1 -1,-0.5 -1,-1s0.6,-0.8 1.2,-0.8 0.8,0.3 0.8,0.8m4.5,-3a1,1 0,0 1,-1 1,1 1,0 0,1 -1,-1 1,1 0,0 1,1 -1,1 1,0 0,1 1,1m5.2,1.3q0,1 -1,1t-1.1,-1c-0.1,-1 0.2,-0.8 0.8,-0.8q1.1,0 1.3,0.8m-3.3,2.7q0,1 -1,1 -1.3,0 -1.1,-1 0.1,-0.8 1,-0.8t1,0.8z"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M314.8,265s1.6,-0.2 1.9,0q0.4,0.6 0.1,1c-0.1,0 -0.8,0.1 -1,-0.1 0,-0.3 -1,-1 -1,-1zM313.1,259.6s1.3,0.3 1.8,0.9q0.6,0.9 0.4,1.1 -0.4,0.3 -1,-0.2zM309.4,262.2s1.5,0.8 1.8,1.3q0.5,0.6 0.2,1c-0.1,0.2 -0.8,0 -1,-0.3 0,-0.3 -1,-2 -1,-2"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M308.7,263.3s1.7,0.2 2,0.5q0.8,0.4 0.8,0.6 -0.1,0.4 -0.5,0.4l-1.2,-0.5 -1,-1zM306.8,258.5s1.7,0.2 2,0.5 0.5,1 0.1,1.2q-0.6,0.1 -1,-0.5l-1,-1.2zM303.2,261s1.5,0.8 2,1.4q0.6,0.6 0,1c-0.4,0 -0.9,-0.7 -0.9,-1z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="m302.8,263 l1.5,-0.2c0.5,0 1.2,-0.1 1.2,0.2s-0.6,0.5 -1,0.4zM311.4,268.6s1.1,-0.7 1.7,-0.6q0.7,0 0.7,0.4c0,0.4 -0.7,0.3 -0.9,0.3zM305.4,267.7s0.4,-0.6 0.9,-0.7q0.6,0 0.7,0.2 -0.1,0.4 -0.6,0.6h-1z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M322.6,266.6s-1.6,2 -4,3a14,14 0,0 1,-7.2 1.1,15 15,0 0,1 -11.2,-5.2l0.4,1.1 2,2.6 2.3,1 3.5,1 3.3,0.3h2.5l3,-0.5 1.9,-0.7 1.3,-0.8 1,-0.8 1.2,-2zM319.6,260.7s-1,0.3 -3,-0.9 -4.3,-2.8 -8.8,-2.8 -5.9,3 -6.2,3.2l2,-2.6 2,-1.1 1.7,-0.3h1.5l2,0.6 1.7,0.6 2.1,0.7 1.8,1 2.2,1.1zM325.6,262.1h-3.4c-0.8,0 -0.8,0.2 -1,0.4l-0.2,0.4 -0.8,-1.7 3.5,-0.5 2,0.7 -0.2,0.6z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M320.7,280.4q0.3,0.9 1.1,1h1.6l3.6,-0.3 1.2,-1.4 0.8,-3.2 1,-1.2 3.3,-1.9 2.8,-0.8 1.9,-0.2 2.5,-0.8 1.9,-1 0.8,-1.2 0.8,-1.8 -0.8,-2.5 -2,-2.7 -3.4,-1.3H335l-3,1.1 -5.5,1.3s-3,1.3 -4.3,3.7c-1.3,2.3 -1.2,3 -1.5,6 -0.2,3.2 0,7.2 0,7.2z"
      android:strokeWidth=".5"
      android:fillColor="#0c8489"
      android:strokeColor="#04534e"/>
  <path
      android:pathData="M322.2,279.2c1,0 0.2,-1.7 1.4,-3.8s2.6,-4.6 3,-4.6 0.4,0.4 0.9,0.3 1.2,-0.8 1.2,-1 0,-1.3 1,-2c0.8,-0.8 2.1,-1.9 2.7,-2q1,-0.3 1,0c0.1,0.3 -0.3,0.7 0.1,0.8s1.4,0.1 1.6,-0.5l0.2,-1s0.6,-0.5 1.8,-0.3 1.3,0.2 1.4,0.7 -0.5,0.5 -0.4,0.8v0.6c0.2,0.2 0.4,1 1.2,1 0.8,-0.1 1.1,-0.6 1.1,-0.6l0.3,-0.4s0.2,-0.5 -0.1,-0.7q-0.4,-0.4 0,-0.3c0.4,0.1 1.6,1.8 2,1.1s0.5,-1.7 0.2,-2l-1.9,-2.7 -3,-1 -4,0.3 -4,2.2 -3.6,2s-0.2,2.9 -0.8,2.3c-0.7,-0.5 -1.6,-2 -1.9,-1.5s-0.9,2.7 -1.3,3.8c-0.3,1 -1.4,4.4 -1.3,5.6s0.8,2.9 1.2,2.9"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M325.8,274.4q-0.6,0.1 -0.8,0.5 -0.3,0.3 -0.2,1c0,0.3 0.7,0.7 0.7,0.7h0.5l1,-0.3 0.3,-0.8q-0.1,-0.7 -0.4,-0.6zM332,269.4c-0.6,-0.3 -0.8,0.1 -1,0.3v0.5s0.2,0.8 0.4,1c0.3,0 1.1,0.3 1.3,0q0.5,-0.2 0.5,-0.9c0,-0.7 -0.7,-0.7 -0.7,-0.7zM337,270.5q-0.1,-0.3 -0.9,-0.2 -0.6,0 -0.6,1 0.4,0.9 1.2,0.6c0.5,-0.1 0.4,-0.8 0.4,-0.9zM322.6,271.5c0,0.3 0.5,1 1.2,0.9a1.3,1.3 0,0 0,1.2 -1c0,-0.4 -0.4,0.3 -1,0.5s-0.7,0.2 -1,0q-0.3,-0.5 -0.4,-0.4"
      android:fillColor="#8cbebf"/>
  <path
      android:pathData="M327.3,275.5s0,0.6 -0.4,1q-0.7,0.4 -1,0.3 -0.5,0 -0.8,-0.3l-0.3,-0.7s0.5,0.8 1.3,0.6a2,2 0,0 0,1.2 -0.9m13.3,-8.3s0,0.6 -0.5,0.9q-0.6,0.4 -1,0.3 -0.4,0 -0.7,-0.4c-0.6,-1 -0.3,-1.5 -0.3,-1.5s0.4,1.6 1.3,1.5a2,2 0,0 0,1.2 -0.8m-11.9,2.9s-0.4,1.2 -1.2,1.2c-0.9,0 -0.8,-0.2 -0.7,-0.4 0,-0.1 0.3,0.2 0.7,0 0.4,0 1.2,-0.8 1.2,-0.8m2.3,-0.3s0,1 0.2,1.2q0.5,0.5 1,0.5 0.6,0.1 0.9,-0.4 0.3,-0.8 0.1,-0.8l-0.2,0.5q-0.4,0.4 -0.8,0.3 -0.6,0 -0.8,-0.3zM335.3,265.5s0.2,0.7 -0.3,1.2 -1.5,0.2 -1.5,0.2 0.8,0.3 1.2,-0.2 0.6,-1.2 0.6,-1.2"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M321.7,281.1s5.3,0 5.7,-0.8c0.4,-0.7 -0.4,-3.6 2.7,-5.8 3.2,-2.2 5.7,-2.2 7.4,-2.3 1.7,-0.2 5.3,-1.3 6,-4.5 0.3,-2 -2.3,-2.2 -3.3,-4 -1,-2 -3.6,-1.2 -3.8,-1.2s-3,0 -4.3,1.3c-0.9,0.8 -0.8,1.8 -1.1,2.2s-4,0 -4.3,0.6c-0.4,0.6 0.1,1.7 -0.2,2 -0.2,0.3 -1,-0.8 -1,-0.8l0.2,-2.8 4.5,-2.6 5,-2.8 3.5,0.1 3.5,1.8 1.7,2s0.3,0.8 0.5,2.3l-0.2,2c-0.8,2.8 -3.7,4.8 -6.7,5a14,14 0,0 0,-7 2.5c-1.4,1 -2.1,5.3 -2.1,5.9 0,0.5 -6.6,0.1 -6.6,0.1z"
      android:fillColor="#04534e"/>
  <path
      android:pathData="M324,270.8q-0.6,0.4 -0.8,0c-0.2,-1 0.3,-2.2 0.3,-2.2v1.6q0.3,0.1 0.5,0.3zM327.2,270.2c-0.2,-0.1 -0.1,-1 0,-1.3 0.2,-0.3 1.7,-1.4 1.7,-1.4l-0.6,1.5c-0.2,0.4 -0.7,1.3 -1,1.2zM326,275.5q-0.4,0 -0.3,-1c0.1,-1 1,-1.6 1,-1.6l-0.4,1.2 0.2,1q-0.1,0.4 -0.5,0.4"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M328.4,274s-0.5,0.6 -1,0.9q-0.9,0.6 -1.4,0.6c-0.4,0 0.1,-0.8 0.3,-0.9zM332.9,267.6s-0.6,0.5 -0.9,1.2q-0.5,1.2 -0.1,1.4c0.3,0.2 0.7,-0.8 0.8,-1.1z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M334.6,269.9s-0.5,-0.4 -1.2,-0.6q-1,-0.2 -1.2,0.2c0,0.2 -0.6,0.5 -0.3,0.7s0.6,-0.3 0.8,-0.3q0.3,-0.3 0.9,-0.2zM333.6,265.4c0.3,0.3 0.7,-0.2 1,-0.6s0.7,-1.2 0.7,-1.2l-1,0.7c-0.4,0.3 -0.9,1 -0.7,1.1m6.9,-0.1s-1,0.4 -1.2,0.8q-0.6,0.6 -0.4,1c0.3,0.3 0.6,-0.2 0.8,-0.5zM338.5,269.9s-0.3,0.6 -0.9,1q-1,0.6 -1.3,0.3 0,-0.4 0.3,-0.7l0.8,-0.2z"
      android:fillColor="#231f20"/>
  <path
      android:pathData="M290.78,254.31a4.3,2.9 98.7,1 0,5.73 0.88a4.3,2.9 98.7,1 0,-5.73 -0.88z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="293.57"
          android:centerY="254.74"
          android:gradientRadius="3.78"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFF15770"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m295.4,247 l-1.7,4.2 1.4,-0.2 0.7,-3.8z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M296.1,245.1q-1,0 -1.2,1 0,1.2 1,1.4 1,0 1.2,-1 0,-1.2 -1,-1.4z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m369.7,255.8 l-4.2,4 1.7,0.4 2.9,-3.8z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M371.6,254.1a1.5,1.5 0,0 0,-2 0.6q-0.7,1.1 0.4,2 1.2,0.4 2,-0.7c0.4,-0.6 0.2,-1.5 -0.4,-1.9z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="m262.8,233 l2.2,4.9 1,-1.4 -2.6,-3.7z"
      android:strokeWidth=".3"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M261.9,230.9q-0.9,0.8 -0.1,1.9c0.4,0.6 1.3,0.7 1.8,0.2q0.9,-0.9 0.1,-2a1.3,1.3 0,0 0,-1.8 -0.1z"
      android:strokeWidth=".2"
      android:fillColor="#fcca3e"
      android:strokeColor="#aa8c30"/>
  <path
      android:pathData="M296.6,245.3s0.3,0.1 0,0c-0.5,0 -1.4,0.4 -1.4,1a1,1 0,0 0,1 1c0.5,0 -0.2,0.2 -0.2,0.2l-0.7,-0.2 -0.2,-0.4 -0.2,-0.5 0.1,-0.6 0.4,-0.5 0.5,-0.2h0.3z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M295.6,248.1s-0.3,0 -0.5,0.3l-0.7,1.1 1,-2.3 0.4,0.2zM372,254.5s0.3,0.4 -0.1,0c-0.4,-0.3 -1.8,-0.2 -2.1,0.5q-0.5,1.2 0.6,1.7c0.6,0.2 -0.2,0 -0.2,0l-0.7,-0.5 -0.1,-0.6v-0.7l0.5,-0.6 0.7,-0.3h0.7l0.3,0.2z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M369.4,257.3s-0.3,-0.2 -0.8,0l-1.4,0.9 2.3,-2.2 0.5,0.6zM262.4,230.6s0.4,-0.1 0,0.1 -0.8,1.4 -0.3,2q1,0.7 1.7,0c0.4,-0.5 0,0.2 0,0.2l-0.7,0.4h-0.6l-0.6,-0.3 -0.4,-0.6v-0.8l0.2,-0.5 0.3,-0.3 0.3,-0.2z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M264,233.8s-0.3,0.1 -0.2,0.6l0.3,1.5 -1.2,-2.7 0.7,-0.2z"
      android:fillColor="#aa8c30"/>
  <path
      android:pathData="M265.73,238.09a4.6,3.1 62.9,1 0,4.19 8.19a4.6,3.1 62.9,1 0,-4.19 -8.19z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="267.88"
          android:centerY="242.22"
          android:gradientRadius="3.75"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFF15770"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M262.6,241.2s1,0.2 1.5,0 0.6,-0.7 0.7,-0.8 0.3,-0.6 0.2,-1l-1,-1.4 -1,-0.1q0,-0.2 -0.2,-0.5c-0.3,0 -1.7,1.6 -1.5,1.8 0.3,0.3 0.4,0 0.5,0.1v1.1c0,0.3 0.8,0.8 0.8,0.8"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M264,238.1c0.1,0.3 0.7,1 0.9,1q0.5,0.3 1,0c0.3,0 1,-0.7 1,-1l-0.1,-0.7c-0.2,-0.3 -0.1,-1 -0.4,-1.1q-0.2,0 -0.5,-0.3v-0.7c-0.3,-0.3 -2.6,1.1 -2.4,1.4q0.3,0.1 0.5,0.1z"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M269.2,237.6c0.2,-0.4 0.1,-1.5 -0.1,-1.8s-0.6,-0.3 -0.7,-0.4q0.1,-0.1 0.3,-0.5c0.2,-0.4 -2.6,0.1 -2.4,0.4s0.6,0.1 0.6,0.3q-0.2,0.3 -0.5,0.6l0.3,1.1q0,0.3 0.2,0.5l0.6,0.2h1.4q0.2,0 0.3,-0.5zM291.2,250.4s0.3,0.7 0.8,1h1l0.6,-0.5c0.2,-0.2 0.4,-1.2 0.3,-1.4 0,-0.2 -0.5,-0.6 -0.5,-0.8q0.2,-0.2 0.2,-0.4c-0.1,-0.2 -2,-0.2 -2,0.2q0.2,0.3 0.2,0.3c0,0.2 -0.5,0.5 -0.6,0.7 -0.2,0.1 0,1 0,1z"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M293.8,249.5c0,0.2 -0.2,1 0,1.1q0.1,0.5 0.5,0.8h1.2l0.4,-0.6c0.1,-0.3 0.6,-0.7 0.5,-0.9q-0.2,-0.2 -0.1,-0.4 0.2,-0.2 0.3,-0.5c0.1,-0.3 -2.1,-1 -2.2,-0.7q0.2,0.3 0.2,0.4c0,0.2 -0.7,0.6 -0.8,0.8"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M297.3,252.5c0.3,-0.2 1,-0.9 1,-1.1q-0.2,-0.6 -0.2,-0.7l0.5,-0.1c0.1,-0.2 -1.6,-1.6 -1.7,-1.3q0.2,0.4 0.2,0.5l-0.7,0.1q-0.3,0.3 -0.5,0.8 -0.2,0.2 -0.2,0.5l0.2,0.4c0.1,0.2 0.8,0.9 1,0.9zM363.3,258s0,0.8 0.4,1.2q0.5,0.4 0.8,0.4h0.7l1,-1q-0.2,-0.6 -0.2,-1c0,-0.4 0.3,0 0.3,-0.2s-1.6,-1.1 -1.7,-0.8c-0.1,0.2 0.2,0.3 0,0.4l-0.8,0.2c-0.2,0 -0.5,0.8 -0.5,0.8"
      android:fillColor="#e92736"/>
  <path
      android:pathData="m366,258.5 l-0.8,0.8q0,0.5 0.2,0.8c0,0.2 0.7,0.6 1,0.5q0.2,0 0.5,-0.2c0.2,-0.1 0.8,-0.2 0.8,-0.4l0.1,-0.4q0.2,0 0.6,-0.2c0.2,-0.2 -1.2,-1.7 -1.4,-1.5q-0.1,0.3 0,0.4c-0.2,0 -0.9,0 -1,0.2"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M366.4,262.2h1.4l0.4,-0.5q0,0.1 0.4,0.2c0.2,0 0,-2 -0.3,-1.8q-0.2,0.4 -0.2,0.4 -0.2,-0.2 -0.5,-0.3l-1,0.1s-0.2,0 -0.3,0.2l-0.2,0.4v1q0,0.2 0.3,0.3"
      android:fillColor="#e92736"/>
  <path
      android:pathData="M264.2,239c0.2,0.4 -0.1,1.2 -0.3,1.4s-1.3,-0.1 -1.3,-0.3q0,-0.4 0,-0.8c0.2,-0.2 0.6,-0.7 0.9,-0.6q0.5,0.1 0.7,0.4zM264.5,237.7q-0.2,0.5 0.2,0.7c0.1,0.2 1,0.4 1.2,0.3s0.6,-1.5 0.4,-1.7q-0.4,-0.2 -0.7,-0.2c-0.2,0 -1,0.6 -1.1,0.9m2.7,-1.3v0.7q0.4,0.3 1,0.5c0.3,0 0.5,-1 0.5,-1.1q0,-0.3 -0.3,-0.4l-1.2,0.2zM293.3,250.1c0,0.4 -0.7,0.7 -0.9,0.7s-0.7,-1 -0.6,-1q0.2,-0.4 0.5,-0.5 0.5,-0.1 0.9,0.2zM294.4,249.5q-0.3,0.2 -0.3,0.6c0,0.2 0.3,0.8 0.5,0.9s1.2,-0.5 1.3,-0.7q0,-0.4 -0.4,-0.6l-1,-0.2zM296.8,250.5q-0.3,0.1 -0.4,0.5s0,0.6 0.3,0.8 0.8,-0.2 0.9,-0.3v-0.4c0,-0.2 -0.8,-0.6 -0.8,-0.6m68.6,8.2c-0.2,0.2 -0.9,0.2 -1,0 -0.3,0 -0.3,-1 -0.1,-1q0.2,-0.3 0.6,-0.2 0.5,0 0.6,0.6zM366.4,258.8q-0.5,0 -0.6,0.3c-0.1,0.1 -0.2,0.7 0,0.9 0.1,0.1 1.2,0.2 1.3,0v-0.5zM367.4,260.8h-0.5q-0.3,0.1 -0.4,0.7c0,0.2 0.7,0.3 0.8,0.3l0.3,-0.2 -0.1,-0.8z"
      android:fillColor="#f7e204"/>
  <path
      android:pathData="M361.58,261.45a2.2,4.5 47.9,1 0,2.95 3.26a2.2,4.5 47.9,1 0,-2.95 -3.26z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="362.96"
          android:centerY="263.1"
          android:gradientRadius="1.95"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFF15770"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M285.8,199.3s-3.8,0.3 -6.5,-0.1 -2,-7.3 -2.1,-7.5c0,-0.2 -1.6,-1.8 -1.4,-3.3s5.2,-5.5 5.8,-5.8c0.5,-0.1 1.5,0.3 1.5,0.3s1.2,-1.2 1.6,-1.2 0.9,0.7 0.8,1c-0.2,0.3 -1.9,1.3 -2,2 -0.2,0.8 0,1.5 -0.5,2.3s-1.4,1.7 -1.5,2.4 -0.4,1.1 0,1.4 2.1,0 3.5,-0.6 2,-1.5 2.1,-1.4c0.2,0 -0.8,1.7 -2.3,2.4s-2.5,1.3 -3.3,1.2c0,0 -0.8,2 1.8,2.2 2.6,0 4,-0.6 4,-0.6zM270.8,215.3s2,0.8 2,3 -2.1,4.1 -2,8c0,4 0.6,4 5.8,8.3a6,6 0,0 1,1.6 2.5c0.3,1.3 1.2,8.9 6.2,8.9s5.8,-3.3 5.8,-3.3l-2.2,-3.1s-1.7,0.5 -2.7,0.3a3,3 0,0 1,-2.3 -2.1c-0.6,-1.2 -0.7,-4.1 -1.6,-5.4 -2,-3.2 -5.8,-3.7 -5.9,-7 0,-3.4 2.3,-3.7 1.7,-7.7 -0.7,-4 -5.4,-5.8 -5.4,-5.8l-1.9,-0.8 -0.7,4z"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="M284.7,187.6c1.7,-0.4 0.7,-4.8 0.7,-4.8l-1.2,1s0.8,1 0.8,2 -0.3,1.8 -0.3,1.8"
      android:fillColor="#f1a720"/>
  <path
      android:pathData="m287.8,231.5 l-0.9,-0.7c-0.6,-0.4 -3.2,-2.6 -2.8,-8.9 0.5,-7.2 11,-11 11.6,-13.8 0.8,-3.2 3.1,-4.5 -5.2,-11l-2,2.2 3.5,2.6s0.7,-0.4 0.8,0.6c0,1 -1,1.7 -1.2,2.5 -0.2,0.7 -5.4,4.3 -5.8,4.6l-3,2.4s-1.3,1.3 -2.5,4.1c-1.3,2.8 -0.7,6.4 -0.7,6.4s0,7.6 8.2,9"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="m282.7,235.5 l0.6,2.7 -1.7,0.6h-0.7l-0.5,0.2 -1.3,1.2h-0.3s-0.2,0 -0.3,-0.8q-0.1,-1.4 -0.2,-1.5l1.6,0.7h0.6l0.3,-0.1 0.7,-1.5zM277.9,236 L277.1,235 276.1,234.1 277.3,233.5h1.2l-0.2,1.5zM280.6,231.7s0.8,0.3 1,1l0.6,1.2 -2,-0.2 -1.4,-0.4 1,-1.4zM279.9,243.6s-0.4,-0.3 -0.7,-0.9l-0.4,-1.6 1.3,-0.3 1.5,0.7h0.4l-0.3,0.8 -1.5,1.3zM284.9,239.5s-1,-0.4 -1.2,-1q-0.3,-0.6 -0.4,-0.4l-0.3,1.5 -0.5,1.5 0.2,0.2 1.1,-0.3 0.9,-0.9 0.3,-0.6zM280.2,244.3 L281.6,245q1.2,0.6 1.2,0.5l0.6,-0.6 0.7,-1.3v-0.1l-3,0.3zM284.6,243.1 L287.3,241.5 287.4,240.1 286.9,239.7h-1.5l-0.5,1.8zM282.8,245.6s1.8,0.8 5,-0.4c1.4,-0.5 1.7,-1.2 1.7,-1.2l-1.3,-0.5 -1.2,1zM280.3,225.3 L279.9,223.9q-0.2,-1.2 -0.1,-1.1l0.9,0.5 1.3,1 -1,0.7zM282.5,229 L281.6,228 281,226.8 282,227 283.1,227.6z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="M276.6,187c-0.2,0.7 -0.4,1.9 0.5,2 0.4,0.2 2.3,0 4.5,-2.4 1,-1.2 1,-2.4 1,-2.4l0.5,-1.3s-0.9,-0.6 -2.2,0.2 -4.3,4 -4.3,4z"
      android:fillColor="#c6c7a6"/>
  <path
      android:pathData="M282.5,183.6m-0.4,0a0.4,0.4 0,1 1,0.8 0a0.4,0.4 0,1 1,-0.8 0"
      android:fillColor="#1c242f"/>
  <path
      android:pathData="m279.7,184 l-1.7,1.7s1.4,-0.4 2.6,-1.5c0.6,-0.6 2,-0.8 2.6,-0.7 0.4,0.1 0.2,-0.4 0,-0.5 -0.3,-0.3 -1.4,-0.4 -1.6,-0.3z"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="M284.1,224.5v-2.2l-0.9,0.8 -0.8,1.1 1.3,0.5zM279.6,221.9v-2.3c0,-1 0.5,-3.1 0.5,-3.1l0.6,1.4 1.5,2.4 0.5,0.4 -1.3,1 -1.4,0.4zM284.1,221.3 L284.4,220.3c0.1,-0.6 0.5,-1.4 0.5,-1.4v0.1l-2,1.7 0.8,0.7h0.5m-3.7,-6s0.2,-0.6 1,-1.5l1.3,-1.8 0.6,2.4 0.4,2v0.4l-0.7,0.2c-0.2,0 -1.6,0 -1.8,-0.3zM285.3,218.3 L286.1,217.4 287.1,216.4 283.9,217 284.4,218zM283.3,211.6s0.3,0 1.2,-1c0.8,-0.8 1.3,-0.7 1.3,-0.7l0.3,3.4 -1.6,-0.5z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="m287.3,216.2 l2,-2 -3.3,-1 0.6,2.3zM288.7,211 L288.9,207.7s-0.7,0.3 -1.5,1l-1.2,0.9 0.4,0.6 1,0.7h1zM291.5,207.8L291.5,205s-0.5,0.8 -1,1.2l-0.9,0.8 0.9,0.6h1zM292.7,211.3 L294.3,210 295.8,208.3 291.6,208 291.8,209.7 292.8,211.3zM283.1,197.5 L282.9,199.3h3.5l-0.9,-1.2 -1,-0.6 -1,-1zM289.7,213.7c0.2,0 1.1,-0.8 1.6,-1.2 0.4,-0.5 1.2,-1 1.2,-1l-3.3,-0.3v2.2zM296,207.8s0.4,-1.3 0.3,-2.6q-0.1,-1.8 -0.2,-1.8l-2.5,1.7 0.4,1.2 1,1z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="M291.8,204.8s0.7,-1 0.8,-1.4v-0.4h2l1.1,0.2h0.2l-0.9,1.1 -1.2,0.7 -1.5,0.2zM295.8,202.8 L294.5,200.8q-0.7,-1 -0.8,-1l-1,2.7v0.3h3z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="m271.6,222.2 l-0.3,0.8s1.3,-0.7 2.3,-0.7 2.1,1.3 2.1,1.3l0.2,-0.9s-1.3,-1.3 -2.2,-1.3c-0.8,-0.1 -2,0.8 -2,0.8zM272.3,216.4 L272.6,216.8c0,0.1 0.1,-1 1.4,-1.5 1.4,-0.6 2,-0.1 2,-0.1s-0.2,-0.8 -0.8,-1q-0.8,-0.2 -1.8,0.5a4,4 0,0 0,-1.1 1.7m4.4,4.4s-0.6,-1.4 -1.6,-1.6c-1.1,-0.3 -2.5,0.5 -2.5,0.5l-0.2,0.7s1.5,-0.8 2.5,-0.4c1.1,0.5 1.5,1.7 1.5,1.7l0.3,-1zM271.7,215.7 L272.1,216.2s-0.2,-1.4 0.5,-2.1l1.1,-1 -0.6,-0.3s-0.9,0.5 -1.2,1.1a3,3 0,0 0,-0.2 1.8m-0.5,-2.3c0.3,-0.8 1.2,-1 1.2,-1l-0.3,-0.3h-0.5s-0.5,0.3 -0.9,1c-0.3,0.9 -0.3,2 -0.3,2l0.6,0.3s-0.1,-1.4 0.2,-2m7.8,-19.2q1.4,-0.4 1.5,-0.3c0.1,0.1 -1.3,1.3 -0.7,2.9q-1,2.1 -0.9,2.3h0.7l0.4,-1.8c0.8,1.3 2,2 2,2h1s0,-1.3 0.4,-2.4h0.1a5,5 0,0 0,2 1.5v-0.4s-1.1,-0.6 -1.7,-1.5l-0.1,-0.1a4,4 0,0 1,1.8 -1.7l0.7,0.6 0.2,-0.2 -0.5,-0.7h-0.6l-1.8,1.7 -0.8,-1.5 -0.4,-0.2s-1.1,0.1 -2,1.7c-0.2,-1.2 1.1,-2.4 1.1,-2.4v-0.5l-2.4,0.6c-1,0.3 -1.7,1.2 -1.7,1.2v0.8s1,-1.1 1.7,-1.6m1.5,2.2c0.6,-1 2,-1.5 2,-1.5s0,0.7 0.7,1.7c-0.5,0.9 -0.8,2.2 -0.8,2.2s-1,-0.6 -1.9,-2l-0.2,-0.3zM272.9,217.5v0.8s0.8,-0.9 2,-1 2.3,1 2.3,1 0,-1 -0.2,-1.2c-0.1,0 -1.1,-0.6 -2,-0.6s-2.1,1 -2.1,1m21.4,-11.2 l-0.5,-1 0.6,-0.3 1.7,-1.6 -0.4,-0.6h-1.6l-1.4,-0.1 0.5,-1.7 0.6,-1.4 -0.6,-0.5s0.2,0.5 -0.8,0.6 -2.8,-0.8 -2.8,-0.8v0.4l2.5,0.7q0.9,0 1.2,-0.3l-0.5,1.3 -0.3,1 0.2,0.2v0.8l1.4,0.1 1.6,0.1s-0.8,1 -1.7,1.5h-0.3l-1,-1.5 -0.1,0.5 0.7,1.2 -1.5,-0.1 -0.4,0.4v2.5l-1.8,-0.6 -0.3,0.3v-0.1l-0.4,0.4s-0.4,1.4 -0.3,3.1v0.1a5,5 0,0 1,-2.4 -1.3l-0.4,0.3s-0.3,1.4 0,3.2c-1.3,-0.3 -2.5,-1.5 -2.5,-1.5l-0.5,0.5 0.7,4.7h-0.7c-1.4,0 -2.3,-1.3 -2.3,-1.3l-0.4,1s0.6,2.7 2,4l0.4,0.2c-1.3,1 -2.8,1.2 -2.8,1.2v0.9l2.2,1.5 -1.6,1 0.2,0.5 1.8,-1.3 1.8,0.5v-0.5l-1.5,-0.3 0.1,-0.1c0.5,-0.4 1.4,-1.8 1.4,-1.8v-1s-0.5,0 -1.1,-0.6c1.8,-1.2 1.9,-1.8 1.9,-1.8l0.4,-0.6s-0.7,-0.3 -1.1,-1.2l2.9,-0.7 0.2,-0.2s-0.7,-1.2 -1,-2.6l3,0.6 0.4,-0.4 -0.5,-2.4c1.8,0 3.2,0.2 3.2,0.2l0.3,-0.3s-1,-1.9 -1,-3h4l0.3,-0.5s-1.4,-1 -1.7,-1.5m-10.5,15.5s-0.5,1 -1.4,2l-0.2,0.2 -0.5,-0.2q-1.5,-1.1 -1.4,-1.3l2.5,-1.6zM284.7,218.6 L282.7,220.6 282.3,220.1c-0.8,-1.2 -2,-2.1 -1.8,-3.8 0,0 0.7,1.2 2.2,1l1,-0.1c0.4,1 1,1.4 1,1.4m2,-2.5 l-2.7,0.7 -0.1,-0.5c-0.5,-1.6 -0.5,-4 -0.5,-4s1,0.8 2.6,1.2zM286.2,213.1c-0.2,-1.4 0.1,-2.9 0.1,-2.9s0.8,0.8 2.3,1q0.2,2.2 0.3,2.1zM292,210.9s-1,0.4 -2.8,0.1v-3.5l2.3,0.7zM292,207.8h-0.2L291.8,205s1,0.3 1.7,0.2c0.2,0.4 0,0.5 0.5,1.2s1.1,1.3 1.1,1.3 -2.3,0.2 -3.2,0zM284.4,226.4 L284.3,225.9s-1,0.7 -1.2,1.4l-0.3,-0.1 -2,-0.8 0.2,0.4 1.8,0.8h0.2l-0.5,1.4 0.2,0.3 0.5,-1.7 1.2,0.1 -0.2,0.8 -0.4,1.5 0.5,0.3h2l-0.3,-0.4 -0.7,-0.1h-1.1l0.3,-1.2 0.3,-0.8 -0.3,-0.7 -1,0.2zM273.1,225.1c1.3,0 2.3,0.9 2.3,0.9v-0.6s-1,-1.4 -2.1,-1.4 -2.5,1.2 -2.5,1.2v1.2s1,-1.3 2.3,-1.3m13.3,19.4c-1.3,0.6 -2.7,0.6 -2.7,0.6l0.8,-1.5c2.5,-0.8 3,-2.4 3,-2.4l-0.2,-0.7s-0.6,1.6 -2.6,2.4c0.4,-1.2 0.7,-3.2 0.7,-3.2l-0.7,-0.2s-1,1.3 -2,1.6a8,8 0,0 0,0.8 -2.7l-0.3,-0.5s-0.7,0.6 -1.7,0.8h-0.6c0.6,-0.8 1.7,-3.2 1.7,-3.2l-0.2,-0.5s-0.8,2.1 -1.7,3.2l-0.3,0.4c-1,-0.3 -2.2,-1 -2.2,-1l0.1,0.3s0.8,0.6 1.8,1l-1.3,1.2v1l3,0.4c-1,1.6 -2,2.1 -2,2.1l0.5,0.7s1.3,0 3.7,-0.6q-1,1.8 -1.2,1.8c-0.2,0 2.2,0.2 3.7,-0.6s2,-1.6 2,-1.6l-0.4,-0.4s-0.4,1 -1.7,1.6m-6.9,-4 l1.2,-1.5c1.6,0.3 2,-0.1 2,-0.1s0.3,0.8 -0.6,2.3c-1.1,0 -2.6,-0.8 -2.6,-0.8zM280.9,243.5 L282.2,241.9 282.5,241.6c1.5,0 2.3,-1.3 2.3,-1.3s0.2,1.2 -0.5,2.7c-1.9,0.7 -3.4,0.5 -3.4,0.5m-1.9,-9.8c1.5,0 3.3,0.7 3.3,0.7l-0.2,-0.7 -2.9,-0.6a4,4 0,0 1,1.6 -1.6l-0.5,-0.3s-0.6,0.1 -1.4,1.5l-0.2,0.4h-0.3a5,5 0,0 0,-2.4 0.7l0.2,0.4s1.1,-0.5 2.3,-0.5l-0.7,2.4 0.3,0.6zM276,227.5 L275.7,226.8s-1.5,-0.6 -2.7,0c-1.1,0.5 -2,1.9 -2,1.9l0.5,0.9s0.8,-1.5 1.9,-1.9 2.6,-0.2 2.6,-0.2m3,2.4 l-1,-0.6s-1.7,0.5 -2.2,1.1 -1.1,2.1 -1,2.5q0.5,0.7 0.7,0.7c0.2,0 0.3,-1.5 1.1,-2.3s2.4,-1.4 2.4,-1.4m-3.9,-0.5a5,5 0,0 1,2 -1l-0.7,-0.5s-1.5,0.2 -2.2,0.9 -1.8,2.1 -1.8,2.1l0.9,0.9s1.2,-1.8 1.8,-2.4m5.2,-44.7c0.8,-1 1.6,-1 1.9,-1h0.6l-0.8,2.5c-0.2,1 -0.7,1.2 -1,1.9s-1.1,1.8 -1.2,2.8q0.1,1.5 0.5,1.7c0.3,0.1 0.8,-0.4 0.7,-0.7 0,-0.3 -0.3,0 -0.4,0q-0.2,0 -0.3,-0.9c-0.1,-0.6 0.7,-2.2 1,-2.8s1,-1.1 1,-2l0.7,-2.5q0,-0.1 -1.3,-0.3 -1.1,0 -1.9,1.2c-0.4,0.6 -1.7,1.1 -1.7,1.1s1.4,0 2.2,-1"
      android:fillColor="#fff"/>
  <path
      android:pathData="M285.2,194.5h-2.5l0.7,2zM282.2,194.4 L281.7,194 281.4,193.7 280.5,194.2 280,196.1 280.3,196.4 282.3,194.4zM281.9,199.4 L280.1,196.7 279.1,198.9 279.6,199.1 280.8,199.3h1.1zM289.3,244.1a9,9 0,0 1,-4.8 1.5,7 7,0 0,1 -3.4,-1 4,4 0,0 1,-1.5 -1.7q-0.8,-1.7 -1,-4 -0.3,-2 -1.1,-3.6c-0.8,-1.3 -2.4,-2.1 -3.8,-3.3a7,7 0,0 1,-2.8 -5.2v-0.8q0.1,-2.5 1,-4.5c0.9,-2 1.2,-2.5 1.2,-3.5v-0.5q-0.6,-1.1 -1.5,-1.9c-0.9,-0.8 -1.2,-0.8 -1.2,-0.8l-1,-0.2 -0.1,0.3 1,0.2v-0.1,0.1l1.3,1q0.9,0.7 1.1,1.5v0.4q-0.1,1.4 -1,3.3a12,12 0,0 0,-1.2 4.7v0.8a8,8 0,0 0,3 5.5c1.5,1.1 3,2 3.7,3.2q1,1.9 1.3,5 0.3,1.3 0.8,2.5c0.5,1.2 0.9,1.5 1.6,2a7,7 0,0 0,3.7 1c2.7,0 4.9,-1.6 4.9,-1.6z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="m287.2,239.2 l-1.3,0.4q-0.8,0 -1.7,-0.8a4,4 0,0 1,-1.2 -2.3q-0.1,-1.3 -1,-3.3c-0.5,-1.5 -2,-2.6 -3.5,-3.7 -1.3,-1.1 -2.6,-2.2 -2.9,-3.4v-0.7q0.1,-1.5 0.8,-3.1a11,11 0,0 0,0.9 -4.8c-0.2,-2 -1.7,-3.5 -3.2,-4.5l-2.8,-1.4 -0.1,0.3h0.2c0.5,0.3 1.8,0.9 3,1.8q2.1,1.4 2.6,3.8v0.8q0,2.2 -0.9,3.9c-0.9,1.7 -0.9,2.2 -0.9,3.2l0.1,0.8c0.3,1.3 1.6,2.4 3,3.6 1.5,1 3,2.2 3.5,3.5q0.8,2 0.9,3.2 0.1,1.4 1.2,2.5 1,1 2,0.9l1.4,-0.4z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="M296.4,231.6s1.7,0.4 1.6,1.8a13,13 0,0 1,-0.8 3.4l-2.4,-2.2 1.3,-1.2 0.5,-0.7z"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="M266.4,214.5s-1.7,0.6 -2.1,-0.1c-0.4,-0.8 -0.2,-1.4 -0.2,-1.5s-1,-0.3 -1,-1.3c0.1,-1 1.4,-0.9 1.6,-0.9s0.4,-1.5 1.3,-1.4c0.8,0 1.2,1 1.2,1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M268.3,215s-1.5,0.8 -2,0c-0.4,-0.7 0,-1.6 -0.1,-1.8 -0.2,-0.1 -1,-0.4 -0.9,-1.3 0.2,-0.9 1.1,-0.6 1.4,-0.8 0.3,-0.1 0.6,-1.4 1.5,-1.2 0.9,0.3 1,1 1,1l-1,4.1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M270.8,215.3s-0.5,1 -1.5,0.8 -0.7,-1.7 -0.9,-2c-0.1,-0.3 -0.9,-0.5 -0.8,-1.4 0.2,-1 1.1,-0.9 1.5,-1s0.7,-1.6 1.4,-1.4 1.3,1.1 1.2,1.5c0,0 -1.5,0.5 -1.7,1.5s0.8,2 0.8,2z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="m293.7,231.2 l-2.2,-2.6c-0.9,-1.3 -2.3,-2.7 -2.3,-2.7l-2.2,-1.1 -1.4,-2.4 -0.2,-2.8 0.6,-1.6 0.8,-1.2s-4.6,1.7 -2.2,10.5c1,3.8 3.5,4.3 3.5,4.3l4.3,-0.3h1.3z"
      android:fillColor="#af7029"/>
  <path
      android:pathData="M291.6,201.7q0.5,0 0.8,0.4l0.2,0.6v0.4c-1.6,3.4 -4.8,5 -7.8,7a14,14 0,0 0,-3.8 4,12 12,0 0,0 -1.6,6.8c0.1,5 2,7.8 4.2,9.3a10,10 0,0 0,5.2 1.5h0.4l1.4,-0.1 3,-0.2 1.8,0.1 0.9,0.4 0.2,0.5q0,0.6 -0.6,1.2l-1,0.7 0.2,0.3 1,-0.8q0.6,-0.6 0.7,-1.4 0,-0.3 -0.3,-0.7t-1.1,-0.5l-1.8,-0.1c-2,0 -4.5,0.3 -4.5,0.3v0.2,-0.2h-0.3a9,9 0,0 1,-5 -1.5c-2.1,-1.4 -4,-4 -4,-9v-0.4q0,-4 1.5,-6.3c1.4,-2.4 3.7,-3.8 6,-5.3a14,14 0,0 0,5.5 -5.7v-0.5q0,-0.4 -0.2,-0.8t-1,-0.5z"
      android:fillColor="#816c2a"/>
  <path
      android:pathData="M297.8,232.7s-1,2 -1.4,2c-0.2,0 1,0.3 1.3,0.8l-0.3,0.5s-1.2,-0.9 -2,-1v-0.6l1.1,-0.7a4,4 0,0 0,0.9 -1.7z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m302,234 l-2.7,0.8 -1.3,-1s0,-1 -0.5,-1.5l-1.6,-1c-0.6,-0.2 -2.5,-0.2 -2.5,-0.2l-5,-5.3 2,0.2 2.8,-0.4 2.7,-0.2 1.4,0.2 1.6,0.4 0.8,0.7 0.8,0.8 0.8,1.3 0.6,1.6 0.2,1.7v1.9z"
      android:fillColor="#fcca3d"/>
  <path
      android:pathData="M302.2,233.8s-1,0.3 -2.2,0.3q-2,-0.1 -2,-0.2l-0.2,0.9q-0.4,1.3 -0.5,1.3v3.2l2.6,0.5 1.4,-0.5 0.7,-3.5z"
      android:fillColor="#af7029"/>
  <path
      android:pathData="M301.5,232.5v1.2l0.6,0.2v-1.2zM299,202.5 L298.7,200.4 297.9,198.9 296.8,197.4 295.6,196.1 295,195.6 294.7,195.1 291.8,195.3 290.4,197s3.8,2.8 5.3,5.8a7,7 0,0 1,2.8 1.7l0.1,-0.2z"
      android:fillColor="#fcca3d"/>
  <path
      android:pathData="M296.3,205q-0.2,3.1 -0.8,3.7l1.8,-2 1.2,-2.2a7,7 0,0 0,-2.8 -1.7q0.6,1 0.6,2.1z"
      android:fillColor="#af7029"/>
  <path
      android:pathData="m300.3,237.9 l-1.4,-1.6q-1.1,-1 -1.3,-1l-0.2,0.4h0.1l1.2,0.8 1.3,1.6z"
      android:fillColor="#816c2a"/>
  <path
      android:pathData="M302,235.5s-0.3,1 -1.3,1.8c-1,1 -2.2,1.5 -2.2,1.5l2.8,1 1.9,-1.7z"
      android:fillColor="#6f5b24"/>
  <path
      android:pathData="m302.4,237.8 l-0.2,0.3 -0.5,0.5 -0.4,0.5 0.6,-3 0.6,1v0.4z"
      android:fillColor="#404118"/>
  <path
      android:pathData="M285.8,199.3s-3.8,0.3 -6.5,-0.1 -2,-7.3 -2.1,-7.5c0,-0.2 -1.6,-1.8 -1.4,-3.3s5.2,-5.5 5.8,-5.8c0.5,-0.1 1.5,0.3 1.5,0.3s1.2,-1.2 1.6,-1.2 0.9,0.7 0.8,1c-0.2,0.3 -1.9,1.3 -2,2 -0.2,0.8 0,1.5 -0.5,2.3s-1.4,1.7 -1.5,2.4 -0.4,1.1 0,1.4 2.1,0 3.5,-0.6 2,-1.5 2.1,-1.4c0.2,0 -0.8,1.7 -2.3,2.4s-2.5,1.3 -3.3,1.2c0,0 -0.8,2 1.8,2.2 2.6,0 4,-0.6 4,-0.6z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#78732e"/>
  <path
      android:pathData="M318.7,170.6s-7,-1.3 -7,4.5c0.1,1.9 0.5,2.5 2,3 1.5,0.3 4.4,0.5 7,2.7s2.7,4 2.7,5.3c0,1.4 -0.7,2.6 -0.7,2.6l1.6,7.7 1.6,5.7 4.8,-2.7 2.8,-8.5 -3.6,-15.2z"
      android:strokeWidth=".4"
      android:fillColor="#ab6d29"
      android:strokeColor="#4d2a15"/>
  <path
      android:pathData="M325.6,200s2.5,-1 3,-3c0.3,-0.5 -2.4,-2.9 -2.4,-2.9 -0.1,0 1.3,0.7 2,1.3q1,1 1.3,0.8c0.2,-0.1 1.3,-2.7 0.9,-3.3s-1.1,-2 -2.1,-2.6 -2.4,-0.6 -2.4,-0.6 1.9,-0.6 2.8,0c1,0.5 1,1 1.1,0.9 0.1,-0.3 -0.2,-2.9 -0.6,-3.7q-0.4,-1.3 -2,-1.6c-1.1,-0.2 -2.3,-0.1 -2.3,-0.1s1.6,-0.9 2.8,-0.6c1.3,0.3 1.3,1 1.4,0.8s-0.6,-2.4 -1,-3a7,7 0,0 0,-2.1 -1.8l-1.8,-0.2h-0.6s1,-0.6 2.1,-0.5 1.3,0.5 1.4,0.4q0.3,0 -0.8,-0.7c-1.1,-0.7 -4.2,-2 -4.2,-2h-3.3s0,-0.8 1,-1q1.3,0 1,-0.1c0,-0.3 -2.6,-0.8 -2.6,-0.8H316l-3,0.3s0.2,-0.5 0.7,-0.9q0.6,-0.5 1.3,-0.6c1,-0.3 -0.6,-1.1 -0.6,-1.1l0.9,-1.3 3.9,0.3 8.2,4.8 3.9,3.3 2.4,6.2 -0.7,10.8 -5.5,4.5 -2.4,0.3z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M318.4,173.3s-0.2,-0.7 -0.2,-1.7 0.2,-0.9 0.2,-0.9 -2.7,-0.3 -4.6,1.2 -0.8,4.1 -0.8,4.1 0.8,-0.8 1.9,-1.2 3,-0.3 3,-0.3c0.2,-0.2 -0.4,-1 -1.1,-1.1s-2.1,-0.2 -2.1,-0.2 0.8,-0.5 2.2,-0.4 1.6,0.5 1.6,0.5z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="m326.7,200.9 l-1.8,0.6 1,7.5 7.2,12.1 3.7,-0.6 1.3,-2.3 1,0.5c0.4,0.4 1.8,2.8 3,3.3s1.7,0.2 2.7,1c1,1 4.9,6.6 5,6.8 0.3,0.2 -0.1,-2.4 -0.1,-2.4l-1.2,-3.4 -1.4,-2.4 -0.3,-0.6h2l1.2,0.8 2,1.5 0.9,1.2 0.5,1.2 0.2,1 0.5,1.6 1,2.3 1.3,2.1 0.4,-0.9 -0.6,-2.5 -2,-6 -1,-2 1.9,1.2c0.4,0.1 1.8,2.2 1.8,2.2l0.9,2.2 0.6,2 0.4,1.7 0.7,1.9 1,-0.7 -0.1,-1.3 -1,-4.2 -0.4,-4.5s0.6,0 1.2,0.7l1.2,2.3 0.5,4.1 0.7,3.8 1.2,3.8 1.2,2.4 0.9,1.3v-2l-1.6,-16 0.2,-0.6s0.2,-0.1 0.8,0.7c0.5,0.7 1,1.9 1,1.9l0.5,2.5 0.2,1.7 0.3,3.3 0.5,3.5 1.6,3.8 0.9,2 0.5,-2.4 -0.8,-17.1 0.4,-0.5s0.9,0.7 1.2,1.3l0.8,1 0.6,3.8 0.4,10.7 0.4,2.8 3.2,-28.2 2,-0.4 0.2,-0.6 -1.5,-1 -3.2,-5 -2.1,-4.9 -3.8,-7 -6.6,-8 -5.2,-4.3 -1.9,-1.3 0.4,-1 1,-1.7 -0.1,-0.1h-0.9l-2.2,0.4 -2.2,-0.7 -4.4,-3 -4.6,-2.3 -2.8,-1 -6.6,-1.6 -6.6,-1h-3l-2.6,0.5s-1.4,0.6 -1.8,1.5 -0.5,1.4 -0.4,2.6c0.2,1.3 1,1.9 2.8,2.7 1.8,0.7 6.2,3.6 6.2,3.6l2,2.1 0.8,1 0.2,0.8 1.4,11.1 -2.8,6z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M333.8,218.2s1.2,2 2.9,0.5c1.7,-1.6 -0.8,-6 -0.7,-6.3 0,-0.3 0.7,-0.3 0.7,-0.3l1.3,2.2s0.7,1.3 0.4,3.2 -2.1,3.7 -2.1,3.7l-2.2,-0.5z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="m375.3,244.2 l-1.6,-4.5 -0.4,-3.5c0,-0.6 -0.3,-7.9 -0.5,-9.3 0,-1.4 0,-3 -0.2,-3.1l-1.6,-2.2 -0.7,-0.7 0.5,-0.3s0.6,0.6 0.7,0.3q0.3,-0.7 -0.6,-4.5c-0.7,-2.5 -3.9,-8.3 -3.9,-8.3l0.4,0.2 0.9,1 1.8,1.5 4.1,1.5 2.2,0.3 1.4,2.9s1,4.9 1,7.4q0.2,3.4 -1.2,7c-1,2.4 -1.2,10.3 -1.3,11.5 -0.1,1.3 -0.6,2.5 -0.6,2.5l-0.5,0.3z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M375.3,231.2s0.8,-1 1.2,-2.4 0.5,-12.4 -0.8,-13.7a10,10 0,0 0,-4 -2.3c-0.2,0 1,3.5 1,3.5l0.8,1.4 0.6,1.8c0.1,1 0.9,3.1 1,6z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M369.3,238s0.5,-5.1 0.3,-9c-0.3,-3.7 -0.6,-4.2 -1,-4.9a32,32 0,0 1,-2.5 -6.3c-0.9,-3.2 -1.5,-5.6 -2,-6.2s-1.7,-2 -2,-1.6 -0.3,1.1 -0.3,1.1l-1.5,-0.4 -0.4,-1 0.5,-1.6v-0.7l-0.3,-1.1 2,1.6 4.2,2.8 2,8.6 2,5.3 0.2,9 -0.2,8 -1,-3.5zM371.7,213.3s1,1 1.7,1.4 1.8,1.1 1.6,1.2l-2,-0.8 -1.2,-0.8v-1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M363.7,236s0.8,-1.8 0.7,-4.1a49,49 0,0 0,-1.6 -8.6c-0.4,-0.7 -2,-1.7 -3.5,-4.8s-1.3,-3.8 -2.3,-5c-1,-1 -2,-2.4 -2,-2.4l0.3,-3.3 3.6,1.8 1.6,0.8 1.8,8.7 1.6,2.5 0.7,3.5 0.9,8 0.4,6.6 -0.2,0.5zM359.5,232.3s0.1,-2.5 -0.5,-4.8q-0.9,-3.5 -1.9,-4.8c-0.7,-0.7 -2.5,-2.6 -4.1,-5 -1.6,-2.5 -3.9,-5 -3.9,-5v-3.3l2,1.5 2.4,1.2 1,3.7 1.2,2.7 1.8,2.4 1.2,1.1 0.7,4 0.4,2.3 1,3.2 -0.4,1.7zM356.5,232.3 L355.5,231.4s0.6,-0.6 0.3,-2q-0.4,-1.8 -1.2,-3.7 -0.7,-1.8 -1,-2.3l-4.5,-4c-1,-1 -2.7,-2.6 -3.7,-3.3q-1.7,-0.8 -1.8,-0.8l-0.5,-4s1.3,1.8 2.1,2l2.1,0.8 2.8,5 1,1.3 2.6,2 1,2.2 0.9,2.5 1,3.4 0.1,2zM351,231.3s-1.7,-2 -2,-3.2c-0.2,-1.2 0.2,-1.2 -0.3,-2.6s-2.3,-3.5 -3.7,-4.6c-1.4,-1.2 -3.4,-2.8 -3.3,-3.8q0.3,-1.2 0.4,-1l0.8,0.8 4.5,4.7 1.4,2.5 1,3.1 0.7,1.5 0.7,1.8zM367,208s3.1,3.8 4,0.3c0.3,-1.6 -1.2,-4.6 -1.2,-4.6s-2.8,-5 -3.8,-6.2l-5.7,-7.3 -3.5,-1.6 1.2,2s2.6,2.6 5.9,8.4c3.3,5.7 2.7,8.9 2.7,8.9zM349.2,185s6.1,5.2 10,10.8 5.5,9.4 4.7,10.2c-1,0.7 -3.4,-0.4 -4.4,-2s-1.8,-4.4 -5.5,-8.6 -5.5,-4.8 -5.5,-4.8l-2.7,-4.3zM346,197.5s3.6,3 6,5.4l3.4,4.2 1.4,1.4s-0.9,-1.5 -0.4,-2 1.1,-0.3 1.1,-0.3l-11.5,-12zM345.3,197.8s1.2,2.3 3,4.1l4.1,4 0.8,2.9s-5.2,-4 -5.2,-3 1.2,3 1.2,3l-1.6,-1 -2.2,-3.1 -1.3,-2.8 -1.5,-3.8z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M329.5,210.2s2.7,2.4 4.2,0c1.5,-2.3 0.3,-5 0.3,-5s3.9,5.6 5,0.3c0.2,-1 -2.4,-4.6 -2.4,-4.6l1,-0.9 1.9,3.3s1.8,-0.8 1.8,-3 -1.9,-5.3 -1.9,-5.3l1.8,2s3.6,0 3.7,-2c0,-2.2 -3,-4.5 -3,-4.5s2.4,0.1 2.4,-1.3 -3.4,-2.4 -3.4,-3.3 1,-2.4 2.5,-1.5 3.5,1.4 4.4,-0.2c0.8,-1.6 -0.1,-2 -0.1,-2l-2.1,-0.4 -3.4,-1.8 -2.7,-1 -2.8,-0.4 1,2 0.8,5.8v2.6l0.5,5.8 -1.4,4.7 -0.7,0.9 -0.5,0.6 -1.2,2.6 -1.6,1.8 -2.2,2.7z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M348.8,220.9s-0.7,-1.7 -2.2,-2.9a8,8 0,0 0,-3.8 -1.2l4,4.2zM354.4,221.9s-1.3,-2.6 -2.8,-4.2 -2.5,-3.6 -3,-3.6h-0.6l0.4,3.3 2.7,2.7s2.6,2 3.3,1.8m4.6,-0.2s-0.6,-3 -2.2,-6c-1.5,-2.8 -3.1,-4 -3.1,-4l0.4,3 1.2,3 2,3zM362.8,220s1,-6.2 0,-8c-0.9,-1.6 -2.7,-2 -2.7,-2l-0.6,1 0.6,4.3 1.5,3.4zM366.5,209.7s0.9,0.4 2,2.5a40,40 0,0 1,2.6 8.6c0,0.3 -0.5,0.2 -0.7,0.2 -0.2,-0.1 -3.9,-6 -3.9,-6l-0.6,-5.4z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M340.4,216.8s2,-0.4 2,-1c0,-0.8 -2.4,-2.2 -2.2,-3.3 0,-0.5 2,0.1 2.1,-0.3s-2,-3.6 -1.3,-4.4 2.4,3.5 2.4,3.5 1,2 0.7,3.2c-1.2,3.6 -3.7,2.3 -3.7,2.3m4.6,-4.4c0.1,-0.3 3.1,0.4 3.1,0.2 0.2,-0.7 -2.7,-3 -2.7,-3s-0.6,-1.2 -0.4,-1.4 2,0.2 2,0c0.2,-0.3 -2,-3.2 -2,-3.7s0.6,0 0.6,0 1.7,1.7 2.6,3.2c1,1.4 1.8,2.6 1.5,4.9 -0.4,2.5 -5,0.6 -4.7,-0.2m13.6,-4.6s-0.5,-2 -0.2,-2.3l1.1,-0.5s-1.6,-1 -2.4,-1q-0.9,-0.2 -1,0c-0.2,0.4 1.3,2.4 1.3,2.4z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M349.4,209.4s4.7,1.2 4.9,0.8 -3.7,-4 -3.7,-4 -0.3,-0.6 -0.2,-0.8 1.7,-0.3 2.2,0.1 0,1.2 0.1,1.5c0.2,0.3 1.7,2.5 2,2.1 0.2,-0.3 -0.4,-3 -0.3,-3.3s1.8,1.6 2.3,1.9 3,2.3 3.3,1.4c0.3,-1 0.4,-1.1 0.3,-1.7 0,-0.6 0.9,2.6 -0.3,3s-2.6,-0.4 -2.6,-0.4l-1.1,-0.8 -0.6,-0.7s0.7,2.5 -0.2,2.7 -1.7,0.2 -1.7,0.2l-2,-0.2zM360.1,206.2s5,3 5.4,2.5 -1.2,-7.5 -1,-8.2c0.3,-0.6 4,7.9 2.2,9 -1.4,1 -2,0.3 -2,0.3l-2.5,-1.3zM373.9,218.8 L374.9,219.3 375.6,219.5 374.2,219.7 373.9,218.7zM369.1,202.4 L369.8,202.7 370.5,203s-0.4,0.3 -0.6,0.2h-0.4zM367.7,199.9s0.5,0.3 1.1,-0.2q0.9,-0.7 0.6,-1c0,-0.2 -1,-0.2 -1.4,-0.5q-0.4,-0.4 -0.3,-0.6h-1.1l-0.4,0.1zM372,214.3s1.5,1 2,1 1.1,0.5 1,0.6c0,0.1 -1.2,0.2 -1.8,-0.1q-0.8,-0.3 -0.7,-0.1zM363.9,191.8h0.6q0.5,-0.1 0.6,0c0.2,0.2 0,1.2 -0.3,1.5l-1,0.5 -1.6,-2.2zM356.8,185.4h1.5q0.8,0 1.5,0.6c0.4,0.3 0.9,3.3 0.6,3.8 -0.2,0.6 -3.6,-2 -3.7,-2.1 0,-0.3 -1.5,-2.5 -1.5,-2.5zM353.6,184s2.5,0.2 2.2,-1.3a3,3 0,0 0,-1.3 -2.3l-2.9,2.1zM347.1,181.2s0.7,0.4 1.4,0c0.7,-0.2 2.6,-2.7 4,-3s1.7,-0.3 2,0c0.2,0.4 0,1.8 -0.2,2.2s-4.4,2 -4.4,2l-1.5,-0.1 -1.3,-0.5zM346.4,176.7c-0.7,-1.4 -1.7,-2 -2.2,-1.9 -0.4,0.1 1.4,1.5 1.7,3.5q0.3,3.1 0.2,3.4l0.8,0.1 0.2,-0.7 0.1,-0.5v-0.5l-0.1,-1 -0.2,-0.9 -0.2,-0.6zM340.6,174.7a15,15 0,0 0,-5.5 -3.7c-0.2,0 4,2.5 5,4 0.9,1.7 0.9,3.9 1.3,4.2s0.8,-0.2 0.9,-0.2 0.3,-0.5 0.2,-0.7l-0.2,-1 -0.7,-1 -0.4,-0.8zM335.1,172.4c-2.4,-2 -6.6,-3 -10,-3 -3.5,-0.2 -5.3,0.2 -4.8,2s2,2.8 5,2.8c2.9,0 3.6,-0.3 5.2,0.2a10,10 0,0 1,3.6 2c0.5,0.5 -1.1,-1.5 -2.6,-2.6q-2.4,-1.7 -2.5,-1.7c-0.1,0 -0.3,-0.7 1.7,-0.4s4.7,1.6 5.5,3.3 0.6,2.6 0.6,3 0.7,-0.2 0.7,-0.2l0.3,-0.9s0,-0.6 -0.3,-1.4c-0.4,-0.7 -0.5,-1 -1.4,-2 -0.9,-1.1 -1,-1 -1,-1zM325.5,200.9c1.5,0 4.8,-2.8 5.2,-6.7s-1.9,-11.9 -2.5,-12.7c-0.5,-0.9 1,-0.4 1.7,0.4a25,25 0,0 1,2.5 10.6c0,4.2 -0.8,5.3 -3,7.5 -2.2,2 -4,1.8 -4,1.8zM330.6,212.5s0.7,1 1.6,0.6c1.5,-0.5 3.8,-1.7 3.8,-3.8 0,-1.4 -1.8,-2.7 -1.9,-3.2v-1l1.6,0.4 0.5,2.2s0.6,0.7 1.3,0.4c0.6,-0.2 2.5,-1.6 2.2,-3 -0.3,-1.2 -2,-2.9 -2.2,-3.5q-0.1,-1.1 0,-1.2l1.6,0.2 0.4,2s0.4,0.6 1.3,0.4 2.4,-1.7 2,-2.5l-1.5,-2.6v-0.9l0.5,0.4s0.6,0.2 1.8,-0.1c1.1,-0.4 2.3,-1.2 2.3,-2s-1,-1.6 -1,-2v-0.6h0.8l1,1.2 0.1,1.4 -0.3,1.2 -1.4,1.6 -1.4,0.6h-0.4l0.8,1.4 -0.2,2 -1.3,1.2 -1.7,1 -0.1,1.9 -1,2 -2,1.2h-0.7l-0.2,1.2 -1.4,2 -2.3,1.5 -1.7,0.3 -1,-1.4z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M343.4,191.4s4,1.1 3.8,-1.4c0,-1.3 -3.8,-4.5 -4,-5s-0.2,-1.3 -0.2,-1.3l1.9,1.8s2.5,0.6 3.2,-0.9c0.7,-1.4 0.1,-2.3 0,-2.3l0.6,-0.1 1.1,0.2 -0.1,2.2 -1.3,5.5s0.3,2.2 -1.4,2.3c-1.7,0.2 -3.6,-1 -3.6,-1m3.7,29.5s0,-1.1 -0.8,-2c-0.9,-0.7 -2.5,-1.4 -2.8,-1.6l2.8,3.5zM352.7,221.1s-2,-2.4 -2.3,-3.3c-0.5,-0.8 -1.5,-2.6 -2.3,-2.7 -0.8,0 0.8,3 0.8,3l3.2,2.9zM358.1,221.1 L356,216.9c-0.4,-0.9 -1.3,-3.4 -2.3,-4.1s0.1,1.1 0.1,1.1l1.1,3.3 1.9,2.9zM362,218.7s-0.1,-2.7 -0.5,-4.6c-0.3,-1.8 -1.7,-3.4 -1.8,-3.3v3.3zM370.1,220.7 L368.1,216c-0.5,-1.6 -1.8,-4.1 -2,-4.2l0.4,3 1.7,4.7zM377.1,212.2s-1.2,-1.2 -3.9,-2.3 -5.4,-1.2 -5.4,-1.2l0.3,0.4 1.3,1 2.8,1.5 3.7,0.9 1.3,-0.3z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M348.2,213.8s0,2.4 1.3,3.8c1.2,1.5 4,4 4.6,4.6s1,3.4 1.9,5.8 1,4.4 1.4,5.1 1.1,1.8 0.5,1.7q-0.8,-0.2 -1.4,-1.7c-0.3,-0.9 -0.2,-0.9 -0.2,-2 0,-1.3 -1.8,-7.5 -2.7,-8.1 -0.9,-0.7 -4.8,-4 -6.4,-6.3 -1.5,-2.3 -1.2,-2.8 -1.2,-2.8z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M354.6,229.3q-1,-2.7 -1.2,-4.3a16,16 0,0 0,-4.4 -4.2c-0.6,-0.3 -1.9,0 -2.4,-0.3l-3.7,-4s-0.5,0.5 -0.3,0.6c0.1,0.1 3.4,3.7 4.6,4.8 1.2,1.2 1.9,3.7 2.7,5.7s0.5,1.6 0.6,2.5q0.3,1.5 1.3,1.5t0.6,-0.4a10,10 0,0 1,-2.2 -3.5c-0.7,-1.7 -1.5,-4.9 -2.5,-6l-0.3,-0.3 1.3,-0.2c0.8,0 4.1,3 4.4,4 0.3,0.8 0,1.2 1.4,4.2s2,3.7 2,3.7h0.2s-1.2,-2 -2,-3.8zM349.4,209.4s2.5,1.5 3.2,1.7l1.2,0.3s0.4,4.4 2.1,6.8c1.8,2.5 3.2,3.4 3.2,3.4s0,4.8 1.4,8 1.5,7.3 1.2,8c-0.3,0.6 -1.4,-2 -2,-4.3l-2,-6.2a13,13 0,0 0,-2.5 -4.4c-0.8,-0.7 -0.2,-0.6 0.3,-0.2s1.7,2 2.5,4.5 1.1,5 1.5,5q0.8,0 0.8,-0.9c0,-0.4 -0.6,-1.8 -1,-4.1a17,17 0,0 0,-1.2 -4.7c-0.4,-0.6 -1,-0.7 -2.4,-3.4a20,20 0,0 1,-2.5 -5.3c-0.2,-1.2 0,-1 -0.2,-1.3 -0.2,-0.1 -0.4,0 -1.6,-0.6 -1.1,-0.6 -2,-2.3 -2,-2.3"
      android:fillColor="#202020"/>
  <path
      android:pathData="M355.7,208.5s1.4,1.2 2.3,1.5 2,0.4 2,0.4 -0.3,4.2 1.3,6.9 3.3,4.2 3.3,4.2 0.3,1 0.8,6.8l0.7,8.3c0.1,2.3 0.2,10.6 -0.1,10.3 -1.3,-1 -0.2,-5.4 -0.9,-7.3s-1.6,-2.1 -2.8,-7.4c-1.2,-5.2 -0.4,-6.4 -1.3,-8.3s-2,-2.2 -2,-2.2 1.6,0.4 2.3,2 0.7,6 1.3,8.2c1.2,4.9 2.9,7.4 2.9,7.4s-0.4,-6.2 -0.8,-9.7 -0.5,-6.4 -1.1,-7.7c-0.7,-1.4 -2.1,-2.4 -3,-4.2s-1.1,-5.3 -1.5,-6.2 -0.6,-0.6 -1.2,-0.8c-2,-0.8 -2.2,-2.2 -2.2,-2.2"
      android:fillColor="#202020"/>
  <path
      android:pathData="M360.1,206.2s1.3,1.5 3.1,2.6c1.8,1 2.8,0.7 3,1 0.3,0.2 -0.3,3.8 1.6,7.2s2.6,3.5 2.6,4l0.6,13.9c0.3,3.4 0.5,14.8 -0.9,14.8s-0.1,-8.5 -0.8,-10.4 -0.7,-1.4 -1.3,-3 -0.7,-7.8 -1.8,-11.5c-0.8,-2.8 -1.6,-3.2 -1.6,-3.2s1.4,0.7 2,3 0.8,10 1.5,11.1c0.8,1.3 1.8,2.8 2,4a157,157 0,0 0,-0.4 -15.6c-0.3,-1 -3.5,-6.6 -3.7,-9.7 -0.3,-3.2 -0.4,-3.5 -0.7,-3.7s-1.1,0 -2.6,-1.5c-1.5,-1.4 -2.6,-3 -2.6,-3"
      android:fillColor="#202020"/>
  <path
      android:pathData="M367.3,208c0,-0.3 1,1.7 3.7,2.8 2.9,1.2 5.6,1 6,1.3 0.3,0.3 2,4.5 2.1,10.2s-1,5.1 -1.8,10 -1,14.8 -1.5,16.3c-0.2,0.5 -0.5,0.8 -0.8,1.7 -0.3,1 0.2,2.8 -0.7,2.8s-1,-1.4 -1,-2.1 1,-3.5 1,-6.2 -0.6,-3 -1,-6.2 -0.4,-13.5 -0.8,-14.5c-0.3,-1 -2.1,-3.1 -2.1,-3.1s1.9,1.2 2.4,2.6c0.4,1.3 0.5,12.7 1,15s0.3,-0.5 0.5,-1.8 0.9,-7 0.8,-10 -1.4,-9.4 -2.4,-10.5q0.1,0 1.1,2 0.1,0.3 0.8,0.7l1,0.5s-0.6,0 -1,-0.3q-0.6,-0.3 -0.6,-0.4a24,24 0,0 1,1.3 8c0.1,5.2 -0.7,16.4 0.1,16.5s0.7,-6 1.5,-11.2c0.8,-5 2,-5.8 1.6,-9.9s-1.4,-8.6 -2.3,-9.2 -3.4,-0.3 -5.6,-1.8c-2.2,-1.4 -3.4,-2.9 -3.3,-3.2m-35.8,5.7s0.8,0.4 1.7,0c1,-0.5 3.5,-2.5 3.5,-3.3v-1.6s0,0.3 0.7,0.4c0.8,0.1 2.8,-1.4 3,-2.4a8,8 0,0 0,-0.1 -3.1c-0.2,-0.4 0.6,0.3 1,0 0.4,-0.4 2.4,-1.7 2.4,-3s-1.1,-2.6 -1.1,-2.6 1,0.2 1.6,0c0.5,-0.1 2.5,-1.3 2.4,-2.4 0,-1.2 -0.7,-3 -2.3,-3.2 0,0 1.2,0.3 2.3,-0.1q1.5,-0.5 1.5,-2.3c0,-1.2 -2.3,-4 -2.6,-4.2s2.4,0.8 3,-0.3q1.1,-1.6 0.7,-2.5c-0.2,-0.7 -0.8,-0.8 -0.8,-0.8s1.2,0.3 2.8,-0.5 3,-2 3.8,-1.9c0.8,0 -2.3,1.7 -2.5,2.6q0.1,0.6 2.3,2 0,0.1 0.2,0.3c0.7,0.3 3,0.4 3.3,0.5 0.2,0.2 -2.4,0.1 -2.2,0.3a34,34 0,0 1,5.7 5.4q0.2,0.2 1,0.5l1.3,0.3h-1.6l3.3,4.7q0,0.3 0.9,0.8 0.9,0.2 1.1,0.2c0.1,0.2 -1.4,0 -1.3,0.2l3.3,6s-3,-5.3 -6.7,-9.5 -4.7,-4.7 -4.8,-4.4c-0.2,0.4 2.7,4.1 4.3,6.7s3,5.9 3,5.9 -3.7,-7.8 -9,-12.6 -5.8,-5.5 -6.3,-5.4c-0.6,0.2 -1,3.8 -0.7,4.5s4.2,3.6 7.4,7.5c3.3,3.8 7.5,10.4 7.5,10.4s-3,-4.7 -8,-10 -6.9,-6.6 -7.3,-6.5 -0.4,1 -1,1.8c-0.6,0.7 -1.8,0.6 -1.8,1s4.2,4.3 5,4.7c0.9,0.5 1.4,0.3 2,0.6l1.4,1 -1.6,-0.5q-1.1,-0.4 -1.3,0a21,21 0,0 0,4 4.6c0.6,0.3 1,0.1 1.7,0.4 0.8,0.3 2,1.1 2,1.1s-1.3,-0.7 -2.1,-0.8q-1.3,-0.2 -1.2,0.2c0.1,0.4 2.6,3.6 2.5,3.6s-4,-4.6 -7.3,-7.5 -4.8,-3.6 -5,-3.5 0,1 -0.9,1.3q-1.4,0.3 -1.5,0.7c0,0.3 3,3 5,5.4l4,4.5 -4.5,-4.3q-3.4,-3.1 -3.8,-3c-0.2,0.2 -0.2,1.3 0,1.6s4.6,5.4 4.4,5.5c-0.1,0.2 -5,-6 -5.4,-6 -0.3,0 -0.5,0.7 -0.8,1s-1.8,0.4 -1.8,1c0,0.4 7,7.3 6.9,7.5 0,0 -5.8,-5 -6,-4.7s2.4,5 2.2,5.2c-0.1,0 -3.4,-5 -3.7,-5s-0.2,0.6 -1,1.3c-1,0.8 -2,0.4 -2,0.8 0,0.3 4.8,5.7 4.7,5.8 -0.2,0.1 -5,-5.1 -5,-4.8q-0.3,0 -0.2,0.7c0.3,1.3 1.4,3.6 1.3,3.8 0,0 -1.6,-3.2 -2,-3 -0.4,0 -0.4,0.7 -1.2,1.1s-1.3,0.4 -1.3,0.7 0.8,1 1.2,1.7 0.7,2.4 0.7,2.4 -0.5,-1.4 -1.1,-2.3q-1,-1.4 -1.9,-1.5c-0.9,-0.1 -0.8,0.5 -0.8,0.5z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M377,212s1.5,0 1.7,-0.4c0.2,-0.3 -0.6,0 -2,-1.6s-5,-12.1 -9,-17.4c-6.6,-8.7 -12.7,-12.7 -12.7,-12.7s7.3,4.2 13,12.4c5.6,8.4 7,14.9 8.6,16.6s3.2,2 3,2.5q0,0.7 -0.9,1l-2,0.1zM329,209.8 L329.3,209.9c0.3,0 2.3,-1.9 2.2,-2.5 0,0 -1,-0.3 -1.6,-1 -0.7,-0.8 -0.9,-1.9 -0.9,-1.9s0.7,1.3 1.2,1.6q1,0.7 1.8,0.3c0.3,-0.2 3.2,-2.4 3,-3.2 0,0 -0.9,0 -1.8,-0.6s-1,-2 -1,-2 0.6,1.1 1.5,1.5q1.4,0.5 2,0c0.2,-0.3 2,-3 1.8,-3.7 0,0 -1,0 -1.8,-0.5s-1.3,-2.2 -1.3,-2.2 0.6,1.3 1.7,1.5c1.1,0.3 2,-0.4 2.2,-1.2s0.8,-3.7 -0.2,-4.5c0,0 -1.4,0.5 -2.4,0s-1.4,-1.9 -1.4,-1.9 0.9,1.2 2,1.4q1.7,0 1.9,-1a6,6 0,0 0,-0.1 -3c-0.4,-1 -1.1,-0.8 -1.3,-2.2 0,0 -0.3,0.5 -1.8,0.5s-2.3,-1.6 -2.3,-1.6 1.2,1 2.4,1a2,2 0,0 0,1.6 -1.9c0,-0.7 -0.6,-1.4 -1,-2.1 -0.2,-0.8 -0.3,-1.7 -0.5,-1.6s-0.5,0.8 -2,0.6 -1.8,-1.3 -1.8,-1.3 1,0.7 2,0.7q1.3,-0.1 1.3,-1c0,-0.6 -0.3,-1.7 -2.2,-3.3l-3.5,-2.7a22,22 0,0 0,6.6 5.3s1.7,0.6 2,0q0.3,-0.9 -0.5,-2.4c-0.5,-1 -2,-2.6 -2,-2.6s1.6,1.3 2.3,2.7c0.8,1.3 0.4,2.2 0.5,2.6s3.3,1.6 4,1.1q1,-0.8 -0.2,-2.3l-1,-1.8s0.7,0.8 1.2,1.8q1,1.5 0.8,2.1 -0.4,0.7 -0.6,0.9l2.3,1.3c1,0.6 2.3,1 2.5,0.8q0.3,-0.6 0.2,-2.2c-0.1,-1 -0.7,-2.7 -0.7,-2.7s0.7,1.6 0.8,2.7 -0.1,2 0.1,2.4 1.1,0.5 1.1,0.5 -0.6,0.4 -2,0c-1.5,-0.4 -7.7,-3.6 -8,-3.3s2.9,2.3 4.3,3.7c1.5,1.3 2.2,2.8 2.2,2.8s-1.1,-1 -2.2,-1.6 -4.4,-2.3 -4.7,-2 1.3,0.5 1.4,0.7c0.1,0.3 -1,1.3 -1,1.8s4.7,3.7 4.5,4c-0.1,0.4 -1.2,1.3 -1.4,1.2 -0.1,0 -2.5,-1.8 -2.5,-1.6s1.4,1.5 1.4,1.7c0,0.3 -1,0.8 -0.8,1.4s2.6,2.6 2.6,3.3 -1.4,1.3 -1.1,1.7l1.5,2s-1.4,-0.9 -1.8,-1.4 -1.3,-1.5 -1.5,-1.3 -1,2 -1,2.5c0.2,0.4 1.3,1 1.5,2.3 0.2,1.1 -0.2,2.4 -0.3,2.4s-1.6,-2 -2,-2.1q-0.4,0 -0.9,0.6c-0.5,0.6 -1.2,2.1 -1,2.4 0,0.3 1.6,0.7 1.6,1.9a3,3 0,0 1,-1 2.3l-2.1,-2.4c-0.2,0 -2.2,2.3 -2.3,2.6 0,0.2 1.4,1.3 1.2,1.7 -0.3,0.4 -1.3,-0.2 -1.8,0.1l-1.7,0.7c-0.2,0 -0.5,-0.6 -0.5,-0.6"
      android:fillColor="#202020"/>
  <path
      android:pathData="m355,180 l0.5,-1c0.3,-0.3 1.5,-1.7 1,-2 -0.4,-0.2 -2,0.3 -3,0.2s-1.7,0.2 -4.6,-2.3c0,0 -9.3,-6.4 -23.6,-7.3 -4.6,-0.2 -6.6,1.4 -6.8,2.2 0,0.2 1.2,-1.8 5.7,-1.7 2,0 16.5,1.1 24,7 0.4,0.3 2.9,2.7 5.2,2.4 2.3,-0.2 2.8,-0.5 2.7,-0.2 0,0.4 -0.7,1.2 -0.9,1.6l-0.3,1.1zM330,182v-0.2c-0.2,-0.4 -1.1,-1.7 -2.7,-3.1a37,37 0,0 0,-8 -4.3c-0.3,-0.1 6,2.9 7.7,4.5z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M330,192.2s-0.4,-0.6 -1.7,-1.4l-2.5,-1.1 3,0.7a5,5 0,0 1,1.1 1.8zM329.2,186.9s-0.4,-0.6 -2.1,-1.2 -2.2,-0.5 -2.2,-0.5 1.6,-0.7 2.8,0a3,3 0,0 1,1.5 1.6zM328.2,182.4s-0.4,-0.8 -2,-1.3q-2.4,-0.8 -2.6,-0.7c-0.2,0.1 1.6,-0.4 3.2,0.2 1.5,0.6 1.4,1.8 1.4,1.8m-2,-2.8s-0.4,-0.6 -3.3,-1.3c-3,-0.7 -4,-0.7 -4,-0.7s4.1,-0.4 5.2,0.1 2.2,1.9 2.2,1.9zM320.9,176.6s-1.8,-0.6 -3.5,-0.7 -4.4,0.1 -4.4,0.1 5,-0.8 6.3,-0.5c1.4,0.3 1.6,1 1.6,1z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M304.5,238.4s0.9,1.6 2.6,1.6c1.8,0.1 3.1,2.6 3.1,2.6l-1.9,1.9 -4.6,0.2 -1.5,-2.4zM304.5,236.8c0.2,-0.3 -1.3,-3.3 -2.4,-3.8s0.4,1 0.2,1.6 -0.4,1.4 -0.3,1.5c0,0 0.9,1.4 0.4,2 -0.4,0.5 2.2,-1.3 2.2,-1.3z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M303.8,241.4s0.8,0.4 1.4,0.2c0.7,-0.2 1.3,-0.9 1.3,-0.9m-3.6,-0.5s2.6,-1.9 2.4,-2.9 -0.7,-1 -1,-0.9a7,7 0,0 0,-2.1 1.7c-0.1,0.3 0.7,2.1 0.7,2.1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M301,241.5s0.6,1.3 1.4,1.3 2,-0.7 2,-1.2c0.1,-0.5 -0.8,-1.5 -1.3,-1.7 -0.6,-0.3 -1.9,0.4 -1.9,0.4z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M303.4,239.8c0,0.4 -1.7,1.2 -2.3,1.2s-1.6,-1.3 -1.2,-1.5l1.5,-0.4c0.4,-0.2 0.5,-0.8 0.7,-0.8 0.3,0 1,0.1 1.1,0.5q0.4,0.6 0.2,1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M301.8,240.9s-2.6,3.4 -3.7,3.3c-1,-0.2 0.5,-5 0.5,-5l1.2,0.6q1.4,0.3 2,1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M297.2,238.4s3.3,0.6 3.4,1.1 -1.5,1.3 -2.5,1.3 -0.9,-2.4 -0.9,-2.4z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M289,243.4s0.5,1.6 1.9,1.7 2.8,-1.5 2.8,-1.5l-3.4,-1.3z"
      android:fillColor="#202220"/>
  <path
      android:pathData="m289,243.4 l0.4,0.9q0.4,0.7 1.5,0.9h0.1c1.4,0 2.7,-1.6 2.7,-1.6l-3.4,-1.4zl1.3,-1 3.3,1.3v-0.1l-0.2,0.2c-0.4,0.4 -1.4,1.2 -2.4,1.2h-0.1a2,2 0,0 1,-1.4 -0.8l-0.3,-0.6v-0.1l-0.1,-0.1z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="M290,243s0.5,1.2 2.4,1.2 3.3,-1.5 3.3,-1.5l-3,-2.3z"
      android:fillColor="#202220"/>
  <path
      android:pathData="M290,243s0.5,1.2 2.4,1.3c2,0 3.4,-1.5 3.4,-1.5l-3,-2.5 -3,2.7zl2.8,-2.5 2.9,2.3v-0.1a5,5 0,0 1,-3.2 1.4,3 3,0 0,1 -2,-0.6l-0.3,-0.4z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="M290.2,243.3s0.9,0.6 1.8,0.7 2.2,-0.3 2.2,-0.3l-1.1,0.4 -0.8,0.1 -0.8,-0.1 -0.6,-0.3zv-0.2z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="M292.4,241.6s0.8,2 2.6,1.8c1.7,-0.2 1.7,-1.2 1.7,-1.2l-2,-2.8z"
      android:fillColor="#202220"/>
  <path
      android:pathData="m292.4,241.6 l0.5,1q0.6,0.8 1.8,1l0.3,-0.1q1.2,-0.2 1.5,-0.7t0.3,-0.6l-2,-3zl2.4,-2.1 1.9,2.7c0,0.2 -0.3,1 -1.8,1.1h-0.2a2,2 0,0 1,-1.7 -0.8l-0.4,-0.6v-0.2z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="M292.6,242s1,1.3 2,1.3a3,3 0,0 0,1.5 -0.4v0.1l-0.5,0.3 -0.8,0.1h-0.8l-0.7,-0.5zM289.4,244s0.8,0.9 1.5,0.9a5,5 0,0 0,1.9 -0.6h0.1l-0.2,0.1 -0.8,0.4 -0.9,0.3 -0.7,-0.1 -0.6,-0.4 -0.3,-0.4z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="M297,238.3c0.8,-0.2 2.4,2 2.4,3.2s-0.5,2.7 -1.3,2.7 -2.6,-1.7 -3,-2.5q-0.8,-1.2 -0.5,-1.9c0.3,-0.4 2.4,-1.5 2.4,-1.5zM289,238.5s-1.4,-1 -1.9,-0.5 -0.3,2.3 0,2.6c0.4,0.4 2,-0.8 2,-0.8z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M288.8,239.7s-1.8,0.2 -1.8,1 0.9,1.6 1,1.8c0.2,0.1 1.8,-0.7 1.8,-0.7z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M289.5,241.1s-1.8,0.6 -1.8,1.2 0.8,1.7 1.3,1.7 1,-1 1.2,-1.3c0.3,-0.2 -0.7,-1.6 -0.7,-1.6zM290.3,236s-0.3,-0.5 -1,-0.2c-0.8,0.3 -1.6,1 -1.5,2.3 0.1,1.1 0.6,2 0.6,2l2.7,-1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M290.5,238s-2.2,0.8 -2.3,1.7q0,1.4 1,2c0.7,0 2.6,-1.8 2.6,-1.8z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M292.5,241.7s-2.2,1.5 -2.5,1.5 -1.2,-1.2 -1,-2 1.7,-1.3 2.3,-1.6c0.6,-0.2 1.2,2.1 1.2,2.1zM294.5,233.5s-0.7,-0.6 -2,-0.5 -2.4,2 -2.6,2.8c-0.2,0.9 0,2.6 0.5,2.8 0.5,0.3 3.3,-2 3.3,-2l0.8,-3z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M291.8,240.3c-1,-0.1 -1.8,-1.5 -1.7,-2.3s2,-2.2 3,-2c1,0.1 0.8,2.5 0.8,2.5s-1.2,1.9 -2.1,1.8z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M294.7,240s-0.4,1.2 -1.4,1.7q-1.4,0.5 -2,-0.7c-0.6,-0.8 0.2,-1.2 0.6,-1.6 0.5,-0.4 1.7,-0.7 1.7,-0.7l1.1,1.4z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M293.2,237.2s-1,0.8 -0.4,1.8 1,1.4 1.8,1.5q1.1,0.2 1.9,-0.9c0.6,-0.7 1.5,-1 1.4,-2q-0.1,-1.4 -1,-1.8c-0.5,-0.3 -2.2,0 -3.7,1.4z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M296.2,233s-1,-0.5 -1.9,0q-1.3,0.9 -1.5,2.4l0.2,2.3s1.2,-0.4 2,-1q1,-0.8 0.8,-1.1c0,-0.2 -0.4,-0.4 -0.4,-1.2 0,-1.4 0.8,-1.5 0.8,-1.5z"
      android:fillColor="#202220"/>
  <path
      android:pathData="M296.2,233s-1.2,-0.3 -1.9,0.1a3,3 0,0 0,-1.3 1.9q-0.3,1.1 -0.1,1v-1.4q0.6,-1.3 1.6,-1.7c0.8,-0.3 1.7,0 1.7,0z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="m328.8,257 l1.6,-0.8 2,-1.2 0.4,2.2 -1.8,1.4h-1.5l-0.9,-0.7 0.2,-1z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M326.4,257.2h1c1.1,0 1.2,-0.3 1.4,-0.3 0.2,0.1 0.5,1.3 0.5,1.3l-0.4,0.7 -1.5,0.9 -1.3,-0.8 -0.1,-1.8zM338.5,256.4s-0.3,-0.5 -0.9,-1l-2.2,-1.9v5l2.3,-0.1 1.1,-1.5z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M326.4,257.2s-0.4,-0.5 -1.3,-0.4 -2,0.9 -2.1,1.5 0,2.6 0.5,3q0.7,0.6 2.2,0.3c1,-0.3 6.4,-3.6 6.4,-3.6l-1.4,0.3c-0.7,0.2 -1.4,-0.1 -1.4,-0.1s-0.7,0.8 -1.6,1q-1,-0.2 -1.2,-0.9z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="m330,258.9 l-1.2,0.5 -1.3,0.6 0.4,1.6 2.3,0.5 1.4,-1.1v-1.6l-1.7,-0.5zM340.4,257.7 L339.6,256.9q-0.8,-0.5 -1,-0.5l-1.3,1.5v1l1.2,0.7 0.9,0.2 1.1,-0.8z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M332.6,257s-1,0.4 -1.5,0.8l-1.2,1c0,0.2 1.1,1.4 1.1,1.4l1.2,0.1 2.5,-1.7 -0.2,-1.6zM342.1,259 L341.6,258.3q-0.7,-0.6 -1.2,-0.6c-0.3,0 -1.3,1.5 -1.3,1.5v0.4l0.8,1 1.6,0.5 1.1,-1.2z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="m338,260.8 l1.2,0.2s0,0.3 0.3,0.5 0.3,0 0.3,0 0.2,1.2 1.4,2c0,0 1.4,0.6 2,0.2 0.8,-0.4 0.7,0 0.7,0l2,-2.4s0,-0.5 -0.5,-0.5c0,0 0.4,-1.6 -1,-2 -1.2,-0.2 -2.2,0.2 -2.2,0.2s-1,1.8 -1.6,1.6l-0.8,-0.5 -0.7,-0.9s-0.4,0.1 -0.9,-0.2 -0.9,-1.1 -0.9,-1.1 -0.7,-0.1 -1,-0.4l-0.5,-0.8 -1.9,1.4s0,0.5 -1.1,1.3 -1.7,0.8 -1.7,0.8 -0.4,1.7 -1.6,1.4 -2,-1.6 -2,-1.6 -0.9,-0.2 -1.4,0.6c-0.6,0.7 -0.9,1 -0.9,1.7 0,0.8 0.5,1.8 1,2.2 0.3,0.3 1.5,1.3 3,0.6 1.4,-0.8 0.9,-2 0.9,-2l0.8,-0.5c0.9,-0.6 1.8,-1.5 2.5,-1.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M335.8,250.8v3.2s0.4,2.9 -0.2,3.7c-0.6,1 -2.9,0.7 -3.2,0 -0.3,-0.8 -0.2,-3.2 -0.2,-3.2z"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"/>
  <path
      android:pathData="M333.3,260.5s-0.5,0.8 1,1q0.6,0 0.9,-0.4 0.4,-0.7 0.3,-0.8c-0.1,-0.1 -0.2,1.2 1.2,1.2 1.4,0.1 1.3,-1.3 1.3,-1.3"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#f8c83c"
      android:strokeColor="#977c2e"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M321.2,263.1s-0.8,-1.4 0.1,-1.9 1.5,0 2.1,0 0.4,-1.4 0.3,-1.7l-0.7,-1.2c-0.2,-0.2 -3.5,0.2 -3.4,2.3 0,2.1 1.6,2.5 1.6,2.5m5,5.2s-1,-1.5 -0.4,-2.4 1,-0.3 1.2,-0.4q0.5,0 0,-1.6 -0.9,-1.4 -1.4,-1.5c-0.4,0 -2.3,1.4 -2,3.2a4,4 0,0 0,2.6 2.7m18.2,-1.8s3.5,1.5 3.7,-2c0.2,-2 -1.5,-3.3 -2,-3.3q-0.5,0 -1.3,1 -1.1,1.2 -0.9,1.6c0.3,0.3 1.3,-0.3 1.8,0.8 0.7,2 -1.3,1.9 -1.3,1.9"
      android:fillColor="#202220"/>
  <path
      android:pathData="M326.2,268.3s-2.7,-0.6 -2.7,-3.3c0,-1.8 2,-2.6 2,-2.6s-1.8,1 -1.8,2.6c-0.1,2.7 2.5,3.3 2.5,3.3m-5,-5.2s-1.6,-0.3 -1.7,-2.6c-0.2,-2.3 3.5,-2.2 3.5,-2.2s-3.5,0.2 -3.4,2.4 1.6,2.4 1.6,2.4m23.2,3.4s0.7,0.3 1.5,0.3 1.8,-0.3 2,-1.6c0.4,-1.3 0.3,-2 -0.6,-3.1 -0.6,-0.9 -1.2,-0.8 -1.2,-0.8s0.6,0 1,0.8c0.6,0.8 1.3,1.6 0.8,3.1s-2,1.5 -2.5,1.5z"
      android:fillColor="#4b4139"/>
  <path
      android:pathData="m302.6,192.4 l-0.6,-0.2 0.4,-1 1.9,-0.7 1.8,-0.1 2,-0.1 0.8,0.6 1,2.3 -3.6,2.2 -0.6,0.2 -0.2,-1 -0.6,-1.4z"
      android:fillColor="#904720"/>
  <path
      android:pathData="m337,245.5 l-0.3,-5.5 -14.8,4.6 5.8,5.5z"
      android:fillColor="#202020"/>
  <path
      android:pathData="m309.4,235.9 l-1.2,-1 -1.4,-0.8h-1.1l-0.2,0.3 0.2,0.5 1.3,1.6 2.3,1.2 0.8,0.7 0.5,0.2 -1.1,-2.7zM309.1,238.2 L309.9,238.4 310.8,239.4 311.3,240.4 312,241.7 312.2,242.5 310.6,242.2 305.8,240.7 306.2,239.5 307,238.8 307.7,238.5 308.7,238.3z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M312.2,242.2h0.8l0.6,-0.5 -0.3,-1.8 0.3,-5.8 -3.9,-4.6 -0.8,2.6 1.1,4.7 0.9,2.8z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M309.4,231.7s-0.2,0.4 0.1,2c0.3,1.5 0.9,4.9 2.2,7.2l-1.3,-1.9s-2,-4.6 -1.7,-8.2 0.7,0.8 0.7,0.8zM312.3,234.6s-0.9,1 -1,1.9v2.2s0,-1 0.8,-1.3 0.7,2.3 0.8,2.6l0.5,-4.7zM312.7,244.2 L308.3,243.2q-3.5,-1.5 -3.4,-2.3c0,-0.6 4,0.1 4.6,0.2l2.4,1h1l1.9,-0.8 0.2,2zM305.4,243.4s1,0.8 0.5,1a4,4 0,0 1,-2.3 0.3l4.2,1 0.9,-0.7z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M304.2,242.2c0.2,-0.2 0.3,0.7 1.8,0.8s2.2,-0.2 2.2,-0.2l0.7,0.4 0.8,0.2 -0.7,1.1 -2.8,-0.8 -1.5,-0.8z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="m314,240.3 l0.8,1 0.5,-2.4v-2.5l-1.6,-1z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="m309.7,236.1 l-1.3,-2.1 -1.7,-3.1 1,-1.8 1.3,1.8s-0.4,1.3 -0.1,2.5c0.2,1.2 0.8,2.7 0.8,2.7"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M300.8,244s0.6,-0.9 1.9,-1l2.4,0.2 2.6,1.4 0.6,0.9 -5,-0.7z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="m314,235 l0.2,4c0,1 0.7,2 0.6,2.3q-0.2,0.5 -1,0.9c-0.6,0.2 -2,0 -2,0s1.3,-0.2 1.4,-0.5 -0.4,-1.4 -0.5,-2.4 0.1,-2.2 -0.3,-2.2q-0.5,0.2 -0.8,0.6s0.4,-3.8 0.8,-4.2 1.6,1.6 1.6,1.6z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M304.7,243s-0.5,-0.4 -0.5,-0.8c0,-0.3 0.6,1.6 3.4,1.5 2.8,0 2.6,-0.2 2.6,-0.2h1.3s-0.3,1.3 -0.5,1.4l-2.2,1.4 -0.7,0.4v-0.2l-0.3,-2.1 -2.4,-1z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M309.7,236.1s-3.2,-3.3 -4.3,-5.5 -0.4,-5.2 -0.4,-5.2l3,4s-0.4,0.7 -0.3,2c0.2,1.2 2,4.7 2,4.7"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M307.6,248.3c0,-0.3 -0.2,-1 0.4,-1.8 2,-2.4 6.8,-4 6.8,-4v2.8l-6.2,3z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M308,246c0.3,-0.1 1.4,-1 1.4,-1.3q0,-0.4 -2.1,-0.7c-1.5,-0.3 -2.6,-1 -2.6,-1s1.7,1.4 2.4,1.7q0.9,0.3 0.5,0.7zM316.3,246.7s-0.9,-1.3 -1.2,-2.1 -0.3,-2.1 -0.3,-2.2l-1,0.7 -2,0.7s2,0 2,0.6 -3,1 -3,1c0,0.1 2.4,-0.4 2.3,0.1s-1.2,0.7 -2.5,1.2l-2.3,1 -0.7,0.5s0.6,0.4 1.5,0.3c0.9,0 2.9,-0.5 2.9,-0.5l3.8,-1.5z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M316.3,246.7s-1,-0.3 -1.8,-4.8 -0.6,-5.7 -0.6,-5.7l1.8,1.3 1.7,3z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M316.9,239.3s-0.9,2.5 -0.9,4q-0.2,2 0.3,3.4c0.5,1.4 1.8,2.3 1.8,2.3l0.7,-0.6 0.3,-2 0.1,-3.4z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M319.4,242.7s0.4,6.3 -1.2,6.5c-0.7,0 -1.9,-2.4 -1.9,-2.5 0,0 1.4,2.3 2,2.1 1,-0.4 0.6,-6.6 0.6,-6.6zM316.3,246.7s-0.3,0 -0.7,-0.8c-0.4,-0.7 0,0.6 -3.4,1.8s-4.6,0.5 -4.6,0.5 1.1,1 4.5,0 3.1,-1.3 3.6,-1.4z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M310,238.7s0.6,0.5 1.2,1.7l1,2h0.2s-0.4,-1.3 -1,-2.2l-2.2,-3.1z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M308,246.5s0.3,-1.1 0,-1.4 -0.7,0 -3,-0.2l-4.2,-0.9s1.6,1 3,1.4l3.5,0.8 0.5,0.1z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m308,246.5 l-0.2,-0.5c-0.3,-0.3 -0.7,0.1 -3.5,-0.7s-3.5,-1.3 -3.5,-1.3 1,1 3.4,1.5z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M309.3,238.2s-2.1,0.1 -2.8,0.7 -1.6,2 -1.6,2l1.3,0.1s-0.1,-1.2 0.5,-1.9q1.1,-0.7 2.6,-0.9"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M305.3,234.2s0.8,1.2 1.7,1.5q1.5,0.6 2.2,1.2c0.4,0.4 0.9,1.8 0.9,1.8s-3,-1.3 -3.7,-2z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M308.2,235s-0.3,-0.5 -1.1,-0.9 -1.4,-0.3 -1.7,-0.1 -0.1,0.2 -0.1,0.2l1,1.7 0.7,-0.2s-1.6,-1 -1.3,-1.3c0.8,-0.7 2.5,0.5 2.5,0.5z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m316.3,231.1 l17,-8.7 8,11.1 -14.7,4 -7.7,0.6 -3.8,-2.7z"
      android:fillColor="#202020"/>
  <path
      android:pathData="m339,235.9 l21,18.2 -2.3,1.6 -5,-1.4 -10.2,-10.1 -3.8,-3.8z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="m366,250.5 l-12,-9.1 -15.3,-11.5 0.4,5.5 17.4,16 5.7,2.7 3,-0.8 1.4,-1.6z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M339,237.6s6,6.9 10.6,10.4 9,7.3 10,6.7c0.8,-0.5 0.5,-1 0.5,-1l-21.3,-17.5z"
      android:fillColor="#803f1d"/>
  <path
      android:pathData="m353.8,255.1 l-2.1,1.7 -2.5,-1 -13,-13.2 0.7,-2.5 1.8,0.3z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M366,250.5s-1.6,3.8 -5,2.9 -22,-18.5 -22,-18.5v1.8s18.8,17.3 22.5,18 6.6,-2.5 6.2,-2.8z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M336.9,240.4s6,7.7 9.1,10.5c3.2,2.9 6,5 7.2,4.8s-0.7,-1.7 -0.7,-1.7l-9.1,-9 -4.7,-4.6s-1.8,-0.7 -1.8,0"
      android:fillColor="#803f1d"/>
  <path
      android:pathData="M338.4,240.4s15.2,15.5 16.4,15.9c2.5,0.7 6.2,-2 6.2,-2l-1.3,-0.7 -0.2,0.5s-0.7,1.6 -4,0.9 -16.6,-15.1 -16.6,-15.1z"
      android:fillColor="#202020"/>
  <path
      android:pathData="m344,255.3 l-5.5,-6.4 -1.2,-2.1 -0.3,-2.7 11,11.1 -1.5,1 -1.4,-0.4z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M354.1,255.5s-1.3,2 -3.2,1.6a5,5 0,0 1,-2.8 -1.7L337.5,245l-0.3,-1.6s11.7,12.3 13.7,12.8c1.6,0.4 2.7,-1.4 2.7,-1.4z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M348.2,255.1s-0.5,1.6 -2,1.4c-1.6,-0.3 -2,-0.9 -2,-0.9l-6.6,-7.7 -0.1,-2s6.8,9.3 8.3,9.8 2,-1 2,-1z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M341.3,234.9s6.3,5.4 6.8,5.6c0.5,0.3 2.7,0.4 2.8,0.7 0.2,0.3 -1.4,0 -1.5,0.3 -0.1,0.4 3.2,3 3.7,3.1 0.5,0 3.2,0 3.1,0.4 0,0.3 -1.8,0 -1.9,0.4s2.9,2.7 3.4,2.8 3.9,0 3.9,0.4c0,0.3 -2.7,0 -2.7,0.5 0.1,0.6 4.6,3.4 4.4,3.8s-5.4,-3.5 -5.5,-3.3c-0.2,0.2 -0.4,1.4 -0.7,1.6s0,-1.6 -0.2,-2.4 -3.4,-3.1 -3.7,-2.9 0,1.7 -0.4,1.7 0.1,-1.7 -0.2,-2.3a16,16 0,0 0,-3.6 -3c-0.4,0 -0.1,1.3 -0.4,1.5s-0.2,-1.5 -0.6,-2 -7,-6.5 -6.7,-7z"
      android:fillColor="#b07229"/>
  <path
      android:pathData="M330.5,252.3s0,2.4 -1,2.8c-1.1,0.3 -3.1,-4.4 -3.1,-4.4l-0.5,-4.4 3.1,3zM338.9,251.1L338.9,250c0,-0.6 -0.3,-1.5 -0.3,-1.5l-0.2,-2.4 -0.6,-1.7 -0.4,-1h-0.7l-1.7,1.2 -0.5,1.6 0.8,3 0.8,2.7 1.3,0.4 1.5,-1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M336.8,255.3c0.3,-0.1 -0.4,-3.4 -0.4,-3.4l-2,-4.8 -0.5,-1.7 -1.5,-0.6 -1,1.2 0.2,1.9s1.5,3.2 2.2,4.3 2.3,3.3 3,3.1"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M335,245a6,6 0,0 1,1.4 2.6s1.3,3.6 1.1,4 -1,0.3 -1,0.3 1.8,1.8 2.3,1.6 0.3,-2 0.1,-2.4 -0.7,0 -1,-0.5c-0.1,-0.4 -0.8,-2.7 -0.8,-4.2v-2.7c0.2,-0.3 1.5,3 1.5,4.7l0.6,0.7 -0.6,-3.2 -0.7,-2.3q0.1,-0.4 0.3,-1.5v-1s0.6,0.5 0.9,0.5q0.5,0.2 0.8,-2c0.2,-1.5 -0.1,-6 -0.1,-6l-6.8,9z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M335,244.3s-0.3,2.4 0.6,4.8l1.3,3c0.2,0.3 0.2,2.9 -0.1,3.2 0,0 -1.8,-4.4 -2.4,-6.6q-0.7,-3 -1,-3.2l-0.8,-0.2z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M334.4,257c0.2,0 0,-1 0,-1l-0.2,-2.3 -2.4,-4.9 -2.6,-2.3 -1.4,-2 -0.2,4s2,4 3.1,5c1.3,1.2 3.3,3.6 3.7,3.5"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M324.7,246s0.4,1.1 0.4,1.8v1.4l1.8,3.5c0.9,1.5 1.7,3.1 2.4,2.8s1,-1.5 1,-1.7c0.1,-0.1 -0.8,1.3 -1.2,1.1a40,40 0,0 1,-2.3 -6.6l-1,-0.8 -1.1,-1.4zM331.7,251.5 L330,248.4q-1,-1.6 -1.4,-1.7c-0.2,0 -0.5,-2 -0.5,-2s0.7,1.3 1.6,1.7q1.4,0.8 2,0.7l0.2,2z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M327.4,248.3s0.5,1.4 1.4,2.7 2.3,2.9 2.3,2.9 -1.6,-1.6 -2.5,-2.8l-1.4,-2.8z"
      android:fillColor="#5c3a1d"/>
  <path
      android:pathData="M332,246.7s0.5,-1.2 0.8,-1.3l1.2,-0.3s-0.7,-0.1 -1.2,-0.8l-1.3,-1.3 0.4,2zM331.6,247.1s2,4 2.4,5.5c0.5,1.3 0.4,3.4 0.4,3.4s-0.2,-1.5 -0.6,-2.7l-2.4,-5c-0.1,-0.3 0.2,-1.2 0.2,-1.2"
      android:fillColor="#202020"/>
  <path
      android:pathData="m334.2,244.4 l0.5,-6 -0.5,-2.2 -2.7,-6s-0.1,0.8 -0.8,0.8a6,6 0,0 1,-3.1 -1.8,9 9,0 0,0 -0.2,4.8l5.3,9.9z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M323.8,248.3s0.8,1 1.3,1c0.4,-0.2 0.3,-2.1 -0.3,-3.2 -0.6,-1 -7,-9.6 -8,-10.1s7,12.3 7,12.3"
      android:fillColor="#8b441f"/>
  <path
      android:pathData="M328.1,244.8c0.2,0.4 0.6,3.1 -0.5,3.4 -1.5,0.3 -8.5,-9.4 -9.6,-10.7 0,0 -4.4,-2.8 -4.5,-3.5s4.7,1 4.7,1l9.5,9z"
      android:strokeWidth=".1"
      android:fillColor="#8b441f"
      android:strokeColor="#5c3a1d"/>
  <path
      android:pathData="M323.8,248.3s-0.8,-1.3 -0.9,-2.3q-0.2,-1.4 -0.4,-1.9c0,-0.3 -5.2,-7.7 -5.7,-8.2s-2.3,-1.4 -2.3,-1.4l0.3,2.4s1.6,3 4,5.7z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M331.6,247c0.7,-0.3 0.4,-2.7 0.1,-3.5l-4.4,-9.4 -1.3,0.4 -2.3,-1.8 -1.4,-1.5s0.5,2.1 0.4,3.1v1.6l3.3,5.9zM336.3,241.8 L337.3,240.5 336.8,236.1 334,226.7s-0.7,0.1 -1.4,-0.3 -1.4,-1.3 -1.4,-1.3l0.7,5.7 2.9,8 0.2,2.3z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M327.7,244s-2.8,-3 -3.7,-4.5l-1.7,-3s-0.8,0.3 -1.8,0c-1,-0.5 -2.2,-1.8 -2.2,-1.8s-0.8,1.5 3.2,5.6c4.6,4.6 6.2,3.7 6.2,3.7"
      android:fillColor="#202020"/>
  <path
      android:pathData="M334.4,245c1.2,-0.4 1.1,-5.4 0,-8.7 -1,-3.3 -2.9,-6.2 -2.9,-6.2s2.3,5.4 2.8,7.5c0.4,2.2 0,3.7 -0.4,3.6s-0.4,-1.3 -1.3,-3.4q-1.6,-2.8 -1.6,-2.6c0,0.2 3.3,8.8 2.3,8.6s-5.3,-9.9 -5.5,-10.2 -0.4,0.4 -0.4,0.4 4,12.2 7,11"
      android:fillColor="#8b441f"/>
  <path
      android:pathData="m337,221.3 l2,4.2 0.6,4.4 0.2,3.7s0,7.4 -0.8,7.6c-1,0.1 -1.4,-0.7 -1.4,-0.9l-1.7,-8.3 -1.7,-8.5 0.6,-0.6 0.2,-1.2 1.3,-0.1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M331.6,247c0.8,0 0,-2 0,-2s-0.6,1 -1.2,0.3 -1.3,-3.1 -1.3,-3.1l-0.5,-1.5s-0.6,1.6 -1.3,1.6 -4.6,-6.4 -4.6,-6.4l-0.4,0.6s5.4,11.1 9.3,10.6z"
      android:fillColor="#8b441f"/>
  <path
      android:pathData="M329.1,242.2s0.7,-1.6 0.4,-3c-0.3,-1.5 -2.2,-5.1 -2.2,-5.1s-0.6,0.1 -1.4,-0.2l-2.2,-1.2a38,38 0,0 0,5.4 9.5m3.1,-2.3s-0.5,-3.4 -2,-6.6c-1.6,-3.1 -2.6,-4.1 -2.6,-4.1v3.5s1.5,0.5 2.6,2.4c1,2 2,4.8 2,4.8m4.3,-5.8s-0.3,-3.7 -0.8,-5.8l-1.2,-5s0,-0.2 0.3,-0.4h0.6c0.8,0.3 1.1,2.2 1.1,2.2l-0.1,-3.1 -0.3,-0.5h-1.4s0.3,1 -0.1,1.3c-0.3,0.3 -0.6,-0.2 -0.7,-0.3l-0.2,0.4 0.4,2 -0.1,1.7s0.5,2.2 1.2,3.5c0.6,1.4 1.3,4 1.3,4m3.3,-0.5s0.6,-5.3 -0.6,-8.7 -2.2,-4.2 -2.2,-4.2l-0.6,0.6s1.8,3 2.4,5.6zM334.4,236.3s-0.6,-4.1 -1.3,-6.8l-1,-3.6 -1.1,-1s0.7,2.7 0.6,3.6l-0.1,1.4s0.5,1.4 1.3,2.6z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M337.2,242.9c1,0 0.1,-6 -0.3,-7.5 -0.4,-1.6 -3,-8.8 -3,-8.8s3,10.3 2.8,10.8c-0.3,0.8 -1.5,-3.1 -1.7,-3 -0.2,0 2.2,6.9 1.3,7 -0.8,0 -1.3,-1.5 -1.3,-1.5l0.1,1.5s0.6,1.4 2.1,1.5"
      android:fillColor="#904720"/>
  <path
      android:pathData="M338.5,239.9c0.7,0 -0.2,-5.7 -0.7,-8.2l-2,-7.5s1.5,3.5 2.3,7.5 1.4,9 0.6,9.2c-0.8,0 -1.2,-0.8 -1.2,-0.8v-0.9s0.3,0.7 1,0.7"
      android:fillColor="#904720"/>
  <path
      android:pathData="M328.8,246.5c0,0.4 0,2.4 -1,2.2s-2.9,-1.9 -3.3,-3c0,0 2.2,2.5 3,2.4 0.8,0 0.8,-2.5 0.6,-3.2 0,-0.6 0.6,1.6 0.6,1.6z"
      android:fillColor="#312317"/>
  <path
      android:pathData="M334.1,219.5s1.4,0.9 2.4,0.3a4,4 0,0 0,1.8 -3q0.2,-1.1 -0.7,-2.4c-0.7,-0.9 1,0.7 1,2.3s0,4.3 -2.5,5c-2.5,0.5 -1.4,-0.2 -1.4,-0.2z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M332.1,214.7s1.6,2.6 2.2,5q0.9,3.3 0.2,3.3t-0.7,-0.9c0,-0.6 -1.7,-7.4 -1.7,-7.4"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m304.1,218 l10.6,15.3 13.2,-15 -8.3,-8.6z"
      android:fillColor="#1e2121"/>
  <path
      android:pathData="m317.5,208.5 l-0.6,2.4 1.3,4.1 2.2,2.3 2.5,1.7 0.6,-2 0.6,-2.2 0.7,-0.2 1.6,0.6 1,-1.5 1.8,-3.1 -0.4,-1.2 -0.4,-0.9 -2.7,-4.6 -0.9,1.7 -0.8,0.5 -1.6,-0.8 -1.8,2.4 -2.3,-1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M323,219s0.4,-0.3 0.6,-1.6c0.2,-1.2 0.2,-4.9 -0.1,-6.4s-1.3,-4.4 -1.5,-4.2 1.5,3.3 1.3,7c-0.3,3.6 -0.2,4.6 -1,5 -0.7,0.3 0.6,0.2 0.6,0.2z"
      android:fillColor="#5c3818"/>
  <path
      android:pathData="M323.6,212.8s1.7,2.5 2.5,1.6 0.5,-4 0,-5.3l-1.2,-3 0.5,-0.6 0.9,3.4c0.3,1.4 1.3,5.2 0.1,6 -1.1,0.6 -2.8,-1.5 -2.8,-1.5z"
      android:fillColor="#5c3818"/>
  <path
      android:pathData="M317.9,214.6s0.6,-1 1.4,-1q1.1,0.2 1,0.3l0.6,2 1.2,2.2 1,1 -0.5,0.6 -0.9,0.2 -2.5,-1.4 -0.7,-1 -0.6,-3z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M322,236.3c-0.8,0.5 -2.7,-0.5 -3.7,-1.4l-6.8,-6.7 -0.8,-3.2 2.2,0.6 3.9,-1.7 0.7,-1.3 4.6,-2.4 2.6,-1.4 5.5,-0.1 1.4,-2.7s1.8,3.2 2.1,4.5c0.4,1.4 0.3,5.4 -0.2,5.7s-2.3,-1.2 -2.6,-1.3c-0.2,-0.1 1.4,5.7 0,6s-3.6,-2.2 -3.6,-2.2 1,5.2 -0.2,5.2a10,10 0,0 1,-5 -2.6s1.3,4.3 0,5z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="m321,229.6 l1.1,1.7c0.3,0.4 0,2.9 -0.2,3.4 -0.3,0.9 -1.6,-1.2 -2.3,-3l-2.2,-3.8c0,-0.4 3.5,1.7 3.5,1.7zM331.8,216.7s2,8.1 1,8.4 -3.6,-2.6 -4,-3l-4,-6.4 -0.8,-0.8 0.2,-0.4 1.7,0.6z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M317.9,232.3c-0.4,0.3 -1.3,-1 -1.7,-1.4l-3,-3.3a12,12 0,0 1,-2.2 -2.5c0,-0.3 -0.8,-1.1 -0.8,-1.1l0.7,-1h0.9s5.4,4.2 5.7,5c0.4,0.6 0.6,4.1 0.4,4.3m8,0c-0.2,0.3 -3.7,-2.3 -5.2,-4.4s-3,-4 -3.5,-5l-0.6,-1.9 1,0.2s8.8,10.8 8.4,11.2zM327.2,228.6c-0.2,0.3 -1.7,-0.9 -2.7,-2.1s-0.4,-2.9 -0.4,-2.9 3.3,4.8 3.1,5m2,-0.5c-0.5,0.3 -2.5,-2.2 -4.2,-5 -1.3,-2 -1.6,-2.3 -1.8,-2.5 -0.5,-0.3 -1.4,-0.4 -1.4,-0.4l-0.2,-0.3 1,-0.2 0.6,-0.8 1,-1.3 4.3,8.5s1,1.9 0.7,2"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M318.3,234.9c0,0.3 0,0.6 -1.2,0.5a21,21 0,0 1,-9.9 -7.8l-3,-5 1.1,-1.4 9.6,11z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M318.3,234.9q-0.1,0.7 -1.1,0.5c-0.6,-0.2 -3,-1 -6.9,-5.3a33,33 0,0 1,-5.3 -7.4l-0.2,-1.8 1.2,-0.5 6.5,10z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M324.6,230.6s-2,-1.5 -3.2,-3a24,24 0,0 1,-3.8 -5.7c0.1,-0.7 1.2,-0.7 1.2,-0.7v-1.8l0.2,-0.6 1.9,0.8 2.3,2.9 0.9,1.1s1.5,3.7 1.2,4 -2.4,-2 -2.7,-1.7 2.4,4.4 2,4.7m-3.7,-1s0.7,1 0.4,1.7 -0.5,1 -1.5,-0.4a16,16 0,0 0,-2.3 -3c0,0.1 0.1,2.3 -0.3,2.3s-2,-1.9 -3,-3.3l-2.5,-3.9v-2l-0.1,-2.5 0.3,0.9 0.4,0.7 3.6,4.2 1,-0.7 2.1,4zM307.4,223.5s2.8,4.8 5.3,7.5a17,17 0,0 0,5.6 3.9l-8.4,-9.9zM328.6,226.3a12,12 0,0 1,-3 -3.4c-1,-1.6 -1.9,-4 -1.8,-5s0.4,-3.4 0.4,-3.4 2.8,3 3.4,4.3c0.5,1.4 1.7,3.3 1.4,3.5 -0.2,0.2 -2.1,-1.3 -2.4,-1s2.3,4.8 2,5m3.2,-3c-0.3,0.1 -1.8,-1.6 -2.1,-2.6l-0.6,-3.2 -1.2,-3v-3.3l1.4,-0.2 1.9,2.7s0.7,1.5 0.7,3.7 0.3,5.6 0,5.8zM309.3,231.3a42,42 0,0 1,-4 -6l-1,-2.9 0.5,0.4s0.8,3.2 4.7,7.1c4,4 4.9,4.2 4.9,4.2s0.7,2.2 0.4,3c0,0 -2.9,-2.6 -5.5,-5.7z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M313.6,235.9s-3.4,-3 -6,-6.4a47,47 0,0 1,-4.4 -6.6l1.2,-1a46,46 0,0 0,9.2 14m4.7,-1 l-6,-6.1c-2,-2.2 -3,-4.4 -3,-4.4l1.6,0.6s0.7,1.7 2,3.5zM317.5,227.9s-2.1,-2.6 -3.1,-4.2l-2,-3.8s2.8,3.3 3.6,3.5 0.9,0 0.9,0 -0.4,1.2 -0.1,2.2zM318.4,217.3s0.3,1.9 0.1,3c-0.2,1 -0.8,1 -0.8,1a16,16 0,0 0,4 4l-1.8,-2.7c-0.5,-1 -1.3,-2 -1,-2.8 0.4,-1 2.1,0.6 2.6,1l2.6,2.8s-0.8,-2.5 -1.8,-3.4z"
      android:fillColor="#202020"/>
  <path
      android:pathData="m327,222.8 l-1.7,-4.2 -1.6,-5.2v2.8c-0.1,1.2 -0.5,3 -1,3.3s-0.7,0.3 -1.7,0 0.7,0.5 0.7,0.5 1,0 1.4,-0.5 0.5,-1.5 1,-1.4 1,1.7 1.3,2.4z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M323.7,213.4s2,2.3 3,1.3c0.8,-1 0.3,-4 0.3,-4s1.2,0.5 1.6,0.2c0.4,-0.4 0.2,-1.5 0.2,-1.5s2.3,3.2 3.3,5.3c1.1,2 1.6,5.8 1.6,5.8s-1.8,-3 -2.7,-5.8c-0.9,-2.6 -2.1,-3.8 -2.3,-3.5a5,5 0,0 0,-0.3 3c0.2,1 1,2.1 1.5,3.5l1.1,3.4 -1.6,-2.5q-1,-1.1 -1.7,-1.6c-0.4,-0.4 -1,-1.2 -1.7,-1.4 -0.6,-0.3 -1.8,-0.9 -1.8,-0.4s-0.3,-1 -0.3,-1z"
      android:fillColor="#202020"/>
  <path
      android:pathData="M316.8,223.3a27,27 0,0 0,4.2 6.3s-2,-1.8 -2.8,-3a16,16 0,0 1,-1.4 -3.3"
      android:fillColor="#171717"/>
  <path
      android:pathData="M330.2,223.9c-0.3,0 -1.3,-0.3 -1.4,0 0,0.4 0.6,1.8 1.3,2.5q1,0.9 1.1,0.6 0.2,-0.2 -0.2,-1.4v-1.7c-0.1,-0.3 -0.8,0 -0.8,0"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m306,209.4 l-1.3,0.7 -1.4,3 0.4,5.3 2.1,3c0.8,1.2 3.5,4.2 4.2,4s1.4,-2.3 1.5,-3.4l0.2,-3.2 1.8,2.3c1.1,1.3 2,2.4 2.6,2.3 0.5,0 2,-3.2 2,-3.6 0.1,-0.4 -3.8,-10 -3.8,-10l-1.8,-1 -6.6,0.6z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="m314.8,217.4 l1,3.7c0.5,1.2 0,2.3 0.3,2.3s2.3,-2.7 2.3,-4 0,-2.5 -0.5,-6.3 0.2,-5 0.2,-5l-1.2,-1 -2,4.2zM307.8,215.2 L308.5,220.2c0.3,1.2 1,3.3 1.4,3.2s1.6,-3.3 1.7,-4.4c0,0 -0.9,-1.6 -0.8,-5.5 0,-4 -0.4,-4.4 -0.4,-4.4z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M302.5,207.5s-3.1,4.6 -3,7c0,2.5 0.4,2 1.1,4.2s1.1,5.6 1.2,5.9c0,0.2 0.5,-0.2 0.5,-0.5z"
      android:fillColor="#45392d"/>
  <path
      android:pathData="M302.5,208s-2.7,4.3 -2.5,6.6 0.5,2.3 1.3,4.4c0.8,2 0.8,5.4 0.8,5.4s1,-0.2 1.9,-0.9c1,-0.7 1.8,-2.1 1.8,-2.1l-1,-2.1 -0.6,-2 -0.8,-1 0.2,-4.1 0.7,-3v-1l-1.8,-0.1z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M317.2,215.3s-1.3,-0.3 -1.9,-0.6 -0.8,-1.1 -0.8,-1.1l0.4,-0.8z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M303,208.5s-2,4.3 -1.7,7.2 1.9,6.6 2,6.7q0.1,0 0.1,-1v-8.7l0.9,-2.8v-1.5z"
      android:fillColor="#d2a567"/>
  <path
      android:pathData="M307.8,209.5s0.3,1.2 0.1,1.8c-0.2,0.5 -1.2,1.2 -1.6,2s-1,2.8 -1,2.8 0.4,-1.1 1,-1.8q1,-1.2 1,-0.8c0.2,0.4 0.4,2 0.3,2.3q-0.4,0.3 -1,2c-0.4,1.3 0,2.4 0,2.4l0.5,-2.2c0.2,-0.6 0.7,-1 0.7,-1l1,2.4s0,-1.1 0.3,-2c0.1,-0.8 1,-2 1.3,-2s0.8,-0.1 -0.3,-0.7c-0.4,-0.2 -0.1,-4.2 0.1,-4.4s1.9,-0.2 1.7,-0.8l-1.6,-2.2c-0.1,-0.1 -2.5,2.2 -2.5,2.2"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m317,218.1 l-1.5,-0.3c-0.5,0 -0.6,-0.6 -0.6,-0.6l0.2,-0.6 1.2,0.7z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M304.4,218.6q-0.1,0.6 -0.5,1.5c-0.3,0.7 -0.4,1.6 -0.4,1.6l-0.4,-3.3 -0.3,-2.4c-0.2,0 -0.7,0.7 -0.7,0.7s0,-1.6 0.3,-2.3 0.5,-1.8 0.3,-2q-0.6,0 -1,0.7l-0.8,1.3s0.3,-1.3 0.6,-1.9 1.8,-2.2 2,-2.8q0.5,-1.2 0.4,-1.4h1s1,0.8 1,1.5c0,0.4 -1,0.9 -1,1s-0.8,-0.1 -1,3.2 0.6,3.2 0.5,4.6m9.4,-8.7s0.2,1.8 0,2.4q-0.4,0.8 -1.1,1.8c-0.4,0.7 -0.4,2.4 -0.4,2.4l0.5,-1.4 0.7,-1q0.3,0.1 0.8,1.7l0.8,2.5s-0.2,-1.2 0.1,-1.2 0.5,-0.1 0.9,0.2l0.9,0.8s0,-0.5 -0.7,-1.1 -1,-0.6 -1.1,-1.5q-0.3,-1.6 -0.2,-2.2c0,-0.3 0.5,0.3 0.9,0.7l1.5,1.4 -0.7,-1v-3q-0.1,-1.7 0.4,-2.1c0.3,-0.3 0.4,-1.6 0.2,-1.8 -0.1,-0.2 -3.5,2.4 -3.5,2.4"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M324.6,206.1a24,24 0,0 0,1.5 7.1s-1,-2 -1.6,-3.6 -0.9,-3.5 -1.3,-3.6 -1,0.8 -1,1.5 0.5,3.3 1.1,4.2 -1,-1.2 -1.3,-2.3l-0.7,-2.2s-0.4,1 -0.3,2c0.2,1.1 1.1,2.7 1.1,2.7l-1,-1 -0.6,-0.7s0,1.5 0.4,2.1l1.2,1.7 -1.3,-1 -0.4,-0.8s0.2,2.1 0.6,3.3l1.5,3s-1.2,-1 -2,-2.9 -1.2,-7.3 -1,-8.1l1.1,-1.9c0.6,-0.8 1.3,-4.7 1.3,-4.7s0.9,3.6 1.5,4.3q1,1 1.2,1zM316,205.1 L316.8,206.4 317.4,207.4s-1,1.1 -2.3,2c-1.2,1 -2.8,2 -3.1,1.5q-0.5,-0.9 -0.4,-1c0.1,-0.1 1.4,-0.8 2.4,-1.8a7,7 0,0 0,1.6 -2.2q0.4,-0.9 0.5,-0.9zM305.1,208.6s0.2,2.2 1.2,2.2 3.7,-2.9 3.9,-3 0.4,-0.8 0.4,-1l-0.2,-0.8s-3,3 -4,3.2c-0.9,0 -1.3,-0.6 -1.3,-0.6m0.1,-3 l-0.3,2q0.1,0.8 0.2,0.9c0.1,0.1 -2.2,0.7 -2.5,0 -0.4,-0.6 -0.1,-1.6 -0.1,-1.6h1.5c0.6,-0.2 1.2,-1.3 1.2,-1.3m20.3,-3.1 l1.5,3.2 1.4,2.8s-1.4,-1.7 -1.9,-2l-1.2,-1 0.2,-1.1z"
      android:fillColor="#1e2121"/>
  <path
      android:pathData="M302.1,207h1.3q0.8,-0.1 0.8,-0.2l0.9,-1.2s-0.5,1.4 -0.2,2.5c0.3,1 1,1.1 1.2,1.1a4,4 0,0 0,2.2 -1.1c0.9,-1 2.1,-1.9 2.1,-1.9s-0.3,1.3 0.2,2.7q0.7,1.8 2.2,0.4c1,-0.8 2,-2.2 2.6,-2.8l0.7,-1 1.1,-1 1,-5.4 -0.5,-0.7 -1.4,0.7 -1.7,-0.7 -0.5,-0.7 -1,1.7 -1,0.6 -0.5,0.1 -1.2,0.3 -0.5,-1.1 -0.3,-0.8 -0.6,-0.3 -2.4,2.5 -1.4,-0.1 -1.3,2.2 -1,1.3 -1,2.4z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M313.9,201.5s0.2,1.3 -0.5,3.6 -1.6,3.9 -1.6,3.9 2.4,-1.5 3.6,-4.2c1.1,-2.6 0.9,-5.7 0.9,-5.7zM316.9,199s0.2,0.4 0,2.4l-0.8,4 0.6,1.2c0.4,0.7 1.2,1.8 1.7,1.6s1.6,-0.9 2.2,-2.2 1.1,-2.7 1.4,-2.9q0.3,-0.1 0.4,0.2c0,0.1 0.3,1.3 0.9,1.8 0.5,0.5 1.3,1.3 1.6,1 0.4,-0.2 0.8,-0.9 0.7,-1.2l-1.3,-4 -1.6,-4 -1,-1.6 -2.8,0.3z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M321.7,195.3s0.8,1.7 0.9,3.6c0,1.8 -0.4,3.8 -0.4,3.8s0.2,-2.3 0,-3.7q-0.6,-2.1 -0.7,-2.7z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M325.6,202.6v2.3c-0.2,0.8 -1.3,-0.5 -2,-3s-0.5,-3 -1,-4.3l-0.9,-2.2 1.5,-0.3zM318.6,207.2s2.8,-1.5 3.1,-6.2c0.2,-2.1 -1.2,-5.7 -1.2,-5.7s0,3.8 -1,6.2l-1.9,4s0.7,-0.2 1,-0.6l0.8,-1.1s0.1,0.7 -0.1,1.6l-0.6,1.8zM316.3,199.1s-0.1,1 -1.3,2c-1,0.9 -3.1,1.9 -3.1,1.9s1.5,-1.4 2,-2.6 0.1,-2 0.1,-2 -0.2,0.6 -0.8,1.1 -1.6,0.6 -1.6,0.6 0,-0.6 0.7,-1.3q0,-0.2 0.2,-0.7c0.3,-1 1.1,-2.3 1.1,-2.3s0.7,1.4 1.3,2z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M324.7,193.6a18,18 0,0 0,0.9 9s-1.4,-2.2 -1.7,-3c-0.4,-1 -0.4,-2.3 -0.7,-3l-1,-1c-0.3,-0.2 -1,-0.1 -1,-0.1s0.8,-0.4 0.8,-1.1v-1l1.4,0.3h1.3zM318.7,203.4s1.4,-2 1.8,-4.6 0,-3.5 0,-3.5 -0.6,0 -1.1,-0.5 -1.3,-1.8 -1.3,-1.8 0.2,1.4 0,3c-0.4,1.6 -1.8,3 -1.8,3s0.8,0.4 1.4,0c1.5,-0.9 1.4,-2.5 1.4,-2.5s0.3,0.2 0.3,2.2 -0.7,4.7 -0.7,4.7"
      android:fillColor="#1e2121"/>
  <path
      android:pathData="M301.2,206.9c-0.3,0 1,-2 1.8,-3.4l2.1,-3.4s0.3,0.3 0.8,0.3a4,4 0,0 0,2.4 -1.7,7 7,0 0,0 1,-2.5c0,-0.3 -0.1,1.8 1,3 1,1.1 0.9,1 1.3,1 0,0 0,1.4 -0.4,2.7l-0.8,3.3s0.2,-2.5 -0.3,-3.2 -0.6,0 -1,1c-0.3,0.9 -1.1,1.5 -1.1,1.5s0.6,-1.1 1,-2.3 0.2,-1.2 0,-1.2q0,-0.2 -0.6,0.4 -0.8,0.7 -0.9,0.6c-0.1,-0.1 0.5,-0.4 0.7,-1l1,-1.8q0.4,-0.5 0.4,-1t-0.2,-0.7 -1.4,1.2l-1.3,1.3 -0.9,2.6 -1,2.8 -0.6,0.4 0.9,-2.4 1,-3.4q-0.5,-0.5 -0.8,0l-2,2.8c-1,1.4 -1,3.1 -1.2,3.2l-1,-0.1z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M325.7,192.1c0,0.2 0,1.2 -1,1.5a4,4 0,0 1,-2.3 -0.2c-0.2,0 -0.2,-1.4 -0.2,-1.4z"
      android:fillColor="#874f20"/>
  <path
      android:pathData="M306,195.4s-0.2,0.9 -0.8,2c-0.5,1.2 -1.4,2.2 -1.3,2.4s0.6,0.7 1.4,0.6 1.1,-4.8 1.1,-4.8l-0.1,-0.2z"
      android:fillColor="#b27129"/>
  <path
      android:pathData="M318.4,192.7s-0.3,6.4 -2.1,6.4 -2.7,-3.2 -2.7,-3.2 -0.1,4.3 -2.4,4.3c-1,0 -2.1,-3.4 -1.9,-4 0,0 -0.4,2.3 -1.5,3.2 -2.2,1.7 -3.5,0.9 -3.6,0.6 0,-0.3 1.8,-2 2,-4.6 0,0 0.4,0.1 1,-0.6s0.7,-1.6 1.3,-1.5c0.5,0.2 3,-1 3,-1l1.4,-1.7s0.2,0.5 1.2,-0.2a4,4 0,0 0,1.3 -1.7l2.7,2.3z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="m311.4,186.6 l2.7,3.8 0.6,-0.5q0.7,0.8 0.4,1.2c-0.4,1 -1.3,2.3 -0.5,3.7 0,0 -0.3,-0.8 0.1,-1.8s0.7,-1.4 1,-1.3c0.2,0 0.6,5.8 1,5.8 0.3,0 0.9,-2.7 0.9,-4.5s0.3,-0.3 0.5,0c0.2,0.4 1.8,3 3.1,2.6s1.2,-2.8 1.2,-3.3 -1,-3.7 -1,-3.7l-6.1,-3.9 -0.5,1.6c-0.3,1 -3.4,0.3 -3.4,0.3"
      android:fillColor="#b27129"/>
  <path
      android:pathData="M320,186s0.4,0.4 1,1.9 0.7,6.4 -0.1,6.4 -2.3,-1 -2.6,-1.6 0,-3.1 -0.4,-3.8a8,8 0,0 1,-1.2 -2.7l0.1,-2 1.4,-0.5z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="m319.6,184.5 l2.6,3.9 2.3,3.3 1.2,0.4a2.5,2.5 0,0 1,-3.2 0.5c-0.9,-0.5 -0.4,-2 -1.5,-4.5s-3.2,-3.6 -3.2,-3.6l0.7,-0.7z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M318.2,182.4s2.3,1.5 3.1,3.2 1.3,2.7 2.3,4.3 2.1,2.1 2,2.2c0,0.1 -0.7,0.3 -1.2,0a5,5 0,0 1,-1.4 -1.6l-2.4,-4.2c-0.5,-0.7 -2.3,-2 -2.3,-1.9s0.5,0.5 1.5,2.1a7,7 0,0 1,1.2 3.1l-1.1,-2c-1,-1.5 -2.8,-3.3 -2.9,-3s1,1.9 1.5,2.9l1.3,3.3 -1.8,-3c-1,-1.5 -1.2,-1.3 -1.6,-1.8l-1.4,-1.8s1,0.3 1.8,-0.3c1,-0.6 1.4,-1.5 1.4,-1.5"
      android:fillColor="#1e2121"/>
  <path
      android:pathData="M312.3,186.7s1.5,0.3 2.3,0.2h1.1s0.5,0.6 0.5,1.4 -0.5,2.1 -0.8,2.2c-0.2,0 -0.2,-1.3 -0.5,-1.8z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M313.1,190.6s0.5,1.6 0.1,3.9 -1.7,4.6 -2,4.6 -0.2,-2.6 -0.4,-4v-0.4l-0.5,1.6 0.4,-2.6 -0.1,-0.8 1.6,-2z"
      android:fillColor="#b27129"/>
  <path
      android:pathData="M310.3,192.3c-0.5,0.5 -1.2,0.4 -1.8,0.6s-0.9,1 -0.9,1 0.5,-0.3 0.7,-0.2c0.3,0.1 0.5,0.1 0.1,1.2s-2.2,3 -2.1,3c0,0 2,-1.5 2.5,-2.5 0.7,-0.9 0.6,-1.4 0.8,-1.8 0,-0.3 0.8,-0.7 1,-0.7q0.3,-0.2 0.6,0.7l0.6,2.6 0.8,-2.6c0.2,-1 0.2,-2.9 0.2,-2.9l-0.6,-0.1c-0.5,-0.1 -1.5,-1 -1.5,-1s0.2,2 -0.5,2.7z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M318.3,180.8s0.6,1.5 -0.7,2.6q-0.5,0.5 -1.4,0.6l-1.7,-0.6 -4,-1.6 -2,-0.6 -1,-0.3 0.2,-1.1h3l3,1.2 1.3,0.5 1.1,0.2h0.9l0.7,-0.2 0.4,-0.3z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M318.3,180.8c0,0.3 -0.3,1.1 -0.6,1.3 -0.3,0 -0.8,0.3 -2,0s-2.3,-1 -3.6,-1.3q-1.9,-0.6 -3.3,-0.7c-1.4,-0.1 -0.5,-0.4 -0.5,-0.4s2.3,-0.3 4.3,0.5c2,0.7 2.9,1.2 4,1.2 1.4,0 1.7,-0.6 1.7,-0.6"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M307.6,179.6h0.9q0.6,-0.2 0.7,-0.3c0.1,-0.1 0,0.5 -0.2,0.7s-1,0.1 -1.2,0.1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M315.3,184.4s1.3,1.3 1,2.4l-2.7,-1.2 -4.2,-1.7 -2.3,-0.7 -0.3,-0.4 0.6,-0.8 0.2,-0.8 2.8,0.4 3,1z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M312.4,186.3s2.4,3.1 1.8,4.1l-5.6,-3.5 -2,3.3 -4.7,-2.3s3.8,-3 4.2,-3.5l0.5,-0.7 2,0.4 3.4,1z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M311,181.6s2,0.5 3.3,1.2 3,0.8 3,0.8 -0.6,0.7 -2,0.8c-0.4,0 -1.4,-0.8 -2,-1.3s-2.6,-1 -2.6,-1z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M307.6,180.6s1.4,0.3 2.2,0.7c1,0.3 1,0.2 1.2,0.1q0.4,-0.1 0.3,-0.2c-0.1,-0.1 0.2,1 -0.5,1s-1,-0.3 -1.8,-0.7c-1,-0.4 -1.5,0 -1.5,0v-1z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M315.4,186s1,0.7 0.9,0.9q-0.3,0.1 -1.6,0.4a3,3 0,0 1,-2 -0.4c-0.6,-0.2 -1,-1.3 -2.5,-2l-2.8,-0.9 -1.3,-0.2 1,-0.4 3.2,0.6 2.8,0.9 1.7,0.8 0.6,0.4zM310,188 L310.6,190.6 310.2,192.1 309.4,192.7 308.6,192.4 308.3,191.9s0.4,-0.6 0,-1.1c-0.3,-0.6 -2,-1.2 -2,-1.2s0.7,-1.3 1.4,-1.6c0.8,-0.3 0.4,-1.2 0.4,-1.2z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M306.8,182.8s0.5,0.4 1.5,0.5q0.8,0 2.2,0.5l1,-0.3 -0.5,0.4c0.9,0.2 2,0.7 2.5,0.9a6,6 0,0 1,2 1.3l-2,-1 -3,-0.9c-2,-0.3 -4,-0.2 -4.3,-0.4 -0.4,0 0.1,-0.1 0.4,-0.3 0.2,-0.2 0.2,-0.7 0.2,-0.7"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M314,189.8s0.7,0.7 0,0.9 -2.4,0.2 -3.2,-0.7l-0.6,-1.2 -2.4,-1.7c-0.7,-0.5 0.3,-0.5 0.3,-0.5l3.6,1.5z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M314,189.9s-1.2,-1.8 -4.9,-3.3l1.2,-0.2h-2l-0.8,-0.3c-2,-0.6 -2,-0.6 -2.2,-0.8q-0.2,-0.3 -0.6,0.3l-0.7,1a5,5 0,0 1,2.5 -0.1c1.1,0.3 1,0.7 1,1 -0.1,0.5 -1.4,2 -1.3,2.1 0.2,0 1,-1.5 2,-1.3q1.3,0.6 1.2,0.8c-0.1,0.2 0,-0.6 -0.2,-0.8s-1.3,-1.3 -1,-1.3q0.6,0 3.3,1.2c1.8,0.9 2,1.1 2.6,1.7z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M308.1,187s2.8,0.8 2.7,3.8 -2.3,2.4 -2.5,1.3 0.3,0.5 1,0.4q1.2,-0.3 1,-2.2 0,-1.4 -1,-2.2l-1.2,-1z"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M296,179.9s0.4,0.2 -0.4,2.3 -1.3,2.8 -2.5,4.2c-2.2,2.4 -3.2,2.5 -3,3.5s1.1,0.8 1.3,0.8 4.3,-6 4.3,-6l0.9,-3.5 -0.2,-1 -0.3,-0.3z"
      android:fillColor="#6c3f18"/>
  <path
      android:pathData="M306.1,175.6s1.2,1.8 0.8,5c-0.3,3.4 -4.7,6.3 -4.7,6.3l-6.4,4.4 -2.7,-0.2h-0.8q-0.7,-0.3 -0.9,-0.5l-0.5,-1.2 0.9,-1 2.3,-2.3 1.4,-2s0.7,-1.3 0.8,-2.5q-0.1,-1.7 -0.2,-1.7l0.5,0.8 0.3,1.8 -0.2,2 1.6,-1.2 2.2,-1s0.7,0 1,-1.3c0.5,-1.2 0.9,-3 0.9,-3.8l-0.1,-1.8h0.3c0.2,0 0.7,1.2 0.7,1.7l0.4,2.8 1,-1 1,-1.5q0.3,-0.9 0.4,-1.8"
      android:fillColor="#dbad6c"/>
  <path
      android:pathData="M306.8,192.2s0.5,0.4 0.3,1.6 -1.1,1.8 -1.6,1.8V194l-1.4,-1.2 -1.5,-0.4 0.3,-0.3c0.1,-0.2 1.7,-0.8 1.7,-0.8z"
      android:fillColor="#904720"/>
  <path
      android:pathData="M305.7,192.2s0.5,0.4 1.2,0.3 1,-1.3 0.6,-1.8 -0.2,1 -0.7,1.3c-0.4,0.1 -1,-1.2 -1.5,-1.3 -0.7,-0.1 -1.6,0.3 -1.9,0.7s1,0 1,0l0.7,0.3h0.5z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M296.5,187.6s0.8,0.3 1.7,0.1l3.1,-1 1.8,-1.3c0.7,-0.7 3.1,-2.2 3.4,-5.1 0.2,-3 -0.4,-4.7 -0.4,-4.7s3.5,3.8 0.5,8.2c-2,2.8 -3.8,3.7 -3.8,3.7s3.4,-1 3.7,-0.2 -0.1,2 -0.2,2.3c0,0 1.9,0.4 2.2,1.4 0.1,0.6 -2.2,-0.7 -4,-0.2s-2.5,1.4 -2.5,1.4 -0.4,-0.5 -2,-0.5c-1.5,0 -2.2,0.7 -3.1,0.7 -1,0 -3.7,-0.6 -4.2,-1.4l1.8,-2.2c0.8,-1.6 2,-1.2 2,-1.2"
      android:fillColor="#904720"/>
  <path
      android:pathData="M303.8,179.7s1.3,-1.3 1.8,-2.3q0.5,-1.6 0.5,-1.8c0,-0.2 0.2,1 -0.6,2.3s-1.7,2 -1.7,2z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M303.4,176.7s1.1,4.4 0,6.1c-1.3,1.8 -6.5,4.1 -6.5,4.1s4,-2.1 5.2,-4.6 1.1,-4.5 1.1,-4.5z"
      android:fillColor="#904720"/>
  <path
      android:pathData="M298.2,187.7s1.7,0 2.6,-0.6 2.3,-1.7 2.3,-1.7 -1.2,1 -1.6,1q-0.6,-0.1 -0.5,-0.9s-0.3,0.8 -1,1.4c-0.8,0.5 -1.8,0.8 -1.8,0.8m3.9,-5.4s1.4,-1.4 1.4,-4.4 -1.2,-2.5 -1.2,-2.5 1,0.7 0.7,3c-0.2,2.3 -1,4 -1,4zM303.8,182.6s1.6,-0.3 2.1,-1.3 0.6,-2.7 0.6,-2.7 -0.2,1.7 -0.7,2.3 -2,1.7 -2,1.7m-1,9.5s0.8,-1.5 1.9,-1.1 1,1.2 1,1.2 -0.4,-0.6 -1.2,-0.7 -1.6,0.6 -1.6,0.6zM299.9,189.4 L303.6,188.4 306.6,187.4s-1.2,1.3 -3,2.3c-1.6,1 -3,1 -3,1s3.2,-1.4 3.4,-2c0,0 -3,0.9 -4,0.7z"
      android:fillColor="#1e2121"/>
  <path
      android:pathData="M296.2,185.8a2.6,2.7 67.8,0 1,-3 4.4"
      android:fillColor="#fff"/>
  <path
      android:pathData="M296.1,186.1a2.3,2.4 67.8,0 1,-2.7 3.8"
      android:fillColor="#f16e16"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M295,187.4a1,1 0,1 1,-0.8 1.4"/>
  <path
      android:pathData="M295.2,188.6a0.3,0.3 0,1 1,-0.6 -0.1l0.3,-0.3a0.3,0.3 0,0 1,0.3 0.4"
      android:fillColor="#d5d3ca"/>
  <path
      android:pathData="M296.8,184.2s1.6,-1.5 1.9,-3.5q0.3,-3.1 0.1,-3.2l0.9,1 0.3,1.4 -0.5,2.6 1.6,-0.8c0.6,-0.8 1.5,-4.2 1.2,-6.3 0,0 0.4,1 -0.2,3.8 -0.6,2.9 -1,3.2 -3.2,4.4a10,10 0,0 0,-3.8 4.2l-2.3,3 1.6,-2.6c0.8,-1.8 1.7,-2.8 1.7,-2.8z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M296.1,185.4s0.7,-2 0.7,-3.1 -0.7,-2.4 -0.7,-2.4 1,-0.1 1,2.2c0.1,2.4 -0.4,2.6 -1,3.3"
      android:fillColor="#904720"/>
  <path
      android:pathData="M299.2,182.8s0.7,-1.7 0.7,-3c0,-1.1 -1,-2.3 -1,-2.3s1.1,0.3 1.2,2.4c0.1,2.2 -0.2,2.2 -1,3z"
      android:fillColor="#4d2a15"/>
  <path
      android:pathData="M292.1,191s-0.6,-0.8 -0.5,-1.3 0,-0.7 1.4,-2.2a13,13 0,0 0,2.5 -3c0.3,-0.9 1.5,-3 0.6,-4.6 0,0 0.6,2.4 -0.8,4.3a16,16 0,0 1,-3.3 3.7q-1.5,1 -1.2,1.7 0,0.9 0.3,1c0.4,0.3 1,0.3 1,0.3z"
      android:fillColor="#ab6d29"/>
  <path
      android:pathData="M305.5,195.6s0.3,0 0.5,-0.3 0.2,-1 -0.3,-1.6a3,3 0,0 0,-1.7 -1.1l-2,-0.4s0.4,0.8 1.3,1.3c0.4,0.2 1.2,0 1.6,0.4s0.6,1.7 0.6,1.7"
      android:fillColor="#6c4119"/>
  <path
      android:pathData="M305.5,195.6s1.5,-0.5 1.6,-2l-0.1,-1.2s0.2,0.5 0.8,0.7l0.6,-0.2s-1,2.8 -3,2.7z"
      android:fillColor="#6c4119"/>
  <path
      android:pathData="m294.8,195.3 l0.8,0.8 0.9,1 3.7,-1 1.2,-1.2 -1,-1.6h-2.7l-3,2z"
      android:fillColor="#bf802d"/>
  <path
      android:pathData="m303.8,194 l-0.7,-0.9 -0.7,-0.6c-0.6,-0.3 -2.8,-0.3 -2.8,-0.3l-1.6,0.3s-0.5,0.5 -1.3,0.3l-2.6,-1 -1.9,-0.7s-1.3,-0.6 -1.6,-0.5 -1.5,1.3 -1.5,1.3 -0.2,0.7 0.3,0.7 -0.7,0.2 -0.7,0.2 -2.8,1.6 -3.2,4.4c-0.5,2.7 4.6,6.8 6.5,4.7 0,0 -2.8,-2 -2.2,-3.7q0.7,-2.6 4,-3c2.1,0 2.3,-0.3 3.2,-1s2.7,-1 4,0.3 -5,2.7 -5,2.7l0.7,1s7.7,-2.7 7,-4.1z"
      android:strokeWidth=".4"
      android:fillColor="#f9c83a"
      android:strokeColor="#8f4620"/>
  <path
      android:pathData="M289.4,200.7s-3.1,-0.8 -2.9,-3.7c0.3,-2.8 3,-3.9 3.4,-4s0.3,-1.6 0.8,-1.8a2,2 0,0 1,2 0.5l1.4,0.8s-5.5,2.4 -5.5,5.6c0,2.1 0.8,2.5 0.8,2.5z"
      android:fillColor="#fcf3d8"/>
  <path
      android:pathData="M303.8,193.8s-0.4,0.2 -0.5,0c-0.6,-1 -2,-1.4 -3,-1.3 -1.5,0 -2.4,0.5 -3.4,0.5s-0.7,-0.1 -1.8,-0.2c-1.1,0 -3.5,-1.8 -4,-1.6q-0.9,0.5 -0.8,1.1c0.1,0.4 -1,0.4 -1,0.1 -0.2,-0.2 0.7,-1.7 1.7,-1.7 2.6,0 4.6,1.7 5.8,1.7s1.7,-0.7 3.4,-0.7 3.5,0.8 3.6,2z"
      android:fillColor="#fdeaaf"/>
  <path
      android:pathData="M295.2,195.8s1.3,-0.2 2.2,-0.6l2.2,-0.8 -2,1.2 -2,0.5z"
      android:fillColor="#513625"/>
  <path
      android:pathData="M290.8,202.3c-0.2,0 -1.3,0 -3,-1.1 -1.8,-1.2 -2.3,-3.2 -2.3,-3.2s-0.4,-1.9 1.4,-3.8 2.1,-1.3 2.2,-1.2q0,0 0,0.1l-1.7,1.3 -0.7,1.3 -0.7,1.2v1.4l0.8,1.5 2.5,1.5z"
      android:fillColor="#f9c83a"/>
  <path
      android:pathData="M289.8,191.4v-0.2,0.3 -0.3,0.3 -0.3l-0.3,0.2h0.2v-0.2l-0.2,0.2 0.2,-0.2h-0.2v0.2l0.2,-0.2h-0.2,0.2 -0.2,0.2 -0.2,0.2l-0.2,-0.1 0.2,0.1 -0.2,-0.1 -0.1,0.1 -0.4,0.5 -0.2,0.6v0.1q0,0.5 0.4,0.6h1.4l0.1,-0.4 -0.3,-0.2 -0.7,0.1h-0.3v-0.4l0.3,-0.5 0.2,-0.1zl0.1,-0.2 -0.3,-0.2 -0.2,0.3 0.3,0.2z"
      android:fillColor="#8b5122"/>
  <path
      android:pathData="m295.1,194.1 l-3.8,1.1c-0.3,0.2 1.2,0 2.4,0l1.3,0.1 0.9,-0.2c1.4,-0.5 4.4,-1.6 4.7,-0.3 0.2,1 -4.1,2 -4.1,2v0.5l3.4,-0.9 1.9,-1 0.5,-0.9 -1.7,-1.2H298l-1.2,0.3z"
      android:fillColor="#f9c83a"/>
  <path
      android:pathData="M289.7,199.5c0.3,0.8 0.9,2 1.6,2.3 0,0.1 -0.5,0.5 -1.7,0.1 -1.2,-0.3 -3,-0.7 -4.1,-4v0.7l0.7,1.3 1.1,1.2 1.9,1.1 1.2,0.3 1,-0.2 0.6,-0.4 -1.1,-1 -1.4,-2.3z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M302.1,232.6v1.2h-0.7v-1.5zM297.4,198.2 L296.2,196.8 295.4,196.9 296.6,198.7z"
      android:fillColor="#fcca3d"/>
  <path
      android:pathData="m302,235.5 l-1.7,-1 -2.4,-0.6v0.3h0.2l2.1,0.6 1.7,1zM302,232.4h-3l-1.3,0.7 0.1,0.2a4,4 0,0 1,2.6 -0.7l1.4,0.1v-0.3zM301.6,229.7 L299,231.1 297.4,232.3 297.6,232.5 298.1,232.1 300.7,230.5 301.7,230zM299.9,227s0,0.7 -0.3,1.2l-0.6,0.8 -2.5,2.6 0.2,0.3 2.5,-2.7 0.7,-0.8q0.4,-1 0.3,-1.4zM297.8,225.8a4,4 0,0 1,-0.2 2.2l-1,2.2 -0.4,0.9 -0.2,0.3 0.2,0.2s1.3,-2.2 1.7,-3.5q0.3,-0.7 0.2,-1.5v-0.9zM295.7,225.5v1.2q0,1 -0.2,1.7l-0.7,1.8 -0.3,1 0.3,0.1 1,-2.8q0.3,-0.8 0.2,-1.8v-1.2zM294.1,225.5v1.2l-0.3,2 -0.5,2.5h0.3l0.2,-0.8 0.3,-1.7c0.3,-1 0.3,-3.2 0.3,-3.2zM292.3,225.8v2.9l-0.4,2.5h0.3l0.4,-2.4 0.1,-1.4v-1.6zM291,226v0.2l-0.3,2.2 -0.6,1.9 -0.2,1h0.3l0.8,-2.9 0.3,-2.2v-0.2zM289.4,226v0.7q0.1,1 -0.4,2l-1.3,1.6 -0.7,0.4 -0.2,0.2 0.1,0.3s1.7,-1 2.4,-2.3q0.5,-1.2 0.5,-2.2v-0.7zM288.5,225.7v0.2l-1,1.9q-0.6,0.6 -1.3,1.1l-0.8,0.5 0.2,0.3s1.4,-0.8 2.1,-1.7l1.1,-2.2h-0.3zM287,225v0.1l-1,1.3q-0.2,0.3 -0.9,0.6l-0.6,0.4 0.1,0.3 0.7,-0.4 1,-0.7 0.7,-0.9 0.3,-0.6zM286.3,224 L286,224.3 285.1,225.3 284.6,225.7 284.2,226 284.3,226.3 285,225.9c0.7,-0.6 1.5,-1.7 1.5,-1.7zM285.9,223.4 L285.7,223.6 284.8,224.2 284,224.5v0.4l0.6,-0.3c0.7,-0.3 1.5,-1 1.5,-1zM285.7,222.7 L284.9,223.1 284.3,223.4 283.9,223.6v0.3q0.3,0 0.5,-0.2l1.4,-0.7zM285.3,221.4 L284.8,221.9 283.9,222.2v0.3a2,2 0,0 0,1.2 -0.5l0.5,-0.4zM285.4,219.2 L284.8,219.7q-0.5,0.4 -0.7,1h-0.1v0.5l0.2,-0.2 0.2,-0.2q0.1,-0.4 0.7,-0.9l0.5,-0.5zM296.1,206.4h1.6l0.1,-0.2v-0.1h-0.1v0.3,-0.1h-0.2,0.1 -0.1v-0.2l0.1,0.1v-0.1,0.1 -0.1h-1.5zM296.1,205.4h0.2q0.3,0 0.6,-0.2 0.6,-0.2 1,-0.6l0.5,-0.3h0.3v-0.4l-0.6,0.2 -1,0.7 -0.8,0.3zM296.1,203.9 L296.8,203.7 298,202.9q0.6,-0.4 1,-0.4v-0.3q-0.4,0 -0.8,0.2l-1.2,0.7 -1,0.5v0.3zM295.5,202.5 L297,201.5 298.6,200.3 298.5,200 296.8,201.2q-0.8,0.8 -1.5,1l0.2,0.4zM294.5,201 L297.7,198.7 297.5,198.4 294.3,200.7zM293,199.4a21,21 0,0 1,3.4 -2.3l-0.2,-0.2 -1.5,1 -1.9,1.3zM292,198.4 L294.6,196.6 295.4,196.1 295.3,195.8 294.5,196.3 291.8,198.3z"
      android:fillColor="#816c2a"/>
  <path
      android:pathData="m294.5,195.4 l0.1,0.1 2.5,2.5q1.6,1.9 1.8,4.1v0.5a10,10 0,0 1,-2.5 5c-2,2.3 -4.8,4.4 -7,6.4a18,18 0,0 0,-3 3.1,6 6,0 0,0 -1.2,3.7 6,6 0,0 0,1.1 3.4,4 4,0 0,0 3.9,2c1.8,0 3.8,-0.7 5.6,-0.7q2.4,-0.2 4.4,1.9c1.3,1.4 1.7,3.6 1.7,5.7q0,2.4 -0.4,4.2l-0.4,1.8h0.3s0.8,-2.8 0.8,-6c0,-2.1 -0.4,-4.5 -1.8,-6a6,6 0,0 0,-4.6 -1.9c-1.9,0 -3.9,0.6 -5.6,0.6a4,4 0,0 1,-3.6 -1.8,6 6,0 0,1 -1,-3.3v-0.1q0,-2.4 1.9,-4.3c1.8,-2 4.5,-4 7,-6.2 2.3,-2.2 4.4,-4.5 4.7,-7.5v-0.5c0,-1.8 -1.1,-3.5 -2.3,-4.8l-2.3,-2.2z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="m292.1,198.4 l-0.4,-0.3 -0.3,0.1 0.6,0.5zM293.1,199.4 L292.8,199.1h-0.1l0.2,0.4z"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="m294.5,201 l-0.2,-0.4 -0.2,0.2zM295.5,202.5 L295.4,202.2h-0.3l0.4,0.5zM296.2,204 L296,203.6v0.4zM296.2,205.5v-0.4l-0.2,-0.1v0.5zM296.2,206.5v-0.5l-0.1,-0.1v0.6zM284,221.1l0.1,-0.7c0.1,-0.3 -0.3,0.1 -0.3,0.1v0.5z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="m284,222.1 l-0.3,0.1 0.1,0.5 0.1,-0.2z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M284,223.9v-0.4h-0.2v0.6z"
      android:fillColor="#78732e"/>
  <path
      android:pathData="M284,224.9v-0.4h-0.1v0.4zM284.3,226.3v-0.4l-0.2,0.1zM284.7,227.7 L284.6,227.2h-0.2l0.2,0.6z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m285.6,229.7 l-0.2,-0.3h-0.2zM287.1,231.1 L286.6,230.8c-0.3,-0.3 -0.1,0.2 -0.1,0.2l0.4,0.2zM296.9,231.9 L296.5,231.6q-0.2,-0.1 0.2,0.4c0.1,0.3 0.2,-0.1 0.2,-0.1"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="m297.7,232.5 l-0.3,-0.3v0.3h0.2z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M297.9,233q-0.2,-0.1 -0.2,0.1l0.2,0.3zM297.9,234.3v-0.5q0,-0.1 -0.1,0v0.5z"
      android:fillColor="#a8ac71"/>
  <path
      android:pathData="m297.4,235.7 l0.2,-0.4h-0.2zM282.6,183.7s0.5,-0.2 0.1,0.4l0.3,-0.5h-0.5z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m294.6,195 l0.4,0.3h-0.5z"
      android:fillColor="#f9c83a"/>
  <path
      android:pathData="m295,195.3 l-0.7,0.1c-0.2,0 0.2,-0.2 0.2,-0.2l0.4,0.1z"
      android:fillColor="#8f4620"/>
  <path
      android:pathData="M301,239.1s0.3,0 0.5,-0.3 -0.1,0.4 -0.1,0.4l-0.3,0.1 -0.2,-0.2z"
      android:fillColor="#977c2e"/>
</vector>
