<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M-0.03,0L640,0l0,480l-640.03,0z"/>
    <path
        android:pathData="M-80,0l720,0l0,480l-720,0z"
        android:fillColor="#198a00"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M555.53,172.5L640,172.5l0,307.5l-84.47,0z"
        android:fillColor="#ef7d00"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M471.16,172.31l84.38,0l0,307.5l-84.38,0z"
        android:fillColor="#000001"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M385,172.5l86.16,0l0,307.5l-86.25,0z"
        android:fillColor="#de2010"
        android:fillType="evenOdd"/>
    <path
        android:strokeWidth="1"
        android:pathData="M583.38,61.5s31.88,-13.59 34.88,-16.41c1.41,1.69 -12.75,18.75 -40.22,25.03 24.94,-5.81 46.22,-24.38 50.63,-23.81 1.22,0.28 0.94,18.09 -56.25,33.56 39.56,-10.31 62.53,-29.44 62.25,-27.38 0.28,0.47 -3.94,15 -38.53,26.34 9.66,-2.25 35.91,-18.19 35.63,-15.09 0.84,1.22 -25.31,34.88 -72.75,23.91 37.88,9.84 62.34,-13.88 65.81,-13.41 0.75,0.09 -6.94,20.63 -54.56,21.94 22.78,-2.34 16.22,-0.09 16.22,-0.09s-13.41,10.5 -29.72,3.38c12.75,3.47 14.16,3.66 14.44,4.69 -0.84,1.5 -11.25,3.56 -21.47,-1.88 8.16,3.38 15.47,3.94 15.56,5.16 -0.09,0.38 -5.25,3.38 -9.94,1.22s-47.16,-28.22 -47.16,-28.22l72.56,-19.97 2.63,0.94zM498.06,132.38c-6.38,0 -6.56,5.63 -6.56,5.63s-0.47,0.47 -0.19,3.19l1.59,-2.44c0.75,0.09 3.66,0.94 8.25,-2.63 -4.22,4.41 -1.69,6 -1.69,6s-0.94,3.47 2.25,4.13c-0.94,-1.41 -0.38,-2.63 -0.38,-2.63s4.5,-0.47 4.22,-5.91c0.09,4.97 2.81,6.19 2.81,6.19s0,2.72 2.91,3c-1.5,-1.41 -1.22,-3.56 -1.22,-3.56s3.75,-2.81 0.66,-7.5c1.88,-1.13 3.47,-4.22 3.47,-4.22s-2.63,-1.13 -3.84,-2.06c-0.56,-1.22 0,-7.97 0,-7.97l-1.69,-8.72 -4.5,13.59c0.19,-1.88 0.28,5.91 -6.09,5.91z"
        android:strokeLineJoin="round"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"/>
    <path
        android:strokeWidth="1"
        android:pathData="M529.56,112.78c0.19,0.09 4.78,5.25 9.28,4.97 1.69,-1.41 -3.47,-4.41 -3.47,-5.16 1.88,1.69 9.47,8.25 14.44,5.91 1.88,-2.81 -3.56,-2.34 -9.56,-9.94 4.22,2.81 14.72,8.91 19.69,6.56 1.97,-2.25 -10.97,-9.28 -15.38,-14.72l-11.25,-5.72 -15.19,12.47 11.53,5.63z"
        android:strokeLineJoin="round"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"/>
    <path
        android:strokeWidth="1"
        android:pathData="M512.41,70.13s5.16,-3 25.88,-1.5c2.25,0.28 14.06,-4.03 17.91,-5.34 6,-1.13 25.31,-5.25 30.38,-9.19 3.75,-0.47 -0.94,6.66 -5.63,8.44 -4.88,2.16 -22.59,8.34 -28.97,7.5 7.59,0.09 3.38,6.28 -9.19,3.38 6,3.38 3.75,3.94 3.75,3.94s-10.59,0.56 -13.41,-1.88c7.13,2.72 4.13,3.75 4.13,3.75s-7.22,0.66 -10.59,-0.94c5.16,1.59 2.53,2.53 2.53,2.53s-4.22,0.75 -7.69,-0.56c-3.56,-1.22 -8.81,-10.03 -9.09,-10.03z"
        android:strokeLineJoin="round"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:strokeWidth="1"
        android:pathData="m520.66,115.69 l0.75,13.69 -0.75,1.13c-0.28,0.38 -9.66,-1.5 -8.44,5.63 0,3 0,3.75 2.06,5.34 -0.47,-1.88 -0.38,-3.28 -0.38,-3.28s2.72,1.59 5.25,-2.81c-1.78,4.31 -0.56,5.91 -0.09,6 0.38,0.75 -0.75,4.22 2.81,4.13 -1.41,-1.31 -0.75,-3.19 -0.75,-3.19s3.75,-0.56 2.44,-7.22c1.31,-1.31 1.88,0 1.88,0s0.38,3.94 3.66,3.09c1.41,0.84 -0.28,3 -0.28,3s2.34,0 3.09,-1.88c0.66,-2.06 1.5,-5.63 -2.34,-7.13 -0.47,-1.41 1.5,-1.5 1.5,-1.5s2.44,0.84 3.19,2.06 0.47,-3.19 -2.63,-3.75c-3.75,0 -3.94,-0.84 -3.94,-0.94l-0.94,-12.84z"
        android:strokeLineJoin="round"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M533.69,127.31c0,-0.66 -6.38,-9.28 -4.69,-10.31 1.59,0.28 4.22,4.22 6.47,2.91 -0.66,-1.5 -2.63,-0.66 -4.78,-4.59 -2.25,-4.5 -2.53,-11.16 -10.31,-18.38 5.06,7.69 16.31,12.19 16.88,9.94s-10.03,-10.69 -9.47,-12.75c2.06,4.13 12.56,13.5 20.25,12.75 0.56,-1.88 -6.28,-5.44 -8.06,-8.81 -4.97,-3.38 -18.19,-14.72 -18.38,-17.34a30.94,30.94 0,0 0,-9.28 -9.94l-0.75,-1.59c-3,-7.03 0.94,-9.38 3.28,-9.66 1.88,-0.28 2.44,0 3.75,-0.66l-4.78,-1.88c2.16,1.59 7.5,0.19 6.38,4.69 2.34,-0.94 7.41,-6.94 -5.72,-8.25 -4.31,-4.88 -21.84,-7.69 -26.25,13.69 0.38,0.28 0.56,0.56 1.69,1.31 -5.63,-2.63 -20.53,-4.69 -25.88,-5.63 -14.63,-4.13 -29.72,-14.25 -31.13,-13.03 -1.88,0.94 8.81,10.88 8.25,11.06a246.56,246.56 0,0 0,-25.78 -12.75c-5.63,-2.06 -11.16,-6.94 -11.72,-6 -2.06,4.22 8.91,14.91 10.97,16.13s18.09,8.72 17.81,8.81c-24.19,-10.97 -28.13,-13.03 -29.44,-14.25 -2.16,-0.47 -7.31,-7.5 -8.72,-7.03 -0.84,0.75 0.75,11.81 12.28,16.88 1.88,1.31 24.75,9.47 24.56,10.13 0,0.19 -26.25,-10.78 -27.19,-11.25 -5.06,-1.88 -11.25,-8.81 -12.38,-8.06s2.81,8.25 7.87,10.59c2.72,1.13 12.94,6.19 22.13,9.38 0.56,0.28 -16.59,-6.47 -24.84,-9.84 -3.75,-2.06 -5.44,-4.69 -6,-4.13 -0.94,0.56 1.31,11.63 27.84,17.81 0.66,0.38 9.38,-1.97 8.91,-1.69a105.94,105.94 0,0 1,-14.81 2.63c-0.28,0.75 1.69,4.69 15.38,3.94 1.69,-0.09 11.25,-3.47 10.41,-2.81a163.13,163.13 0,0 1,-13.88 4.59c-0.84,0 -5.25,0.56 -5.44,0.94 -0.19,0.75 3.28,3.56 10.59,3.94 6.56,0.38 18.94,-3.94 18.75,-3.66 -0.38,0.28 -12,4.31 -12.28,4.5 -0.47,0.28 -4.69,0.47 -4.88,0.84 -0.38,0.84 6.19,6.94 25.41,0.38 -1.88,2.63 -11.25,4.41 -11.25,4.88s2.06,2.34 5.34,3.09c1.59,0.38 3.84,0.28 5.91,0 3.75,-0.75 7.5,-2.16 12.47,-6.75 0.56,1.41 -12.28,7.97 -11.72,8.53 2.81,2.63 12.09,-0.28 12.66,-0.66 0.66,-0.28 17.81,-10.03 17.81,-11.25 0.28,1.5 -22.22,13.78 -21.94,14.25 1.41,2.25 9.09,-0.09 9.38,-0.38l10.13,-5.44c0.28,-0.09 -11.06,6.56 -10.13,7.69 -0.47,4.41 17.81,-3 19.22,-3.94 0.66,-0.47 -8.81,4.13 -8.81,6.28 3.09,5.16 12.84,3.56 14.34,2.44 0.75,-0.56 -0.94,3.75 -0.38,3.38a15,15 0,0 0,2.81 -4.03c-0.19,1.5 -1.31,3.75 -1.88,6.38s-0.84,5.53 -1.69,8.63c-0.19,1.31 6,-2.72 5.06,-12.38 0.56,5.06 -1.88,14.44 -1.22,15 1.22,0.94 4.88,-4.69 5.25,-8.53 1.03,1.88 3.28,5.91 5.25,6.84 -0.19,-2.91 0,-2.81 -0.94,-5.63q0.75,-5.91 0.75,-15.09c5.25,10.03 7.41,14.34 5.91,22.03 1.59,0.66 4.03,-5.44 3.84,-8.44a12.19,12.19 0,0 0,10.78 9.56"
        android:strokeLineJoin="round"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M446.31,63.47s-3.66,2.72 -8.06,2.53c1.13,4.5 10.31,1.03 10.31,1.03s-4.41,4.97 -7.59,5.72c1.88,1.59 9.94,1.03 11.25,0.28s3.75,-3.94 3.75,-3.94 -8.25,9.56 -9.38,9.47c-0.28,1.13 8.53,0.75 10.31,-0.94s6.56,-4.13 6.56,-4.13 -12.19,8.44 -12.66,8.44c4.5,1.13 13.59,-1.5 19.41,-5.06 -8.63,5.72 -9.38,6.75 -13.78,8.72 3.94,0.84 5.91,3.47 19.03,-2.25 7.5,-3.56 11.81,-10.13 11.81,-10.13a76.88,76.88 0,0 1,-20.25 16.88c-0.47,0.94 8.81,4.88 20.53,-7.41"
        android:strokeLineJoin="round"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M510.06,90s0.84,3.75 4.03,6.56 3.38,5.63 3.38,5.63m-8.91,-36.56s1.03,3.47 4.31,5.63c3.38,2.06 8.44,8.81 8.72,10.03 0.38,1.41 2.34,11.81 2.16,12.94m-35.44,-23.44c0.19,1.41 -4.69,7.78 2.06,15.38 -6.19,7.5 -6.19,8.44 -6.19,8.44s3.47,1.88 9.56,-3.38c10.13,12.38 6.75,18 6.75,18"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M499.66,100.5s-0.94,-1.31 0.75,-6.28c1.5,1.88 3.19,2.16 3.84,2.81 0.75,0.84 8.44,1.88 8.81,7.03"
        android:strokeLineJoin="round"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M508.66,53.91c0,-0.38 -1.59,-3.75 -7.31,0.38 3.09,0.19 6.09,1.97 7.31,-0.38z"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M563.22,68.91c0.28,0.19 17.06,3.75 22.69,1.97 -7.13,9.56 -21.47,3.09 -21.47,3.09 7.03,2.06 7.31,1.88 9.56,3.94 0.75,1.88 -12.94,0.94 -17.34,-1.59 12.47,3.94 12.75,3.75 13.13,5.16 0.56,2.16 -20.44,-0.56 -22.22,-3.28 5.63,4.31 8.63,5.16 11.72,7.13 -3.75,1.69 -10.78,3.38 -23.63,-5.72 16.88,15.38 32.16,14.44 34.59,17.16 -5.91,8.72 -28.78,-5.16 -39.28,-12.19s22.97,16.97 26.06,16.59c-1.5,2.34 -12.56,0.28 -13.31,-0.94"
        android:strokeLineJoin="round"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M567.53,85.03c-1.88,0.28 -7.78,0.28 -8.63,0.09"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M441.34,60.94s12.75,6.75 18.19,6.28a9.38,9.38 0,0 1,-3.56 1.88c1.31,0.56 5.06,2.53 10.69,1.31l-3.09,2.81s4.88,1.88 10.31,-0.94l-2.63,3.47 3.84,0.28"
        android:strokeLineJoin="round"
        android:strokeWidth="1.03"
        android:fillColor="#ef7d00"
        android:strokeColor="#000"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
</vector>
