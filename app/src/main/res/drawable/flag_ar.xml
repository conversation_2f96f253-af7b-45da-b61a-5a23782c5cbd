<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#74acdf"/>
  <path
      android:pathData="M0,160h640v160H0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m316.93,241.25 l27.36,59.52s0.48,1.15 1.25,0.86c0.77,-0.38 0.29,-1.54 0.29,-1.54l-22.75,-61.44m-0.67,23.23c-0.38,9.02 5.18,14.02 4.51,22.08s3.65,12.67 4.8,15.84c0.96,3.17 -1.15,4.99 -0.29,5.47 0.96,0.48 2.88,-2.02 2.3,-6.53s-4.03,-5.76 -3.26,-15.65 -4.03,-12.19 -2.88,-21.12"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m316.68,239.98l2.5,65.46s0,1.25 0.82,1.28c0.86,-0.06 0.85,-1.31 0.85,-1.31l2.49,-65.47m-9.51,21.21c-3.81,8.19 -0.57,14.93 -4.28,22.13s-1.48,13.1 -1.63,16.47c-0.33,3.29 -2.97,4.17 -2.36,4.95 0.7,0.81 3.43,-0.76 4.63,-5.15s-1.52,-6.86 2.97,-15.71 0.94,-12.81 5.42,-20.61"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m316.95,238.71l-22.74,61.43s-0.48,1.15 0.27,1.49c0.81,0.27 1.29,-0.88 1.29,-0.88l27.36,-59.53m-16.9,15.95c-6.65,6.11 -6.25,13.58 -12.42,18.8s-6.38,11.54 -7.81,14.59c-1.56,2.92 -4.34,2.72 -4.07,3.67 0.34,1.02 3.46,0.61 6.25,-2.99s1.22,-6.92 8.76,-13.37 5.77,-11.47 12.9,-16.97"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m317.67,237.64l-44.52,48.05s-0.88,0.88 -0.32,1.48c0.65,0.56 1.53,-0.32 1.53,-0.32l48.06,-44.53m-21.72,8.27c-8.48,3.1 -10.97,10.15 -18.67,12.62s-10.31,8.22 -12.8,10.5c-2.56,2.1 -5.05,0.85 -5.17,1.83 -0.08,1.07 2.96,1.89 6.91,-0.37s3.78,-5.93 13.21,-9 9.72,-8.39 18.41,-10.74"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="M324.13,263.42c0.48,8.64 5.38,12.48 4.42,20.45 2.11,-6.24 -2.98,-11.14 -2.69,-20.35m-7.39,-22.85 l18.72,40.9 -15.65,-42.14"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M314.85,263.22c-2.86,8.17 0.19,13.59 -3.75,20.58 4.34,-4.96 1.51,-11.43 5.3,-19.83m1.91,-23.94l1.64,44.95 1.67,-44.92"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M306.36,259.48c-5.77,6.45 -5.02,12.63 -11.34,17.58 5.91,-2.92 5.77,-9.98 12.49,-16.29m10.93,-21.38l-15.68,42.15 18.74,-40.87"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M299.94,252.78c-7.8,3.75 -9.47,9.74 -17.2,11.9 6.57,-0.44 9.15,-7.01 17.77,-10.27m18.28,-15.57l-30.62,32.95 32.95,-30.58"
      android:fillColor="#85340a"/>
  <path
      android:pathData="m318.75,236.93l-59.52,27.36s-1.15,0.48 -0.86,1.25c0.38,0.77 1.54,0.29 1.54,0.29l61.44,-22.75m-23.23,-0.67c-9.02,-0.38 -14.02,5.18 -22.08,4.51s-12.67,3.65 -15.84,4.8c-3.17,0.96 -4.99,-1.15 -5.47,-0.29 -0.48,0.96 2.02,2.88 6.53,2.3s5.76,-4.03 15.65,-3.26 12.19,-4.03 21.12,-2.88"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m320.02,236.68l-65.46,2.5s-1.25,0 -1.28,0.82c0.06,0.86 1.31,0.85 1.31,0.85l65.47,2.49m-21.21,-9.51c-8.19,-3.81 -14.93,-0.57 -22.13,-4.28s-13.1,-1.48 -16.47,-1.63c-3.29,-0.33 -4.17,-2.97 -4.95,-2.36 -0.81,0.7 0.76,3.43 5.15,4.63s6.86,-1.52 15.71,2.97 12.81,0.94 20.61,5.42"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m321.29,236.95l-61.43,-22.74s-1.15,-0.48 -1.49,0.27c-0.27,0.81 0.88,1.29 0.88,1.29l59.53,27.36m-15.95,-16.9c-6.11,-6.65 -13.58,-6.25 -18.8,-12.42s-11.54,-6.38 -14.59,-7.81c-2.92,-1.56 -2.72,-4.34 -3.67,-4.07 -1.02,0.34 -0.61,3.46 2.99,6.25s6.92,1.22 13.37,8.76 11.47,5.77 16.97,12.9"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m322.36,237.67l-48.05,-44.52s-0.88,-0.88 -1.48,-0.32c-0.56,0.65 0.32,1.53 0.32,1.53l44.53,48.06m-8.27,-21.72c-3.1,-8.48 -10.15,-10.97 -12.62,-18.67s-8.22,-10.31 -10.5,-12.8c-2.1,-2.56 -0.85,-5.05 -1.83,-5.17 -1.07,-0.08 -1.89,2.96 0.37,6.91s5.93,3.78 9,13.21 8.39,9.72 10.74,18.41"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="M296.58,244.13c-8.64,0.48 -12.48,5.38 -20.45,4.42 6.24,2.11 11.14,-2.98 20.35,-2.69m22.85,-7.39l-40.9,18.72 42.14,-15.65"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M296.78,234.85c-8.17,-2.86 -13.59,0.19 -20.58,-3.75 4.96,4.34 11.43,1.51 19.83,5.3m23.94,1.91l-44.95,1.64 44.92,1.67"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M300.52,226.36c-6.45,-5.77 -12.63,-5.02 -17.58,-11.34 2.92,5.91 9.98,5.77 16.29,12.49m21.38,10.93l-42.15,-15.68 40.87,18.74"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M307.22,219.94c-3.75,-7.8 -9.74,-9.47 -11.9,-17.2 0.44,6.57 7.01,9.15 10.27,17.77m15.57,18.28l-32.95,-30.62 30.58,32.95"
      android:fillColor="#85340a"/>
  <path
      android:pathData="m323.07,238.75l-27.36,-59.52s-0.48,-1.15 -1.25,-0.86c-0.77,0.38 -0.29,1.54 -0.29,1.54l22.75,61.44m0.67,-23.23c0.38,-9.02 -5.18,-14.02 -4.51,-22.08s-3.65,-12.67 -4.8,-15.84c-0.96,-3.17 1.15,-4.99 0.29,-5.47 -0.96,-0.48 -2.88,2.02 -2.3,6.53s4.03,5.76 3.26,15.65 4.03,12.19 2.88,21.12"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m323.32,240.02l-2.5,-65.46s-0,-1.25 -0.82,-1.28c-0.86,0.06 -0.85,1.31 -0.85,1.31l-2.49,65.47m9.51,-21.21c3.81,-8.19 0.57,-14.93 4.28,-22.13s1.48,-13.1 1.63,-16.47c0.33,-3.29 2.97,-4.17 2.36,-4.95 -0.7,-0.81 -3.43,0.76 -4.63,5.15s1.52,6.86 -2.97,15.71 -0.94,12.81 -5.42,20.61"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m323.05,241.29l22.74,-61.43s0.48,-1.15 -0.27,-1.49c-0.81,-0.27 -1.29,0.88 -1.29,0.88l-27.36,59.53m16.9,-15.95c6.65,-6.11 6.25,-13.58 12.42,-18.8s6.38,-11.54 7.81,-14.59c1.56,-2.92 4.34,-2.72 4.07,-3.67 -0.34,-1.02 -3.46,-0.61 -6.25,2.99s-1.22,6.92 -8.76,13.37 -5.77,11.47 -12.9,16.97"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m322.33,242.36l44.52,-48.05s0.88,-0.88 0.32,-1.48c-0.65,-0.56 -1.53,0.32 -1.53,0.32l-48.06,44.53m21.72,-8.27c8.48,-3.1 10.97,-10.15 18.67,-12.62s10.31,-8.22 12.8,-10.5c2.56,-2.1 5.05,-0.85 5.17,-1.83 0.08,-1.07 -2.96,-1.89 -6.91,0.37s-3.78,5.93 -13.21,9 -9.72,8.39 -18.41,10.74"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="M315.87,216.58c-0.48,-8.64 -5.38,-12.48 -4.42,-20.45 -2.11,6.24 2.98,11.14 2.69,20.35m7.39,22.85l-18.72,-40.9 15.65,42.14"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M325.15,216.78c2.86,-8.17 -0.19,-13.59 3.75,-20.58 -4.34,4.96 -1.51,11.43 -5.3,19.83m-1.91,23.94l-1.64,-44.95 -1.67,44.92"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M333.64,220.52c5.77,-6.45 5.02,-12.63 11.34,-17.58 -5.91,2.92 -5.77,9.98 -12.49,16.29m-10.93,21.38l15.68,-42.15 -18.74,40.87"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M340.06,227.22c7.8,-3.75 9.47,-9.74 17.2,-11.9 -6.57,0.44 -9.15,7.01 -17.77,10.27m-18.28,15.57l30.62,-32.95 -32.95,30.58"
      android:fillColor="#85340a"/>
  <path
      android:pathData="m321.25,243.07l59.52,-27.36s1.15,-0.48 0.86,-1.25c-0.38,-0.77 -1.54,-0.29 -1.54,-0.29l-61.44,22.75m23.23,0.67c9.02,0.38 14.02,-5.18 22.08,-4.51s12.67,-3.65 15.84,-4.8c3.17,-0.96 4.99,1.15 5.47,0.29 0.48,-0.96 -2.02,-2.88 -6.53,-2.3s-5.76,4.03 -15.65,3.26 -12.19,4.03 -21.12,2.88"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m319.98,243.32l65.46,-2.5s1.25,-0 1.28,-0.82c-0.06,-0.86 -1.31,-0.85 -1.31,-0.85l-65.47,-2.49m21.21,9.51c8.19,3.81 14.93,0.57 22.13,4.28s13.1,1.48 16.47,1.63c3.29,0.33 4.17,2.97 4.95,2.36 0.81,-0.7 -0.76,-3.43 -5.15,-4.63s-6.86,1.52 -15.71,-2.97 -12.81,-0.94 -20.61,-5.42"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m318.71,243.05l61.43,22.74s1.15,0.48 1.49,-0.27c0.27,-0.81 -0.88,-1.29 -0.88,-1.29l-59.53,-27.36m15.95,16.9c6.11,6.65 13.58,6.25 18.8,12.42s11.54,6.38 14.59,7.81c2.92,1.56 2.72,4.34 3.67,4.07 1.02,-0.34 0.61,-3.46 -2.99,-6.25s-6.92,-1.22 -13.37,-8.76 -11.47,-5.77 -16.97,-12.9"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="m317.64,242.33l48.05,44.52s0.88,0.88 1.48,0.32c0.56,-0.65 -0.32,-1.53 -0.32,-1.53l-44.53,-48.06m8.27,21.72c3.1,8.48 10.15,10.97 12.62,18.67s8.22,10.31 10.5,12.8c2.1,2.56 0.85,5.05 1.83,5.17 1.07,0.08 1.89,-2.96 -0.37,-6.91s-5.93,-3.78 -9,-13.21 -8.39,-9.72 -10.74,-18.41"
      android:strokeWidth="1.06"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="M343.42,235.87c8.64,-0.48 12.48,-5.38 20.45,-4.42 -6.24,-2.11 -11.14,2.98 -20.35,2.69m-22.85,7.39l40.9,-18.72 -42.14,15.65"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M343.22,245.15c8.17,2.86 13.59,-0.19 20.58,3.75 -4.96,-4.34 -11.43,-1.51 -19.83,-5.3m-23.94,-1.91l44.95,-1.64 -44.92,-1.67"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M339.48,253.64c6.45,5.77 12.63,5.02 17.58,11.34 -2.92,-5.91 -9.98,-5.77 -16.29,-12.49m-21.38,-10.93l42.15,15.68 -40.87,-18.74"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M332.78,260.06c3.75,7.8 9.74,9.47 11.9,17.2 -0.44,-6.57 -7.01,-9.15 -10.27,-17.77m-15.57,-18.28l32.95,30.62 -30.58,-32.95"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M320,240m-26.7,0a26.7,26.7 0,1 1,53.4 0a26.7,26.7 0,1 1,-53.4 0"
      android:strokeWidth="1.4"
      android:fillColor="#f6b40e"
      android:strokeColor="#85340a"/>
  <path
      android:pathData="M329,234.3c-1.7,0 -3.5,0.8 -4.5,2.4 2,1.9 6.6,2 9.7,-0.2a7,7 0,0 0,-5.1 -2.2zM329,234.7c1.8,0 3.5,0.8 3.7,1.6 -2,2.3 -5.3,2 -7.4,0.4q1.6,-2 3.8,-2z"
      android:strokeWidth="1"
      android:fillColor="#843511"/>
  <path
      android:pathData="M329.4,233c-2.7,0 -3.3,0.6 -4.5,1.7 -1.2,1 -1.9,0.8 -2,1 -0.3,0.2 -0,0.8 0.3,0.6q0.7,-0.2 2.5,-1.6c1.8,-1.4 2.5,-1 3.7,-1 3.7,0 5.7,3 6.1,2.8 0.5,-0.2 -2,-3.5 -6.1,-3.5"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M336.8,234.3c-4.7,-4.1 -10.7,-4.8 -14,-1.7a8,8 0,0 0,-1.5 3.4q-0.6,3.6 2.1,7.5l-0.8,0.4q-2.4,-4.7 -1.6,-9.4l0.6,-2.3c4.5,-3.7 10.7,-4 15.2,2z"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M329,236.3m-1.8,0a1.8,1.8 0,1 1,3.6 0a1.8,1.8 0,1 1,-3.6 0"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M334.3,237.5c-3.5,2.7 -7,2.5 -9,1.3 -2,-1.3 -2,-1.7 -1.6,-1.7s0.8,0.4 2.4,1.3c1.7,0.8 4.1,0.8 8.2,-0.9"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M316,243.7a1.8,1.8 0,1 0,1.8 2.9,4 4,0 0,0 2.2,0.6h0.2q1,0 2.3,-0.6 0.5,0.7 1.5,0.7a1.8,1.8 0,0 0,0.3 -3.6q0.8,0.3 0.8,1.2a1.2,1.2 0,0 1,-2.4 0,3 3,0 0,1 -2.6,1.7 3,3 0,0 1,-2.5 -1.7q-0.1,1.1 -1.3,1.2 -1,-0.1 -1.2,-1.2c-0.2,-1.1 0.3,-1 0.8,-1.2zM318,249.1c-2.1,0 -3,2 -4.8,3.1 1,-0.4 1.8,-1.2 3.3,-2s2.6,0.2 3.5,0.2 2,-1 3.5,-0.2q2,1.3 3.3,2c-1.9,-1.2 -2.7,-3 -4.8,-3q-0.7,0 -2,0.6z"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M317.2,251.6q-1.1,0 -3.4,0.6c3.7,-0.8 4.5,0.5 6.2,0.5 1.6,0 2.5,-1.3 6.1,-0.5 -4,-1.2 -4.9,-0.4 -6.1,-0.4 -0.8,0 -1.4,-0.3 -2.8,-0.2"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M314,252.2h-0.8c4.3,0.5 2.3,3 6.8,3s2.5,-2.5 6.8,-3c-4.5,-0.4 -3.1,2.3 -6.8,2.3 -3.5,0 -2.4,-2.3 -6,-2.3"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M323.7,258.9a3.7,3.7 0,0 0,-7.4 0,3.8 3.8,0 0,1 7.4,0"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M303.4,234.3c4.7,-4.1 10.7,-4.8 14,-1.7a8,8 0,0 1,1.5 3.4q0.6,3.6 -2.1,7.5l0.8,0.4q2.4,-4.7 1.6,-9.4l-0.6,-2.3c-4.5,-3.7 -10.7,-4 -15.2,2z"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M310.8,233c2.7,0 3.3,0.6 4.5,1.7 1.2,1 1.9,0.8 2,1 0.3,0.2 0,0.8 -0.3,0.6q-0.7,-0.2 -2.5,-1.6c-1.8,-1.4 -2.5,-1 -3.7,-1 -3.7,0 -5.7,3 -6.1,2.8 -0.5,-0.2 2,-3.5 6.1,-3.5"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M310.6,234.3c-1.7,0 -3.5,0.8 -4.5,2.4 2,1.9 6.6,2 9.7,-0.2a7,7 0,0 0,-5.1 -2.2zM310.6,234.7c1.8,0 3.5,0.8 3.7,1.6 -2,2.3 -5.3,2 -7.4,0.4q1.6,-2 3.8,-2z"
      android:strokeWidth="1"
      android:fillColor="#843511"/>
  <path
      android:pathData="M310.9,236.3m-1.8,0a1.8,1.8 0,1 1,3.6 0a1.8,1.8 0,1 1,-3.6 0"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
  <path
      android:pathData="M305.9,237.5c3.5,2.7 7,2.5 9,1.3 2,-1.3 2,-1.7 1.6,-1.7s-0.8,0.4 -2.4,1.3c-1.7,0.8 -4.1,0.8 -8.2,-0.9"
      android:strokeWidth="1"
      android:fillColor="#85340a"/>
</vector>
