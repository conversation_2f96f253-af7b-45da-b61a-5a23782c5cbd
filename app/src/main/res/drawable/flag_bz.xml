<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#ce1126"/>
  <path
      android:pathData="M0,34.3h640v411.4H0z"
      android:fillColor="#003f87"/>
  <path
      android:pathData="M320,240m-188.6,0a188.6,188.6 0,1 1,377.2 0a188.6,188.6 0,1 1,-377.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M320,239m-156.3,0a156.3,156.3 0,1 1,312.6 0a156.3,156.3 0,1 1,-312.6 0"
      android:strokeWidth="3.9"
      android:fillColor="#00000000"
      android:strokeColor="#552300"/>
  <path
      android:pathData="M302,73.2c14,-15.2 26.9,2.3 26.5,10.6C328.2,92 314,110 301,95.5c13.4,3.4 17.4,-11.5 24.1,-11.7 -6.7,0.2 -9.5,-14.6 -23.2,-10.6zM154.8,192.2c-7.4,-19.4 14,-23 21.3,-19 7.3,3.9 17.3,24.6 -1.6,29.6 9,-10.4 -2.7,-20.5 0.2,-26.6 -2.9,6 -17.5,2 -19.9,16M471.9,173c19.4,7.6 7.6,25.8 -0.2,28.4 -7.9,2.7 -30,-4 -21,-21.2 1.6,13.6 17,12 19.7,18.1 -2.6,-6.2 10.3,-14 1.5,-25.3M218.8,377.4c-20.7,-2 -14.3,-22.7 -7.5,-27.4s30,-4.3 26,14.7c-5.3,-12.6 -19.7,-6.8 -24,-12 4.3,5.2 -6,16.3 5.5,24.7m209.3,-15c0.3,20.7 -21.1,16.8 -26.6,10.6s-7.7,-29 11.8,-27.3c-12,6.6 -4.6,20.2 -9.4,25 4.8,-4.8 17.1,4 24.2,-8.2zM170,153c-1.9,-20.6 19.8,-18.3 25.7,-12.6 6,5.8 10,28.4 -9.6,28.2 11.5,-7.6 3,-20.5 7.4,-25.6 -4.4,5 -17.3,-2.8 -23.5,10m22.2,193c-20.5,-3.5 -12.7,-23.7 -5.6,-27.9s30.1,-2.3 25,16.4c-4.5,-13 -19.3,-8 -23.1,-13.6 3.8,5.5 -7.2,16 3.7,25zM354.3,74c18.6,-9.2 24.3,11.7 21,19.3 -3.2,7.5 -23,19.4 -29.8,1.3 11.2,7.9 20.3,-4.7 26.7,-2.4 -6.4,-2.3 -3.7,-17 -17.9,-18.2M394,388.3c-9.3,18.5 -26.5,5.3 -28.5,-2.7s6.5,-29.3 23,-19c-13.8,0.5 -13.4,16 -19.8,18 6.4,-2 13.3,11.4 25.3,3.7m90.5,-178.7c15.6,13.7 -1.7,26.8 -10,26.7 -8.2,-0.1 -26.8,-13.8 -12.5,-27 -3,13.4 12,17 12.4,23.6 -0.3,-6.7 14.5,-9.8 10,-23.3zM221.5,100.8c4.7,-20 24.5,-11.2 28.4,-3.9s0.5,30 -18,23.8c13.3,-3.7 9.3,-18.6 15,-22 -5.7,3.4 -15.5,-8 -25.4,2.1m-61.6,172.5c-18.2,-10 -4.2,-26.5 4,-28.1 8,-1.7 29.2,7.7 18,23.7 0.2,-13.7 -15.4,-14 -17.2,-20.5 1.8,6.5 -12,12.7 -4.8,25zM423.5,113.7c20.6,-3 19.4,18.7 14,24.8 -5.5,6.2 -28,11.4 -28.9,-8 8.2,11 20.8,1.9 26.2,6 -5.4,-4.1 1.9,-17.3 -11.3,-22.8M306.3,408.1c-19,8.4 -23.8,-12.7 -20.3,-20 3.6,-7.5 23.9,-18.6 30,-0.1 -11,-8.4 -20.5,3.8 -26.8,1.3 6.3,2.5 3,17.2 17.1,18.8M476.8,293c8.6,18.8 -12.6,23.7 -20.1,20.2s-18.8,-23.6 -0.2,-29.7c-8.4,10.9 4,20.3 1.4,26.6 2.5,-6.3 17.3,-3 19,-17.1zM151.7,232.4c-12,-17 8,-25.7 16,-23.6s22.8,19.8 5.7,29.2c6.2,-12.3 -7.6,-19.3 -6.3,-25.9 -1.3,6.6 -16.5,6.1 -15.4,20.2zM264,395.6c-19.6,7.1 -23,-14.2 -18.9,-21.4 4,-7.2 25,-16.9 29.9,2 -10.3,-9.1 -20.7,2.4 -26.8,-0.5 6,2.9 1.8,17.3 15.8,19.9m-5.7,-314c11.6,-17.2 27,-2 28,6.3 0.9,8.1 -10.3,28.2 -25.4,15.8 13.8,1.3 15.4,-14 22,-15.2 -6.6,1.2 -11.7,-13 -24.6,-7zM458.5,333.7c1.3,20.6 -20.3,17.7 -26,11.7 -5.8,-5.9 -9.1,-28.6 10.4,-27.8 -11.7,7.2 -3.6,20.4 -8.2,25.4 4.6,-5 17.3,3.3 23.8,-9.3m-5.3,-194.4c20.4,4 12,24 4.7,28s-30.2,1.3 -24.4,-17.2c4,13.1 19,8.7 22.6,14.4 -3.6,-5.7 7.7,-15.7 -3,-25.2zM194.8,124.7c0.2,-20.6 21.5,-16.2 26.8,-9.9s7,29.3 -12.4,27c12.2,-6.3 5,-20 10,-24.7 -5,4.7 -17,-4.5 -24.4,7.6M169,311c-18.6,-9.4 -5.1,-26.4 2.9,-28.3s29.5,6.7 19,23c-0.4,-13.7 -16,-13.4 -18,-19.8 2,6.4 -11.6,13.1 -4,25zM392,91c19.5,-7.3 23,14 19,21.2s-24.9,17.1 -29.9,-1.7c10.4,9 20.7,-2.6 26.9,0.3 -6.2,-2.9 -2,-17.3 -16,-19.8m-41.3,314.8c-15.2,14.2 -26.7,-4.1 -25.8,-12.3s16.3,-25.3 28.2,-9.9c-13,-4.3 -18,10.4 -24.9,10 6.8,0.4 8.5,15.3 22.5,12.2m132.7,-158c14.2,15.1 -4.4,26.5 -12.6,25.5s-25.4,-16.3 -9.8,-28c-4.4,13 10.3,18 10,24.7 0.3,-6.7 15.4,-8.4 12.4,-22.2"
      android:fillColor="#007f00"/>
  <path
      android:pathData="M301,95.5c13.4,3.4 16.7,-11.3 23.5,-11.5 -4,4.7 -14.2,16.7 -23.5,11.5m1,-22.3c14,-15.2 26.9,2.3 26.5,10.6 -11.5,-20 -15,-17 -26.6,-10.6zM174.4,202.8c9,-10.4 -2.8,-19.9 0,-26 2.6,5.6 8.9,20.1 0,26m-19.7,-10.7c-7.4,-19.3 14,-22.9 21.3,-19 -23.1,1.4 -22,6 -21.3,19m295.9,-11.8c1.6,13.5 16.7,11.3 19.3,17.5 -5.9,-2 -21,-7.1 -19.3,-17.6zM471.8,173c19.4,7.6 7.6,25.8 -0.3,28.4 14.7,-17.8 10.5,-20 0.3,-28.4M237.3,364.7c-5.3,-12.6 -19.2,-6.4 -23.4,-11.7 6.2,0.3 22,1.2 23.4,11.7m-18.5,12.7c-20.7,-2 -14.3,-22.7 -7.5,-27.4 -9.2,21.1 -4.6,22.1 7.5,27.4m194.5,-31.7c-12,6.6 -4.2,19.6 -9,24.3 -0.4,-6 -1.3,-21.8 9,-24.3m14.8,16.7c0.3,20.7 -21.1,16.8 -26.6,10.6 22.3,6.6 22.7,2 26.6,-10.6M186,168.4c11.5,-7.5 2.7,-19.8 7,-25 1,6.1 3,21.8 -7,25M169.9,153c-1.9,-20.6 19.8,-18.3 25.7,-12.6 -22.7,-4.9 -22.8,-0.2 -25.7,12.6m41.5,181.5c-4.4,-13 -18.7,-7.6 -22.5,-13.2 6.1,0.7 21.9,2.7 22.5,13.2M192.1,346c-20.5,-3.5 -12.7,-23.7 -5.6,-27.9 -10.6,20.5 -6,21.8 5.6,27.9M345.4,94.6c11.3,7.9 19.7,-4.7 26.1,-2.5 -5.4,3 -19.3,10.6 -26,2.5zM354.3,74c18.6,-9.2 24.3,11.7 21,19.3 -3.5,-22.8 -8,-21.1 -21,-19.3m34.2,292.6c-13.8,0.5 -12.8,15.6 -19.3,17.6 2.5,-5.6 9,-20 19.3,-17.5zM394,388.3c-9.3,18.5 -26.5,5.3 -28.5,-2.7 16.7,16 19.2,12 28.5,2.7m68,-179c-3.1,13.4 11.9,16.3 12.2,23 -4.9,-3.8 -17.2,-13.7 -12.2,-23m22.5,0.3c15.6,13.7 -1.7,26.8 -10,26.7 19.9,-12 16.7,-15.4 10,-26.7m-252.6,-89c13.3,-3.6 8.8,-18 14.6,-21.5 -1,6 -4,21.5 -14.6,21.6zM221.5,100.8c4.7,-20 24.5,-11.2 28.4,-3.9 -20,-11.7 -21.6,-7.2 -28.4,4zM182,268.8c0.1,-13.6 -15.2,-13.3 -17,-19.8 5.6,2.7 19.9,9.7 17,19.9zM160,273.3c-18.3,-10 -4.3,-26.5 3.9,-28.1 -16.8,15.9 -13,18.6 -4,28.1zM408.6,130.5c8.2,11 20.2,1.6 25.6,5.7 -6.1,1.2 -21.7,4.1 -25.6,-5.7m14.9,-16.8c20.6,-3 19.4,18.7 14,24.8 3.7,-22.7 -1,-22.5 -14,-24.8M316,388c-11,-8.4 -19.9,3.9 -26.1,1.4 5.5,-2.8 19.7,-9.8 26,-1.4zM306.3,408.1c-19,8.4 -23.8,-12.7 -20.3,-20 2.7,22.8 7.2,21.4 20.3,20m150.2,-124.7c-8.4,10.9 4,19.7 1.5,25.9 -2.8,-5.4 -10,-19.5 -1.5,-26zM476.8,292.9c8.6,18.8 -12.6,23.7 -20.1,20.2 23,-2.8 21.6,-7.2 20.1,-20.2m-303.3,-55c6.2,-12.3 -7.7,-18.6 -6.4,-25.2 3.8,4.8 13.5,17.3 6.4,25.2m-21.8,-5.7c-12,-16.9 8,-25.5 16,-23.5 -22.1,7 -19.8,11 -16,23.6zM275,376.2c-10.3,-9.1 -20,2.5 -26.2,-0.4 5.7,-2.4 20.4,-8.5 26.2,0.3zM264,395.6c-19.6,7.1 -23,-14.2 -18.9,-21.4 1.1,23 5.7,21.9 18.9,21.4m-3,-291.9c13.7,1.3 14.7,-13.8 21.3,-15 -3.1,5.3 -11.4,18.8 -21.4,15zM258.3,81.5c11.6,-17.1 27,-1.8 28,6.3 -14.5,-18 -17.6,-14.3 -28,-6.3M443,317.6c-11.7,7.2 -3.3,19.8 -7.9,24.8 -0.7,-6.1 -2.3,-21.8 8,-24.8zM458.6,333.7c1.3,20.6 -20.3,17.7 -26,11.7 22.4,5.7 22.7,1 26,-11.7m-25,-183.6c4,13.1 18.5,8.3 22.1,14 -6,-1 -21.8,-3.5 -22,-14zM453.3,139.3c20.4,4 12,24 4.7,28 11.3,-20.1 6.8,-21.6 -4.7,-28m-244,2.6c12.2,-6.4 4.7,-19.6 9.6,-24.2 0.3,6.1 0.8,21.9 -9.6,24.2m-14.4,-17.2c0.2,-20.6 21.5,-16.2 26.8,-9.9 -22,-7.1 -22.6,-2.4 -26.8,10zM190.9,305.7c-0.3,-13.7 -15.6,-12.8 -17.6,-19.2 5.7,2.4 20.2,9 17.6,19.2M169,311c-18.5,-9.4 -5,-26.4 3,-28.3 -16.2,16.4 -12.3,19 -3,28.3m212.2,-200.4c10.4,9 20,-2.8 26.2,0 -5.7,2.6 -20.3,8.7 -26.2,0M392,91c19.5,-7.3 23,14 19,21.3 -1.2,-23 -5.8,-21.9 -19,-21.3m-38.9,292.6c-13,-4.3 -17.4,10.2 -24.2,10 4.3,-4.5 15.4,-15.8 24.2,-10m-2.4,22.2c-15.2,14.2 -26.7,-4.1 -25.8,-12.3 10,20.7 13.8,17.9 25.8,12.3M461,245.3c-4.4,13 10.2,17.3 9.9,24 -4.5,-4.2 -15.8,-15.3 -9.9,-24m22.4,2.5c14.2,15.1 -4.4,26.5 -12.6,25.5 21,-9.9 18.1,-13.6 12.6,-25.5"
      android:fillColor="#005800"/>
  <path
      android:pathData="M349,120.3s0.6,-1.3 1.7,-0.6c1,0.8 1.3,4.2 1.3,4.2l-1.6,0.8s0.9,-1.3 -1.3,-4.4zM340.2,151s0.8,-1 -0.3,-3.4l1.5,-0.5s0.6,1.8 -0.2,3.9h-1m27,-5.4 l-3.7,2.3 1.9,0.8 4.5,-2.6 -2.7,-0.5m-49.7,14.6c-1,0.8 -1,2.1 -1.7,3 -0.4,0.7 -2,1.8 -2,3 -0.2,1.1 -0.3,1.9 0.2,3.5 0.6,1.5 3,8.5 2,13.7 -1,5.3 -0.6,7.5 -0.3,8.3 0.4,0.8 1.6,3 2.3,3 1.6,-0.2 0.9,-0.7 1.5,-1.7 1.4,-2.5 0,-22.2 -0.7,-27.6 -0.5,-4.2 -2.5,-8 -2.7,-8.4l-0.8,-2.6c-1.5,-3.5 -3.3,-6.5 -3.3,-13.9 0,0 -4.7,1.1 -8.8,-8.2 -2.2,-4.7 -5.3,-4.2 -9,-5.7l1.6,-1.1s3.2,1.6 4.7,1.6c1.6,0 1,-3.2 1,-3.2l1.7,-1.5v6.8c0,3.1 4.3,7.4 6.8,8.7 1.6,0.8 3.6,-2.4 3.1,-5.6 -0.5,-3 0,-7.3 0,-7.3l2.2,1.3s-0.8,1.3 -0.6,2.1 1.3,-0.7 2.1,-1.5q-0.1,0.1 1,0.2l-2.3,4c-0.8,1.2 -0.8,6.2 -0.8,8 0,2 0.7,6.2 0.7,7.6 0,1 1.8,5 3.3,7.4l1.6,3.5c1.2,1.9 1.3,3.2 2,4.7q0.6,1.1 1.6,7l0.7,7c0.2,2.4 2.2,0.6 4.5,-1 2.4,-1.8 2.4,-6 2.4,-6s1.8,-1.6 1.5,-0.7c-0.2,1 1,2 1,2 -1.4,3.3 -0.6,2.9 0.9,2.8s6.5,-4 6.5,-4c0.7,-0.2 3.8,-0.2 3.3,0s-1.4,1.7 -1.4,1.7l3.3,-0.4c-1.1,0.9 -5.5,3.4 -6.5,4a91,91 0,0 1,-8.6 4.1c-2.3,1 -6.7,3.2 -6.9,5.4 0,2.3 0.3,8.3 0,10.3l-1,10.6h-10.4s2,-7.6 0,-9.7c-2.1,-2.1 -7,-8.4 -10,-10.5 -3.2,-2 2,-0.5 2,-0.5v-2.1l4.8,5.7s8.3,-7.3 0,-17.3c-2.6,-3.1 -5.3,-7.9 -7.3,-8.4 -2.2,-0.6 -3.3,-1 -6.9,0 -3.7,1.1 -12.6,1.1 -16.3,-4.8l3.1,-0.5 2.7,-1s-1.1,3.7 6.3,3.7 8.4,0 7.8,-6.8l2.2,-1.1 1.6,1.5s-1.1,3.8 -0.5,5.3c0.5,1.6 3,2.7 5.1,4.2q1.4,1 2.2,0.8c0.9,-0.1 1,-0.1 2.2,-1.3 1.8,-2 1.4,-1.8 3.2,-3.4l1,-1.6z"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M319,155c0.5,-0.4 1.2,-1.9 2,-3.1 1.1,-2 2.5,-3.2 4,-5.6 1.5,-2.2 2,-5.6 1.5,-7.9l1.4,0.8v3.7l2.7,-2 0.8,0.4s-5.9,6 -11.3,15.9zM267.7,134.7c0.8,1.4 1.6,4 3.9,6l1.6,-0.4s-3.6,-4.2 -3.6,-6l-1.9,0.4m9.2,-2.4s0.5,5 -0.8,6.9l1.6,-0.8s1,-3.4 0.7,-5.3zM328.3,116.6s-0.3,7 1.1,11.3l1.9,-0.5s-0.3,-1.3 1.2,-1.9c1.6,-0.5 5.3,-2 7.2,-3.6l-0.6,-1.6s-2.8,2.3 -4.2,2.9c-1.3,0.5 -2,1 -3.1,0.7s-2.4,-2 -1.6,-6l-1.9,-1.3m20.7,32.8s-0.4,5 -0.2,7.2l1.7,0.4s-0.4,-6 0.7,-6.5l-2.2,-1m7.5,3.7s-1.9,3.2 -4,3.5l3.4,-0.7s1,-0.5 2.4,-2l-1.8,-0.8"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m326.6,138.4 l1.3,0.8 1.3,0.2 3.2,2.6c1.2,1 1.2,-0.2 1.2,-0.2l0.8,3.1h1l-0.2,3.2 1.8,-1.6 2.2,1.9s1.5,-1.4 2.3,-1.4 1.1,-0.8 1.1,-0.8h0.1q-8,-3.6 -5.9,-7.6c2.6,-4.4 6.8,-1.6 8.1,-1l19,10.2s1.3,-0.8 0.4,-1.6 -1.4,-2.7 -1,-3.5c0.2,-0.8 2.4,2.4 3.4,2.7l3.5,0.7v-1s3,2.2 3,3.3c0,0 2.3,-1.2 1.5,-1.9s3.5,1.7 3.7,3l1,-1c-0.2,0 0.2,-3.2 1.3,-4.6a5,5 0,0 1,2 -1.9s1,-1.7 2,-1.7c0,0 -1,-0.6 -1.4,-1.4 -0.5,-0.8 -1.6,-1.2 -1.6,-1.2s1.4,-1 1.8,-1.5q0.7,-0.8 0.8,-0.7c0.1,0.1 -2.2,-1.9 -3.8,-2.2 0,0 -0.1,-2.5 -2,-2.7h0.8s-1.4,-2.8 -2.8,-2.8c0,0 0.9,-1 1.6,-1.1 0,0 -1.6,-2.2 -3.9,-1.4l1.5,-0.6s-1,-1.1 -4.4,-0.6c0,0 0.3,-1.3 -1.2,-2.4 0,0 -1.2,0 -1.2,1 0,0 -1.2,-1 -2.7,-1 -1.4,0 0.5,1.6 0.5,1.6s-2,-1.3 -3.1,-1c0,0 0.5,-2.2 -0.8,-3.3 0,0 -1.4,0.7 -1.8,1.7l-0.4,-1s-2,2.4 -2.4,1.7c-0.3,-0.4 -0.6,-2 -0.6,-2l-1.1,1.7 0.2,-2.4s-1.4,0.8 -2.5,3.3c0,0 -1.3,1.3 -1.3,2.4 0,0 -1.1,-1.2 -2,0 -1,1.4 0,1.1 0,1.1s-0.5,0.5 -1.5,0.7l0.2,0.8s-1.2,-0.7 -2.2,-0.4l0.5,1.2s-2.6,0.2 -2.7,-2c0,0 -2.1,-1 -2.4,0.2l-1.6,1s-0.5,-1 -1.3,-1.2c-1,-0.3 -0.2,1.3 -0.2,1.3s-0.3,-1.2 -1.5,-1.8c-1.2,-0.5 -0.2,1 -0.2,1s-1.5,-1.2 -0.8,-2.2c0,0 -1.4,1.4 -0.7,2.7l-1,0.7s-0.5,-0.5 -0.4,-1c0,-0.3 -1.7,1.3 -1.7,1.3s-1,-2.6 0,-2.6c0.1,0 -1.8,0 -2.1,2.2 0,0 -1.3,0 -2.5,0.4s1,1.3 1,1.3 -2,-0.4 -3,0.2l0.7,0.9s-0.7,-0.7 -2.4,-0.1 0.4,0.8 0.4,0.8 -1.7,-0.4 -2.6,0 0.3,1 0.3,1 -3.2,-1 -3.7,-0.6c-0.5,0.3 1,1.8 1,1.8s-2,0.5 -2.3,2l1.8,0.7s-0.8,0.4 -1.2,1.1 1.2,0.4 1.2,0.4 -2.2,1.6 -2.2,2.8c0,0 1.6,0.5 2.7,-0.7s0.4,-0.8 0.4,-0.8l0.1,0.8s2.3,-1 2.3,-1.6c0,-0.5 -0.4,0.8 -0.4,0.8l2.2,-1.2z"
      android:strokeWidth=".5"
      android:fillColor="#289400"
      android:strokeColor="#030"/>
  <path
      android:pathData="m324.7,108 l-0.5,-2.2 5,0.6 0.9,-2s2.4,1 3.1,1.8c0,0 4.6,-1.2 6,-0.7l-0.6,2.1s4.2,0.3 5.6,0.8c1.2,0.5 -0.6,1.6 -0.6,1.6s4.8,0.6 6.8,1.8c2.2,1.3 2.2,2.4 1.1,2.7l2.6,4.2s-4.2,0.5 -5,1.5c-0.7,1.1 -1.5,-1 -1.5,-1l-0.8,2.9s-2.6,-1.4 -3.7,-2.4c0,0 -1.3,1.6 -0.5,2.4l-4.8,-3.1s-2,-0.3 -2,1.5l-3,-3.4 -2.6,0.8s-0.8,-1 -1.7,-1.4c-1.1,-0.2 -0.3,1.4 -0.3,1.4l-1.6,-1.9 -1.6,-1.8s-1.3,1.8 -2.4,1.8 -1,-1.2 -0.8,-2.1c0.3,-0.8 -3.1,-1 -3.4,0 0,0 -1,-4 2.6,-5l3.7,-1zM305.7,121.3 L304.1,122.9 303.1,122.4 301.2,124.5 300.8,123.4 299.8,125.1 298.1,124.5 297.3,125.8 295.8,125.5s-1.2,1.2 -2,1.5c-0.8,0.2 0,-1.8 0,-1.8s-1.2,2 -2,2 -0.6,-1 -0.6,-1 -0.5,1.4 -1.4,1.6c-0.9,0 -0.5,-1 -0.5,-1l-1.6,1.2v-1l-0.7,-0.3s0.4,-1.8 1.1,-2.2 -0.7,-1.5 -0.7,-1.5l0.7,-0.8s-2,-0.5 -2,-0.1c0,0 1,-2.6 2.4,-3.1l0.1,-3s1.4,-1.1 2.6,-1.2c1.4,-0.2 -0.4,-2.3 -0.4,-2.3l3.2,-1.8 0.4,-2.3 4.7,0.4 1,-2 3.8,1 3.3,-1.7 2.3,2.7 3.5,0.3v3.1s4.1,1.3 4.6,1.7c0,0 -0.8,2.5 -0.8,3 0,0 3,-0.8 3.8,-0.6l0.4,3.4s5,1.6 5.3,2.5c0,0 -1,0.7 -1.7,1.6 0,0 0.6,3 0.6,4l-0.4,0.5s-1.4,-1.3 -2.6,-1.5l-1,2.1 -1.7,-1.1h-1.1s-0.9,-1 -1.6,-1v-1l-1.4,0.9s-2.2,-1.1 -2.2,-1.8l0.1,-1.5 -2.2,1.5s-0.5,-1.1 -0.4,-1.6 -0.9,1.1 -0.9,1.1 -1.2,-2 -1,-2.4l-0.4,1.7s-2.3,-0.8 -2.6,-2.4z"
      android:strokeWidth=".5"
      android:fillColor="#289400"
      android:strokeColor="#030"/>
  <path
      android:pathData="M274.5,130.9s4.1,2 4.1,3c0,0 1.1,-2.1 0.5,-3l1.6,1.1s0.5,-1.9 -0.1,-2.5c-0.5,-0.5 3.5,-2.1 4.4,0.5 0,0 2.4,-2.5 -2.3,-4.7 0,0 -0.4,-1.6 2.5,-1.2 0,0 -0.2,-1 -0.9,-1.3h2.7s-1.6,-1.4 -4,-1.8 -1,-0.7 -1,-1.2c-0.2,-0.5 0,-2.2 0,-2.2s-1.4,-0.5 -2.5,0l0.4,-0.4s-2.5,-0.7 -3.5,-0.1c0,0 -1.5,-0.6 -1.3,-2.1 0,0 -1.5,-0.1 -2.1,1.4 0,0 -1.4,1.6 -3.3,1.6 -0.3,0 -1.7,-0.8 -1.7,-0.8l-1.2,2.7L265,119.9l0.5,1s-2.8,-0.8 -3.7,0c0,0 1.1,1.7 0.8,2.8 0,0 -2.2,0.4 -2.7,0.8s0.8,0.7 0.8,0.7 -2.6,0.8 -3.2,1.6l1,0.7 -1.2,1.6 1,0.2 -2.5,0.8c-0.9,0.3 1.5,0.5 1.5,0.5l-1.1,1 1.8,0.3s-1.4,4 -1.5,5.8c0,0 2.2,-2.7 2.8,-3l-1.8,3s3.4,-1 3.7,-1.6l0.5,-1 0.1,1.6s1.3,-1.7 1.5,-2.4 2,0.7 2,0.7v-1l2.4,0.7q1,-0.2 0.8,-0.3l1.6,-0.2 -0.3,1.4s1.2,-0.8 1.5,-1.6c0.2,-0.8 0.8,0 0.8,0l0.8,-3.1zM261.1,144.7 L262.7,148c-1.3,0 -3.1,1.5 -3.1,1.5 0.3,5.4 -1,5.1 -1,5.1 0.3,1 -0.3,2.8 -0.3,2.8l7.7,-4.6 0.1,-0.5 9.2,-4.3 0.3,0.5 2.6,-1.5 1.9,4.9 -1.9,1c0.1,1.7 2.1,4.6 2.1,4.6l2.4,-0.6 2.7,-1v1c0,-0.3 2.9,-1.3 2.9,-1.3l1.3,1.3c1,-0.1 2.7,-2.5 2.7,-2.5l2,0.8s0.7,-1.2 0.7,-2 2.6,-0.8 2.6,-0.8l1.7,1.2 2.3,-1.8s0.7,1.3 1.6,1.5 3.4,2 3.4,1.6 1,-2.5 1,-2.5l4.1,1.6c-0.5,-1.8 -2,-4 -2,-4s2,-1.6 1.7,-2 -1.6,0 -1.6,0l1.2,-2.4h-1.6l0.5,-0.6 -2.5,-1 1.3,-2.8c-0.6,-0.7 -7.3,-2 -7.3,-2l1.6,-1.5s-0.8,-1.2 -1.6,-1.2 -4.9,0.1 -5,-0.3l-1.8,-1.8 -2.6,1.9 -1.3,-0.4 -2.2,0.8s0,1.3 -0.3,0.4 -2.3,-2.8 -2.3,-2.8c-0.4,0.7 -3,3.8 -3,3.8s-0.5,1.7 -1.3,0.5c-1,-1.2 -3,-2.4 -3.4,-1.5 -0.2,1.1 -2.1,2 -2.5,1.8 -0.4,-0.3 -2.5,-0.1 -2.7,0.5 -0.1,0.7 -2.5,2.4 -2.5,2.4 -1.4,-0.7 -3.8,-0.3 -3.8,-0.3s-0.7,1.1 -0.8,2zM305.3,184.3v-2l2.3,1.5 1.4,-1.5 1.8,1.7 1,-1.7v2.8l2.7,-2.3 1.5,0.6v-2.2s1.9,-3.1 1.1,-3.7l1.6,-0.5s-2.7,-2.6 -3.4,-3.1c-0.8,-0.6 -1.9,-0.6 -1.9,-0.6s-2,-2 -3.4,-2.7c-1.3,-0.5 -2,0.6 -2,0.6s-0.8,-1.5 -2.2,-1.8c-1.3,-0.3 -1.3,1 -1.3,1s-1,-1 -2.2,-1.4c-1.3,-0.6 -0.4,1.2 -0.4,1.2l-4.5,-1.4 0.6,1.4 -4.2,-1.1 0.5,1.5 -3,-0.5 -0.2,1.3 -4.2,0.8 0.8,1.4 -2.6,-1.1v1.9l-3,1 1.1,1 -4.7,2.4c-0.7,0.5 1.6,1.6 1.6,1.6l-4.2,-0.6 1.6,1.4 -3.7,3.4 2.6,1.9 -1.6,2.3 4,0.8 1.5,2.6 3.2,-0.6v3l4.7,-3.7s3.4,3 3.7,2.1c0.2,-0.8 0.2,-3.4 0.2,-3.4h1.7L293.8,187l1.2,1 2.2,-3.1 2.8,3.9 2.2,-5zM324.4,172.5 L324,168 322.6,169.4 323.2,165.2 321,164.8 321.8,160.7 320.2,159.9 320.6,157.9 322.2,156.8 325,154 331.6,154.1 332.3,152 337,152.8c0.8,0.8 3.3,-1.8 3.3,-1.8h1l1,2s3.4,-0.4 5,0.6l-1.9,2.6 3.5,0.3 2.3,0.5 2.4,-0.8 3.2,-0.2 0.7,1.3h3l-1,1.1 3.8,1.8 -0.8,0.8s2.2,0.8 3.7,0.8h0.8s0.8,1.8 -0.3,2.6c0,0 1.1,2.6 3.4,3.1 2.4,0.5 2.4,0.5 2.7,1s0,3.2 -2.7,3.2v2.7s-2,1.8 -2.3,2.6l-1.6,-1s-1.3,1.5 -1.3,2.5c0,0 -3.5,-4.5 -4.7,-4.7 -1.4,-0.2 -1,2.6 -1,2.6l-3.8,-3.1 -0.5,2.1 -3.1,-2.6 -1.9,1.3 -3.3,-3.5 -3.2,0.4 1.2,-1.6h-3.9l-0.7,-3.4 -0.6,2.8 -2.4,-1.6 -3,3.3s-0.4,-0.4 -0.9,-2c-0.5,-1.5 -1.8,2 -1.8,2l-1.1,-2.5 -1.3,2.4s-1,-1.8 -2.1,-2.4c-1,-0.5 -2,3 -2.4,4.5zM325,182.8 L325.2,181.2 330.8,179 335.5,177.3 337.8,176.2 338.9,178.1 340.3,175.4 342.4,178.2 343.6,176.7 345.4,178.1h1.6l0.3,2s3.9,0.3 4.2,-0.5 0.8,2.7 0.8,2.7l3.7,0.5 0.8,4.2s1.8,3.9 1.2,5.4c-0.5,1.7 -2,-2 -2,-2s-0.3,2.9 -0.6,3.7 -5,-0.8 -5,-0.8l-2,3.9s-2.2,-3.1 -3.2,-3.1 -1,3.1 -1,3.1l-4.5,-5.3c-1.3,-1.5 -1.6,1.6 -1.6,1.6s-2,-3.4 -2,-4.4 -1.9,-2.2 -1.9,-2.2 -3.2,3.7 -5.6,4.7c0,0 0.3,-2.3 -0.5,-3 -0.7,-0.9 -3,1.5 -3,1.5s-1.7,-3.6 0,-7.4z"
      android:strokeWidth=".5"
      android:fillColor="#289400"
      android:strokeColor="#030"/>
  <path
      android:pathData="m350.8,124.5 l0.5,2s1.8,-0.4 3,-0.3l-0.7,2.2s2.3,-0.4 3.4,0.3l-0.5,1.2 2.7,1.2 -2.5,2s4,1.5 4.9,2.7c0,0 -2.7,1.8 -3,1.7 -0.5,-0.2 -0.2,0.8 0.5,1.7l-2.2,-0.1s0.9,2 0.8,2.8c-0.2,0.8 -3.8,0.6 -3.8,0.6m14.1,-5.3s0.7,-3.9 -0.4,-4.9c0,0 2.5,2.2 4.2,2.3s0,-3.2 0,-3.2 2.3,1.2 3.5,1.5c1.2,0.2 -0.9,-2.4 -0.9,-2.4s0.7,-1.3 2.7,-0.1m-45.4,-15.5s0,-1 -0.3,-1.4c-2.4,-3.2 2.4,2.4 3.4,2.6m4.5,0s1.6,1.2 2.3,1.2c0.8,0 -0.5,-3.1 -0.9,-3.5 -0.3,-0.4 2.9,1.3 4,1.4 1.2,0.3 1.4,-2 1.4,-2s3.6,0.7 4.4,1.2m-25,-0.7 l-0.5,-3m-24.2,12.7c-0.2,-0.3 1,-2 0.5,-2.9 0,0 0.7,0.6 1.4,0.7s0.4,-2.2 0.4,-2.2l0.7,0.3s2,-1 1.7,-1.8 1.4,1.2 1.4,1.2 1.1,-1.2 1.1,-1.7 1.4,1.2 1.4,1.2 1.5,-0.3 1.5,-1.2 1.8,0.3 1.8,0.3 0.8,-1 0.7,-1.8m-19.7,9.8s0.9,-2 -0.1,-3.4m-23.6,9.8c0,-1 0.5,-3.6 0,-4s1,0.4 2.2,0.7m9.3,-0.4c-1.2,-1.1 -1.9,-1.7 -3.7,-1.7m-16.2,8.3c0.3,-0.7 0.8,-2.4 0.5,-3.3m25,51.7 l1.3,-2.6 1.6,3.7 1,-2.3 0.4,0.7 2.4,-2.5 2.5,3.8m40.7,-16.5 l1.7,-3.3 2.3,1.2 1,-3.3 2.4,1.5 1.3,-3 2.7,0.7 0.2,-2.2s1.8,1 2.3,1.9c0.6,0.7 0,-2.7 0,-2.7l2.2,1s1.5,-1.6 -0.6,-2.4"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#004b00"/>
  <path
      android:pathData="m343.6,163.1 l2.3,3.4 2,-2.2 1.7,3.8 2.3,-1.4 3.2,1.9 1.5,-1.2 3.5,2.5 1.4,-3 3.2,1 2,-3.5m-84.4,-9.2s1.4,-2.3 1,-3.4c-0.7,-1 2.8,1.8 2.8,1.8s0.7,-2.3 0.3,-3.4c-0.6,-1 3.9,3.2 3.9,3.2s0.7,-3.7 0,-4.4c-0.8,-0.8 4.3,1 4.3,1s2,-0.6 1.2,-1.6c-0.8,-1.1 3.4,1.5 3.4,1.5s1,-2.6 0,-3.6 3.2,1.3 3.2,1.3l-1.6,-2.8 3.4,0.2 -0.3,-3.2m30.3,45.1 l2.2,-1 2,2 2.2,-3.5 2.6,2.5 0.4,-2.5 2.8,1.2 0.6,-1.6 3.1,1.7 0.3,-2.4 1.6,0.5 0.3,-1.5"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#004b00"/>
  <path
      android:pathData="M283.1,298.1a70,70 0,0 1,-6 -13l42.4,-37.2 42.3,37.1a65,65 0,0 1,-6.2 13.3l-0.3,0.5 -3.2,-1.2s-1.4,1.3 -2.5,1.4c-1.4,0.2 -2.7,-1.3 -2.7,-1.3s-1.8,1.6 -3.7,1.3c-1.9,-0.2 -2.6,-1.4 -2.6,-1.4s-2,1.6 -3.4,1.3c-1.2,-0.3 -2.1,-1.2 -2.1,-1.2s-1.2,1.5 -2.8,1.2 -3,-1.2 -3,-1.2 -2.5,1 -4,1.3c-1.6,0.3 -3,-1.4 -3,-1.4s-2,1.4 -2.8,1.4 -3.7,-1.4 -3.7,-1.4 -3,1 -4.1,1 -4,-1.3 -4,-1.3 -2.9,0.8 -4.1,0.8c-1.4,0 -3.4,-0.8 -3.4,-0.8s-2.3,1.1 -3.6,0.8q-2.2,-0.6 -2.4,-0.8c-0.2,-0.2 -2.6,1.1 -4.2,0.8s-3.4,-1.4 -3.4,-1.4 -0.3,0.8 -3.4,1.4"
      android:fillColor="#9dd7ff"/>
  <path
      android:pathData="M319.5,205.1V248l-42.3,37.4a84,84 0,0 1,-4.4 -27.1V205h46.7"
      android:strokeWidth=".7"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M319.5,205.1V248l42.2,37.4a84,84 0,0 0,4.3 -27.1V205h-46.5"
      android:strokeWidth=".7"
      android:fillColor="#ffd83c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M292.2,289.2c3.2,1.7 2,1.2 8.7,3.5a62,62 0,0 0,20.7 2.8c8,0 12.4,-0.7 15,-1.4s3.1,-1.8 3.1,-1.8l-6.5,6.7s-26.3,1.4 -31,-1.7c-2,-1 -6,-4.2 -10,-8z"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M330.8,276.8s8.4,4.8 0,14m-37.8,-1 l12,-11.6 -1.3,15.4m6.3,1.4v-25.8m20.8,25.8v-27.4m-10.5,27.9V266"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m310,270.6 l-5.4,1.7 5.4,0.6v-2.3m10.3,-4 l-5.8,2.1 5.8,0.2v-2.4m10.5,1.6 l-5.8,2.2 5.8,0.3L330.8,268zM334.2,280.5c-0.3,-1.1 3.4,-1 3.4,-1s1,3.9 0,6.3l-3.1,-0.6s0.3,-2.6 -0.3,-4.7z"
      android:strokeWidth=".5"
      android:fillColor="#ff0018"
      android:strokeColor="#000"/>
  <path
      android:pathData="m294.8,288.1 l10.3,-10 -0.5,6s-5.3,3.5 -5.3,5.9l-4.5,-1.9m5.5,2.1s1,-3.1 4,-4.7l-0.3,6.3zM315,272.9s4.2,-0.6 7.4,0c0,0 -0.5,4.2 0,5 0,0 -5.8,-1.4 -8.1,0.8 0,0 -0.8,-4 0.7,-5.8zM306.1,273.9s4.2,-0.5 7.4,0c0,0 -0.8,4.8 -0.3,5.5 0,0 -4.7,-2.6 -7,-0.4 0,0 -1.6,-3.3 0,-5z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M305.9,279.4s4.2,-0.4 7.3,0c0,0 -0.5,4.3 0,5 0,0 -5.5,-1.2 -7.8,0.8 0,0 -1.1,-3.9 0.5,-5.8zM314.3,278.7s6,-0.8 9.1,-0.3c0,0 -1.2,3.4 0.3,5.7 0,0 -6.5,-1.7 -9,0.3 0,0 -2,-3.9 -0.4,-5.7zM305.3,285.2 L313.5,285.8s-0.8,6 -0.3,6.7c0,0 -6.6,-1.5 -7.8,0.6 0,0 -2.2,-5.4 0,-7.9zM314.5,285.2s7,-0.5 10.3,0c0,0 -1.9,4.5 0.2,8.4 0,0 -9.2,-2.6 -10.5,-0.5 0,0 -2.1,-5.4 0,-7.9zM325.3,272.9s4.2,-0.6 7.3,0c0,0 -0.5,4.2 0,5 0,0 -5.8,-1.4 -8,0.8 0,0 -0.9,-4 0.7,-5.8zM325.3,278.7s4.2,-0.5 7.3,0c0,0 -0.5,4.2 0,5 0,0 -5.8,-1.3 -8,0.7 0,0 -0.9,-3.9 0.7,-5.7zM325.8,284.7s4.2,-0.6 7.4,0c0,0 -1,6.6 -0.6,7.4 0,0 -4.8,-2.3 -7,-0.3 0,0 -1.4,-5.2 0.2,-7z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M334.5,285.2s1.3,5 3,5.6l-2.2,2.8s-2.9,-2 -3.2,-4.4c0,0 1.9,-2.1 2.4,-4z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M355.4,298.7a66,66 0,0 1,-36 28.3,67 67,0 0,1 -36.3,-28.8h0.1c3,-0.7 3.4,-1.5 3.4,-1.5s1.8,1.1 3.4,1.4 4.2,-0.8 4.2,-0.8 1,0.5 2.4,0.8 3.6,-0.8 3.6,-0.8 2,0.8 3.3,0.8 4.1,-0.8 4.1,-0.8 3,1.3 4,1.3c1.1,0 4.2,-1 4.2,-1s2.9,1.4 3.7,1.4c0.7,0 2.8,-1.4 2.8,-1.4s1.4,1.7 3,1.4c1.5,-0.2 4,-1.3 4,-1.3s1.4,0.9 3,1.2 2.8,-1.2 2.8,-1.2 0.9,0.9 2.1,1.2c1.4,0.3 3.3,-1.3 3.3,-1.3s0.8,1.2 2.7,1.4c1.8,0.3 3.7,-1.3 3.7,-1.3s1.3,1.5 2.7,1.3c1.1,-0.1 2.5,-1.4 2.5,-1.4l3.3,1"
      android:strokeWidth=".6"
      android:fillColor="#006ac8"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M353.7,301.2c-1.4,0.1 -1.6,-0.8 -1.6,-0.8s-2,1.6 -3.8,1.3c-2,-0.3 -2.9,-1.5 -2.9,-1.5s-2,1.6 -3.4,1.3a5,5 0,0 1,-2.3 -1.1s-1.3,1.4 -3,1.1 -3,-1.1 -3,-1.1 -2.7,1 -4.4,1.3c-1.6,0.3 -3,-1.5 -3,-1.5s-3.8,1.2 -4.6,1.2c-0.9,0 -3.6,-1.2 -3.6,-1.2s-1.9,1.1 -3,1.1 -4.2,-1.3 -4.2,-1.3 -3,0.8 -4.3,0.8c-1.4,0 -3.5,-0.8 -3.5,-0.8s-2.5,1 -3.9,0.8l-2.4,-0.8s-2.7,1 -4.4,0.8c-1.6,-0.4 -3.6,-1.4 -3.6,-1.4s-0.3,0.7 -3.5,1.4h-0.1"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M293.1,305.2s-0.3,0.7 -2.8,1.2l-0.7,0.2 0.6,-0.2 -0.7,0.3a44,44 0,0 0,11.7 11c4.5,3 9.6,6.6 18.4,9.2a71,71 0,0 0,17.8 -9c6,-4.5 9.2,-7.2 12.6,-11.7 -2.7,1.5 -2.3,1.5 -3.7,1.3 -1.3,-0.3 -2.3,-1.3 -2.3,-1.3s-1.3,1.5 -3,1.3c-1.6,-0.3 -3,-1.3 -3,-1.3s-2.2,1.5 -4.4,1.4c-1.6,-0.1 -3,-1.6 -3,-1.6s-3.8,1.3 -4.6,1.3c-0.9,0 -3.6,-1.3 -3.6,-1.3s-1.9,1.2 -3,1.2 -4.2,-1.4 -4.2,-1.4 -3,0.8 -4.3,0.8 -3.4,-0.8 -3.4,-0.8 -2.5,1 -4,0.8c-1.3,-0.3 -2.4,-0.8 -2.4,-0.8s-2.7,1 -4.4,0.8c-1.6,-0.3 -3.6,-1.4 -3.6,-1.4z"
      android:strokeWidth=".6"
      android:fillColor="#5ac800"
      android:strokeColor="#000"/>
  <path
      android:pathData="M319.6,326.9a89,89 0,0 0,17.1 -8.6c-1,0 -4,-1 -2.9,-1.2 0,0 -1.2,1 -2.3,1s-4.2,-1.3 -4.2,-1.3 -3,0.7 -4.3,0.7c-1.4,0 -3.4,-0.7 -3.4,-0.7s-2.6,1 -4,0.7l-2.4,-0.7s-2.8,1 -4.4,0.7 -3.6,-1.3 -3.6,-1.3 -2.4,1.3 -4,1.4a55,55 0,0 0,18.4 9.3z"
      android:strokeWidth=".6"
      android:fillColor="#ffd800"
      android:strokeColor="#000"/>
  <path
      android:pathData="M313.2,249c1.9,-1.6 3,-3 2.3,-4.2 -0.7,-1 -1.8,0.6 -3,0.2 0,0 -2,-1.1 -2.5,-2 -1,-1.2 -2.5,-2.3 -3.3,-3.4 -0.9,-1 -4.2,-5.3 -9.1,-10.3 -1.5,-0.9 -1.1,-4.4 -2.2,-5.5 0,-0.8 -10,-10.6 -13.4,-14 -1.5,-1.1 -2.2,-1.8 -4.7,0.5 -2,1.7 -2.7,3.6 -0.1,5.8l12.8,11.7c1.9,1.8 4.5,2.1 5.6,3l13,13.4c1.7,1.4 2.5,2.4 1.5,3.6 -1,2 0.1,2.3 0.3,2.3 0.2,0.1 1.1,0.1 1.7,-0.4z"
      android:strokeWidth=".5"
      android:fillColor="#b34b00"
      android:strokeColor="#000"/>
  <path
      android:pathData="m280.7,214 l13.4,13.2"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m298.8,220 l1.1,-2.5c0.2,-0.4 3.8,-6.2 5.6,-6.3l11.3,8.2c0,0.5 -5,7.8 -5.3,7.2 0,0 -13.2,-5.5 -12.7,-6.6z"
      android:strokeWidth=".6"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M306,222c-1.6,-1.2 -4.1,-2.4 -6.2,-3.8l-0.9,2c0.7,0.8 10,5.1 11.6,6 0,0 -1.8,-2 -4.6,-4.2z"
      android:strokeWidth=".5"
      android:fillColor="#ccb8c8"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m299.8,218 l5,3.1c2.7,2 4.4,4 6.4,5.2"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m277.1,253 l25.5,-31.7c0.5,-0.4 2.9,-0.4 3.2,2l-24.2,33s-2.7,-0.3 -4.5,-3.4z"
      android:strokeWidth=".5"
      android:fillColor="#782121"
      android:strokeColor="#000"/>
  <path
      android:pathData="M362.7,253.3c-1,0.7 -2.4,0.8 -2.4,0.8 -1.7,-1.9 -3,-4 -5.4,-6.5l-25.5,-31.3 2.7,-2.6 23,26.9c0.6,0.7 4.6,4.9 8.4,9.8 0,0 0.3,2 -0.8,2.9z"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m338.1,220.3 l-4,-5s-3.2,0.5 -3.8,2.1c0,0 -2.2,3.7 -8.2,4.1 0,0 1.7,7 8.6,10.7 0,0 2.5,-7.2 5,-8.4 0,0 2.5,-1.9 2.4,-3.6z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M327.9,242q-0.9,-0.3 -2,0.4c-0.9,0.5 -1.8,0.3 -1.7,-0.7 0,-0.8 0,-0.7 1,-1.4a6,6 0,0 1,5 -0.5l-2.3,2.1m27.8,-27.7c-0.2,-0.7 -0.4,-1.4 0.3,-2.2s0.7,-2.2 -1,-1.5q-2.7,1 -0.9,5.4z"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m327.3,242.5 l28.9,-28.7 7.1,7.6s-1.5,-0.6 -2,-0.2 0.5,1.9 0.5,1.9 -1.3,-0.5 -1.9,-0.1c-0.7,0.4 0,2 0,2s-1.2,-0.8 -1.8,-0.5c-0.5,0.4 0,2.3 0,2.3s-1.5,-0.5 -2,-0.1c-0.3,0.4 0,2 0,2s-1.5,-0.7 -2.3,0 0.4,1.9 0.4,1.9 -2,-0.8 -2.7,0.4 0.1,2.2 0.1,2.2 -1.6,-0.7 -2,-0.4c-0.4,0.4 0,2.3 0,2.3s-1.3,-0.8 -2.2,0.2 0.1,2 0.1,2 -1.3,-0.8 -2,-0.4 0.2,2.2 0.2,2.2 -1.2,-0.9 -2.1,0 0,2 0,2 -1,-1 -2.2,0c-1,1 0,2.1 0,2.1s-1.5,-0.8 -2.1,0 0.2,1.9 0.2,1.9 -1.2,-0.7 -2,-0.3c-0.7,0.4 0,2.1 0,2.1s-1.1,-1.3 -1.6,-0.6 0,2.4 0,2.4 -1,-1.3 -1.6,-0.6c-0.7,0.6 0.4,2 0.4,2s-2,-0.8 -1.5,-0.8z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="m354.5,216.2 l-2.2,2.2 0.5,7.7s0.5,-2.2 1.1,-0.7c0,0 0.2,-1.8 0.7,-1.5 -0.4,-0.2 0,-7.8 0,-7.8m-4.3,4.3 l-1.6,1.5 0.6,7.7s0.4,-2.9 1.1,-0.6v-8.6m-17.6,17.2 l-1.5,1.7 0.4,4.7s0.5,-3 1.1,-0.7l-0.2,-2.6z"
      android:strokeWidth=".5"
      android:fillColor="#7e4b7e"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M330.9,218.3c1,1 4,5 4,5m-2.6,-2.9c-0.2,0.6 -0.6,2.3 -1.5,3.2m2,-2.8c-0.2,0.4 -0.2,1.7 -1.5,3m2,-2.4q-0.3,1 -0.8,1.7m1,-1q-0.1,0.5 -0.4,0.8m-3.2,-2.7a29,29 0,0 1,-4.8 2.8m4,-1.8a14,14 0,0 1,-3.1 2.2m2.8,-1c-0.5,0.4 -1.5,1.4 -2.3,1.7m1.8,-0.5q-0.7,0.7 -1.5,1.1"
      android:strokeWidth=".9"
      android:fillColor="#00000000"
      android:strokeColor="#7e4b7e"/>
  <path
      android:pathData="M311.3,224.7c0.3,-0.6 0.9,-1.8 1.6,-2.8 1,-1.4 1.3,-1.6 1.8,-2"
      android:strokeWidth=".9"
      android:fillColor="#00000000"
      android:strokeColor="#7e4b7e"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M217,303.4c1.5,0.7 0,-4.8 0.5,-6.8 0.6,-2.2 4.8,5.2 6.8,6.2 2.2,1 15.3,-5.2 15.3,-5.2l3.1,-5.8 1.7,4.8s3.6,-4.2 4.6,-5.8c1.1,-1.6 0,3.1 -1,4.7 -1.1,1.5 3.1,2.1 2.6,5.3 -0.5,3 4.2,-2.2 4.2,-2.2l12.1,1s2,-7.3 5.3,-11.5l1,4.8 5,-5.1a66,66 0,0 0,41.3 39.2,67 67,0 0,0 40.2,-36.6l6.7,7s5.3,-1.6 6.3,-2.2a70,70 0,0 1,15.5 5.6c1,0.7 0.3,-3.5 0.3,-3.5s5.8,1.1 5.3,4.2c0,0 1.2,-2.9 2.3,-3.4s3.6,2.7 4.2,3.7c0.5,1 13,2.6 14.7,3.6 0,0 1.3,1.6 1.3,2.7 0,0 2.7,-0.3 4.7,-0.3 0,0 -1.5,0.8 -1.5,2.4q0,2.5 -2.4,3.6c-1.5,0.8 0,1.4 2,1.6 2.2,0.3 0,3 -4.1,4.5 0,0 -1,2.3 0.8,3.1 0,0 -3.7,0.3 -4.2,-0.8 0,0 -1.3,2.5 -1.3,3.8 0,0 -4,-1.3 -5,-1.9 0,0 -1.6,1.6 -0.5,2.3 0,0 -7.1,-0.7 -8.5,-3.4 0,0 -2.5,0.8 -2,1.7 0,0 -5,-3.4 -6.6,-3.4l-1,1.7s-3.5,-2 -4.5,-2 -0.2,1.5 -0.2,1.5l-4.8,-2.9 -1.9,3 -5,-2.7c-1,-0.8 -1.2,1.6 -2,1.9 0,0 -1.6,-2.2 -3.7,-1.9 0,0 -0.8,2.6 -0.5,3.4l-7.1,-1.5c-1.1,-0.3 -0.5,1.2 0,2.3 0,0 -4.5,-1.3 -5.5,-2l-0.3,2s-3.7,-0.3 -4.5,-1l-2.3,3.1h-3.5l-0.7,2.9s-6.6,-1.9 -7.2,-0.8c-0.5,1 0.3,2.6 0.3,2.6l-5.7,-0.7 -0.5,2.3s-6.7,-2 -8.2,-1c0,0 -2,2 -3.4,2.6 -1.3,0.5 0,-0.8 0,-0.8l-2,-2.9s-2.4,1.3 -3.8,0.8 -5.5,2 -6.8,1.3c0,0 0,-1.9 -1.1,-2.4 -1,-0.4 -2.8,-0.4 -3.6,0.8l-2.2,-3c-1.2,-2.2 -3.1,0.1 -3.1,0.1l-0.3,-2.5 -3.6,-0.3c-2.2,-0.3 -2.6,-2.2 -2.6,-2.2s-5.6,0.6 -6.4,0.3l1.4,-1.3s-7.6,1.6 -9.2,1c-1.7,-0.5 1.8,-2.6 1.8,-2.6l-8.4,-2c-1.3,-0.4 -9,1.2 -10,1.2s-1.6,-1.8 -1.6,-1.8 -1.8,1.6 -3.4,2.1c0,0 -1.3,-3.1 -0.5,-3.4 0,0 -5,1.3 -5.3,2.6 0,0 -1.2,-2 -1,-3 0,0 -2.4,3.8 -3.5,4.6 -1,0.8 -0.2,-1.9 -0.2,-1.9s-2.9,3.7 -4,4v-2.6s-8.4,4.5 -10.7,3.9c0,0 0,-1.9 0.8,-2.4l-5.3,-0.2q-2,-0.5 -4.2,-1.1c-1.3,-0.2 1.9,-1.6 4,-1 0,0 -5.8,-2.1 -7.6,-1 0,0 -0.8,-2.2 2.3,-3 0,0 -4,-3.9 -6.3,-3.6 0,0 2.6,-1.6 4.8,-0.8 0,0 -2.5,-6.6 -4,-7.4 0,0 5.5,-1 7.1,-0.2z"
      android:strokeWidth=".5"
      android:fillColor="#289400"
      android:strokeColor="#060"/>
  <path
      android:pathData="M208.9,188.8 L280,152c0.3,-2 -0.7,-3.4 -1.9,-5 -20.3,10 -38.8,22.2 -61.4,29.5 -1.3,0.5 -12.5,5.2 -18.3,12.1a8,8 0,0 0,-2 4.5s0.2,3.1 0.9,4c0,0 0.1,0.8 1.2,1.5 0,0 0.7,0.3 1.1,0.2s1.4,0 2.6,-2a52,52 0,0 1,6.7 -8z"
      android:strokeWidth=".5"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m266.1,152.4 l9.2,-4.4s3.1,2.3 2.8,5c0,0 0.7,6 8.2,10.2 0,0 -6.3,8 -17.4,8.6 0,0 1.2,-10.4 -1.1,-13.5 0,0 -0.4,0.4 -0.2,-0.1 0.3,-1 -0.2,-4.5 -1.7,-5.3z"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M276.9,153.6c-2,0.8 -8.3,4.1 -8.3,4.1m5.2,-2.1c0,1 -0.6,3.3 0,5m-0.8,-4.7c0,0.5 -0.8,2.2 0,4.6m-1,-4.1c-0.2,1 -0.4,1.5 0,2.6m-0.8,-2q-0.1,0.7 0,1.2m5.8,-1.5c0.3,0.7 2.9,4.8 4.5,6.5m-4.1,-4.7c0.3,0.8 1.7,3.6 2.8,4.7m-3,-3c0.4,1 1.1,2.7 2,3.7m-2,-1.8a8,8 0,0 0,1.1 2.5"
      android:strokeWidth=".8"
      android:fillColor="#00000000"
      android:strokeColor="#7e4b7e"/>
  <path
      android:pathData="m342.3,148.3 l26.1,12.8c5.2,2.5 7.1,0.2 10,1.5l51.6,26.2c4.5,2 7,2.5 6.3,4.8 -0.1,3.7 1.7,3.7 2.2,3.8 0.4,0 2.1,-0.2 2.8,-1.2l0.6,-0.6c0.6,-0.6 0.5,-0.7 0.7,-1 2.1,-3 3.3,-5.8 1,-7.5 -2.5,-1.3 -2.8,1.5 -5.6,1.2 0,0 -4.8,-1.8 -6.6,-3.1 -3.2,-1.8 -7.8,-3.9 -8.8,-5 -3.3,-0.6 -28,-14.6 -42.1,-21.6 -1.5,-1.2 -2.3,-5.9 -5.3,-7.4 -0.8,-1.3 -19.8,-10 -29.8,-15 -3.6,-1.3 -5.7,-2.2 -8.3,2.3 -2.3,4.4 -1.8,6.8 5.2,9.8z"
      android:strokeWidth=".5"
      android:fillColor="#b34b00"
      android:strokeColor="#000"/>
  <path
      android:pathData="m344.5,142.8 l29.2,14.6"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M257.31,158.5c-0.19,0.58 -0.67,0.19 -0.67,0.38 -0.38,2.11 -0.29,3.17 -0.77,4.32a6.72,6.72 0,0 1,-2.98 2.5c-1.92,0.67 -4.32,2.98 -8.16,0.86l-0.58,-0.48s1.06,3.94 -2.88,5.95l-15.07,7.87s-1.44,2.69 -0.48,6.24c0,0.58 0.29,1.06 -0.29,1.44 -0.96,0.67 -2.59,2.69 -3.55,5.76 0,0 -2.88,-1.25 -2.88,-3.07s1.15,-2.88 1.44,-4.22c0.29,-1.25 0.96,-4.51 0.67,-5.47 -0.38,-1.06 -2.21,-1.44 -2.88,-1.92 -0.58,-0.29 -1.34,0.48 -1.25,1.34 0.1,0.96 0.77,0.77 1.54,0.96 0,0 -0.86,1.92 -0.77,3.26l-8.83,4.61 1.54,2.21c0.38,0.67 1.25,9.79 3.84,14.11s3.55,3.07 4.13,3.65l1.92,1.92c0.77,0.48 3.84,0.77 4.32,-0.96a8.64,8.64 0,0 1,3.07 -3.94c1.92,-1.54 5.76,-5.18 5.76,-6.91 0,0 2.21,6.53 1.92,11.9 0,0 8.16,1.63 15.46,1.82 7.3,0 11.04,-1.44 11.04,-1.44s0.96,-6.14 0.58,-7.39c0,0 1.15,4.03 2.11,5.38 0.96,1.73 2.02,5.09 4.13,5.28 2.11,0.1 3.84,-2.02 4.03,-2.88l0,-6.53l8.35,0s3.65,1.44 4.8,1.25 1.92,-1.25 0.77,-1.63l-0.38,-0.29c1.15,0 2.5,0.67 2.5,0.67l4.13,0l-0.1,-0.48c-0.29,-1.15 -2.5,-2.11 -4.03,-2.88 -1.54,-0.48 -3.36,-1.15 -4.51,-1.25a19.2,19.2 0,0 0,-5.95 0.38c-0.77,0 -3.74,0.38 -5.57,1.25 -0.48,0.29 -0.96,0.38 -0.77,-0.96 0.29,-1.25 0.19,-7.49 -1.25,-10.08 -1.15,-1.92 -0.96,-3.46 -0.86,-4.99a83.52,83.52 68.61,0 0,0 -8.45c-0.29,-1.63 -2.4,-3.84 -6.34,-4.22 -3.65,-0.48 -6.72,-3.55 -7.01,-5.95 -0.29,-2.5 0.38,-6.91 0.77,-8.93"
      android:strokeWidth="0.48"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="250.88"
          android:centerY="186.56"
          android:gradientRadius="42.38"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFEF5D"/>
        <item android:offset="1" android:color="#FFEA5700"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M240.7,147.46c-0.67,3.17 -0.67,5.86 -0.58,8.93 0.1,2.02 1.06,4.42 1.92,6.43a9.6,9.6 0,0 0,2.69 3.74c3.46,2.21 6.72,-0.19 8.54,-0.96a5.76,5.76 0,0 0,2.59 -2.4c0.48,-1.15 0.29,-2.02 0.77,-4.32l0.67,-0.38c0.96,-1.44 0.67,-1.92 1.15,-2.88s-0.48,-2.11 -1.73,-1.92q1.06,-2.88 0.38,-5.66c-0.19,-0.58 0.48,-2.11 -0.58,-1.73 -4.42,1.73 -9.6,1.34 -14.21,1.34 -0.38,0 -0.77,-0.77 -1.34,-0.67l-0.29,0.48"
      android:strokeWidth="0.48"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="249.38"
          android:centerY="156.8"
          android:gradientRadius="9.53"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFEF5D"/>
        <item android:offset="1" android:color="#FFEA5700"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M240.32,149.09q-0.77,-0.48 0,-3.07c0.67,-1.92 0,-1.92 1.06,-3.07s0.86,-1.54 1.54,-1.82 2.69,-0.19 3.36,-0.48a10.56,10.56 0,0 1,8.64 -0.29c1.44,0.77 2.4,2.69 3.26,3.46 0.96,0.67 1.63,3.46 1.44,7.58 -0.29,4.13 -1.15,3.36 -1.15,3.36s-1.15,-1.92 -2.11,-0.29c0,0 0,-1.63 0.19,-2.88 0.1,-1.06 -0.58,-3.26 0.1,-4.7 0,0 -5.57,1.73 -8.35,1.63 -2.88,-0.19 -7.1,-0.86 -7.3,-1.54l-0.67,2.11"
      android:strokeWidth="0.48"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M243.2,145.73c-0.29,-0.58 -1.73,-2.78 -0.58,-3.46 0.96,-0.58 0.38,2.69 1.92,3.36 0,0 -0.58,1.44 -1.34,0.1z"
      android:strokeWidth="0.48"
      android:fillColor="#fff"
      android:strokeColor="#fff"/>
  <path
      android:pathData="m246.37,153.7 l-0.86,3.07m10.18,1.54c0.19,0.58 0.38,0.29 0.96,0.38m0.29,-3.84c0.48,-0.48 1.15,-0.19 0.77,1.25m-16.99,-4.7 l2.69,0.96c1.44,0.58 0.58,2.78 0.19,4.13 -0.19,0.67 -0.96,1.92 -0.58,2.4 0.48,0.77 1.06,0.29 1.92,0.38 0,-0.29 0.58,-0.48 0.77,-0.48 0.67,0 1.06,0.48 1.63,0.29s0.48,-1.15 0.29,-1.63m-0.1,-4.32c0,-0.38 0.96,-0.77 1.44,-0.86 2.02,-0.38 3.46,-0.67 5.18,0.48m-5.47,10.66c-1.73,0.38 -3.46,0.67 -4.99,-0.19m0,-0.77c0.77,-0.38 1.06,-0.77 2.02,-0.29l1.06,-0.38q1.06,0 2.11,0.77m-5.28,0.29c0.96,-0.29 3.17,-0.29 5.18,0.1"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M240.61,153.7c0.67,0 1.54,-0.86 3.07,0.29 1.06,0.77 -1.73,1.44 -1.25,1.34 -1.34,0.19 -1.73,-0.48 -1.82,-1.15q-0.1,-0.29 0,-0.48zM248.67,154.46c0.67,-1.34 1.73,-0.96 2.69,-0.77 0.96,0.29 0.67,0.1 1.63,0.67 -1.15,0.1 -0.86,0.19 -1.63,0.58q-1.34,0.77 -2.21,0.29 -0.48,-0.29 -0.67,-0.96 0,0.29 0.19,0.29z"
      android:strokeWidth="0.29"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M242.05,154.94q0.58,-0.1 0.67,-0.77 0,-0.96 -0.67,-0.96 -0.77,0 -0.86,0.96 0,0.67 0.86,0.77zM250.3,155.14q0.58,0 0.67,-0.96a0.96,0.96 0,0 0,-0.77 -0.77q-0.58,0 -0.67,0.77 0.1,0.96 0.77,0.96z"
      android:strokeWidth="0.29"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M292.06,205.15a3.84,3.84 0,0 0,-1.82 -1.34l-1.92,-0.96q-1.15,-0.38 -2.3,-0.86m0.48,2.5 l-0.86,-0.48q-0.48,0 -0.96,-0.19 0,-0.1 -0.19,-0.19a3.84,3.84 0,0 1,-2.3 -1.25m-60.29,-8.93c0.96,0.67 3.07,2.11 3.26,4.9a10.56,10.56 0,0 0,0.96 2.88M213.44,193.92c0.48,1.63 0.58,7.1 5.76,12.86m-4.8,-20.54c-0.38,1.06 -0.96,2.11 0.19,2.88m13.82,10.27c1.06,-2.5 4.42,-2.3 4.42,-4.61"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M233.41,199.01c-0.29,-2.78 -0.96,-4.32 -0.38,-8.35 0.19,-1.15 0.38,-5.66 0,-7.78 0,0 1.54,7.49 -3.55,8.93m33.02,12c0,-3.84 0.96,-5.95 0.96,-5.95 1.15,-5.38 -1.15,-7.39 -0.29,-8.74 0.86,-1.15 1.92,-3.65 0.48,-8.54 0,0 2.69,10.08 -4.32,10.08 -7.2,0 -6.91,-2.59 -6.91,-2.59"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M233.02,182.88c0,1.92 0,8.06 8.64,8.06 2.69,0 5.57,-1.92 7.1,-3.17m2.11,-2.59q-0.38,-2.88 -0.29,-6.72m-13.06,-3.26c2.3,-0.29 4.22,-0.58 8.64,-0.58m0.19,-6.05c0.48,2.11 -0.29,5.57 3.84,6.05m4.13,-6.82c-0.58,1.54 -0.58,3.17 -0.77,4.22m0.19,2.59c1.63,0 4.8,-1.06 8.64,-0.29m1.34,21.89c0.86,1.54 0.96,3.65 3.46,5.95m-30.43,-10.94c0.19,1.25 -1.73,5.47 -0.29,8.93a11.52,11.52 0,0 1,-1.15 10.56m6.43,-18.24c-0.86,1.63 -2.21,3.17 -2.5,4.51m6.34,-1.54c0,0.96 -2.59,8.35 -4.51,10.46 0,0 3.46,1.34 2.88,6.34m7.1,-20.16c0,1.15 -0.19,1.54 0.96,2.3m2.3,0c1.25,1.54 5.28,6.24 4.42,8.83m-7.1,-6.24c0.29,0.96 0.29,6.05 -0.58,8.64m-2.02,-0.19c-1.06,0 -2.69,0.77 -2.88,2.59m8.35,-2.88c1.06,-0.29 3.17,1.54 3.17,6.72m15.07,-9.79c-2.02,0.58 -2.88,1.63 -2.3,1.06 -1.06,0.96 -2.11,1.54 -2.11,1.54"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M238.21,190.08q0.96,0 1.06,-0.96 -0.1,-0.58 -1.06,-0.67 -0.86,0.1 -0.96,0.77 0.1,0.77 0.96,0.86zM260.29,190.37q0.96,0 1.06,-0.96 -0.1,-0.58 -0.96,-0.67t-1.06,0.77c-0.1,0.77 0.48,0.86 0.96,0.86z"
      android:strokeWidth="0.29"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M229.28,230.21c1.25,-7.68 4.8,-13.92 4.9,-14.59 0,0 3.74,0.86 10.18,1.44s8.06,0.38 11.04,0.19l6.53,-0.77s1.15,2.3 1.25,4.61c0.29,4.99 2.21,26.59 2.4,32.35 0,0 0.77,3.46 0.77,11.81 0,9.22 2.5,27.84 2.5,37.82l-2.5,0.48s-13.92,1.06 -13.92,-0.77c0,-2.02 -0.77,-25.92 1.34,-40.42 0,0 -0.48,-2.88 -1.34,-5.28s-4.13,-9.6 -4.13,-11.23c0,0 -5.47,15.36 -5.76,18.05 -0.29,2.5 -1.15,36.86 -1.63,39.65 0,0 -7.87,6.05 -8.93,6.72 -1.06,0.38 -8.64,-3.26 -8.93,-4.22a202.56,202.56 129.94,0 1,4.42 -31.87c1.34,-6.82 0.58,-8.06 0.58,-9.98s0.1,-26.4 1.25,-33.98z"
      android:strokeWidth="0.48"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M242.53,264q-0.77,3.26 -3.46,5.47m-2.59,-51.46c-0.29,1.25 -2.21,7.01 -6.91,10.46m13.73,-9.41c-1.06,3.46 -5.28,21.79 -6.05,43.58m-0.29,6.82 l-1.63,19.97m-0.77,2.88a68.16,68.16 0,0 0,-2.59 17.95m30.91,-34.56c-0.19,1.92 -0.96,15.07 3.46,26.88m-17.76,-82.56c-0.29,2.21 -0.77,16.03 -0.77,17.09s1.54,2.69 2.3,2.88c0,0 -1.54,0.58 -1.92,5.76m-3.84,-12.29c-0.29,1.63 -1.92,8.16 -2.88,10.85m12.48,-24.48s4.8,0.96 5.09,0c0,0 1.54,28.32 3.65,44.16m-10.75,-40.51 l-0.58,15.84m2.4,2.02s1.82,2.69 1.34,3.84m-17.09,-26.88 l2.88,0.77m4.99,1.54c-0.29,0.86 -0.58,4.42 -0.58,4.42m8.45,37.34c0.58,0.77 1.34,1.92 0.77,4.22"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M233.6,215.42s0,-3.07 1.06,-4.61c0,0 3.36,0.67 8.06,1.15q7.1,0.96 12.1,0.48c3.55,-0.38 7.1,-1.25 7.1,-1.25s0.96,4.61 0.48,5.28c0,0 -3.84,0.77 -7.97,0.96a82.56,82.56 0,0 1,-13.92 -0.77,54.72 54.72,0 0,1 -7.01,-1.25z"
      android:strokeWidth="0.48"
      android:fillColor="#9b5f00"
      android:strokeColor="#000"/>
  <path
      android:pathData="m236.48,215.81 l0.67,-4.61l0.96,0l-0.19,4.9 -1.34,-0.19m21.12,-3.94 l0.1,5.28 1.25,-0.19 -0.1,-5.38 -1.25,0.29m-14.98,0 l-0.38,5.09l0.67,0l0.67,-5.09l-0.96,0"
      android:strokeWidth="0.48"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M225.73,307.97q-0.58,1.92 -0.96,5.28c0,0.48 -0.58,3.17 0.96,3.26 0.96,0 1.34,-2.78 1.34,-2.78s-0.96,3.17 0.29,3.55c1.73,0.38 1.92,-3.46 1.92,-3.46 -0.19,1.06 -1.34,3.84 0.29,4.13 1.82,0.29 2.3,-3.74 2.3,-3.74s-1.54,4.03 0.29,4.13c1.63,0 1.63,-3.84 1.63,-3.84s-0.86,4.8 1.44,4.32c1.34,-0.19 1.63,-1.92 1.92,-2.88 0.38,-1.44 1.25,-3.26 0.19,-6.34 -0.77,-2.21 -0.48,-2.88 -0.48,-2.88s-2.78,2.11 -4.9,3.46c-0.96,0.58 -6.24,-2.21 -6.24,-2.21zM254.24,303.74c-0.48,-0.29 -0.38,1.82 -0.58,2.78s0.19,3.07 3.46,2.88 6.43,-0.96 8.54,-0.48c2.02,0.58 5.09,0.67 6.72,0.67q2.4,0 3.55,-0.48c0.67,-0.29 2.11,0.38 2.88,0.38s1.63,-0.96 1.54,-1.73c0,-1.25 -1.25,-1.25 -2.88,-1.34a21.12,21.12 0,0 1,-4.8 -0.96c-1.06,-0.29 -2.98,-1.44 -5.47,-1.92l-5.66,0.19c-0.96,0 -6.72,0.29 -7.3,0z"
      android:strokeWidth="0.48"
      android:fillColor="#ffa54b"
      android:strokeColor="#000"/>
  <path
      android:pathData="M205.47,182.21c-0.48,0.29 -1.06,-1.92 0.38,-2.78 0,0 0.38,-1.92 1.92,-2.02 0,0 1.34,-1.82 3.17,-1.63 0,0 2.11,-1.44 2.88,-1.25 0.86,0.1 2.88,2.02 2.88,2.78 0.19,0.67 0,1.82 -0.48,2.3s-0.96,-0.1 -1.06,-0.96c0,0 0.38,1.54 -0.77,2.11s-1.06,0.19 -1.15,-0.58c0,0 -0.38,1.54 -1.15,1.82s-1.25,-0.48 -1.92,-0.48c0,0 1.06,0.86 0.38,1.44q-1.15,0.58 -1.92,-0.1c-0.48,-0.48 -1.15,-1.82 -3.17,-0.58z"
      android:strokeWidth="0.48"
      android:fillColor="#ffb366"
      android:strokeColor="#000"/>
  <path
      android:pathData="M215.07,178.56c-0.48,-1.44 -2.11,-2.88 -3.84,-2.69m2.11,4.22q-0.29,-0.58 -0.77,-1.15c-0.86,-1.44 -2.69,-1.34 -4.22,-1.73m2.3,4.32c-0.58,-0.38 -0.77,-1.15 -1.34,-1.44q-1.34,-0.77 -3.17,-0.67"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M381.54,158.5c0.67,-0.29 0.48,0.58 0.58,0.96 0.38,1.25 0.38,2.59 0.86,3.74s0.77,1.44 1.73,1.92c1.54,0.96 6.14,3.46 9.41,1.44l0.67,-0.48s-1.25,3.94 2.88,5.95l14.98,7.87s1.44,2.59 0.58,6.24q-0.48,0.86 0.19,1.44c0.96,0.67 2.5,2.69 3.65,5.76 0,0 2.88,-1.25 2.88,-3.07s-1.25,-2.88 -1.54,-4.22c-0.19,-1.25 -0.96,-4.51 -0.58,-5.47 0.38,-1.06 2.02,-1.44 2.69,-1.92 0.67,-0.29 1.54,0.48 1.25,1.34 0,0.96 -0.77,0.77 -1.54,0.96 0,0 0.96,1.92 0.77,3.26l9.02,4.61 -1.54,2.21c-0.48,0.67 -1.44,9.79 -4.03,14.02s-3.55,3.17 -4.03,3.84l-2.02,1.73c-0.58,0.48 -3.65,0.86 -4.32,-0.86a8.64,8.64 0,0 0,-2.98 -3.94c-1.92,-1.54 -5.76,-5.18 -5.76,-6.91 0,0 -2.21,6.53 -1.92,11.9 0,0 -8.06,1.63 -15.55,1.82 -7.3,0 -11.04,-1.44 -11.04,-1.44s-0.96,-6.14 -0.58,-7.39c0,0 -1.15,4.03 -1.92,5.38 -1.06,1.73 -2.11,5.09 -4.22,5.28 -2.11,0.1 -3.84,-2.02 -4.03,-2.88l0,-6.53l-8.35,0s-3.65,1.44 -4.8,1.25 -1.82,-1.25 -0.86,-1.63l0.38,-0.29a6.72,6.72 0,0 0,-2.4 0.67L345.92,205.06l0.1,-0.48c0.29,-1.15 2.5,-2.11 4.13,-2.88 1.54,-0.48 3.17,-1.15 4.42,-1.25 1.82,-0.19 3.07,-0.1 5.95,0.38 0.77,0 3.84,0.38 5.57,1.25 0.48,0.29 0.96,0.38 0.77,-0.96 -0.29,-1.25 -1.15,-5.28 0.29,-7.97 0.96,-1.82 0.86,-3.36 0.77,-4.99 -0.19,-1.63 -1.06,-5.38 -1.06,-7.01 0,-5.47 4.22,-7.68 7.87,-8.83 4.13,-1.34 6.82,-2.69 6.72,-5.09 -0.19,-2.59 0.48,-6.72 0.1,-8.64"
      android:strokeWidth="0.48"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="388.02"
          android:centerY="186.48"
          android:gradientRadius="42.31"
          android:type="radial">
        <item android:offset="0" android:color="#FF952D1A"/>
        <item android:offset="1" android:color="#FF570A00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M398.05,147.46a19.2,19.2 0,0 1,1.15 5.09c0,1.15 -0.48,1.34 -0.1,2.69 0.67,2.3 -0.77,4.8 -2.4,7.68 -0.77,1.15 -1.34,2.88 -2.59,3.65 -3.26,2.02 -7.2,-0.38 -9.22,-1.25a3.84,3.84 0,0 1,-1.92 -2.11q-0.48,-1.54 -0.77,-3.84c0,-0.29 0,-1.15 -0.58,-0.86 -1.25,-1.34 -0.96,-1.92 -1.54,-2.88 -0.48,-0.96 0.96,-2.11 1.92,-1.92a9.6,9.6 0,0 1,-0.19 -5.66c0.19,-0.58 -0.58,-2.11 0.48,-1.73 4.32,1.73 9.6,1.34 14.21,1.34 0.38,0 0.67,-0.77 1.25,-0.67l0.29,0.48"
      android:strokeWidth="0.48"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="389.59"
          android:centerY="156.76"
          android:gradientRadius="9.82"
          android:type="radial">
        <item android:offset="0" android:color="#FF952D1A"/>
        <item android:offset="1" android:color="#FF570A00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M398.53,149.09c0.38,-0.38 0.1,-2.21 -0.38,-4.13 -0.1,-0.96 -0.96,-1.44 -1.92,-2.5s-1.54,-0.96 -2.21,-1.34c-0.58,-0.19 -1.92,-0.19 -2.69,-0.48 -0.58,-0.29 -2.11,-0.1 -3.55,0.1 -1.92,0.29 -3.07,0.19 -4.51,0.96s-2.69,1.73 -3.17,3.17c-0.38,0.96 -0.96,2.4 -0.77,6.53s0.96,3.36 0.96,3.36 1.06,-1.63 1.82,-0.58l-0.29,-3.46q-0.19,-1.73 0.48,-4.42s4.42,1.63 8.64,1.63c2.88,0 6.72,-0.29 6.82,-0.96l0.67,2.11z"
      android:strokeWidth="0.48"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M392.38,153.7c0.67,1.73 0.1,1.25 0.38,2.3m-9.79,1.92c0,0.77 -0.58,0.58 -0.96,0.58"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M398.62,152.45c-0.96,-0.67 -2.59,-0.19 -3.36,0.1 -1.44,0.67 -0.67,2.21 -0.19,3.55 0.19,0.67 0.96,1.63 0.48,2.3 -0.38,0.77 -0.67,0.38 -1.54,0.48 0,-0.29 -0.77,0 -1.06,0 -0.58,0 -0.96,0.38 -1.44,0.19s-0.48,-1.15 -0.29,-1.63m0.1,-4.32c0,-0.38 -1.25,-0.77 -2.59,-0.86 -2.21,0 -2.4,0.1 -4.03,1.25m11.04,8.64q-0.48,-1.06 -2.02,-0.38c0,-0.19 -1.54,-0.38 -1.54,-0.38q-0.96,0 -2.02,0.67m5.38,0.58c-0.96,-0.58 -3.17,-0.19 -5.38,-0.19m0.19,0.77c1.63,0.48 3.36,0.77 4.99,0"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M398.14,153.7c-0.67,0 -0.96,-0.86 -2.98,0.29 -1.06,0.67 1.73,1.44 1.34,1.34 1.15,0.19 1.54,-0.48 1.63,-1.15q0.19,-0.29 0,-0.48zM390.18,154.46c0,-0.77 -1.73,-0.96 -2.78,-0.77 -0.96,0.29 -0.48,0.1 -1.44,0.67 0.96,0.1 0.67,0.19 1.54,0.58 0.86,0.48 0.96,0.86 2.21,0.29q0.48,-0.19 0.77,-0.96 0,0.29 -0.29,0.29z"
      android:strokeWidth="0.29"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M396.9,154.94a0.96,0.96 0,0 1,-0.77 -0.77q0,-0.96 0.77,-0.96 0.58,0 0.67,0.96 0,0.67 -0.67,0.77zM388.54,155.14q-0.58,0 -0.67,-0.96 0.1,-0.77 0.77,-0.77 0.86,0 0.67,0.77 0,0.96 -0.77,0.96z"
      android:strokeWidth="0.29"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M346.78,205.15a3.84,3.84 0,0 1,1.82 -1.34l1.92,-0.96q1.15,-0.38 2.3,-0.86m-0.48,2.5l0.86,-0.48q0.48,0 0.96,-0.19 -0,-0.1 0.19,-0.19a3.84,3.84 0,0 0,2.3 -1.25m60.29,-8.93c-0.96,0.67 -3.07,2.11 -3.26,4.9a10.56,10.56 0,0 1,-0.96 2.88M425.41,193.92c-0.48,1.63 -0.58,7.1 -5.76,12.86m4.8,-20.54c0.38,1.06 0.96,2.11 -0.19,2.88m-13.82,10.27c-1.06,-2.5 -4.42,-2.3 -4.42,-4.61"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M405.44,199.01c0.29,-2.78 0.96,-4.32 0.38,-8.35 -0.19,-1.15 -0.38,-5.66 -0,-7.78 -0,0 -1.54,7.49 3.55,8.93m-33.02,12c-0,-3.84 -0.96,-5.95 -0.96,-5.95 -1.15,-5.38 1.15,-7.39 0.29,-8.74 -0.86,-1.15 -1.92,-3.65 -0.48,-8.54 -0,0 -2.69,10.08 4.32,10.08 7.2,0 6.91,-2.59 6.91,-2.59"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M405.82,182.88c-0,1.92 -0,8.06 -8.64,8.06 -2.69,0 -5.57,-1.92 -7.1,-3.17m-2.11,-2.59q0.38,-2.88 0.29,-6.72m13.06,-3.26c-2.3,-0.29 -4.22,-0.58 -8.64,-0.58m-0.19,-6.05c-0.48,2.11 0.29,5.57 -3.84,6.05m-4.13,-6.82c0.58,1.54 0.58,3.17 0.77,4.22m-0.19,2.59c-1.63,0 -4.8,-1.06 -8.64,-0.29m-1.34,21.89c-0.86,1.54 -0.96,3.65 -3.46,5.95m30.43,-10.94c-0.19,1.25 1.73,5.47 0.29,8.93a11.52,11.52 0,0 0,1.15 10.56m-6.43,-18.24c0.86,1.63 2.21,3.17 2.5,4.51m-6.34,-1.54c-0,0.96 2.59,8.35 4.51,10.46 -0,0 -3.46,1.34 -2.88,6.34m-7.1,-20.16c-0,1.15 0.19,1.54 -0.96,2.3m-2.3,0c-1.25,1.54 -5.28,6.24 -4.42,8.83m7.1,-6.24c-0.29,0.96 -0.29,6.05 0.58,8.64m2.02,-0.19c1.06,0 2.69,0.77 2.88,2.59m-8.35,-2.88c-1.06,-0.29 -3.17,1.54 -3.17,6.72m-15.07,-9.79c2.02,0.58 2.88,1.63 2.3,1.06 1.06,0.96 2.11,1.54 2.11,1.54"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M400.64,190.08q-0.96,0 -1.06,-0.96 0.1,-0.58 1.06,-0.67 0.86,0.1 0.96,0.77 -0.1,0.77 -0.96,0.86zM378.56,190.37q-0.96,0 -1.06,-0.96 0.1,-0.58 0.96,-0.67t1.06,0.77c0.1,0.77 -0.48,0.86 -0.96,0.86z"
      android:strokeWidth="0.29"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M409.57,230.21c-1.25,-7.68 -4.8,-13.92 -4.9,-14.59 -0,0 -3.74,0.86 -10.18,1.44s-8.06,0.38 -11.04,0.19l-6.53,-0.77s-1.15,2.3 -1.25,4.61c-0.29,4.99 -2.21,26.59 -2.4,32.35 -0,0 -0.77,3.46 -0.77,11.81 -0,9.22 -2.5,27.84 -2.5,37.82l2.5,0.48s13.92,1.06 13.92,-0.77c-0,-2.02 0.77,-25.92 -1.34,-40.42 -0,0 0.48,-2.88 1.34,-5.28s4.13,-9.6 4.13,-11.23c-0,0 5.47,15.36 5.76,18.05 0.29,2.5 1.15,36.86 1.63,39.65 -0,0 7.87,6.05 8.93,6.72 1.06,0.38 8.64,-3.26 8.93,-4.22a202.56,202.56 50.06,0 0,-4.42 -31.87c-1.34,-6.82 -0.58,-8.06 -0.58,-9.98s-0.1,-26.4 -1.25,-33.98z"
      android:strokeWidth="0.48"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M396.32,264q0.77,3.26 3.46,5.47m2.59,-51.46c0.29,1.25 2.21,7.01 6.91,10.46m-13.73,-9.41c1.06,3.46 5.28,21.79 6.05,43.58m0.29,6.82l1.63,19.97m0.77,2.88a68.16,68.16 0,0 1,2.59 17.95m-30.91,-34.56c0.19,1.92 0.96,15.07 -3.46,26.88m17.76,-82.56c0.29,2.21 0.77,16.03 0.77,17.09s-1.54,2.69 -2.3,2.88c-0,0 1.54,0.58 1.92,5.76m3.84,-12.29c0.29,1.63 1.92,8.16 2.88,10.85m-12.48,-24.48s-4.8,0.96 -5.09,0c-0,0 -1.54,28.32 -3.65,44.16m10.75,-40.51l0.58,15.84m-2.4,2.02s-1.82,2.69 -1.34,3.84m17.09,-26.88l-2.88,0.77m-4.99,1.54c0.29,0.86 0.58,4.42 0.58,4.42m-8.45,37.34c-0.58,0.77 -1.34,1.92 -0.77,4.22"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M405.25,215.42s-0,-3.07 -1.06,-4.61c-0,0 -3.36,0.67 -8.06,1.15q-7.1,0.96 -12.1,0.48c-3.55,-0.38 -7.1,-1.25 -7.1,-1.25s-0.96,4.61 -0.48,5.28c-0,0 3.84,0.77 7.97,0.96a82.56,82.56 0,0 0,13.92 -0.77,54.72 54.72,0 0,0 7.01,-1.25z"
      android:strokeWidth="0.48"
      android:fillColor="#9b5f00"
      android:strokeColor="#000"/>
  <path
      android:pathData="m402.37,215.81l-0.67,-4.61l-0.96,0l0.19,4.9 1.34,-0.19m-21.12,-3.94l-0.1,5.28 -1.25,-0.19 0.1,-5.38 1.25,0.29m14.98,0l0.38,5.09l-0.67,0l-0.67,-5.09l0.96,0"
      android:strokeWidth="0.48"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M413.12,307.97q0.58,1.92 0.96,5.28c-0,0.48 0.58,3.17 -0.96,3.26 -0.96,0 -1.34,-2.78 -1.34,-2.78s0.96,3.17 -0.29,3.55c-1.73,0.38 -1.92,-3.46 -1.92,-3.46 0.19,1.06 1.34,3.84 -0.29,4.13 -1.82,0.29 -2.3,-3.74 -2.3,-3.74s1.54,4.03 -0.29,4.13c-1.63,0 -1.63,-3.84 -1.63,-3.84s0.86,4.8 -1.44,4.32c-1.34,-0.19 -1.63,-1.92 -1.92,-2.88 -0.38,-1.44 -1.25,-3.26 -0.19,-6.34 0.77,-2.21 0.48,-2.88 0.48,-2.88s2.78,2.11 4.9,3.46c0.96,0.58 6.24,-2.21 6.24,-2.21zM384.61,303.74c0.48,-0.29 0.38,1.82 0.58,2.78s-0.19,3.07 -3.46,2.88 -6.43,-0.96 -8.54,-0.48c-2.02,0.58 -5.09,0.67 -6.72,0.67q-2.4,0 -3.55,-0.48c-0.67,-0.29 -2.11,0.38 -2.88,0.38s-1.63,-0.96 -1.54,-1.73c-0,-1.25 1.25,-1.25 2.88,-1.34a21.12,21.12 0,0 0,4.8 -0.96c1.06,-0.29 2.98,-1.44 5.47,-1.92l5.66,0.19c0.96,0 6.72,0.29 7.3,0z"
      android:strokeWidth="0.48"
      android:fillColor="#ffa54b"
      android:strokeColor="#000"/>
  <path
      android:pathData="M431.94,185.76c0.48,0.19 1.92,-1.92 0.48,-2.88 0,0 -0.48,-1.92 -1.92,-1.92 0,0 -1.34,-1.92 -3.26,-1.63 0,0 -1.92,-1.44 -2.78,-1.34 -0.96,0.19 -2.88,2.11 -2.88,2.88 -0.19,0.48 0,1.73 0.48,2.3s0.96,-0.1 0.96,-0.96c0,0 -0.29,1.44 0.86,2.11s1.06,0 1.15,-0.67c0,0 0.29,1.54 1.06,1.92 0.86,0.19 1.34,-0.48 1.92,-0.48 0,0 -0.96,0.77 -0.29,1.25s1.44,0.29 1.92,0c0.58,-0.48 0.29,-1.73 2.3,-0.58z"
      android:strokeWidth="0.48"
      android:fillColor="#730000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.2,182.21c0.48,-1.73 2.11,-3.07 3.84,-2.78m-1.92,4.22q0.1,-0.58 0.48,-1.15c0.96,-1.54 2.88,-1.54 4.42,-1.82m-2.4,4.32c0.58,-0.38 0.77,-1.06 1.34,-1.34a4.8,4.8 0,0 1,3.07 -0.67"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.01,179.33c-0.86,0.67 -1.73,1.73 -1.82,2.11 0,0.67 0,1.82 0.48,2.4q0.96,0.58 1.06,-0.77c0,0.48 0,1.44 0.77,1.92 1.25,0.67 1.15,0.1 1.25,-0.67 0,0 0.29,1.54 0.96,1.92 0.86,0.19 1.34,-0.48 1.92,-0.48 0,0 -0.96,0.77 -0.29,1.25s1.44,0.29 1.92,0c0.58,-0.48 0.29,-1.73 2.4,-0.58q0.48,0.1 0.96,-1.06l-7.39,-5.28z"
      android:strokeWidth="0.48"
      android:fillColor="#730000"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M421.47,180.67c-1.44,2.5 0.48,3.94 1.06,2.5 -0.1,2.3 1.63,2.21 2.21,1.44 0.67,2.4 1.82,1.34 2.88,1.15 -1.25,2.21 1.34,1.54 1.92,0.96 0.96,-1.25 1.34,-0.19 1.92,-0.29l1.25,-1.25m-7.68,-5.47q-2.4,1.44 -2.5,3.55m4.8,-2.11q-2.59,1.44 -2.5,3.55m5.18,-1.63c-1.63,0.86 -1.73,1.63 -2.4,2.69"
      android:strokeWidth="0.48"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m238.5,310.7 l-0.2,-4.5 1.9,1.6 0.4,-3.4c0.8,-0.2 2.1,1.3 2.1,1.3l-0.2,-3.4 2.8,3.1s-0.2,-2.6 0.8,-4.2c0,0 2.2,1.9 2.2,4 0,1.9 1.8,-2.6 1.8,-2.6l1.6,5 1.3,-1.9 1,4 2.4,-3.5 1.8,4 4.2,-0.3 0.5,2.4 1.9,-2 2.4,1.5s2,-0.6 3.1,-0.6 2.4,1.4 2.4,1.4 0.8,-1.4 1.5,-1.9 1.9,0.8 2.2,1.6l1.6,-2.9s2,2 2.3,2.9c0,0 0.3,-2.4 0.8,-3.2 0.5,-0.7 1.2,0.3 1.8,1.3l1.3,-4.2s1.4,0.3 2.3,1.9c1.1,1.5 1.1,-2.4 1.1,-2.4s2.4,3.2 3,5.3c0,0 -16.2,5.6 -27.6,4a306,306 0,0 1,-24.5 -4.3m110.9,-0.5s0.7,-2.1 1.8,-3.2c0,0 2,2.4 2,3.2l1.7,-1 1.6,1.5 1.8,-1.3 1.8,1.6 3.2,-1.4 1.3,1.4 5,-0.6 1.8,0.8 1.8,-2.3 1.6,1.8 2.7,-2.6s1.3,1 1.5,1.8c0,0 2.2,-1 2.7,-1.8 0,0 2,-0.8 2.3,0.5 0,0 1.3,-1 1.2,-2 -0.1,-1.2 1.5,-0.4 2,1 0,0 0.5,-2.2 2,-2.4 0,0 1.2,1.3 0.9,3.7 0,0 2.3,-1.9 3.4,-2.1 0,0 0.5,2.6 -0.3,5 0,0 2.4,-4 3,-2.7 0.1,0.8 0.7,3.5 0.1,5.3l2.2,-1.3 0.8,2.6 2.3,-0.5s-13.9,2.1 -27,0.9 -25.3,-5.9 -25.3,-5.9m-9.6,9.7c0.6,-0.4 -4.4,10.3 -16.5,10.9 -16.9,0.8 -31.3,-7.7 -31.3,-7.7s1.8,-2.9 2.9,-3.7c0,0 2.3,1.9 3,4.2 0,0 0.4,-1.5 1.2,-2.9 0,0 2.9,1.4 3.4,3.2 0,0 1,-1.8 1.8,-2.3 0.8,-0.6 1.6,2 1.6,3.6 0,0 1.9,-2 3,-2 0,0 2,1 2.6,4.3 0,0 2,-1.8 2.6,-1.5 0,0 2.3,1.5 1.8,3.7 -0.3,1.2 2.3,-1.4 5.5,-2.2l2.9,2.2 3.4,-4.7s2.1,0.8 2.1,2c0,0 1,-3.7 1.8,-4 0.8,-0.5 1.8,1.1 1.8,2.2 0,0 0.3,-2.8 1.9,-3.4 0,0 1.6,1 1.6,2.1 0,0 1.6,-3.2 2.9,-4"
      android:fillColor="#5ac800"/>
  <path
      android:pathData="m238.5,310.7 l-0.2,-4.5 1.9,1.6 0.4,-3.4c0.8,-0.2 2.1,1.3 2.1,1.3l-0.2,-3.4 2.8,3.1s-0.2,-2.6 0.8,-4.2c0,0 2.2,1.9 2.2,4 0,1.9 1.8,-2.6 1.8,-2.6l1.6,5 1.3,-1.9 1,4 2.4,-3.5 1.8,4 4.2,-0.3 0.5,2.4 1.9,-2 2.4,1.5s2,-0.6 3.1,-0.6 2.4,1.4 2.4,1.4 0.8,-1.4 1.5,-1.9c0.8,-0.5 1.9,0.8 2.2,1.6l1.6,-2.9s2,2.1 2.3,2.9c0,0 0.3,-2.4 0.8,-3.2 0.5,-0.7 1.2,0.3 1.8,1.3l1.3,-4.2s1.4,0.3 2.3,1.9c1.1,1.5 1.1,-2.4 1.1,-2.4s2.4,3.2 3,5.3m58.7,-0.8s0.8,-2.1 1.9,-3.2c0,0 2,2.4 2,3.2l1.7,-1 1.6,1.5 1.8,-1.3 1.8,1.6 3.2,-1.4 1.3,1.4 5,-0.6 1.8,0.8 1.8,-2.2 1.6,1.7 2.7,-2.6s1.2,1 1.5,1.8c0,0 2.2,-1 2.7,-1.8 0,0 2,-0.8 2.3,0.5 0,0 1.3,-1 1.2,-2 -0.1,-1.2 1.5,-0.4 2,1 0,0 0.5,-2.2 2,-2.4 0,0 1.2,1.3 0.9,3.7 0,0 2.3,-1.9 3.4,-2 0,0 0.5,2.5 -0.3,4.9 0,0 2.4,-4 3,-2.7 0.1,0.8 0.7,3.5 0.1,5.3l2.2,-1.3 0.8,2.6 2.3,-0.5m-109.8,8s1.8,-3 2.9,-3.7c0,0 2.3,1.8 3.1,4.2 0,0 0.3,-1.6 1,-3 0,0 3,1.4 3.5,3.2 0,0 1,-1.8 1.9,-2.3 0.7,-0.6 1.5,2 1.5,3.6 0,0 1.9,-2 3,-2 0,0 2,1 2.6,4.4 0,0 2,-1.9 2.6,-1.6 0,0 2.4,1.6 1.8,3.7 -0.3,1.2 2.3,-1.4 5.5,-2.1l2.9,2.1 3.4,-4.7s2.1,0.8 2.1,2c0,0 1,-3.6 1.8,-4 0.8,-0.5 1.9,1.1 1.9,2.2 0,0 0.3,-2.8 1.8,-3.4 0,0 1.6,1 1.6,2.1 0,0 1.6,-3.2 3,-4"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#060"/>
  <path
      android:pathData="M319.5,361.9c22,0.2 35,-7.8 42,-12.6 16.8,-11.2 20.3,-11.6 23,-11.4 3.1,0.3 7.7,1.3 8,4 0.2,4.3 -6.6,6.1 -10.5,6.1s-14.5,-2.6 -14.5,-2.6l-2.7,1.9c1.8,0.8 23.2,8 27.3,1.2s8.4,-17.9 8.4,-17.9 -4.7,-8.4 -15.1,-8.4c-10.6,0 -20.8,6.7 -27.6,11.7s-15.5,11.8 -38.3,11.8c-22.9,0 -31.6,-6.8 -38.4,-11.8 -6.9,-5 -17.1,-11.7 -27.6,-11.7a18,18 0,0 0,-15.2 8.4s4.2,11 8.4,18c4.1,6.7 25.5,-0.5 27.3,-1.4l-2.6,-1.8S261,348 257,348s-10.9,-1.8 -10.6,-6c0.2,-2.8 4.8,-3.8 8,-4.1 2.7,-0.2 6.2,0.2 22.9,11.4a72,72 0,0 0,42.2 12.6"
      android:fillColor="#fff"/>
  <path
      android:pathData="M284.1,336s0,-1.9 -0.3,-4.6c-0.4,-3.5 -2.4,-4.4 -4,-3.9 -1.2,0.4 -3.3,3 -3.3,3v0.1l-3,-1.8a18,18 0,0 1,6.3 -3.6c1,0 14.5,5.4 18.8,9.1 1,1 1.8,7 1.2,9a47,47 0,0 1,-15.7 -7.2m-6.5,13.6s-2.9,3 -3,8.3c0,6.3 4.5,6.6 7.5,6.5 3.7,0 6.6,-2.1 6.5,-8.9 0,0 -6,-2.5 -11,-6m77.1,-13.6 l0.4,-4.6c0.4,-3.5 2.4,-4.4 4,-3.9 1,0.4 3.2,3 3.2,3v0.1l3,-1.8c-0.7,-1 -5.2,-3.6 -6.2,-3.6s-14.5,5.4 -18.8,9.1c-1,1 -1.8,7 -1.3,9a46,46 0,0 0,15.7 -7.2zM361.2,349.7s2.9,3 3,8.3c0.2,6.3 -4.5,6.6 -7.5,6.5 -3.6,0 -6.5,-2.1 -6.5,-8.9 0,0 6,-2.5 11,-6"
      android:fillColor="#fff"/>
  <path
      android:pathData="M271.3,345.4S260.9,348 257,348s-10.9,-1.8 -10.6,-6c0.2,-2.8 4.8,-3.8 8,-4.1 2.3,-0.2 5.3,0 17,7.5m96.1,0S378,348 382,348c3.9,0 10.7,-1.8 10.4,-6 -0.2,-2.8 -4.8,-3.8 -7.9,-4.1 -2.3,-0.2 -5.4,0 -17,7.5m-83.4,-9.3 l-3.1,-2.3q-2.2,-1.5 -4.5,-3.2s2.1,-2.7 3.2,-3c1.7,-0.6 3.7,0.3 4.1,3.8 0.3,2.7 0.3,4.7 0.3,4.7m-7.1,27.6c1.8,1 3.9,1 5.1,0.8 3.7,0 6.6,-2.1 6.5,-8.9 0,0 4.5,2.3 11.8,4 0,0 1.2,1.5 1.4,4.4 0.3,2.5 -1.4,5.1 -3.3,5 0,0 -10.3,-2 -16.5,-3 -3.7,-0.7 -5,-2.3 -5,-2.3m77.7,-27.6 l3.2,-2.3 4.4,-3.2s-2.1,-2.7 -3.2,-3c-1.6,-0.6 -3.6,0.3 -4,3.8zM361.8,363.7c-1.8,1 -3.9,1 -5.1,0.8 -3.6,0 -6.5,-2.1 -6.5,-8.9 0,0 -4.5,2.3 -11.7,4 0,0 -1.3,1.5 -1.5,4.4 -0.2,2.5 1.5,5.1 3.4,5 0,0 10.3,-2 16.5,-3 3.6,-0.7 5,-2.3 5,-2.3"
      android:fillColor="#69f"/>
  <path
      android:pathData="M319.5,361.9c22,0.2 35,-7.8 42,-12.6 16.8,-11.2 20.3,-11.6 23,-11.4 3.1,0.3 7.7,1.3 8,4 0.2,4.3 -6.6,6.1 -10.5,6.1s-14.5,-2.6 -14.5,-2.6l-2.7,1.9c1.8,0.8 23.2,8 27.3,1.2s8.4,-17.9 8.4,-17.9 -4.7,-8.4 -15.1,-8.4c-10.6,0 -20.8,6.7 -27.7,11.7s-15.4,11.8 -38.2,11.8 -31.6,-6.8 -38.4,-11.8c-6.9,-5 -17.1,-11.7 -27.6,-11.7s-15.2,8.4 -15.2,8.4 4.2,11 8.4,18c4.1,6.7 25.5,-0.5 27.4,-1.4l-2.7,-1.8S261,348 257,348s-10.9,-1.8 -10.6,-6c0.2,-2.8 4.8,-3.8 8,-4.1 2.7,-0.2 6.2,0.2 22.9,11.4a72,72 0,0 0,42.2 12.6z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M271.3,345.4S260.9,348 257,348s-10.9,-1.8 -10.6,-6c0.2,-2.8 4.8,-3.8 8,-4.1 2.3,-0.2 5.3,0 17,7.5zM367.5,345.4S378,348 382,348c3.9,0 10.7,-1.8 10.4,-6 -0.2,-2.8 -4.8,-3.8 -7.9,-4.1 -2.3,-0.2 -5.4,0 -17,7.5zM284.1,336.1 L281,333.8q-2.2,-1.5 -4.5,-3.2s2.1,-2.7 3.2,-3c1.7,-0.6 3.7,0.3 4.1,3.8 0.3,2.8 0.3,4.7 0.3,4.7z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M284.1,336s0,-1.9 -0.3,-4.6c-0.4,-3.5 -2.4,-4.4 -4,-3.9 -1.2,0.4 -3.3,3 -3.3,3v0.1l-3,-1.8a18,18 0,0 1,6.3 -3.6c1,0 14.5,5.4 18.8,9.1 1,1 1.8,7 1.2,9a47,47 0,0 1,-15.7 -7.2m-6.5,13.6s-2.9,3 -3,8.3c0,6.3 4.5,6.6 7.5,6.5 3.7,0 6.6,-2.1 6.5,-8.9 0,0 -6,-2.5 -11,-6z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M280.1,351.1s-2.3,2.5 -2.7,6.5c-0.2,3.1 1.8,4.9 4.5,4.7 3.7,-0.2 5.1,-5.3 3.4,-8.1l-5.2,-3"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M277,363.7c1.8,1 3.9,1 5.2,0.8 3.6,0 6.5,-2.1 6.4,-8.9 0,0 4.5,2.3 11.8,4 0,0 1.2,1.5 1.4,4.5 0.3,2.4 -1.4,5 -3.3,4.8L282,366c-3.7,-0.7 -5,-2.3 -5,-2.3zM354.7,336.1 L358,333.8q2,-1.5 4.4,-3.2s-2.2,-2.7 -3.3,-3c-1.6,-0.6 -3.6,0.3 -4,3.8z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m354.7,336 l0.4,-4.6c0.4,-3.5 2.4,-4.4 4,-3.9 1,0.4 3.2,3 3.2,3v0.1l3,-1.8c-0.7,-0.9 -5.2,-3.6 -6.2,-3.6s-14.5,5.4 -18.8,9.1c-1,1 -1.8,7 -1.3,9a46,46 0,0 0,15.7 -7.2m6.5,13.6s2.9,3 3,8.3c0.2,6.3 -4.5,6.6 -7.5,6.5 -3.6,0 -6.5,-2.1 -6.5,-8.9 0,0 6,-2.5 11,-6z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M358.7,351.1s2.4,2.5 2.8,6.5c0.3,3.1 -1.9,4.9 -4.5,4.7 -3.7,-0.2 -5.1,-5.3 -3.4,-8.1l5.1,-3"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M361.9,363.7c-2,1 -4,1 -5.2,0.8 -3.6,0 -6.5,-2.1 -6.5,-8.9 0,0 -4.5,2.3 -11.7,4 0,0 -1.3,1.5 -1.5,4.5 -0.2,2.4 1.5,5 3.4,4.8l16.5,-2.9c3.6,-0.7 5,-2.3 5,-2.3z"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M299.6,361.4s3.3,2.4 -0.7,5.3m-9,-6.8 l10,4.3m-8.3,-2.1 l7.2,3.1m-4.6,-5 l5.4,2.5m39.7,-1.3s-3.3,2.4 0.6,5.3m9,-6.8 l-10,4.3m8.4,-2.1 l-7.2,3.1m4.6,-5 l-5.4,2.5"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M261,331.4q0.2,0.3 0.3,0.7v0.8a2,2 0,0 1,-1.2 1.6,3 3,0 0,1 -2.2,0.4l-1,-0.4 -1,-0.6 -0.3,0.4 -0.4,-0.1 0.4,-2.9 0.5,0.1q0,0.6 0.2,1l0.3,1 0.7,0.6q0.3,0.3 0.9,0.4h0.7a1,1 0,0 0,0.9 -0.6l0.2,-0.6 -0.1,-1 -0.7,-0.7 -0.8,-0.5 -0.8,-0.6q-0.7,-0.4 -1,-1t-0.1,-1.4q0,-0.5 0.4,-0.8a3,3 0,0 1,1.5 -1h1l1,0.4 0.8,0.6 0.3,-0.4 0.5,0.1 -0.5,2.8 -0.5,-0.1v-1l-0.4,-0.8 -0.5,-0.7 -0.8,-0.4 -1,0.2q-0.6,0.3 -0.6,0.8v1l0.7,0.7 0.8,0.5 1.3,1q0.3,0.1 0.5,0.5m12,1.6 l-0.5,-0.1 -0.5,-0.1q-0.3,0 -0.5,0.3l-0.6,1 -1.8,3.1a2,2 0,0 1,-1.9 1.3h-1.1l-1,-0.5 -1,-0.8 -0.7,-1 -0.1,-1 0.2,-0.8 2.4,-4.3 0.1,-0.4v-0.3l-0.3,-0.3 -0.4,-0.3 0.2,-0.3 3.1,1.7 -0.2,0.3 -0.3,-0.1 -0.4,-0.1h-0.4l-0.2,0.3 -2.2,4 -0.3,0.7v0.8q0,0.3 0.2,0.7l0.9,0.8 1,0.3a2,2 0,0 0,1.5 -0.5l0.5,-0.6 1.6,-3 0.5,-1v-0.6q0,-0.2 -0.4,-0.5l-0.4,-0.3 0.2,-0.4 3,1.7 -0.2,0.3m4.3,7.5a2,2 0,0 1,0 1.8l-0.8,0.8 -1,0.3 -1,-0.1 -0.9,-0.4 -3.5,-2 0.2,-0.4 0.8,0.3h0.3l0.3,-0.3 3,-5.3 0.1,-0.3 -0.1,-0.4 -0.3,-0.3 -0.3,-0.2 0.2,-0.4 3.4,2 0.7,0.5 0.6,0.7a2,2 0,0 1,0 1.6l-0.6,0.6 -0.7,0.3h-0.7l-0.8,-0.2v0.1l0.6,0.6zM276.7,338.7q0.3,0 0.5,-0.3l0.5,-0.6q0.3,-0.6 0.2,-1.2 -0.2,-0.4 -0.9,-0.9l-0.4,-0.2 -0.4,-0.3 -1.6,2.8 0.6,0.4 0.8,0.3zM276.1,341.4q0.4,-0.6 0.1,-1.3 0,-0.6 -1,-1.1l-0.5,-0.4 -0.4,-0.1 -1.5,2.5a1,1 0,0 0,0 0.7q0.2,0.3 0.7,0.5 0.8,0.4 1.4,0.3 0.7,-0.3 1.2,-1zM295,345.7h-0.5l-0.6,-0.1q-0.2,0 -0.4,0.4l-0.5,1 -1.5,3.3q-0.2,0.6 -0.7,1l-1,0.4h-1.1a4,4 0,0 1,-2.2 -1l-0.7,-0.8 -0.3,-1 0.2,-0.9 2,-4.5v-0.4l-0.1,-0.3 -0.3,-0.3 -0.4,-0.2 0.2,-0.4 3.3,1.4 -0.2,0.4 -0.4,-0.1h-0.7l-0.2,0.3 -1.9,4.3 -0.2,0.7a2,2 0,0 0,0.4 1.4q0.3,0.4 1,0.7l1,0.2a2,2 0,0 0,1.5 -0.7l0.4,-0.6 1.3,-3.1 0.3,-1v-0.7l-0.4,-0.4 -0.5,-0.3 0.2,-0.4 3.1,1.4 -0.1,0.3m9.2,10.8 l-3.6,-1v-0.4h0.6l0.4,0.1 0.4,-0.2 0.2,-0.3 1.7,-5.8h-0.1l-4.8,6h-0.3l-0.6,-7.9 -1.5,4.8 -0.2,1.1 0.1,0.6 0.5,0.4 0.5,0.3 -0.2,0.4 -3.2,-1v-0.4h1.1q0.3,0 0.5,-0.4l0.3,-1 1.2,-4.2 0.1,-0.6 -0.1,-0.4 -0.4,-0.4 -0.5,-0.2 0.1,-0.4 2.8,0.8 0.6,6.7 3.5,-4.5 0.3,-0.5 0.2,-0.4 2.7,0.8 -0.1,0.4h-0.5l-0.4,-0.1 -0.3,0.1 -0.1,0.4 -1.7,5.8v0.3l0.1,0.3 0.8,0.4 -0.1,0.4m8.7,-2.6a2,2 0,0 1,0.7 1.7l-0.4,1q-0.3,0.4 -0.8,0.6t-1,0.3h-1l-4,-0.5v-0.4h0.9l0.3,-0.2 0.1,-0.4 0.8,-6v-0.3l-0.3,-0.3 -0.4,-0.2h-0.3v-0.5l3.9,0.5q0.4,0 0.9,0.2l0.7,0.4a2,2 0,0 1,0.6 1.5l-0.2,0.8 -0.6,0.5 -0.6,0.3 -0.8,0.2 0.8,0.3zM311.6,352.4 L312,351.9 312.2,351.2a2,2 0,0 0,-0.3 -1.1q-0.3,-0.5 -1.1,-0.5l-1,-0.1 -0.4,3.2h0.8q0.5,0 0.8,0zM312.1,355.2q0,-0.8 -0.3,-1.3 -0.5,-0.4 -1.4,-0.6l-0.7,-0.1h-0.4l-0.3,2.9 0.2,0.6 0.8,0.3q0.8,0.1 1.5,-0.4 0.4,-0.4 0.6,-1.4zM323.5,357.8h-2.2l-1.4,-2 -1.3,-1.8h-1v2.7l0.1,0.4 0.3,0.2h0.4l0.5,0.1v0.4h-3.6v-0.4h0.4l0.4,-0.1 0.2,-0.2 0.1,-0.4v-6.4l-0.3,-0.3h-0.4l-0.4,-0.1v-0.4h4.9l0.9,0.4 0.6,0.6a2,2 0,0 1,0 1.8l-0.4,0.7 -0.7,0.4 -0.8,0.3 1,1.4q0.5,0.5 1,1.4l1,0.8 0.8,0.1zM320.4,351.6q0,-0.8 -0.4,-1.2 -0.5,-0.5 -1.3,-0.5h-1v3.6h0.8q0.8,0 1.3,-0.5t0.6,-1.4zM333.4,357.2 L329.8,357.5v-0.4l0.7,-0.2q0.3,-0.1 0.3,-0.2v-0.3l-0.9,-1.8 -3,0.2 -0.2,0.7 -0.1,0.6 -0.1,0.4v0.3q0,0.3 0.4,0.3l0.9,0.1v0.4l-3.2,0.3v-0.4l0.4,-0.1 0.7,-0.6 0.2,-0.5 1.1,-3.6 1.1,-3.6h0.5l3.4,7 0.2,0.3 0.7,0.4h0.4v0.4zM329.7,354.1 L328.1,350.9 327.1,354.3zM345.4,347.4 L345,347.6 344.2,346.7q-0.6,-0.4 -1,-0.3h-0.3l-0.6,0.2 -1.4,0.4 0.9,3.2 1,-0.2 0.6,-0.3 0.3,-0.4 0.1,-0.4v-0.6l0.4,-0.2 0.9,3.2 -0.4,0.1 -0.3,-0.5 -0.4,-0.4 -0.5,-0.2 -0.6,0.1 -1,0.3 0.8,2.8s0,0.2 0.2,0.3l0.3,0.2h0.4l0.4,-0.1 0.1,0.4 -3.5,1 -0.1,-0.4 0.4,-0.2 0.4,-0.2 0.2,-0.3v-0.3l-1.6,-5.9 -0.2,-0.3 -0.3,-0.2h-0.9l-0.1,-0.3 6.4,-1.8 0.6,2m9.4,-0.7 l1,2.4 -6,2.8 -0.2,-0.3 0.4,-0.3 0.3,-0.2 0.1,-0.3v-0.4l-2.7,-5.4 -0.2,-0.3h-1.2l-0.2,-0.3 3.3,-1.6 0.2,0.4 -0.4,0.2 -0.3,0.3 -0.2,0.3 0.1,0.4 2.5,5q0,0.4 0.3,0.6l0.3,0.2h0.4l0.7,-0.3 0.4,-0.2 0.4,-0.3 0.3,-0.2 0.2,-0.3 0.2,-2 0.3,-0.2m4.5,-8.3 l1.5,0.8 1.2,1.4 0.6,1.7v1.6l-0.7,1.5 -1.2,1a4,4 0,0 1,-3.2 0.4l-1.4,-0.7 -1.2,-1.4 -0.6,-1.7v-1.7a4,4 0,0 1,2 -2.4l1.5,-0.6zM361.6,344 L361.4,342.7 360.7,341.3 359.7,340 358.7,339.2 357.7,339q-0.7,0 -1.2,0.3 -0.6,0.4 -0.9,1l-0.3,1q0,0.7 0.3,1.3a7,7 0,0 0,1.5 2.6q0.5,0.6 1,0.8l1.2,0.3q0.5,0 1.1,-0.3a2,2 0,0 0,1.2 -2zM372.1,338.3 L370.2,339.6 367.9,338.7 365.9,338.1 365.1,338.6 366.7,340.7 366.9,341h0.4l0.3,-0.1 0.4,-0.3 0.3,0.4 -3,2 -0.2,-0.3 0.6,-0.5v-0.7l-3.6,-5 -0.2,-0.2h-0.4l-0.7,0.3 -0.3,-0.4 3.2,-2.2 0.9,-0.5 0.9,-0.2 0.8,0.1a2,2 0,0 1,1.1 1.4v0.8l-0.2,0.7 -0.5,0.8 1.6,0.5 1.6,0.5 1.2,0.1h0.4l0.4,-0.3zM365.9,335q-0.4,-0.6 -1,-0.7 -0.7,-0.2 -1.3,0.3l-0.8,0.6 2,3 0.7,-0.5q0.6,-0.4 0.8,-1.2 0.1,-0.7 -0.4,-1.5zM379.1,333 L379.8,335.4 373.2,337.8 373,337.4 373.8,337 374,336.7v-0.4l-2.2,-5.6 -0.1,-0.3 -0.4,-0.1h-0.9l-0.1,-0.3 6.1,-2.3 0.7,1.9 -0.4,0.1 -0.8,-0.7 -0.9,-0.3 -0.4,0.2 -0.6,0.2 -1.2,0.4 1.1,3.1 1,-0.3q0.3,0 0.5,-0.3l0.3,-0.4v-1.1l0.4,-0.1 1.1,3 -0.4,0.2 -0.3,-0.5 -0.4,-0.3 -0.5,-0.2q-0.3,0 -0.6,0.2l-0.9,0.3 0.9,2.4 0.2,0.5 0.3,0.3h0.5l0.7,-0.3 0.5,-0.2 1,-0.4 0.2,-0.3 0.2,-1v-1l0.5,-0.1m7.1,-5.8q0.6,0.4 1,1.3 0.5,0.7 0.6,1.7v1.8l-0.8,1.5a4,4 0,0 1,-2.7 1.6,4 4,0 0,1 -3,-0.9l-1,-1.2 -0.6,-1.7v-1.9l0.8,-1.5a4,4 0,0 1,2.7 -1.5h1.6zM386.1,333.2q0.3,-0.6 0.3,-1.3v-1.5l-0.4,-1.5 -0.7,-1.2 -0.9,-0.7 -1.1,-0.1q-0.8,0 -1.2,0.5 -0.5,0.4 -0.7,1l-0.3,1.2a7,7 0,0 0,0.4 3l0.7,1.1q0.3,0.6 0.9,0.7 0.4,0.3 1.2,0.2l1,-0.5 0.8,-1z"
      android:strokeWidth=".7"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M366,205.1v53c0,10.6 -1.4,52.6 -46.5,69 -45.3,-16.4 -46.7,-58.4 -46.7,-69v-53H366"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
</vector>
