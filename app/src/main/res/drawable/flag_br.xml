<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:strokeWidth="1"
      android:fillColor="#229e45"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m321.4,436 l301.5,-195.7L319.6,44 17.1,240.7z"
      android:strokeWidth="1"
      android:fillColor="#f8e509"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M452.8,240c0,70.3 -57.1,127.3 -127.6,127.3A127.4,127.4 0,1 1,452.8 240"
      android:strokeWidth="1"
      android:fillColor="#2b49a3"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m283.3,316.3 l-4,-2.3 -4,2 0.9,-4.5 -3.2,-3.4 4.5,-0.5 2.2,-4 1.9,4.2 4.4,0.8 -3.3,3m86,26.3 l-3.9,-2.3 -4,2 0.8,-4.5 -3.1,-3.3 4.5,-0.5 2.1,-4.1 2,4.2 4.4,0.8 -3.4,3.1m-36.2,-30 l-3.4,-2 -3.5,1.8 0.8,-3.9 -2.8,-2.9 4,-0.4 1.8,-3.6 1.6,3.7 3.9,0.7 -3,2.7m87,-8.5 l-3.4,-2 -3.5,1.8 0.8,-3.9 -2.7,-2.8 3.9,-0.4 1.8,-3.5 1.6,3.6 3.8,0.7 -2.9,2.6m-87.3,-22 l-4,-2.2 -4,2 0.8,-4.6 -3.1,-3.3 4.5,-0.5 2.1,-4.1 2,4.2 4.4,0.8 -3.4,3.2m-104.6,-35 l-4,-2.2 -4,2 1,-4.6 -3.3,-3.3 4.6,-0.5 2,-4.1 2,4.2 4.4,0.8 -3.3,3.1m13.3,57.2 l-4,-2.3 -4,2 0.9,-4.5 -3.2,-3.3 4.5,-0.6 2.1,-4 2,4.2 4.4,0.8 -3.3,3.1m132,-67.3 l-3.6,-2 -3.6,1.8 0.8,-4 -2.8,-3 4,-0.5 1.9,-3.6 1.7,3.8 4,0.7 -3,2.7m-6.7,38.3 l-2.7,-1.6 -2.9,1.4 0.6,-3.2 -2.2,-2.3 3.2,-0.4 1.5,-2.8 1.3,3 3,0.5 -2.2,2.2m-142.2,50.4 l-2.7,-1.5 -2.7,1.3 0.6,-3 -2.1,-2.2 3,-0.4 1.4,-2.7 1.3,2.8 3,0.6 -2.3,2M419,299.8l-2.2,-1.1 -2.2,1 0.5,-2.3 -1.7,-1.6 2.4,-0.3 1.2,-2 1,2 2.5,0.5 -1.9,1.5"
      android:strokeWidth="1"
      android:fillColor="#ffffef"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m219.3,287.6 l-2.7,-1.5 -2.7,1.3 0.6,-3 -2.1,-2.2 3,-0.4 1.4,-2.7 1.3,2.8 3,0.6 -2.3,2"
      android:strokeWidth="1"
      android:fillColor="#ffffef"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m219.3,287.6 l-2.7,-1.5 -2.7,1.3 0.6,-3 -2.1,-2.2 3,-0.4 1.4,-2.7 1.3,2.8 3,0.6 -2.3,2m42.3,3 l-2.6,-1.4 -2.7,1.3 0.6,-3 -2.1,-2.2 3,-0.4 1.4,-2.7 1.3,2.8 3,0.5 -2.3,2.1m-4.8,17 l-2.6,-1.5 -2.7,1.4 0.6,-3 -2.1,-2.3 3,-0.4 1.4,-2.7 1.3,2.8 3,0.6 -2.3,2m87.4,-22.2 l-2.6,-1.6 -2.8,1.4 0.6,-3 -2,-2.3 3,-0.3 1.4,-2.7 1.2,2.8 3,0.5 -2.2,2.1m-25.1,3 l-2.7,-1.5 -2.7,1.4 0.6,-3 -2,-2.3 3,-0.3 1.4,-2.8 1.2,2.9 3,0.5 -2.2,2.1m-68.8,-5.8 l-1.7,-1 -1.7,0.8 0.4,-1.9 -1.3,-1.4 1.9,-0.2 0.8,-1.7 0.8,1.8 1.9,0.3 -1.4,1.3m167.8,45.4 l-2.6,-1.5 -2.7,1.4 0.6,-3 -2.1,-2.3 3,-0.4 1.4,-2.7 1.3,2.8 3,0.6 -2.3,2m-20.8,6 l-2.2,-1.4 -2.3,1.2 0.5,-2.6 -1.7,-1.8 2.5,-0.3 1.2,-2.3 1,2.4 2.5,0.4 -1.9,1.8m10.4,2.3 l-2,-1.2 -2.1,1 0.4,-2.3 -1.6,-1.7 2.3,-0.3 1.1,-2 1,2 2.3,0.5 -1.7,1.6m29.1,-22.8 l-2,-1 -2,1 0.5,-2.3 -1.6,-1.7 2.3,-0.3 1,-2 1,2.1 2.1,0.4 -1.6,1.6m-38.8,41.8 l-2.5,-1.4 -2.7,1.2 0.6,-2.8 -2,-2 3,-0.3 1.3,-2.5 1.2,2.6 3,0.5 -2.3,1.9m0.6,14.2 l-2.4,-1.4 -2.4,1.3 0.6,-2.8 -1.9,-2 2.7,-0.4 1.2,-2.5 1.1,2.6 2.7,0.5 -2,2m-19,-23.1 l-1.9,-1.2 -2,1 0.4,-2.2 -1.5,-1.7 2.2,-0.2 1,-2 1,2 2.2,0.4 -1.6,1.6m-17.8,2.3 l-2,-1.2 -2,1 0.5,-2.2 -1.6,-1.7 2.3,-0.2 1,-2 1,2 2.1,0.4 -1.6,1.6m-30.4,-24.6 l-2,-1.1 -2,1 0.5,-2.3 -1.6,-1.6 2.2,-0.3 1,-2 1,2 2.2,0.5 -1.6,1.5m3.7,57 l-1.6,-0.9 -1.8,0.9 0.4,-2 -1.3,-1.4 1.9,-0.2 0.9,-1.7 0.8,1.8 1.9,0.3 -1.4,1.3m-46.2,-86.6 l-4,-2.3 -4,2 0.9,-4.5 -3.2,-3.3 4.5,-0.6 2.2,-4 1.9,4.2 4.4,0.8 -3.3,3.1"
      android:strokeWidth="1"
      android:fillColor="#ffffef"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M444.4,285.8a125,125 0,0 0,5.8 -19.8c-67.8,-59.5 -143.3,-90 -238.7,-83.7a125,125 0,0 0,-8.5 20.9c113,-10.8 196,39.2 241.4,82.6"
      android:strokeWidth="1"
      android:fillColor="#fff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m414,252.4 l2.3,1.3a3,3 0,0 0,-0.3 2.2,3 3,0 0,0 1.4,1.7q1,0.8 2,0.7 0.9,0 1.3,-0.7l0.2,-0.9 -0.5,-1q-0.3,-0.5 -1.5,-1.8a8,8 0,0 1,-1.8 -3,4 4,0 0,1 2,-4.4 4,4 0,0 1,2.3 -0.2,7 7,0 0,1 2.6,1.2q2.1,1.5 2.6,3.2a4,4 0,0 1,-0.6 3.3l-2.4,-1.5q0.5,-1 0.2,-1.7 -0.2,-0.8 -1.2,-1.4a3,3 0,0 0,-1.8 -0.7,1 1,0 0,0 -0.9,0.5q-0.3,0.4 -0.1,1 0.2,0.8 1.6,2.2t2,2.5a4,4 0,0 1,-0.3 4.2,4 4,0 0,1 -1.9,1.5 4,4 0,0 1,-2.4 0.3q-1.3,-0.3 -2.8,-1.3 -2.2,-1.5 -2.7,-3.3a5,5 0,0 1,0.6 -4zM402.4,244.8 L404.9,246.1a3,3 0,0 0,-0.2 2.2,3 3,0 0,0 1.4,1.6q1.1,0.8 2,0.6 0.9,0 1.3,-0.8l0.2,-0.8q0,-0.5 -0.5,-1l-1.6,-1.8q-1.7,-1.6 -2,-2.8a4,4 0,0 1,0.4 -3.1,4 4,0 0,1 1.6,-1.4 4,4 0,0 1,2.2 -0.3,7 7,0 0,1 2.6,1q2.3,1.5 2.7,3.1a4,4 0,0 1,-0.4 3.4l-2.5,-1.4q0.5,-1 0.2,-1.7 -0.4,-1 -1.3,-1.4a3,3 0,0 0,-1.9 -0.6,1 1,0 0,0 -0.8,0.5q-0.3,0.4 -0.1,1 0.3,0.8 1.7,2.2 1.5,1.5 2,2.4a4,4 0,0 1,0 4.2,4 4,0 0,1 -1.8,1.6 4,4 0,0 1,-2.4 0.3,8 8,0 0,1 -2.9,-1.1 6,6 0,0 1,-2.8 -3.2,5 5,0 0,1 0.4,-4m-14.2,-3.8 l7.3,-12 8.8,5.5 -1.2,2 -6.4,-4 -1.6,2.7 6,3.7 -1.3,2 -6,-3.7 -2,3.3 6.7,4 -1.2,2zM367.5,224 L368.6,222 374,224.7 371.5,229.7q-1.2,0.3 -3,0.2a9,9 0,0 1,-3.3 -1,8 8,0 0,1 -3,-2.6 6,6 0,0 1,-1 -3.5,9 9,0 0,1 1,-3.7 8,8 0,0 1,2.6 -3,6 6,0 0,1 3.6,-1.1q1.4,0 3.2,1 2.4,1.1 3.1,2.8a5,5 0,0 1,0.3 3.5l-2.7,-0.8a3,3 0,0 0,-0.2 -2q-0.4,-0.9 -1.6,-1.4a4,4 0,0 0,-3.1 -0.3q-1.5,0.5 -2.6,2.6t-0.7,3.8a4,4 0,0 0,2 2.4q0.8,0.5 1.7,0.5h1.8l0.8,-1.6zM277.3,201.7 L279.3,187.7 283.5,188.4 284.6,198.2 288.5,189.2 292.7,189.8 290.7,203.6 288,203.2 289.7,192.3 285.3,202.8 282.6,202.4 281.5,191.1 279.9,202.1zM263.2,200 L264.5,186 274.8,187 274.6,189.4 267.1,188.7 266.8,191.7 273.8,192.4 273.5,194.8 266.5,194.1 266.2,197.9 274,198.6 273.8,201z"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="M216.5,191.3q0,-2.2 0.7,-3.6a7,7 0,0 1,1.4 -1.9,5 5,0 0,1 1.8,-1.2q1.5,-0.5 3,-0.5 3.1,0.1 5,2a7,7 0,0 1,1.6 5.5q0,3.3 -2,5.3a7,7 0,0 1,-5 1.7,7 7,0 0,1 -4.8,-2 7,7 0,0 1,-1.7 -5.3"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="M219.4,191.3q0,2.3 1,3.6t2.8,1.3a4,4 0,0 0,2.8 -1.1q1,-1.2 1.1,-3.7 0.1,-2.4 -1,-3.6a4,4 0,0 0,-2.7 -1.3,4 4,0 0,0 -2.8,1.2q-1.1,1.2 -1.2,3.6"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#f7ffff"/>
  <path
      android:pathData="m233,198.5 l0.2,-14h6q2.2,0 3.2,0.5 1,0.3 1.6,1.3c0.6,1 0.6,1.4 0.6,2.3a4,4 0,0 1,-1 2.6,5 5,0 0,1 -2.7,1.2l1.5,1.2q0.6,0.6 1.5,2.3l1.7,2.8h-3.4l-2,-3.2 -1.4,-2 -0.9,-0.6 -1.4,-0.2h-0.6v5.8z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="M236,190.5h2q2.1,0 2.6,-0.2 0.5,-0.1 0.8,-0.5 0.4,-0.6 0.3,-1 0,-0.9 -0.4,-1.2 -0.3,-0.4 -1,-0.6h-2l-2.3,-0.1z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="m249,185.2 l5.2,0.3q1.7,0 2.6,0.3a5,5 0,0 1,2 1.4,6 6,0 0,1 1.2,2.4q0.4,1.4 0.3,3.3a9,9 0,0 1,-0.5 3q-0.6,1.5 -1.7,2.4a5,5 0,0 1,-2 1q-1,0.3 -2.5,0.2l-5.3,-0.3z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="m251.7,187.7 l-0.5,9.3h3.8q0.8,0 1.2,-0.5 0.5,-0.4 0.8,-1.3t0.4,-2.6l-0.1,-2.5a3,3 0,0 0,-0.8 -1.4l-1.2,-0.7 -2.3,-0.3z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="m317.6,210.2 l3.3,-13.6 4.4,1 3.2,1q1.1,0.6 1.6,1.9t0.2,2.8q-0.3,1.2 -1,2a4,4 0,0 1,-3 1.4q-1,0 -3,-0.5l-1.7,-0.5 -1.2,5.2z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="m323,199.6 l-0.8,3.8 1.5,0.4q1.6,0.4 2.2,0.3a2,2 0,0 0,1.6 -1.5q0,-0.7 -0.2,-1.3a2,2 0,0 0,-1 -0.9l-1.9,-0.5 -1.3,-0.3z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="m330.6,214.1 l4.7,-13.2 5.5,2q2.2,0.8 3,1.4 0.8,0.7 1,1.8c0.2,1.1 0.2,1.5 0,2.3q-0.6,1.5 -1.8,2.2 -1.2,0.6 -3,0.3 0.6,0.7 1,1.6l0.8,2.7 0.6,3.1 -3.1,-1.1 -1,-3.6 -0.7,-2.4 -0.6,-0.8q-0.3,-0.4 -1.3,-0.7l-0.5,-0.2 -2,5.6z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="m336,207.4 l1.9,0.7q2,0.7 2.5,0.7t0.9,-0.3q0.5,-0.3 0.6,-0.9 0.3,-0.6 0,-1.2a2,2 0,0 0,-0.8 -0.9l-2,-0.7 -2,-0.7 -1.2,3.3z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="M347,213.6a9,9 0,0 1,1.7 -3.2,7 7,0 0,1 1.8,-1.5l2,-0.7q1.5,-0.1 3.1,0.4a7,7 0,0 1,4.2 3.3q1.2,2.4 0.2,5.7a7,7 0,0 1,-3.4 4.5q-2.3,1.3 -5.2,0.4a7,7 0,0 1,-4.2 -3.3,7 7,0 0,1 -0.2,-5.6"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="M349.8,214.4q-0.7,2.3 0,3.8c0.7,1.5 1.2,1.6 2.3,2q1.5,0.5 3,-0.4 1.4,-0.8 2.1,-3.2 0.8,-2.2 0,-3.7a4,4 0,0 0,-2.2 -2,4 4,0 0,0 -3,0.3q-1.5,0.8 -2.2,3.2"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="m374.3,233.1 l6.4,-12.4 5.3,2.7a10,10 0,0 1,2.7 1.9q0.8,0.7 0.8,1.9c0,1.2 0,1.5 -0.4,2.2a4,4 0,0 1,-2 2q-1.5,0.4 -3.1,-0.2 0.6,1 0.8,1.7 0.3,0.9 0.4,2.8l0.2,3.2 -3,-1.5 -0.4,-3.7 -0.3,-2.5 -0.5,-1 -1.2,-0.7 -0.5,-0.3 -2.7,5.2z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="m380.5,227.2 l1.9,1q1.8,1 2.3,1t1,-0.2q0.4,-0.2 0.7,-0.8t0.2,-1.2l-0.7,-1 -1.8,-1 -2,-1z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="M426.1,258.7a9,9 0,0 1,2.5 -2.6,7 7,0 0,1 2.2,-0.9 6,6 0,0 1,2.2 0q1.5,0.3 2.8,1.2a7,7 0,0 1,3 4.4q0.4,2.6 -1.4,5.5a7,7 0,0 1,-4.5 3.3,7 7,0 0,1 -5.2,-1.1 7,7 0,0 1,-3 -4.4q-0.4,-2.7 1.4,-5.4"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
  <path
      android:pathData="M428.6,260.3q-1.4,2 -1.1,3.6a4,4 0,0 0,1.6 2.5q1.5,1 3,0.6t2.9,-2.4q1.4,-2.1 1.1,-3.6t-1.6,-2.6c-1.4,-1.1 -2,-0.8 -3,-0.5q-1.5,0.3 -3,2.4z"
      android:strokeAlpha="0.5"
      android:strokeWidth="1"
      android:fillColor="#fff"/>
  <path
      android:pathData="m301.8,204.5 l2.3,-9.8 7.2,1.7 -0.3,1.6 -5.3,-1.2 -0.5,2.2 4.9,1.1 -0.4,1.7 -4.9,-1.2 -0.6,2.7 5.5,1.3 -0.4,1.6z"
      android:strokeWidth="1"
      android:fillColor="#309e3a"/>
</vector>
