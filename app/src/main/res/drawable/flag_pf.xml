<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M0,0h640v480H0z"/>
    <path
        android:pathData="M0,0h640v480H0z"
        android:fillColor="#fff"/>
    <path
        android:pathData="M80,0h480v480H80z"
        android:fillColor="#fff"/>
    <path
        android:pathData="M277.3,340.8s10.8,-8.8 21.4,-8.8 13.4,7.3 20.8,7.9c7.3,0.6 13.4,-7.3 22.5,-7.6s20.5,6.4 20.5,6.4l-39.8,12 -45.4,-10zM254.4,327.8 L389.8,328.5s-11.7,-12.7 -25.5,-13c-13.8,-0.2 -10,6 -20.5,6.8 -10.6,0.9 -13.2,-6.4 -22.9,-6.2s-15.2,6.2 -22.5,6.5 -16.7,-7.3 -22.3,-7 -25.5,8.7 -25.5,8.7l3.8,3.6zM237.1,311.8 L404.1,312.3c2.7,-3.8 -8.2,-12.9 -18.1,-13.7 -8.2,0.3 -14,8.5 -20.8,8.8s-14.4,-8.5 -22,-8.2 -15.5,8.2 -23.1,8.2 -13.2,-8.5 -22.9,-8.5 -14,9.3 -21.4,8.8 -13.8,-9.4 -20.8,-9.4 -18.7,10.5 -21,10c-2.4,-0.7 2.9,4.3 3.1,3.4z"
        android:strokeWidth="2"
        android:fillColor="#083d9c"
        android:fillType="evenOdd"
        android:strokeColor="#083d9c"/>
    <path
        android:pathData="m301.3,218.9 l38.4,10.2v-54.8c-17.6,1 -32.2,-33.4 -1.2,-35.7 -30.5,-4.4 -34,3.5 -37.5,12z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.5"
        android:fillColor="#ff0000"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="m277,258.7 l86.7,0.3"
        android:strokeWidth="5"
        android:fillColor="#083d9c"
        android:fillType="evenOdd"
        android:strokeColor="#083d9c"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m281.1,238 l10.3,13.7m-10.3,0 l11.1,-13.5M287,237l-0.3,8.5m10.8,-7.6 l10.3,13.8m-10.3,0 l11.1,-13.5m-5.2,-1.2 l-0.3,8.5m11.1,-7.6 l10.3,13.8m-10.3,0 l11.1,-13.5m-5.2,-1.2 l-0.3,8.5m11.7,-7.6 l10.2,13.8m-10.2,0 l11.1,-13.5m-5.3,-1.2 l-0.2,8.5m11,-7.6 l10.3,13.8m-10.2,0 l11.1,-13.5M354,237l-0.3,8.5"
        android:strokeWidth="4"
        android:fillColor="#00000000"
        android:strokeColor="#000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m218.7,259.6 l36.9,0.3v-23.1l-42.2,-2.1zM216.9,227.6 L256.2,231.5 255.9,215.1 217.5,199.8 216.9,227.7zM224.9,194.9 L255,209.5 259.3,205s-2.8,-1.9 -2.6,-3.7c0,-1.7 2.8,-2 2.8,-4 0,-1.7 -3,-2 -3.1,-3.7 -0.2,-2 2.4,-4 2.4,-4l-27.2,-23.7 -6.8,29zM422.9,259.9h-39l-0.3,-22.6 42.8,-3.2zM384.2,232l46.3,-5.6 -10,-26.7 -36.6,15.6zM417.9,192.4L384.5,210c-0.5,-2 -0.9,-3.8 -3.2,-5.3 0,0 2,-1.2 2,-3.2s-2.6,-2.4 -2.6,-3.5 2.4,-2.2 2.6,-4.9c-0.3,-1.8 -2.6,-4.4 -2.2,-4.9l26,-19.8zM345.5,231.5 L362.2,230.8 362.5,224.1zM294.5,231 L277,230.5v-7l17.6,7.5zM294.5,229 L277,220v-11.8s-2,0.3 -1.8,-2c0.1,-4.9 12.9,8.9 19.4,13.4zM345.5,227.9v-7.7s15.8,-14.2 19.1,-16.9c0,3 -1.8,5.2 -1.8,5.2v11.2zM243,163.8l17.8,19.7c0.4,-1.8 4.5,-2.1 8.6,-1.8s7.3,-0.3 7.3,2.6 -2,2.5 -2,4.6 3,1.9 3,4.5 -2.2,2.1 -2.2,4.1c0,1.7 2.4,1.8 2.4,1.8l16.6,16.1v-17.2l-34.2,-53.7zM270.4,143.4 L293.7,190.9s0.2,-43.8 4.1,-46.1l-6.5,-12zM371.4,145.3 L345.4,191.5L345.4,172s2.2,-3.2 -1.2,-3 -7.5,-0.2 -7.5,-0.2l10.4,-36.6zM398,165c-0.3,0.5 -17.5,18 -17.5,18 -0.8,-2 -6,-1 -11,-1s-5.6,1.6 -5.3,2.9c0.5,3.3 2.2,0.8 2.2,4 0,3.1 -2.4,2 -2.7,4.2 0.3,2.7 3.8,2 1.7,4l-19.9,19.2v-18.2l37.1,-57.6z"
        android:fillColor="#ef7d08"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M309.8,268.4c-8.3,13.8 -30.6,9.7 -35.9,0 -1.5,-0.4 -0.6,-59.5 -0.6,-59.5s-2.5,-1.1 -2.6,-3 3.4,-2 3.4,-4.3 -3.6,-1.4 -3.7,-3.8c0,-2.2 3.9,-2 3.7,-4 -0.2,-2.4 -4.3,-2 -4.5,-4.2 0,-1.7 3,-3.2 3.8,-4h-2.8l-6.4,0.1c-4.6,0 0,1 0,3.6 0,1.7 -2.3,2.9 -2.5,4.3 -0.1,1.6 3.2,2.6 3.3,4.5 0,1.6 -3.3,1.7 -3.2,3.3 0.2,2.5 3,3.1 2.9,4.7 0,1.5 -3.6,2.1 -3.7,3.3 0.2,2.4 0.6,60.8 0.6,60.8 5.7,29.8 38.8,37.3 48.2,-1.8zM331.7,268.4c8.3,13.8 30.6,9.7 35.8,0 1.6,-0.4 0.7,-59.5 0.7,-59.5s2.5,-1.1 2.6,-3 -3.2,-2 -3.2,-4.3 3.4,-1.4 3.4,-3.8c0,-2.2 -3.5,-2 -3.3,-4.2s3,-2 3.1,-4.2c0.1,-1.9 -1.7,-3 -2.6,-3.8h2.7l6.4,0.1c4.5,0 0,1 0,3.6 0,1.7 2.3,2.9 2.5,4.3 0,1.6 -3.2,2.6 -3.3,4.5 0,1.6 3.3,1.7 3.2,3.3 -0.2,2.5 -3,3.1 -3,4.7 0.1,1.5 3.7,2.1 3.7,3.3l-0.5,60.8c-5.7,29.8 -38.9,37.3 -48.2,-1.8z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.5"
        android:fillColor="#ff0000"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M301.7,295.6L339,295.6c0.3,-0.3 -8.4,-13 -18.6,-12 -11.5,0.3 -19.3,12 -18.7,12zM420.6,294.6h-51s6.6,-3.8 8.4,-7.4c3.3,1.8 2.4,3.6 9,3.9s12.9,-7.5 19.2,-7.2 14.4,11 14.4,10.8zM219.6,294.6h51s-6.6,-3.8 -8.4,-7.4c-3.3,1.8 -2.4,3.6 -9,3.9s-13,-7.5 -19.2,-7.2c-6.3,0.3 -14.4,11 -14.4,10.8zM223.4,278.6 L259.7,278.9s-2.3,-5 -2.6,-11.1c-9.4,-3.2 -17,7 -23.8,7.3 -6.7,0.3 -13.7,-7.3 -13.7,-7.3zM417.4,278.6 L381,278.9s2.3,-5 2.6,-11.1c9.4,-3.2 17,7 23.8,7.3 6.7,0.3 13.7,-7.3 13.7,-7.3zM311,279l18.4,-0.5s0.3,-5.6 -9.3,-5.6 -8.8,6.4 -9.1,6.1zM299.8,271.1a17,17 0,0 0,8.2 -7.6l-12.6,0.3s-5.8,3.5 -8.7,7.3zM340.6,271.1a17,17 0,0 1,-8.2 -7.6l12.6,0.3s5.8,3.5 8.7,7.3z"
        android:strokeWidth="2"
        android:fillColor="#083d9c"
        android:fillType="evenOdd"
        android:strokeColor="#083d9c"/>
    <path
        android:pathData="M-40,360h720v120L-40,480zM-40,0h720v120L-40,120z"
        android:fillColor="#de2010"
        android:fillType="evenOdd"/>
  </group>
</vector>
