<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0.1,0h640.1v480H0.1z"
      android:fillColor="#ffd520"/>
  <path
      android:pathData="M0.1,480h640.1V0z"
      android:fillColor="#ff4e12"/>
  <path
      android:pathData="M345.4,150c-4,-1.3 -6.6,0.7 -6.4,5.9 0,5.1 2.8,8 6.8,6.1z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M348.9,140.4c-3.3,-2.6 -6.4,-1.5 -8,3.4s0.1,8.6 4.5,8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M354.4,131c-2.8,-3 -6,-2.4 -8.4,2.2 -2.3,4.6 -1.3,8.5 3.2,8.7zM350.8,176.5c-4.9,1.8 -5.4,8.5 -2.3,12.6 3,4.1 8.7,4.9 11.8,0z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M345.1,162.3c-4.6,-1.5 -8.8,4.7 -9.5,10.3 -0.9,7 -11,9.4 -5.4,20.1 1.2,-6.8 5.7,-10.6 9.3,-10.8s9,-1 11.3,-5.4zM359.8,189.9c-5.4,1.3 -6.2,8.5 -2.3,13.6 3.3,4.4 13.7,3.4 13.4,-1zM375.1,233.3c0.3,-4.7 -7.2,-6.5 -10.8,-5.6s-10.5,-0.1 -12,-4c-1.4,3.1 0.5,6.5 5.7,8.2 4,1.2 3.9,4 2.7,5.4 3,0.5 11.7,0.5 14.4,-4z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M370.9,203.7c-5.3,-2.4 -8.4,1.2 -10.4,4.6 -3,5 -12.1,-1.4 -15.2,5.3 4.2,-1.8 8.4,2 10.4,3.3 5.7,3.7 16.6,2.6 18.2,-6.2z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M374,209.8c-5.3,4 -7.4,8.8 -7.2,12 0.1,3.2 4.6,10.3 9.5,10.7 2.8,-5.8 4.3,-18 -2.3,-22.7zM352,234.7c0,-2 2.8,-2.7 4.8,-2 1.9,0.6 4.9,2.5 3.8,4.6zM323,224c-0.5,-2.3 3.2,-6.3 8.2,-4.1s5.7,6.4 3.6,8.1z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M335.2,228.4c-0.4,-1.3 3.3,-3.9 9.4,-2.4 6.2,1.6 7.7,5.6 7.5,8.7zM322.3,224.1c3.3,-2.4 2,-7 -0.9,-8.5 -5.2,-2.6 -3.3,-9.2 -6.7,-10.5 -3.3,-1.3 -6.5,-3.6 -6.7,-6 -1.6,3.3 -0.6,6.2 1.7,8.3 2.3,2 -1.8,10.4 1.2,12.6zM253.3,222.3c-2.7,-4.2 -9.1,-3.5 -11.8,-0.5s-2.3,7.3 0.2,9.3zM268.8,216c-1,-5.7 -7.9,-6.3 -11.6,-4.9 -3.8,1.4 -6.3,7.1 -3.9,11.2z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M279,215.2c2.5,-4.7 -2.3,-11.8 -7.7,-12.8 -4.5,-1 -9.8,-0.9 -11.6,-5.3 -1,3.8 1.8,6.3 5.2,8.5 3.3,2.2 -0.6,7.8 4.8,11z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M278.6,215.4c-1.2,-3.4 1.1,-8 5.7,-7.6 4.7,0.3 7.3,3.6 5.3,7.9z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M288.9,215.7c-0.7,-3.6 2.2,-7.8 6.8,-6.8s6.6,4.7 4,8.5l-10.8,-1.8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M299,217.3c-0.4,-3.6 2.7,-7.6 7.2,-6.4s6.4,5 3.6,8.7l-10.7,-2.3zM221.4,276.3c-8.7,0 -10.8,2 -12,11 -1.6,11 13.5,12.3 12,-11z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M225.2,264.7c-13.2,-5 -20.4,16 -33.6,12.2 4.7,7.5 16.1,0 20.4,0.8 7.2,1.3 22.8,-1.4 13.2,-13zM216.6,293.4c-6.6,-3 -13.6,7 -12.4,11.5 1.7,5.5 16.7,1 12.4,-11.5zM186,336.7c3.6,1 8,3 7.2,10s-14,21.1 -25.8,22c-11.8,0.7 -16,15 -26.3,11 9.6,-1.8 9.6,-12.6 17,-16 -5.4,-2 -8.2,10.3 -15.2,10.3s-10.3,11.1 -18.8,10.3 -9.3,13.5 -26.4,13.7c-13,0.1 -29,15.3 -34.9,8.7 12.7,-1.8 17.8,-8.8 25.3,-16.5 12,-12.3 25.7,-6.8 30.4,-17.7a32,32 0,0 1,-18.3 5.4c-8,-0.2 -16.6,12.6 -25.5,7 5.1,-0.7 8.5,-2.9 13.9,-8.6 5.5,-5.8 13.7,-2 20.1,-8 10,-9.2 18.7,-1.5 28.3,-13 -2.7,-1.4 -8.5,-0.5 -13.9,2.4 -5.4,2.8 -12.3,-2 -18.5,1.4 0.7,-7.6 15.2,-3.3 24.2,-8.5 10.2,-6 18.6,-4.2 26.6,-3.5 -11.2,0 -15.5,-10.7 -31,-7.6 -6.7,1.4 -12.1,-9.3 -18.8,-3.9 0.2,-4 7.2,-7.2 14.4,-3.3s10.1,-3.4 24.5,5.7c6,3.7 16.1,-2.4 22.5,1.6 -0.8,-2.4 -4.5,-4 -8.8,-3.6 2.7,-5.6 20.2,-4.9 27.8,0.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M197.4,328.3c-5.6,-4.4 -13.5,0.9 -19,-1.2 0,3.6 1.9,9 7.8,11.1 1.8,-1.3 10,-8.4 11.2,-9.9z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M206.3,315.8c-8.9,-4.5 -10.4,6.7 -17.4,4.4 0.3,3.2 2.9,7.2 8.5,8.1z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M211.6,305.6c-13.1,-5.1 -14.8,7.5 -22.5,5 1.8,4.4 12.8,6.8 18.5,5.2zM229.6,250.4c-3.5,-5 -10.8,-1 -12,4.9 -1.1,5.8 1.7,13.9 6.6,12z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M238.5,235c-6,-1.5 -13,-0.8 -12.2,5 -2.4,1.1 -3,8.7 3.3,10.4z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M242.5,231c-6,-7 -12.5,-6.9 -16.2,-4 -6.9,5.5 -13.5,2.4 -13.7,7.8 4.1,-3.2 7.8,0.6 11,-0.5 3.4,-1.2 5.9,5.3 15.2,2.6zM228.2,334.9c0.9,1.7 6.4,2.4 9.1,-0.4 3.6,-3.8 -0.3,-14.2 -6,-15 -5.7,-0.7 -6.2,11.8 -3.1,15.4z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M221.8,335.1c8,3 11.5,-3.7 7.2,-8a80,80 0,0 1,-7.2 8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M191.4,346.2c-1.6,4.7 -9.6,5.4 -18.6,19.8s-17.6,8.4 -19.8,18.3c10.8,-8.7 19.3,-3 25.8,-11.6 9.7,-13.1 17.8,-11.2 21.6,-20 5.4,-12.7 29,-12.4 30.4,-32.3 -8,-1.5 -33.3,19.9 -39.4,25.8zM394.6,151.5c10.3,3.4 10.5,16.7 22.4,21.1s13,15 22.7,12.4c-9,-2.5 -8.4,-12.9 -17.8,-15.5 -11,-3 -15.2,-19.8 -24,-22.4m44,74.3c1.8,4.2 1.5,11.5 -5,13.4 3.5,2.2 8.7,0.2 11.5,-4.6 -4.2,9.4 -1.4,17.9 5.3,19.6 -3.2,-6.6 4,-9.7 1.7,-14 4.2,1.9 8,7.7 7.8,11.3 5.6,-6.2 -4,-14.5 -2.3,-20.3zM375,287.6c-6.3,-5.5 -9,1.5 -12,-1 -3,-2.2 -6.8,-2.5 -8.3,-0.3 5.4,0.2 2.8,4.4 13.3,5.4 -10.5,0.7 -8.6,12.5 -15.6,11.9 7.4,7 11.3,-6.3 17.4,-4.1 -1.8,0.5 2.8,4.7 -0.4,10.4 5.2,-0.1 7.3,-7.3 8,-11zM236,347.8c-2.2,-2 -9,-2.9 -11.5,-1.3 -2.6,1.5 -1.7,2 1.4,2.2s7,5.2 0.4,5.5c-3.1,0.1 -2,7.6 -8.5,8.1 2.6,3.2 10.2,1.1 12.9,-2.4 -0.5,2.9 3.3,5.5 1.8,9 4.7,0.5 2,-9.7 9.5,-9.2 -3,0.4 -1.8,7.4 3.6,5.6 -3.2,1.5 -1.5,5.3 2,4.4 -2.2,0.7 -3,3.7 0.2,5.4 3.1,-4.3 -0.4,-19.5 -11.7,-27.3zM516.6,205.7a17.9,17.9 0,1 0,0 -35.7,17.9 17.9,0 0,0 0,35.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M423.4,227.2c5.5,-5.1 13.7,-7.7 19.4,-3.8 5.6,3.8 24.4,8.4 33.7,2s13.7,-9.8 17.8,-9q4.5,6.8 11.4,7.2c1.4,1.6 6.5,2.8 9.3,2.5 4.1,1 9.1,-0.3 13.1,-4.7 6.2,1 12,-3.7 14.2,-10.7 6.6,-0.7 7,-8 2.8,-13 -3.8,-0.7 -0.9,-13.8 -14.9,-11.2 6,3.6 1.4,10.8 6.3,14.2 -3.3,0 -7.7,1.4 -8.7,6.4 1.3,-3.4 -0.2,-5.8 -1,-6.5 0,-3 -6.5,-10.3 -12.7,-7.6 4.4,1 2,8 5.2,10.8a8,8 0,0 0,-6.2 3.3c-1.7,-3 -7.6,-6 -11.2,-6.3 0,-1 -0.2,-3 -0.7,-4.1 -1.6,-3.1 -3,-6.8 -2.3,-11.5a48,48 0,0 0,-7.2 11.4c-4.9,-3.4 -17,1.5 -22.7,2.8s-24.7,-1.8 -29,-6.4a49,49 0,0 0,-21 -9.8c-11,-3.2 -11,-15.2 -23.1,-23.5 -0.3,15 22.4,62.4 27.5,67.5zM297.2,341.1a17.3,17.3 0,1 0,0 -34.6,17.3 17.3,0 0,0 0,34.6z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M256,327.8c3.5,4.5 9.4,4.2 11.9,3.9 2,5.4 8.6,5.2 11.4,8.2s12.5,2.7 15.3,1c-2.5,-0.2 -5.9,-1.8 -9,-4.5 -4,-3.2 -2.2,-9.8 -5.2,-12a11,11 0,0 0,2.2 -8.6c2.4,-1.4 4.2,-3.7 4.4,-4.9a15,15 0,0 0,9.6 -4.1c2.2,2 7.7,-0.6 10.7,2.8 0.6,-8.5 -7.5,-13 -13,-10.1 -2.1,-1.2 -7.9,-0.4 -9,1.1 -1.7,-0.8 -6.8,1.8 -9,3.5 2.5,-1.4 2.9,-5.7 1.9,-7.2 2.2,-1 4.6,-3.9 4.9,-6 3,0.5 7.7,-1.6 9.7,-1.1 -3.3,-4.4 -8.8,-6 -14.5,-5.5 -5.9,0.3 -8.4,4.4 -9.2,8.8 -3.4,2.1 -4.6,9 -3.3,11.5 -2,0 -3.9,1.9 -4.6,3a27,27 0,0 0,-9.4 -2m1.3,-7c-1.2,-3.4 0.3,-6.4 1.1,-9 2,-6.8 0.8,-8.6 -5.3,-7.7a47,47 0,0 0,4.2 16.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M248.6,282.3c1.6,1.6 7,2.3 7.6,-2.6 0.7,-5.6 -1.6,-7.8 -6.5,-5.6 -0.4,1.3 -0.8,6.5 -1,8.2z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M249.8,273.9c2,0.8 6.5,2.5 9,-2.3 2,-4 -0.7,-7 -5,-6.8 -1,1.2 -3,5.3 -4,9.1z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M253.6,264.4c0.5,1.6 5.8,6.7 9.6,3 3.9,-3.7 3.9,-9.3 -1.9,-11.3 -1.5,0.2 -6.2,5.6 -7.7,8.3z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M261.3,256c1.1,3.3 4.8,8.8 11.5,6.3s3.8,-11 0.7,-12.7a33,33 0,0 0,-12.2 6.5z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M273.5,249.6c-0.5,2.9 0,10.6 9.2,10.5 9.1,-0.2 6.6,-10.9 4.2,-12.4 -3.7,0 -10,0.1 -13.4,2z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M287.3,248c-1,2.3 -3.3,16.7 14.6,12.7 2.3,-0.5 8.3,-13.8 -14.6,-12.6z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M297.1,249.4c-1.8,1.8 2.8,16.3 15,13.9 12,-2.5 1.9,-16.3 -15,-14z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M307.4,251.6c-2,4 1,15.8 15.9,15.8 13.5,0 -0.7,-15.6 -15.9,-15.8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M319.1,255c-1,2.3 -2.1,14.8 15.5,15.9 12.7,0.8 9.6,-17.3 -15.5,-15.9z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M338,260.3c-2.1,3.9 -4.4,13.5 14.9,14.3 12.3,0.5 4.7,-14 -14.8,-14.3z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M354.1,263.3c-2.8,3.8 -0.7,11.4 6.5,12.8 9,1.8 10.3,-6.7 4.1,-10.8 -6.2,-4 -10.6,-2 -10.6,-2z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M363,265c-2.1,3.7 -0.9,12.4 12.8,12.4 2.8,0 13.6,-11 -12.9,-12.3zM257.1,433a20,20 0,1 0,0 -40,20 20,0 0,0 0,40z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M404.1,141.7a35,35 0,0 0,-5.4 8c-6.7,20 11.2,35 21.6,56.7a63,63 0,0 1,-5.6 60.7c-4.4,5.8 -3.1,7.5 -8.8,13.4 -2.2,2.3 -4.6,5.2 -3.8,13.4 3.6,-1.3 8.7,2 9.7,5 2.6,-1.4 6.2,-0.9 7.5,0.7 4.4,-2 8,-1 11.9,3 3.3,-0.4 7,0 10.3,3.7 1.8,-3.6 5.4,-5 8,-4.1 -0.3,-4.7 4.3,-8 8.4,-6.2a7.6,7.6 0,0 1,9.8 -9c4.7,-3.6 14,-3.9 18.6,1.5 -8.3,-2.3 -8,6.5 -15,5.7 1.8,5.1 -2.8,8.1 -7.4,9.8 3,-1.4 6.2,-3.1 7.2,-1.3 2.6,-2.3 7.7,-1.4 9,-0.3 3.4,-1 6.7,-0.2 8.2,3.9 4.7,2.8 7.8,10 4.4,15.4 -1,-5.6 -4.9,-5.4 -6.4,-7.7 -3.6,1.3 -7.2,1.3 -8.3,-1 -2,2 -9,3.9 -12,0.8 -1.2,4.6 -5.2,8.5 -9.9,8.5 1.3,3.6 -2.3,9.7 -5.1,12.8 4.4,2.3 3,7.5 2,10.6 6.8,1 1,7 12.7,10.8 -5.7,1.8 -16.8,0 -18.3,-7 -5.7,-0.2 -9.5,-5.9 -9.3,-11.8 -4.4,-4.1 -5,-10 1,-14.2 -5.1,1.6 -8,-6.7 -15.4,-3.3 -3.7,1.7 -13.5,-1.2 -13.4,-4.6 -1.5,2.5 -11,1.5 -12.2,-2.9 -3.1,1.7 -10.3,-1.1 -10.2,-5.4 -4,1.8 -9.4,-1.4 -9.1,-5.5 -3.8,-0.5 -4.2,-3.9 -4,-6.7 -3.3,-1.6 -2.4,-4.8 -1,-8.6 -2.4,-2.6 -1.4,-6.2 0.4,-9.6 -2.5,-2.6 -2,-5.6 -1.3,-9.3 -12.3,-1 -27.8,-4 -63.3,-14.9 -53.6,-16.5 -68,22.2 -56.2,46.4 13.7,28 -1.5,34 3.1,54.8 5,1 7.5,5.2 7.2,9.6 3,0.1 5,2.8 4,8a9,9 0,0 1,7.6 2.3c1.8,-3.4 7.8,-4.2 10.8,-0.3 6.7,-0.5 10.1,5 9.8,11.6a18,18 0,0 1,-1.5 19.3c0.4,-2.7 0,-6.5 -0.1,-8.9 -0.3,-4.2 -6.2,-5.1 -5.6,-8.6 -3,0.3 -6,-1.4 -7,-3.7a7,7 0,0 1,-6.6 1.3c3.4,1.5 6.2,7.7 5.1,11.8 1.8,3.1 1.4,8.8 -0.7,11.2 -1,5 -5,6.8 -10,4.6 2.9,-1.8 3.9,-5 3.8,-7.7a10,10 0,0 1,-2.9 -6.3c-5,0.8 -12,-3.5 -13.2,-5.2a20,20 0,0 0,-20 20.1c-0.6,-4.1 -5.8,-8.2 -5.1,-11.7 -3.1,-9.5 1.3,-18.4 13.9,-20.2 -1.6,-3.6 3.8,-7.3 1.8,-11.4a97,97 0,0 0,-14.7 -20.1c4.4,-7.5 3,-17.5 0.5,-23.7 -3.7,-8.9 -7.2,-6.7 -20.3,7.7 -21.4,23.5 -50,17 -75.2,32.5 -6.7,4 -13.4,5.6 -6.2,-1.6s26.2,-14.4 38.6,-20.6c23.2,-11.6 42.8,-30.9 50.5,-68.5 18.1,-88.4 85,-59.2 127.2,-42.8 39.7,15.5 32.5,-19.5 12.4,-40.7 -24.2,-25.3 -19.3,-45.3 -8,-61.3 20.3,-2.8 59.4,4.3 51.5,11.1z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.9,358.8a22,22 0,1 0,0 -44.1,22 22,0 0,0 0,44z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M391.8,142.7c-5,21.7 -0.8,31.5 6.4,41 14.9,19.7 26.8,64.6 9.8,94"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M417.5,252.3c2,-0.7 6,-3.2 6.8,-7.4m-5.2,-2c0.6,-3.7 6.4,-5.3 6.5,-9.3m-6.4,-5.2c-0.4,-3.9 5.8,-7.4 4.9,-11.2m-8.3,-2.7c-0.5,-2.2 5.2,-6.3 3.6,-9.8m-7.8,-3.8c-1.2,-2.4 2.7,-5.2 1,-7.8m-7.2,-3c-0.4,-1.6 2,-5.3 0.7,-7.5m-6.9,-5.2c0.5,-0.7 2.6,-2.2 1.8,-4.1m-6,-5.3c0.8,-0.4 3.3,-1.2 3,-3"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M266,410.9c-5,-1.8 -11.5,0.7 -12.8,5.1m3.9,4c0.6,-4.4 7.3,-6.3 9.3,-4.3 -4.2,-2.3 -6.3,6 -2.5,6.2m34.6,-103.8c-3,1.6 -4,7.2 0,11.5m4.6,-10.2c-2.1,1.8 -2,7.2 1.2,8.5 -2.7,-2 0,-5.3 2,-5.4 1.9,-0.1 3.2,2.2 0.8,4.5m177,5.2c-7.2,-2 -13,6.4 -6.4,13.9 -0.2,-7.2 5,-12 11.3,-10.7m-3,5a2.7,2.7 0,0 0,-2.7 2.7c0,1.4 1.2,2.8 3.2,2.8 1.3,0 2.4,-1.5 2.4,-2.7m22.6,-161c1.2,4.4 7.2,6.3 12,5.2m0,-2.8c-3.7,0.1 -6.8,-3.4 -6.6,-6.3 0,2.2 5,3.2 6.6,1.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M267.9,331.7c-1,-2.6 3,-5.2 3.2,-7.6 0.1,-2.5 4.6,-4.4 9.3,0.2m-2.2,-27.4 l-2.5,1.2m11.4,12.8c-1.1,0 -3.4,0 -4.6,-0.9m0,5.8c-0.8,0.5 -2.8,1.2 -4,1.5m-40.1,76.3c-0.2,2.3 2.2,5.7 3.4,6.7m6.6,-12.2a10,10 0,0 0,-1.3 7.6m20.5,0c-2.3,-1.5 -0.8,-5.4 -1,-8 -0.3,-2.6 2.7,-6.7 8.5,-3.2M246,381c2.3,-0.4 4.7,-0.3 6.3,0.4m23,-7.7a8,8 0,0 0,-1.4 4m12.3,-4.3c-1.8,0 -3.4,1.3 -4.2,2.6m-20.8,-68.5c2,0.7 7.4,4 7.6,7.4m14.3,-24.2c-6.3,-0.1 -8.8,-6.5 -4,-6.5m15.3,15.2c-2.4,1 -1.3,5.2 2.2,7.3m-17.3,33.1c-1.2,-1.6 0.4,-6 4.4,-4.7m5,51.7c0.3,-4 5.2,-6.2 7.2,-1.8m-25.5,13c-0.3,-4.3 2,-5.7 3.8,-6 2,-0.2 4.7,1.4 6,4.2m-48.1,5c0.2,-2.6 2.4,-5.3 4.7,-4.9m231,-109.4c-1.7,1.2 -2.8,6.7 3.5,7.2M458,296c0,0.6 0.8,1.5 1.3,2m29,8.3c-1.6,-1.3 -6,4 -2,7.7m-39,35.6c-0.9,-3.7 2.5,-4.7 5.8,-3.9m-14,-22.2c2,-1.2 4,-2.7 6.4,-3.3m-7.4,17.5c0,-3 1.6,-5.7 3,-6.4m8.4,-29.1a16,16 0,0 0,2.4 9.2m28.2,-9q-3,0.7 -4.2,2.9m2.1,7.7q1.6,-1.3 2.4,-2.3m46.3,-110.2c0,3.6 -4.5,5.6 -7.5,3.3m17.3,-3.1c2,1.5 8.9,0 7.3,-4M528,221.4a11,11 0,0 1,-4.9 -3m19.1,-7.7c-2.3,0.5 -3.9,0 -5,-0.5m-31.6,13.4a17,17 0,0 0,6.5 -1.6M502,200.8q-2.5,-0.3 -3.9,0.8m29.8,5.5a11,11 0,0 1,-3 5.2"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M497.5,212.8c3.2,-1.4 7.2,9.5 15,5.7m0.6,-11.4a11,11 0,0 0,-1.8 5.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M359,190.4c1,-0.2 2.9,-0.5 3.3,-1.8M226.5,310.3c3.9,2.2 6.6,5.9 5,11.4m172.1,-143.2c1.2,0.8 5.5,0.8 8,0m3,2.6c0,1.8 0.5,8.6 -3,10m1.3,-0.8c3.2,1 9.6,0.6 11.7,-5.3m-4.5,5c1.7,2.6 2.2,7.6 -2.8,10.3m4,-6c3.8,1.2 12.4,1.4 11.5,-6m-3,6c2.7,3.5 14,7.8 12.2,0.3m-22.5,10c4.3,1.1 10.5,-1.9 8,-9.6m12.3,3.9c0.6,3 15,6 13.1,-0.7m-2.7,3.9c2.7,6.2 17,5.7 12.5,-2.6m-2.3,6.5c2.8,3.4 15.5,1.4 10.4,-7m-0.1,6.8c7.9,6 17,-2.5 7,-8.7m4.6,6.8c7,5.5 15.5,-4.5 9.4,-7.3m-64.4,5c2.2,0.6 6.8,0.4 7.9,-3.6m-1.8,2.7c-0.2,5.8 9.6,8 12.1,1.3m-3.3,3.7c1.8,3.9 10.5,5.4 11.9,-0.1m-1.4,2.6c1.4,3.8 8.9,3.4 11,-0.6m-2.8,2.6c2.3,5 11.9,5 14,-2.3m-1.6,3.2c3.7,2.3 11.7,1.4 11,-5.8m-1.5,5.2c5.6,4.5 13.4,0.1 9.5,-7.5m-0.3,13.4c3,-0.5 4.5,-6.4 1.4,-8m-70,9c6,-3.3 7.3,-9 3,-14.5m2.2,8.9c3.9,2.3 11.2,-0.2 12.5,-5.8m-7.2,6.4c2.2,2.8 2.6,6.3 -0.3,9.5m2,-5.9c6.9,-4.2 15.4,3.6 9,8.4m-1,-8.8c1.5,-0.4 4.2,-3.2 4.4,-6.4m-1.4,9.4c2.9,-3 22.2,3 10,9.3m-1,-17.4c3.8,1.4 5.7,6.7 0,8.5m5,4.4c4,-4.3 17,-1.6 12.3,3.8m-3.1,-5.5c2.3,-7.9 16.1,-3 11.6,0.2m-14.4,-8c0.4,1.5 0.5,5.3 -2,7.4m13.6,-9a6,6 0,0 1,-0.8 5.3m9.5,-5.2c0.8,1.4 2,4.1 -0.8,6.3m-109.4,-65.5c0.1,7.3 2.7,12.2 12.6,7.6m-9.5,1.1c-5,6.6 0.6,13.7 10.3,6.6M365,165c6.7,7.2 18.7,2 11,-9m8,15.2c-1.2,7.1 4.6,8.5 9.3,5.3m-34.2,-10.3c1.2,7.1 8.5,12.7 15.6,8.4m-6.9,1.4c0,10.2 14,11.3 17.3,0.8m-5,6.4c4.5,9.3 14.3,5.5 17.5,-0.1m-27.4,-14.7c1.8,4.5 5.4,9.5 13.7,5.8m-39.5,-8c1.2,3.7 7.9,8.2 15.6,3m-10.8,1.8c-4.2,6 4,11.7 14,2.7m-9.1,4.7c1.6,8.5 5,15.4 17,4.4m-6,4.4c4.4,5.4 11.1,8.7 17.4,-0.4"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M387.3,188.8c-0.4,6.5 0.8,9.7 5.9,9.4 4,-0.2 7.7,-3.3 9.9,-6.7m-10.8,6.7c-0.2,7.4 5.6,13.2 16.5,5.7m-12,3c-2,5.5 4.1,14.5 16.2,9.9m-41.9,-24.6c-0.7,7 5.8,11.8 16.4,2.7m-11.2,5.1c0.4,5.8 7,12.8 16.6,2.7m-12,4.7c-0.7,9.9 8.4,12.7 16.1,5.1M367.2,200c2,0.2 3.7,-1.6 4.7,-3m-0.4,10.2c1.8,0.4 5,-1.2 6,-3.3m-1.4,15c2,2.3 9,0.7 9.9,-2.3m-2.2,2.2c3.8,9 14.1,8.7 18.4,-1.5m-2,3.6c2,5.3 6.9,8.6 14.9,6.8m-11,-1.4c-4.5,7.3 1.4,16 11.5,7.5m-9,3.5c-0.5,4.6 3.7,9.9 9.5,10.5m-28.6,-24c-1.5,10.4 6,15.4 15.3,9.8m-26.2,-4.8c2.4,1.9 6.8,2.1 11,0.6m5,6c-2.4,8.8 6.6,15.1 14.3,5.3M380,230.2c0.2,4.5 4.5,9.4 12.1,8m21.3,9c-5.2,3.4 -6.2,9.6 1,13.6m-13.6,-15.4c0.2,5.2 2.7,8.2 8.5,8.7m-16.1,-11.4c-7.8,7 -0.2,15.3 9,8.3m-3.6,2.2c-2.6,8.1 7,13 12.2,4.8m-28.2,-22c-2.8,8 0.8,13.2 7.4,12.7m-17.6,-14.3c0.4,4.8 4.5,6.5 9.2,5.4m-6.2,-0.4c-3.5,6.7 1.8,10.3 8.8,7.8m21,15c-1,4.2 -0.4,7.2 5.9,8.7m-5.4,-2.7c-7.6,3.4 -8.3,10.8 -2.4,15.5m-3.8,-23.7a7.6,7.6 0,0 0,1.2 12.3m-35,-35.8c-4.3,4.3 -0.2,16.2 9.5,9.7m15.7,5.3c-6,5 -3.3,13.8 6.5,11.4m-16.3,-15c-3.1,8.7 -0.2,11.8 6.8,11.9"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M359.3,236.1a8.2,8.2 0,0 0,-1.5 12.2c2.3,2.6 6.7,1.4 8,-1.9m-17.6,-13.8c-5.9,7.6 0,16.6 8.4,14m23.3,8.6c-6,2.1 -10.7,7.6 -7,12.5 2.3,2.8 11.8,3.2 14.5,-7.8M369,248.1c-3.5,5 -2.4,9.8 4,12.2m-4.4,-2.8c-3.8,2 -6.2,4.5 -5.3,9m-3.1,-16.9c-1.4,6 0.3,9.8 4,11.6m-4.1,-4.4c-5.8,-0.4 -8.8,2 -6.8,7.8m0.5,-6.4c-5.7,-2 -6.6,-7 -4.1,-12m-0.8,7.6c-6.2,0.2 -9,3.3 -9,7.5"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M340.5,229.7c-4.5,1.9 -6,8 -4.3,11.3s7,3.6 10.2,1.4M328,224.9c-4.1,4.6 0.6,13.7 8,11.5m-18.6,-15.1c-3.8,5.4 0.4,14.4 10.3,11.8m0.5,25.1c-1,-6.4 5.7,-10.6 14,-2.3m-4,-13a10,10 0,0 0,-3.3 8.5m-28,-33.2c-4.3,7.2 0.9,13.8 10,11.2m13.8,6.7c-6,6 -4,12.1 0.5,15.6m-4.4,-8.5c-9.1,0.1 -9.6,10.7 -2.2,14m-4.7,-24.6c-3.8,2.6 -5,9.7 1.5,12.5m-1.8,3.4c-3.9,-1.8 -8.5,0.4 -8.1,4.9m2.3,-4.8c-3.5,-8.2 -13.6,-6.8 -12.7,1.7m15.5,-11.9c-2.1,0.4 -6.5,1.7 -8.2,5m0.6,-14.3a7,7 0,0 0,2.5 11.4M296.7,216c-0.8,5.6 1.4,8.3 8.4,7.8m-6,-0.7c-2.6,6.7 1,9.7 8,9.3m-6.4,-1c-4.1,4.9 -1.3,10.1 2.7,12.3m-4.6,-6.1c-7.4,-1 -8.5,7.6 -6,11.5"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M292.9,215.5c-4.5,2 -7.1,7.6 -4.7,11 2.5,3.5 7.4,2.4 10,0.6m-8.4,1c-3.8,5.7 -0.4,10 3.6,11.6"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M275.3,214.8c-3,3.1 -1.1,9.4 6,9.6 5.9,0 8.8,-5.4 7,-9.3m-8,9.3c-2.3,5.1 -0.8,10.7 8,9.8m3.6,8c-5.3,-2.4 -12.6,0 -9.5,6.5m-0.7,-15.6c-2.2,3.1 -1.5,7.5 1,10.2m-1.4,-2c-3.6,0.7 -7.1,2.6 -5,8.3m-0.5,-4.6c-4.6,-1.4 -10.5,2 -7,6.7m-0.7,-4.5c-4,-0.7 -8.7,3.6 -4.9,7.8m-1.5,-3.5c-3.6,1 -7.9,5 -4.1,8.3m22.3,-28.5c-5.2,2.3 -5.7,8.1 -3.3,12.4m-2.2,-23c-7.7,3 -7.6,13.2 1,16.4m-3.7,-2.3c-4.6,2.7 -5.5,7.9 -2.4,11.2m-5.7,-29.2c-3,1 -2.8,10.5 5.4,10.3m-13.9,-7.1c-6.2,4.1 1.3,14.5 11.3,7m-5.5,2.7c-0.9,4.2 0.3,8.8 7.1,9.4m-6.7,-3.8c-4.9,1.8 -5.8,11.9 3,12.7m-18.6,-21.8c-6.3,5.8 5.2,10.8 9.4,2.4m-18.1,7.2c-3.1,3.8 7.7,13.6 12.5,-2.8m1.1,-0.3q0.4,6 7.3,6.5m0,3.7c-7.7,1.2 -10.1,10.7 -1.6,12.8m-12.5,-14c-0.3,3.5 3.5,6.5 7.7,5.9m3.9,7.8c-7,1.6 -7.9,10.6 -1.7,10m-3.2,8.4c-5.8,-1.7 -6,-8.6 -0.8,-11.2"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M245.7,267.8c-4.9,3 -3,10 -0.4,11s4.7,-0.2 5,-2.4m-0.5,10c0.6,3.3 -11.9,2 -5.5,-8.4m0,8.9c-4.2,6.6 2.6,12 6.9,6.3m-6.4,1.6c-1.7,5.3 4.7,9.1 8.8,5M231,245.4c-2.3,4.7 9.3,6.5 10,-3.3m-13.2,10.3c-2.3,9.3 15.2,7.4 10.7,-4.6m0.4,6.2a8.2,8.2 0,0 0,11.6 -6.3m-5.3,6.6a10,10 0,0 0,4.7 6.3m-13.1,-3.9c-0.8,5 4.3,9.2 10,8.8m-9.1,-3.9c-2.8,3.9 -3.7,11.3 5.2,11.8M224,263c-1.4,4 7,8.1 11.8,1.5m-14.4,8.7c-0.7,3 6.8,7 11,-5.8m-3.3,6.5c1.8,3.2 7.9,5.8 11.6,-0.5m-6,3.9c-1.6,4.5 2.7,8.7 7.8,7.5m-17.5,-8.5c-1,6.4 6.5,10.1 11,6.8m-15.9,-4c-2.2,8.2 8.4,11.3 12.4,5.1m-2.7,2.5c0.4,4.6 7.6,8.6 13.2,4.8m-26.1,-1c-0.5,2.1 8.1,4.2 9.4,-3m-4.4,4.9c2,5.2 9,6.5 13,0m-2.3,2.6c1,5 7.2,7.7 12.8,4.3m2.3,2c-1.4,6.5 5.4,11.8 9.6,8.2m-20.6,-9.4c-2,7.2 7,11.3 12,7.3m-22.8,-11c-0.5,6.8 4.8,10.8 10.8,7.8m-22.3,-7c-1.8,4.3 7.8,7.9 12.3,4.2m-18.2,7.7c2.5,2.8 11,0 11.7,-6.3m-2.3,4.6c3.1,3.6 10.5,5.6 13.4,-2.2m-2.6,4c0,5.6 9.8,9.6 13,-0.6m11.7,2c-1,2.7 1.2,7 5.7,7.5m-13.9,-9.2c-0.6,3 3.9,7.4 8.7,5.7m0.9,1.3c-1.3,3.3 -0.2,8.1 4.3,8m-3.9,-1.8c-3.4,2.8 -2,7.9 2.9,8m-4.6,-3.2c-4.8,3.2 -3.1,10.3 3.2,9.9M239,313c0,7 8.7,8 10.3,1.6m-3.3,4c-1.3,4.2 2,8.2 7.3,7m-6.3,42.8c1.2,1.7 6,-1.2 4.7,-4s-6.4,-1.3 -5.8,1.7m4.6,-2.9c0.6,-5.6 -6,-6.5 -7.7,-1.6m2.4,-2.9c1.7,-2.8 -4,-6.7 -6.3,-2.5m2,-1.7c1.6,-4.1 -4.8,-6 -5.3,-2.2m-1.5,-5.4c0.8,-2 8,-0.8 5,3.5m5.5,5.1c2.8,-2.6 -2,-7.8 -5,-6m24,13.9c-2.2,0.2 -4.5,1.7 -2.9,6.5 1.2,3.3 6.1,3.4 6.9,1.5"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M260.5,365.9c-2.2,-1.5 -7,1 -4.8,5.6 1.6,3.5 5.8,2 6.4,0.2m-14,0.8c1.2,2 6.6,1 7.6,-1m-3.8,-5.4c1.2,-0.8 3.3,0.2 3.7,1.2m-4,-33c-3.8,2.5 -1.5,10 4.3,8m-5.6,-1.9c-3,2.3 -0.2,9.6 5.3,6.8m-4.3,0c-2.1,2.1 -0.1,7.8 5.2,6.7m-6,-3.8c-1.2,-0.5 -4.2,-0.2 -5.5,1.8m2.1,-28c-2.8,2.8 -1.4,8.3 3.8,8.8m-4.8,-3c-4,2 -4,10 4,10.3m-4.3,-1.3c-2.3,1.8 -1.2,8.8 4.6,7.9m-2.8,-0.3c-0.8,0.9 -1,3 -0.2,3.9m-2.3,-6.5c-2,0 -4.8,1.5 -5.5,3.7m-3.7,-7.5c0.7,-2 7.1,-2 7.6,4.1m0.6,-7.2c-0.9,0.2 -2.6,1 -3,2.8m0.2,-21.7c-2.7,2.6 -4,10.5 4.3,12m-9.9,4.4c0,-2.1 5.8,-4 7.8,-0.8M232,322.8c0.8,1.6 4.8,3.8 7.2,2m-28,-14.6a6.3,6.3 0,0 0,7.2 6.5c4.3,-0.5 5.2,-4.2 3.9,-6.7m-3.2,6.9c-2.9,3.4 0.6,8.3 3.8,6.9m-0.6,-9.4c1.7,-0.6 7.2,-1.4 8.9,1.2m-26.8,-0.2c-1.3,2 6.7,3.9 9.4,0.2m-2.7,1.9c-0.3,2.6 1,7 7.6,5.1"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M219.8,326.6c1,-2.2 -3.8,-5.8 -7.6,-1.8s0.6,8.5 2.9,7M202.7,318c-2,3.4 5.5,9.5 9.8,3.8"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M197.7,323.8c-2.7,2.8 0.7,7.9 4.7,6.3 4.1,-1.5 3.7,-5.8 2.7,-6.9"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M192.5,329c-2.2,2 0,6.6 3,6.6s4.9,-2.3 4.3,-5.4m5,-1.6c-0.4,3 4,5 6.9,2.2m0,-5.4c0.5,-0.6 0.2,-1.7 -0.5,-2.3m-23.4,9.8c-2.5,2 3,7.6 6.3,2.5m13.1,-3.5c-1.6,1.5 1,5.5 3.6,4.3m-12.4,-1.5c0.4,2.7 5.8,4.8 9.2,0.6m-6.3,2.1c-0.8,1.8 0.9,4.5 3,4.2m48.3,11.8c-2,3.9 4.4,8.5 9,3.7m-4.4,2.1c-1,2.1 0.2,5.1 2,6.2m-8,-1.7c0.4,-2 3.2,-3.8 5.7,-2.9m-9,-2.2c0.3,-1.8 2.6,-3.5 4.4,-3m147.2,-77.4c-9.2,0.1 -5.3,14.8 2.6,11.9m-5.6,-1c-1.8,2.9 1.7,7.6 5.6,4.8m-1,0.6c-2.8,4.5 6.9,11.4 10.8,4.3m-3.1,2.7c0,4.3 12.5,7 10.7,-1.5m-1.2,4.6c3,5 14,5.5 12.9,-2m-2.5,5.2c2.3,3.4 13.2,5.4 12.8,-1.5m-56.7,-40.8c1.5,4 6.8,5.4 12.6,3m-16,-0.2c3.7,2.3 -1.6,12.9 -7.1,8.6m7.2,-2c4,1.9 8.9,0.5 10.3,-5.2m-2.5,4.4c0.4,3 4.7,5.3 10.2,4.1m-20.1,-1c5.2,4.4 -2.3,13.4 -5.7,9.3m7.6,-6c2.5,1.8 8.8,0.8 9.4,-4m-2.8,4c0.5,3.1 3,4.3 6.4,4.4m-14.4,-0.1c2.6,3.6 9,4.6 11.9,-0.1m-2.9,2.7c-0.2,3.4 3.6,6.3 7.6,5.5M375,295c3.3,1.7 7,-4.7 4.1,-9m-0.2,15.7c4,0.2 4.5,-5.8 0.7,-9.2m8.4,14.7c3.4,-0.8 2.3,-8.1 -5.8,-8.7m16,14.1c3.2,-0.9 0.6,-9 -8.3,-8.5m20.5,11.4c2.2,-3.4 -5,-9 -11,-6m17,10.6c4,1.2 6.8,-9.4 -5.9,-7.7m13.3,7.7c3.5,-1 6,-7.2 -4.2,-5.2m12,5.7c3,1.6 4.4,-7.5 -5,-5m-47,-23c3.9,2.1 10,-0.4 9.4,-5.3m-1.5,4.2c1.3,2.3 0.8,6.8 -1.7,8m2,-2.6c2.6,1 6.1,0.1 8.2,-4m-3.6,4c0.7,1.8 1,5.4 -0.7,7.2m1.3,-4.1c2.9,1.6 6.2,-0.5 7.6,-3.5m-1.8,2.6c2,1.3 3.9,7.3 0.4,9.8m2,-2.8c2.4,0 6,0 8.3,-3m-1.7,1.7c2.2,0.6 4.8,4.2 3.9,7.4m0.1,-1.1c2.5,-0.3 6.7,-2 7.9,-5m-1.2,2a7,7 0,0 1,3.2 6.5m0,-2.1c2.5,-0.1 4.7,-1.4 5.3,-4.1m-0.6,1.8c1.8,0.6 4,2.7 4.1,5.1m-0.3,-1.5q2.6,-0.5 4.3,-3.2m4.9,-0.3c2.3,2.3 -0.8,10 -5.6,8.6m-43.9,-164.7c-4.7,2.9 -18.3,2 -11.6,-9.2m13,1.6c-9.5,3.8 -21,-3.5 -9,-11.3m-3.2,-2.6c-7.8,0 -13.1,12.6 -2.6,17M348.6,138c-2,4 5.3,8.4 10.3,4.4 3.9,-3.1 3.6,-11.5 1.3,-14.7m-15.6,19.8c-2.6,8.5 16.4,9 13.4,-4.5m-14.4,17.2c0.6,6.4 18.1,4.3 12.5,-8.7m2,-3.6c1.4,2 5.3,5.3 11.9,4.6m-9,-13.5c0.6,2.3 4.5,4 9.5,2.3M185.4,334.8c-4.5,3.1 2.4,8.1 4.9,2.6m-8.4,-0.2c-4.4,3.2 2.4,8.2 4.9,2.7m-8.6,-0.3c-4.4,3.2 2.4,8.1 5,2.7m-8.8,-0.5c-4.3,2.3 0.7,7.8 5,3m-9.2,-0.9c-4.3,2.3 0.7,7.8 5,3m16.6,-9.6c-0.1,3.3 6.1,4.6 8.4,-0.3m-4.5,3.3c-2,2.5 1.6,5.5 3.5,4.4m-11.8,-4.8c-0.2,2.8 5.2,4.4 7.8,1.3m-4.9,1.6c-1.7,2.2 1.7,5.3 4,4.4m-10.5,-4.8c0.2,2.2 3.3,4.7 6.6,3.3m-4.6,-0.3c-1.3,1.1 -0.7,3.8 1.1,4.7m-7.8,-5c-0.5,2 2.9,5.6 6.4,3.3m-5.7,-1c-2,1.7 -1.8,4.1 0.8,4.5m-5.8,-4.7c-0.2,1.4 1.5,3.6 3.7,3.3m-14.7,-3.4c-2,1.1 3.2,6.7 6.5,1.5m-11.6,0.8c-2.6,1.2 3.8,7.4 6.7,1.1m10,-1.3c-2,0.4 -3.3,3.3 -1.6,4.5m-5,-3.5c-0.2,1.2 2.3,3 4.3,2.4m-5.9,-1.9c-2,1.2 -0.9,4.6 1.4,4.1m-8.3,-1.8c-1.8,1.3 -0.5,4 2,3.4m1.3,-3.6c0,0.7 1.7,1.8 3,1.3m-12.5,-2.5c-2.3,1 -2.1,6.6 5.5,3.6m-10.6,-1.3c-3.2,1.5 -2.1,5.8 4.8,1.7m0.5,0.3c-0.8,0.7 -1.7,3.5 1.5,2.4m-7.2,-1c-1.3,1.1 -0.5,3.5 2.3,2.6m-9,-2.2c-2.3,1.3 2.8,3.1 6.2,-0.5m-4.6,2.1c-1,1 -1,4.1 1.6,3m-8.2,-1.7c-1.5,1.1 1,2.7 6,0.5m-4,1.1c-1.9,1.5 -1.7,3.4 1,2.8m-6.4,-2c-2,1.9 0.9,3.1 4.4,1.6m-4.4,0.5c-3.4,1.5 -1.9,4.4 0.2,3.3m113.8,6.5c-0.8,1.7 1.8,3.1 4.3,2.7 2.3,-0.3 4.6,-2.3 2.8,-5m0.1,3.4c2.4,1.8 6.5,-0.3 6.5,-3"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M396.8,103c-10.3,-5 -31.7,-14.6 -37.8,-6.9 5.6,-2.3 21.8,0.1 35.2,12.5z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M403,102.5c-11.9,-13.9 -19,-10.8 -27.5,-15.5 -8,-4.5 -20.8,-5.4 -23.3,1.7 11.7,-5.7 22.5,3 29.2,4.2 9,1.4 14.2,8.5 16.9,11.7l4.7,-2zM447.9,100.7c-6.2,-14.1 -19.4,-10.4 -25.2,-16.4 -8.5,-8.8 -30,-17 -39,-10.9 19.6,-1 28,13.5 38.4,18.6 7.5,3.6 15.5,11.3 25.8,8.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M424.7,99.2c-10.5,-13.1 -26.8,-24.7 -34.2,-20.4 9.8,0.6 12.9,7.4 19.8,11.8 7,4.3 3.9,10.7 14.4,8.6zM374.5,122.4c-11,-4.1 -32.7,-6.2 -42.8,6.4 16.8,2.9 42,1.3 42.8,-6.4z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M372.4,127.3c-11,-5.2 -19.7,2 -30.1,1 -19.8,-2 -34,-0.8 -35.8,8.8 11.3,-10.2 30.4,-1.8 38.1,-3.9s36.3,-0.3 45.3,3.6c-4.6,-5.7 -11.8,-7.2 -17.5,-9.5zM407.2,97.8c-2.6,-8.5 -2.4,-17.5 10.3,-16.9 -3.2,-4 -15,-6 -17,8.9 -14,-10.3 -29.4,-12.1 -32.2,-3.2 7.2,-6.2 18.4,-1.7 31.8,13.5a24,24 0,0 1,7.1 -2.3z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M387.9,109.5c-8,-5.2 -18.8,-13.5 0.1,-16.9 -8,-4.3 -20,-2.4 -18.7,12.5 -21.6,-8.7 -37.1,-5.8 -40.4,2.9 -3.6,9.5 9.8,14.8 12.1,8.7 -2.4,1 -10.8,-1.8 -6.4,-7.2s26.8,-1.3 48.1,9.8c6,3 26.3,2.6 5.2,-9.8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M382.2,123.7c-6.1,-12.6 -26.1,-1.2 -30.1,-13.4 -5.6,17.9 28.4,8 30.1,13.4zM509.3,137.3c4.2,2.1 7.8,-1.2 1.4,-3.7 4.2,2 7.9,-1.1 1.5,-3.7 4.2,2 7.8,-1.1 1.4,-3.7 -1.7,1.7 -4.1,8 -4.3,11.1zM511.5,113.1c9.3,-9.8 -0.7,-13.1 10.6,-23.2 9.3,-8.2 1.8,-13.7 10.5,-20 2.9,-2.1 9,-6.2 9.6,-10.4 3.7,9.3 -11.6,10.6 -10.6,25.5 0.7,9.5 -5.8,8.7 -8.2,24.8 -0.5,3.3 -2.9,10.8 -11.9,3.3z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M515.6,117.5c5.2,-11 11.1,-10.9 14,-15.2 5.4,-8.3 16.8,1.4 26.5,-6 -1.7,10.5 -14.7,6.8 -20.4,13.5s-10.3,9.7 -20,7.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M517,121.1c9,-7.2 15.6,-2.4 21.8,-6.2 15.7,-9.5 22,2 36,-2.6 -3.6,9 -24.4,1.3 -33.4,8s-40.7,13.2 -24.5,0.8zM490.7,172.5c-0.2,-4.1 -4,-9.4 -9.4,-10 -5.4,-0.7 -7.8,-6.4 -11.9,-6.6 -4.1,-0.3 -6.8,-8.5 -12.5,-8.4 -5.6,0.1 -8,7.5 5.3,14.2s28,14.4 28.5,10.8zM473.9,176c-5.6,0.2 -6.4,8.5 -11.8,8.7 7.4,4 12.9,-1.8 16.7,-7L474,176z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M478.6,177c-5,4.1 -6.4,12.7 0.7,15.2 -4.2,-5.9 7.5,-8.5 3.9,-14z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M483.4,177.3c-3.8,7.4 6.1,8.3 3.5,14 5.7,-1.3 6.6,-12 1.4,-14.8l-4.9,0.7z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M445.6,161.3c9.3,-0.5 17.7,4.5 23.4,12.6 3.6,5.1 15.7,7.2 19.9,3 4,-4 1.8,-12.8 -8.5,-10 -2.6,-4.3 -10,-2.8 -13.7,-6.4s-17.5,-13.9 -21.1,0.8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M480.4,167c-2.5,0.5 -3.3,4.7 -1.7,6.9m7,-4.4c0.7,1.3 0.2,3.1 -0.2,4m-25.2,-11.8c4.7,0.2 5.6,3.7 10.8,5"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M457.1,150a161,161 0,0 1,37.6 12.3c8.1,4.6 20.7,6 31.6,2.8 11,-3 32.2,-5.9 31.1,7.8 5.8,-6.9 -1.5,-14.2 -16.2,-15.5 0.3,-6.7 -6.9,-12.7 -12,-8.7 4.7,-0.7 8.8,8.2 -0.6,11.8a8.4,8.4 0,0 0,-11.7 -9.8c4.4,1.3 9,8.8 -1,11.6 -6.3,1.8 -15.4,-0.5 -22.2,-4.6s-45,-19 -36.6,-7.8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M498.6,143.3c-5.1,2.3 -1.8,7.7 -9.7,10.8 -8,3 -13.6,10.1 -11.8,16.2 5.4,-11.9 15,-11.3 18.3,-16s8.3,-11.4 3.2,-11z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M500,144c-0.2,9.4 -7.6,6 -4.7,19.2 0.9,4.1 2.6,10.8 -0.3,17.5 8.3,-6 3,-18.7 6.7,-23.5 1.8,-2.4 4.2,-6 5,-9 -1.9,5.3 -1.7,15.4 3.5,18 -4.2,-10 11.5,-18.3 0.7,-30 -1.6,3 -6.5,8 -10.8,7.8zM473.8,134.6c1.3,2 2.6,6.8 1.4,9.6 2.6,-1.6 6.3,-5.6 7.5,-8.3 5.3,0.8 7.3,7.3 2.3,10.2 3,0 8.4,0 11.3,-3.5 -3.6,-4 -14.8,-10.2 -22.5,-8z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M393.7,116.1a10,10 0,0 0,-4.8 -2c-7.8,-1.6 -3.7,-8.6 2.9,-8.5 14.2,-15.5 22.1,-3.6 39.4,-8.4 6,-1.8 10.2,-1.3 13.4,0.2 7.8,-5.2 16.8,-3.8 23.4,2.3a5,5 0,0 1,3 -2.4c6.1,-1.7 11,3.4 12.7,10.1 4.7,-0.8 10.1,1.5 13.7,4.7q7.4,-3.8 9.5,0c4.4,-2 10,-3.4 12.9,3.6s-6.7,4.9 -8.3,19.6c-1,9 -11,12.6 -19,7.2 -12.8,-8.7 -25.3,-10 -31.5,3 -6.1,13.2 -11,20.8 -26,16.5a16,16 0,0 0,-16.7 6.5c-4.4,6 -11,0.4 -19,1 10,-1.5 6.1,-4 14.9,-4.6 8,-0.5 5.9,-8 11,-9 -20,5.1 -19.3,-2.4 -35.8,2.8 7.2,-9.3 18.6,-4.1 24.2,-9.5 -14.9,-0.3 -21.6,-10 -28.3,-6 -10.5,6.5 -6,24.8 -33.5,23.2 -13.4,-0.7 -21.9,1 -29.8,9 13.9,-28.8 32.8,-13 42,-22.1a68,68 0,0 0,12.3 -14.7,6 6,0 0,1 4,-3.2c-22.9,-7.2 -9,-18.5 13.4,-19.3z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M506.9,112q0.5,1.2 0.5,3c0,5.6 -8.5,5.8 -9,14.2 -0.3,4.4 -0.8,6.8 -3.7,6.3s-5.6,-5.1 -2.8,-10.5"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M398,107.7a11,11 0,0 0,-6.2 -2.1m52.8,-8.3c6.8,3 10.1,10 20,10.4 9.6,0.3 15.5,14.2 31.5,5.1l1.2,-0.6M468,99.7a13,13 0,0 0,-1.4 8.2m-29.8,21c-12,0 -15.2,6.1 -15.2,12s5.7,13.7 15.7,13.7 15.5,-6.2 15.5,-12.9 -6.2,-12.9 -16,-12.9z"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M439,154.3c-0.2,-3.3 -6.3,-2.8 -6,-5.4s3.3,-3.6 3.3,-7.2 5.4,-3.9 7.2,-1c1.8,2.8 7.2,8.3 8.4,5.5m-8.4,-5.5a10,10 0,0 0,-0.4 13.1m4.3,-8.8c-1,2 -1.2,5 -0.1,6.8"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M495.5,135.6c9.5,0.8 11,-9.7 4,-10.3m-15.7,-17.8c-3.2,-4.2 -10.8,-5.6 -11,3.2"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M472,120.8c-3.2,-8 -11.5,-9.1 -15.8,-5.1 -3.7,3.3 -3.8,12 4,13.8 2.8,-3.3 8,-7.5 11.8,-8.7zM468.5,115.8c-4.7,-4.1 -11.5,3.7 -5.3,10.8m-83,8.7c3.2,-0.7 7.7,0.8 14.7,4 4.3,2.1 17.5,6.5 25.7,2.1 -8.5,3.1 -15,-9.7 -21.4,-8.2 -6.4,1.6 -18.2,4 -23.1,-0.8 12,0.8 18.5,-8.8 32.4,-0.5a24,24 0,0 0,13.1 3.6c-11.3,-13.6 -26.2,-4.9 -27.8,-16 6.8,7.4 23.5,-1.6 32,12.1m-29.5,-10c-1.6,-2 -1,-4 -2.6,-5.5"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M483.8,107.5c-2.8,-3 -9,-0.7 -7.2,5.4a10,10 0,0 1,7.2 -5.5zM466.3,124a21,21 0,0 1,5.7 -3.2,12 12,0 0,0 -3.8,-5.2c-1.5,-1 -5.7,4.7 -1.9,8.3z"
      android:strokeWidth=".5"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M458.7,113.5c-4.6,-3.1 -8.9,-2.8 -10.3,-0.3 -3.3,0 -6.1,2.4 -6.2,7.2m7.1,2.1c-5.5,-3.8 -13.1,-2.4 -12.6,6.1m-3.4,4.9c2.7,-2.2 6.6,-3.8 9.6,0m31.8,-8.4c-1,1.4 -1.8,3.6 -0.1,7 -2,-2.8 -7.2,-2.8 -12.8,4.4m25.1,-11.8c-6.7,0.8 -6.5,5.2 -1.1,7m-46.5,-23.4c-5.7,-1.1 -9.8,2.2 -2,5m12.8,-8.8c-8,-2 -11.2,0.1 -7.6,2m-15.5,30c-0.2,2.9 1.3,6.6 6,2.4m-4.4,6.1q0.1,1.3 -0.7,2.4m-17.9,-40c-4.9,-1.5 -5.8,-6 0,-5.6m-1.6,16c-5.4,-1.9 -5.4,-7.1 -1,-6.6m11,3.8c-6.3,-1.5 -6.5,-5.9 -1.8,-5.2m2.6,-8.2c-3.4,-0.2 -8.5,3.5 0.1,5.7m9.3,1.8c-7.9,-1.1 -7.7,2 -3.1,4.1m9,-11.6c-6,-1.1 -8.1,2.5 -4,4m-15,18.3c-1.5,-1.2 -2.7,-7 4.3,-5.3m10.3,3.5c-4.7,-1.3 -9.2,3.4 -4.9,6m11.2,-11.5c-5,-1 -9.2,0.5 -6.5,2.3"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M483.6,107.5a10,10 0,0 0,-7 5.4"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
</vector>
