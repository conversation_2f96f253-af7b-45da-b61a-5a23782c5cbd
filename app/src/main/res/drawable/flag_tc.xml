<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M640,480V0H0v480z"
      android:fillColor="#002868"/>
  <path
      android:pathData="M590,174.6v90c0,60 -30,124.6 -105,159.6 -75,-35 -105,-99.6 -105,-159.6v-90z"
      android:strokeWidth="8"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="M590,174.6v90c0,60 -30,124.6 -105,159.6 -75,-35 -105,-99.6 -105,-159.6v-90z"
      android:strokeWidth="7"
      android:fillColor="#00000000"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M447.1,273.7c-2.7,4.7 -7.4,5.7 -11,4.2 -3.7,-1.5 -7.1,-10.3 -10.9,-15 -3.6,-4.7 -4.6,-2.7 -7,-5.4a38,38 0,0 0,-12 -7.4c-3.3,-1.5 -6.7,-2.9 -9,-8.6a10,10 0,0 1,5.2 -5.1c1.9,-0.7 3.4,-3.4 3.6,-5.8 0.3,-2.2 2.3,-4.7 5,-6.6 -1.3,0.2 -3.9,-0.3 -5.1,-1.1 -1.1,-1 -3,-0.8 -4.1,0q-1.7,1.2 -4.6,0.8c0.8,-0.6 2.2,-2 2.3,-3.2s1.6,-1.9 4.5,-1.9c2.8,0 4.6,-3.2 6.9,-3.2 -1.6,0 -1.3,-2.8 -0.4,-3 -4,-0.7 -3.5,-4 -1.9,-4 -0.1,-0.4 -0.6,-1 -2.5,-1.5 -1.7,-0.7 -0.9,-2.3 0.5,-3.3 -1.2,-2.5 -0.1,-4.4 1.9,-6 -2.8,-0.1 0.3,-4 1,-6.1 0.8,-2 1.1,-2.3 2,-1 1,1.1 3.3,3.7 5.9,4.5q3.7,1 4.5,4.2c0.5,2.3 1.5,3.4 4.2,4.4 2.9,0.9 6.8,3.3 6.3,8.6a9,9 0,0 1,5.2 1.9"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#fcad56"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M454,206a8,8 0,0 0,3.1 6.4c1.9,1.5 3.5,2.7 3.1,4.6 -0.3,2 0.4,3.2 2,4.5 1.8,1.2 3,2.7 2.6,5.2s-0.2,4 0.8,5.9 2.8,3.4 2,6.4 -0.4,7 0.6,9c1,1.9 0,3.4 -1.7,6.7 -1.8,3.3 -2.8,4.5 -6.4,5.8 -3.7,1.1 -4,3.4 -4,7.2q0,6.2 -7.5,5.3c-5,4.1 -8.1,-0.8 -9.4,-6.9s-1.4,-12.8 -8.8,-18.1c-7.3,-5.5 -4.4,-14.8 1.7,-18.5s5.4,-14.4 4.6,-21c-0.6,-6.6 3.8,-10.4 7.2,-14 4.5,1.7 10.1,8.6 10.1,11.5"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#ffa1a1"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M443.4,248.6c-1.4,-3.2 -2.9,-6.5 0.7,-13.5 3.8,-7.1 1.4,-12.5 0.3,-16.1 -1.1,-3.5 -1.4,-6.8 0,-8.3 1.5,-1.3 2.1,-2.2 2.2,-4.6 0.1,-2.2 2,-1.5 2.3,-0.4s0.5,3 -1.5,6.4 5.6,12 2.1,21.1c-2.4,6.4 0.8,14.5 2,20.2s2,19 -6.3,21c5.7,-2.7 0.4,-20.5 -1.8,-25.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#f1b2dc"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M406.3,247.6c1.3,0.3 2,-0.5 2.4,-1.4s0.7,-1.3 2,-1.5c1.2,0 3.2,-0.3 3.7,-1.3s1.9,-0.2 3.2,-2.7c1.3,-2.6 2.1,-5.6 5.5,-6.7m9.3,-21.8c-1.3,0 -4.9,0.2 -6.4,3.4m-10.4,-18.9c3,0.9 1.8,2.9 3.3,4 2.7,2.2 0.2,4.4 2.5,5s2.1,1 1.6,3.5c-0.8,2.9 2,3 1,4.9m-13,9.9c1.8,-1.6 6.1,-2.9 7.1,-4.6m-7.2,-4c2.6,-0.2 6.1,0.8 6.6,-0.2m-7,-2.7c1.3,0 3.9,-0.9 4.4,-2.6m-6.3,-1.4c1.5,0 3.4,-0.1 5.3,-1.5m-7.3,-3.4c1.1,-0.9 3.1,0.1 5,-1.1m-3.1,-4.9c0.9,0 2.8,-0.2 2.8,0.9"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M528.13,220.48s-8.75,-7.38 -16,-12.38 -16,-11.13 -16,-12.38c1.38,-1 8.75,3.75 18.88,9.88 10.25,6.25 16,12.38 16,12.38zM527.5,225.48s-8.5,-5.13 -16.88,-8.75c-8.75,-3.63 -21.88,-3.63 -23.25,-4.88 0,-1.25 15.25,-2 26.13,2.38 11.13,5.13 16.88,8.75 16.88,8.75zM527.38,233.98s-9.25,-4.25 -18.25,-6.25c-8.75,-2 -21.25,-1.25 -22.75,-2.38 0,-1.25 15.5,-3.75 27.13,0 11.75,3.63 16.75,6.13 16.75,6.13zM526.88,242.6s-7,-4.75 -16.25,-5.75c-8.75,-0.88 -20.38,5.75 -21.88,4.5 0,-1.25 10.13,-8.63 22.75,-7.38 12.38,1.25 18.25,6.13 18.25,6.13zM529.75,215.35s-8.88,-8.13 -8.88,-13.63c0,-4.88 5.38,-12.38 7.75,-12.38 -1.25,2.5 -5.38,7.5 -5.38,12.38 0,5 9.38,11.13 9.38,11.13z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M529.75,215.35s1.75,-8.13 1.75,-13.63c0,-7.38 -5.25,-14.13 -4.75,-16 1.88,1.88 7.13,7.38 7.13,16 0,7.5 -1.25,11.13 -1.25,11.13z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M531.88,212.73c0,-2.38 -0.88,-4.5 -2.38,-6.13 -2,-2.38 -5,-3.13 -8.13,-2.63 -3.75,0.63 -6.75,2.88 -8.63,6.38 -2.5,4.63 -2.75,9.63 -2.88,14.5 0,16.88 0.63,23.75 1.13,40.38 0.13,4.88 1,14.88 -1.25,22.75 -0.88,3.38 -1.88,6.5 -3.38,9.75 1,-4.13 1.5,-6.25 2,-9.38 1.38,-8.13 1,-15.75 0.75,-23.25 -0.5,-15.38 -1.38,-23.25 -1.88,-41.88 -0.13,-5.63 0.38,-9.88 3,-14.38 2.75,-4.63 7.5,-8 12.88,-8 2.25,0 4.13,0.38 6,1.5 3.75,2 5.5,6.13 5.5,10.25z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#9e540a"
      android:strokeColor="#fcd116"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M531.88,212.73c0,-2.38 -0.88,-4.5 -2.38,-6.13 -2,-2.38 -5,-3.13 -8.13,-2.63 -3.75,0.63 -6.75,2.88 -8.63,6.38 -2.5,4.63 -2.75,9.63 -2.88,14.5 0,16.88 0.63,23.75 1.13,40.38 0.13,4.88 1,14.88 -1.25,22.75 -0.88,3.38 -1.88,6.5 -3.38,9.75 1,-4.13 1.5,-6.25 2,-9.38 1.38,-8.13 1,-15.75 0.75,-23.25 -0.5,-15.38 -1.38,-23.25 -1.88,-41.88 -0.13,-5.63 0.38,-9.88 3,-14.38 2.75,-4.63 7.5,-8 12.88,-8 2.25,0 4.13,0.38 6,1.5 3.75,2 5.5,6.13 5.5,10.25z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M532.63,272.98s-3.5,6.25 -6.13,10.75 -4.75,5.5 -6.75,4.13c-1.88,-1.38 -0.25,-5 1.25,-6.75s9.88,-9.88 9.88,-9.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M533.5,273.48s-1.38,7.13 -2.5,12.25c-1,5.13 -2.75,6.88 -4.88,6.25 -2.25,-0.5 -1.88,-4.63 -1,-6.75s6.13,-12.88 6.13,-12.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,273.73l0,12.5c0,5.25 -1.25,7.38 -3.63,7.25 -2.25,-0.13 -2.75,-4.13 -2.25,-6.5 0.38,-2.25 3.5,-13.88 3.5,-13.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M541.88,220.48s8.75,-7.38 16,-12.38 16,-11.13 16,-12.38c-1.38,-1 -8.75,3.75 -18.88,9.88 -10.25,6.25 -16,12.38 -16,12.38zM542.5,225.48s8.5,-5.13 16.88,-8.75c8.75,-3.63 21.88,-3.63 23.25,-4.88 -0,-1.25 -15.25,-2 -26.13,2.38 -11.13,5.13 -16.88,8.75 -16.88,8.75zM542.63,233.98s9.25,-4.25 18.25,-6.25c8.75,-2 21.25,-1.25 22.75,-2.38 -0,-1.25 -15.5,-3.75 -27.13,0 -11.75,3.63 -16.75,6.13 -16.75,6.13zM543.13,242.6s7,-4.75 16.25,-5.75c8.75,-0.88 20.38,5.75 21.88,4.5 -0,-1.25 -10.13,-8.63 -22.75,-7.38 -12.38,1.25 -18.25,6.13 -18.25,6.13zM540.25,215.35s8.88,-8.13 8.88,-13.63c-0,-4.88 -5.38,-12.38 -7.75,-12.38 1.25,2.5 5.38,7.5 5.38,12.38 -0,5 -9.38,11.13 -9.38,11.13z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M540.25,215.35s-1.75,-8.13 -1.75,-13.63c-0,-7.38 5.25,-14.13 4.75,-16 -1.88,1.88 -7.13,7.38 -7.13,16 -0,7.5 1.25,11.13 1.25,11.13z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M538.13,212.73c-0,-2.38 0.88,-4.5 2.38,-6.13 2,-2.38 5,-3.13 8.13,-2.63 3.75,0.63 6.75,2.88 8.63,6.38 2.5,4.63 2.75,9.63 2.88,14.5 -0,16.88 -0.63,23.75 -1.13,40.38 -0.13,4.88 -1,14.88 1.25,22.75 0.88,3.38 1.88,6.5 3.38,9.75 -1,-4.13 -1.5,-6.25 -2,-9.38 -1.38,-8.13 -1,-15.75 -0.75,-23.25 0.5,-15.38 1.38,-23.25 1.88,-41.88 0.13,-5.63 -0.38,-9.88 -3,-14.38 -2.75,-4.63 -7.5,-8 -12.88,-8 -2.25,0 -4.13,0.38 -6,1.5 -3.75,2 -5.5,6.13 -5.5,10.25z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#9e540a"
      android:strokeColor="#fcd116"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M538.13,212.73c-0,-2.38 0.88,-4.5 2.38,-6.13 2,-2.38 5,-3.13 8.13,-2.63 3.75,0.63 6.75,2.88 8.63,6.38 2.5,4.63 2.75,9.63 2.88,14.5 -0,16.88 -0.63,23.75 -1.13,40.38 -0.13,4.88 -1,14.88 1.25,22.75 0.88,3.38 1.88,6.5 3.38,9.75 -1,-4.13 -1.5,-6.25 -2,-9.38 -1.38,-8.13 -1,-15.75 -0.75,-23.25 0.5,-15.38 1.38,-23.25 1.88,-41.88 0.13,-5.63 -0.38,-9.88 -3,-14.38 -2.75,-4.63 -7.5,-8 -12.88,-8 -2.25,0 -4.13,0.38 -6,1.5 -3.75,2 -5.5,6.13 -5.5,10.25z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M537.38,272.98s3.5,6.25 6.13,10.75 4.75,5.5 6.75,4.13c1.88,-1.38 0.25,-5 -1.25,-6.75s-9.88,-9.88 -9.88,-9.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M536.5,273.48s1.38,7.13 2.5,12.25c1,5.13 2.75,6.88 4.88,6.25 2.25,-0.5 1.88,-4.63 1,-6.75s-6.13,-12.88 -6.13,-12.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,273.73l-0,12.5c-0,5.25 1.25,7.38 3.63,7.25 2.25,-0.13 2.75,-4.13 2.25,-6.5 -0.38,-2.25 -3.5,-13.88 -3.5,-13.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,273.6c-6.63,-0.13 -7.25,-2.5 -7.25,-2.5s-0.88,-3 -1,-4.88c-0.13,-0.25 -1.88,-4 -1.38,-5.63 -0.63,-1.5 -2.13,-5.13 -0.75,-5.75 -0.63,-1.75 -1.63,-3.75 -1.75,-5.63 -0.5,-1.38 -1.75,-4.38 -0.75,-5.38 0,0 -2.38,-5 -1.25,-6.25 0,0 -1.13,-4.88 0,-6.13 0,0 0,-5 1.25,-6.25 0,0 0,-4.88 1.13,-6.13 0,0.25 3.5,-6.88 11.75,-6.88l-0.13,10.63l0.13,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,246.35c-10.63,-0.13 -12.88,-2.5 -12.88,-2.5M535,240.1c-10.63,0 -14.13,-2.5 -14.13,-2.5M535,233.98c-10.63,-0.13 -14.13,-2.5 -14.13,-2.5M535,227.73c-10.63,-0.13 -12.88,-2.5 -12.88,-2.5m12.75,-2.38c0.13,0 -8,0 -9.25,-1.25l-2.38,-2.38M535,268.6c-3.13,0 -6.63,-0.5 -8.25,-2.38m8.25,-2.63c-5.88,0 -9.38,-2.38 -9.38,-2.38s-0.13,-0.25 -0.25,-0.63M535,257.48c-7.75,-0.13 -9.88,-1.75 -10.38,-2.63M535,252.48C524.38,252.48 523.25,249.98 523.25,249.98l-0.38,-0.75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,273.6c6.63,-0.13 7.25,-2.5 7.25,-2.5s0.88,-3 1,-4.88c0.13,-0.25 1.88,-4 1.38,-5.63 0.63,-1.5 2.13,-5.13 0.75,-5.75 0.63,-1.75 1.63,-3.75 1.75,-5.63 0.5,-1.38 1.75,-4.38 0.75,-5.38 -0,0 2.38,-5 1.25,-6.25 -0,0 1.13,-4.88 -0,-6.13 -0,0 -0,-5 -1.25,-6.25 -0,0 -0,-4.88 -1.13,-6.13 -0,0.25 -3.5,-6.88 -11.75,-6.88l0.13,10.63l-0.13,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#9e540a"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M535,246.35c10.63,-0.13 12.88,-2.5 12.88,-2.5M535,240.1c10.63,0 14.13,-2.5 14.13,-2.5M535,233.98c10.63,-0.13 14.13,-2.5 14.13,-2.5M535,227.73c10.63,-0.13 12.88,-2.5 12.88,-2.5m-12.75,-2.38c-0.13,0 8,0 9.25,-1.25l2.38,-2.38M535,268.6c3.13,0 6.63,-0.5 8.25,-2.38m-8.25,-2.63c5.88,0 9.38,-2.38 9.38,-2.38s0.13,-0.25 0.25,-0.63M535,257.48c7.75,-0.13 9.88,-1.75 10.38,-2.63M535,252.48C545.63,252.48 546.75,249.98 546.75,249.98l0.38,-0.75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M485,388.6c-5.38,5 -15.75,1.75 -19,-0.5 -3.38,1.13 -12.38,-2.38 -14.75,-6.25 -6.5,0.88 -8.75,-8.38 -7.63,-10.75 1.75,-3.5 -1.38,-5.75 0.88,-8.13 3.13,-3 -1.75,-5.88 1.25,-7.88 3,-2.13 -0.75,-5.63 2.25,-7.88 3.13,-2.38 -1.63,-5.63 1.63,-7.25s-0.38,-5.88 2.38,-7.5 -1.63,-5.75 2.13,-7.63c3.63,-1.88 0.38,-5.13 3.25,-6.5 3.13,-1.63 0.25,-5 3.13,-6.13 3.25,-1.38 0.88,-5 3.63,-6 2.38,-0.88 0.88,-3.13 2.38,-5.13L485,301.1"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#009e49"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M472.63,301.85c-2.5,2.5 0.5,4.38 -2,7.38 -2.63,3.13 -0.25,4.75 -2.38,7.5s1,4.13 -1.38,7.25c-2.38,3 0.5,4.88 -2.13,7.38 -2.5,2.63 0.5,4.88 -2.13,7.63 -2.5,2.88 0.5,4.75 -2,7.25 -2.63,2.63 0.38,5.38 -2.13,7.88 -2.5,2.63 0.5,5.88 -2.38,8.88 -2.75,3 0.5,5.38 -1.75,7.63 -2.38,2.38 -0.25,4.75 -1.88,6.38s-1.88,3.88 -1.25,4.88M477.5,302.73c-2.25,3.13 1.25,4.5 -0.88,7 -2.13,2.63 0.88,3.5 -0.75,6.5s1.25,3.75 -0.88,6.75 1.13,5.38 -1.13,8.38c-2.38,3 1.13,4.63 -1,7.63 -2,3.13 1,4.75 -0.88,7 -1.88,2.38 1.38,4.63 -1,7.5 -2.25,2.75 1,4.88 -0.88,7.38 -1.88,2.63 0.75,5.38 -0.88,7.5 -1.63,2 1.13,5.13 -1.25,7.88 -2.25,2.75 0.5,4.63 -0.88,6.5s-2.5,4.63 -1.13,5.38"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M485,388.6c5.38,5 15.75,1.75 19,-0.5 3.38,1.13 12.38,-2.38 14.75,-6.25 6.5,0.88 8.75,-8.38 7.63,-10.75 -1.75,-3.5 1.38,-5.75 -0.88,-8.13 -3.13,-3 1.75,-5.88 -1.25,-7.88 -3,-2.13 0.75,-5.63 -2.25,-7.88 -3.13,-2.38 1.63,-5.63 -1.63,-7.25s0.38,-5.88 -2.38,-7.5 1.63,-5.75 -2.13,-7.63c-3.63,-1.88 -0.38,-5.13 -3.25,-6.5 -3.13,-1.63 -0.25,-5 -3.13,-6.13 -3.25,-1.38 -0.88,-5 -3.63,-6 -2.38,-0.88 -0.88,-3.13 -2.38,-5.13L485,301.1"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#009e49"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M497.38,301.85c2.5,2.5 -0.5,4.38 2,7.38 2.63,3.13 0.25,4.75 2.38,7.5s-1,4.13 1.38,7.25c2.38,3 -0.5,4.88 2.13,7.38 2.5,2.63 -0.5,4.88 2.13,7.63 2.5,2.88 -0.5,4.75 2,7.25 2.63,2.63 -0.38,5.38 2.13,7.88 2.5,2.63 -0.5,5.88 2.38,8.88 2.75,3 -0.5,5.38 1.75,7.63 2.38,2.38 0.25,4.75 1.88,6.38s1.88,3.88 1.25,4.88M492.5,302.73c2.25,3.13 -1.25,4.5 0.88,7 2.13,2.63 -0.88,3.5 0.75,6.5s-1.25,3.75 0.88,6.75 -1.13,5.38 1.13,8.38c2.38,3 -1.13,4.63 1,7.63 2,3.13 -1,4.75 0.88,7 1.88,2.38 -1.38,4.63 1,7.5 2.25,2.75 -1,4.88 0.88,7.38 1.88,2.63 -0.75,5.38 0.88,7.5 1.63,2 -1.13,5.13 1.25,7.88 2.25,2.75 -0.5,4.63 0.88,6.5s2.5,4.63 1.13,5.38"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M485,388.6L485,301.1"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#009e49"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M485,305.6c-1.13,4.88 1.63,9.13 0,13.25 -1.63,4 1.13,5.63 0.25,8.88 -1,3.25 1.13,5.63 -0.25,9.13s1.88,6.25 0,10.88c-1.88,4.5 1.25,5.63 0,10.25 -1.38,5.25 2.38,8 0,11.75s2.13,4.63 0.25,8.88c-1.88,4.13 1.13,5.75 -0.25,10"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M486.88,321.35c-0.63,1.38 1.88,4.63 0.5,7.13 -1.5,2.38 0.75,6.13 -0.75,9.13s2.38,6.75 0.88,10.25c-1.63,3.5 0.75,7.88 0,11.13 -0.63,3.25 1.63,9.5 0,11.75 -1.63,2.38 1.63,3 -0.25,8.75s-3.13,0.88 -2,-0.88c2.38,-3.88 -2.63,-5.13 -0.25,-8.88s-1.38,-6.5 0,-11.75c1.25,-4.63 -1.88,-5.75 0,-10.25 1.88,-4.63 -1.38,-7.38 0,-10.88s-0.75,-5.88 0.25,-9.13c0.88,-3.25 -1.88,-4.88 -0.25,-8.88 1.25,-4 2.5,1.13 1.88,2.5m-10.5,4c-0.88,1.5 1.5,4.38 -0.63,7.25 -1.5,2 1,4.38 -1.13,7.25 -1.38,2 1.38,4.5 -0.5,7.38 -1.25,1.63 0.88,4.75 -0.63,7.13 -1.5,2.5 0.75,4.5 -1.88,7.75 -1.5,1.75 1.13,6.13 -0.5,7.5s-0.13,6.75 -1.25,7.5c-1,0.88 0,4.75 -2.75,5.63 1.38,-1.88 -1.38,-3.75 0.88,-6.5 2.38,-2.75 -0.38,-5.88 1.25,-7.88 1.63,-2.13 -1,-4.88 0.88,-7.5 1.88,-2.5 -1.38,-4.63 0.88,-7.38 2.38,-2.88 -0.88,-5.13 1,-7.5 1.88,-2.25 -1.13,-3.88 0.88,-7 2.13,-3 -1.38,-4.63 1,-7.63 2.25,-3 -1,-5.38 1.13,-8.38s-0.75,-3.75 0.88,-6.75c3,1.13 1.5,7.63 0.5,9.13m-6.25,-8.13c-1.25,2 0.5,4.38 -1.38,6.88 -1.88,2.63 0,5.88 -1.88,8 -1.88,2 0.5,6 -1.88,7.63s-0.88,6.5 -2.75,8.13 0,6.25 -2.13,8.63 -1.13,7.25 -2.38,8.38c-1.13,1.13 1.63,3.5 -0.38,5.13 -2.13,1.63 -2.13,4.88 -4.88,7 1.63,-1.63 -0.5,-4 1.88,-6.38 2.25,-2.25 -1,-4.63 1.75,-7.63 2.88,-3 -0.13,-6.25 2.38,-8.88 2.5,-2.5 -0.5,-5.25 2.13,-7.88 2.5,-2.5 -0.5,-4.38 2,-7.25 2.63,-2.75 -0.38,-5 2.13,-7.63 2.63,-2.5 -0.25,-4.38 2.13,-7.38 2.38,-3.13 -0.75,-4.5 1.38,-7.25 1,-1.25 1,-2.38 1,-3.38 1.5,-3.88 1.5,2.88 0.88,3.88"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M467.63,303.98c0,-26.25 3.63,-35.38 17.38,-35.38s17.38,9.13 17.38,35.38z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#ce1126"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M467.63,303.98c0,-26.25 3.63,-35.38 17.38,-35.38 -6.25,0 -10.13,4.88 -11.13,8.13 -1.13,3.25 -0.5,3 0.88,1.75s0.75,1.5 -0.5,3c-1.13,1.5 -2.25,5.88 -0.63,4.5 1.63,-1.5 2.88,0.13 1.13,2.63 -1.75,2.38 -3.25,8.13 -1.5,6.13s2.13,1.25 1,2.88c-1,1.63 -0.88,3.13 0,2.38 1,-0.75 0.88,2.63 0,4zM491.5,303.48C491.5,299.6 491.63,290.48 489.5,287.48c-1.5,-2.38 -1.5,-4.88 0.25,-2.13s2.63,1.38 1.38,-1.38c-1.5,-3.5 -3.5,-7.38 -3,-8.13 0.63,-0.88 1,-0.5 2.25,1.38s1.5,-0.88 0.13,-2.75c-1.38,-1.75 -1.25,-2.13 1.63,-0.88 5.13,2.13 7.63,8.25 7.63,28.88z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#000001"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M485,307.73c-5.5,0 -11.5,-0.88 -15.5,-2.5 -4.75,-1.88 -5,-3.5 -3.38,-5.5 1.25,-1.5 3.75,-0.63 6.25,0.5 2.38,1 9.13,1.75 12.63,1.75s10.25,-0.75 12.63,-1.88c2.5,-1 5,-1.88 6.25,-0.38 1.63,2 1.38,3.63 -3.38,5.5C496.5,306.85 490.5,307.73 485,307.73"
      android:strokeLineJoin="round"
      android:strokeWidth="0.28"
      android:fillColor="#ce1126"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M0,0h320v240H0Z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5ZM120,150.5 L123,168 27,240L0,240ZM320,0v1.5l-124.5,94 1,-22L295,0ZM0,0l119.5,88h-30L0,21Z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M120.5,0v240h80V0ZM0,80v80h320V80Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,96.5v48h320v-48zM136.5,0v240h48V0Z"
      android:fillColor="#c8102e"/>
</vector>
