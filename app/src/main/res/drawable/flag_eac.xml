<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,326.4h640V348H0z"
      android:strokeWidth="3.1"
      android:fillColor="#fff"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,132h640L640,153.6L0,153.6z"
      android:strokeWidth="3.1"
      android:fillColor="#fff"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,348h640v132H0z"
      android:strokeWidth="3.1"
      android:fillColor="#0087ff"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,0h640v132L0,132z"
      android:strokeWidth="3.1"
      android:fillColor="#0087ff"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,290.4h640v36H0z"
      android:fillColor="#ff0000"/>
  <path
      android:pathData="M0,153.6h640v36H0z"
      android:fillColor="#000001"/>
  <path
      android:pathData="M0,206.4h640v67.2H0z"
      android:fillColor="#ff0"/>
  <path
      android:pathData="M0,273.6h640v16.8H0z"
      android:strokeWidth="3.1"
      android:fillColor="#008300"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M0,189.6h640v16.8L0,206.4z"
      android:strokeWidth="3.1"
      android:fillColor="#008300"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M320.02,160.13a69.82,69.82 0,0 0,-49.48 118.95l-29.85,9.6 2.71,6.81 -9.6,2.09 6.98,22.34l158.49,0l6.89,-22.34 -9.6,-2.09 2.71,-6.89 -29.85,-9.51a69.82,69.82 0,0 0,-49.4 -118.95z"
      android:strokeWidth="1.75"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="m242.26,297.93 l6.11,19.72l143.3,0l6.02,-19.72z"
      android:fillColor="#008300"/>
  <path
      android:pathData="m246.1,300.46 l4.45,14.66l138.85,0l4.45,-14.66L281.45,300.46z"
      android:fillColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M255.89,309.1q0,0.48 -0.1,0.92 -0.1,0.42 -0.35,0.74t-0.66,0.52 -1.06,0.18q-0.58,0 -0.99,-0.15 -0.43,-0.17 -0.71,-0.37l0.45,-1.16q0.25,0.17 0.54,0.28t0.61,0.13q0.49,0 0.71,-0.28 0.21,-0.26 0.21,-0.91l0,-4.34l1.34,0zM259.52,311.46q-0.64,0 -1.09,-0.18 -0.46,-0.2 -0.76,-0.55t-0.45,-0.83q-0.13,-0.5 -0.13,-1.07l0,-4.16l1.34,0L258.43,308.67q0,0.41 0.08,0.68t0.23,0.46 0.33,0.26q0.21,0.07 0.46,0.07 0.49,0 0.79,-0.33t0.3,-1.15l0,-4.03l1.35,0l0,4.16q0,0.57 -0.16,1.07 -0.13,0.48 -0.43,0.85t-0.78,0.54q-0.46,0.18 -1.09,0.18zM264.83,304.66q0.16,0.31 0.36,0.79l0.43,1 0.43,1.11 0.41,1.07 0.4,-1.07 0.43,-1.11 0.43,-1q0.2,-0.48 0.36,-0.79l1.22,0l0.16,1.48 0.12,1.72q0.07,0.89 0.1,1.79l0.08,1.68L268.46,311.33l-0.07,-2.12 -0.13,-2.31 -0.33,0.91 -0.4,0.98q-0.16,0.5 -0.35,0.94l-0.3,0.76l-0.94,0l-0.28,-0.76 -0.36,-0.94 -0.38,-0.98 -0.33,-0.91q-0.1,1.16 -0.13,2.31l-0.07,2.12l-1.32,0l0.08,-1.68 0.1,-1.79 0.13,-1.7 0.16,-1.5zM273.31,311.46q-0.63,0 -1.09,-0.18 -0.46,-0.2 -0.76,-0.55t-0.45,-0.83q-0.13,-0.5 -0.13,-1.07l0,-4.16l1.34,0L272.22,308.67q0,0.41 0.08,0.68t0.23,0.46 0.33,0.26q0.21,0.07 0.46,0.07 0.49,0 0.79,-0.33t0.3,-1.15l0,-4.03l1.35,0l0,4.16q0,0.57 -0.16,1.07 -0.13,0.48 -0.43,0.85T274.4,311.26q-0.46,0.18 -1.09,0.18zM277.04,304.66l1.35,0l0,6.67l-1.34,0zM281.95,307.4 L282.65,306.03q0.33,-0.68 0.59,-1.37l1.48,0q-0.49,1.07 -1.02,2.09t-1.11,2.03l0,2.55l-1.34,0l0,-2.53q-0.59,-1.02 -1.12,-2.03t-1.02,-2.11l1.57,0q0.26,0.68 0.59,1.37zM288.58,311.33 L288.37,310.63 288.14,309.89l-2.31,0l-0.25,0.74 -0.2,0.7L283.97,311.33l0.64,-1.99 0.58,-1.72 0.58,-1.53q0.28,-0.74 0.58,-1.42l1.29,0q0.3,0.68 0.58,1.42l0.56,1.53 0.59,1.72 0.64,1.99zM286.97,306.16q-0.03,0.17 -0.13,0.41l-0.2,0.57 -0.25,0.74 -0.26,0.81l1.7,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM295.03,307.4 L295.73,306.03q0.33,-0.68 0.59,-1.37l1.48,0q-0.49,1.07 -1.01,2.09 -0.53,1.02 -1.12,2.03l0,2.55l-1.32,0l0,-2.53l-1.12,-2.03q-0.53,-1.03 -1.01,-2.11l1.55,0l0.59,1.37zM301.66,311.33 L301.45,310.63 301.22,309.89l-2.31,0l-0.23,0.74 -0.21,0.7l-1.39,0l0.63,-1.99 0.59,-1.72 0.56,-1.53q0.28,-0.74 0.59,-1.42l1.27,0q0.3,0.68 0.58,1.42l0.58,1.53 0.59,1.72 0.63,1.99zM300.06,306.16 L299.93,306.57 299.73,307.14 299.49,307.88 299.22,308.69l1.68,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM309.93,311.33 L309.71,310.63 309.48,309.89l-2.31,0l-0.23,0.74 -0.21,0.7l-1.39,0l0.63,-1.99q0.3,-0.92 0.59,-1.72l0.56,-1.53q0.28,-0.74 0.58,-1.42l1.29,0q0.3,0.68 0.58,1.42l0.56,1.53 0.59,1.72 0.64,1.99zM308.31,306.16 L308.2,306.57 308,307.14 307.75,307.88 307.47,308.69l1.7,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM312.14,311.33l0,-6.67L316.13,304.66l0,1.26l-2.64,0l0,1.4l2.34,0l0,1.26l-2.36,0l0,2.77zM318.82,304.59q1.34,0 2.05,0.54t0.73,1.66q0,0.7 -0.3,1.15 -0.28,0.42 -0.82,0.68l0.38,0.55 0.38,0.68 0.38,0.74 0.33,0.74l-1.48,0l-0.35,-0.66 -0.33,-0.65 -0.36,-0.61 -0.33,-0.52l-0.66,0l0,2.44l-1.35,0l0,-6.58q0.45,-0.09 0.91,-0.13l0.82,-0.04zM318.9,305.86L318.61,305.86l-0.21,0.04l0,1.81l0.38,0q0.76,0 1.09,-0.22t0.33,-0.72 -0.33,-0.68q-0.33,-0.22 -0.99,-0.22zM322.81,304.66l1.34,0l0,6.67l-1.34,0zM329.05,311.33q-0.18,-0.33 -0.43,-0.7l-0.54,-0.76q-0.28,-0.39 -0.59,-0.74t-0.61,-0.63l0,2.83l-1.35,0l0,-6.67l1.35,0l0,2.51q0.53,-0.61 1.04,-1.27l0.99,-1.24l1.6,0q-0.61,0.81 -1.24,1.55t-1.29,1.51q0.71,0.66 1.37,1.59t1.29,2.03zM335.6,311.33 L335.4,310.63 335.17,309.89l-2.31,0l-0.25,0.74 -0.2,0.7l-1.4,0l0.64,-1.99 0.58,-1.72 0.58,-1.53q0.28,-0.74 0.58,-1.42l1.29,0q0.3,0.68 0.58,1.42l0.56,1.53 0.59,1.72 0.64,1.99zM334,306.16 L333.88,306.57 333.68,307.14 333.42,307.88 333.15,308.69l1.7,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM341.47,304.66q0.16,0.31 0.35,0.79l0.43,1 0.43,1.11 0.41,1.07 0.4,-1.07 0.43,-1.11 0.43,-1q0.2,-0.48 0.36,-0.79l1.22,0l0.16,1.48 0.12,1.72q0.07,0.89 0.1,1.79l0.08,1.68l-1.3,0l-0.07,-2.12 -0.13,-2.31 -0.33,0.91 -0.4,0.98q-0.16,0.5 -0.35,0.94l-0.3,0.76l-0.94,0l-0.28,-0.76 -0.36,-0.94 -0.38,-0.98 -0.33,-0.91q-0.1,1.16 -0.13,2.31l-0.07,2.12l-1.32,0l0.08,-1.68 0.1,-1.79 0.13,-1.7 0.16,-1.5zM351.6,311.33 L351.37,310.63 351.15,309.89l-2.33,0l-0.23,0.74 -0.21,0.7l-1.39,0l0.63,-1.99 0.59,-1.72 0.56,-1.53q0.28,-0.74 0.59,-1.42l1.27,0q0.3,0.68 0.58,1.42 0.3,0.74 0.58,1.53l0.59,1.72 0.63,1.99zM349.98,306.16 L349.85,306.57 349.65,307.14 349.4,307.88 349.14,308.69l1.68,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM355.59,310.19q0.28,0 0.46,-0.06t0.3,-0.13q0.12,-0.11 0.16,-0.22 0.03,-0.13 0.03,-0.28 0,-0.33 -0.28,-0.55t-0.94,-0.46l-0.59,-0.26q-0.28,-0.17 -0.51,-0.37 -0.23,-0.24 -0.38,-0.55 -0.16,-0.33 -0.16,-0.79t0.16,-0.83 0.45,-0.63 0.69,-0.41q0.4,-0.13 0.91,-0.13 0.59,0 1.04,0.13 0.43,0.15 0.73,0.33l-0.4,1.18q-0.25,-0.15 -0.56,-0.26t-0.73,-0.11q-0.49,0 -0.69,0.15t-0.21,0.46q0,0.18 0.08,0.3t0.21,0.24l0.33,0.18 0.41,0.17q0.46,0.18 0.81,0.37 0.33,0.18 0.56,0.44t0.35,0.59 0.12,0.81q0,0.92 -0.59,1.46 -0.58,0.5 -1.75,0.5 -0.4,0 -0.73,-0.06 -0.31,-0.04 -0.56,-0.13l-0.43,-0.15 -0.28,-0.18 0.38,-1.18q0.26,0.17 0.66,0.3 0.38,0.13 0.96,0.13m6.96,-5.52l1.35,0l0,6.67l-1.35,0l0,-2.83l-2.24,0l0,2.83l-1.35,0l0,-6.67l1.35,0L360.31,307.19l2.24,0zM369.25,311.33 L369.05,310.63 368.82,309.89l-2.31,0l-0.25,0.74 -0.2,0.7l-1.4,0q0.33,-1.07 0.66,-1.99 0.28,-0.92 0.58,-1.72l0.56,-1.53q0.28,-0.74 0.58,-1.42l1.29,0q0.3,0.68 0.58,1.42l0.56,1.53 0.59,1.72 0.64,1.99zM367.65,306.16 L367.53,306.57 367.33,307.14 367.07,307.88 366.81,308.69l1.7,0l-0.26,-0.83 -0.25,-0.72 -0.2,-0.55zM373.21,304.59q1.35,0 2.06,0.54t0.71,1.66q0,0.7 -0.28,1.15 -0.3,0.42 -0.82,0.68l0.38,0.55 0.38,0.68 0.38,0.74 0.33,0.74l-1.48,0l-0.35,-0.66 -0.35,-0.65 -0.33,-0.61 -0.36,-0.52l-0.66,0l0,2.44l-1.34,0l0,-6.58q0.43,-0.09 0.89,-0.13zM373.29,305.86L373.04,305.86l-0.21,0.04l0,1.81l0.38,0q0.76,0 1.09,-0.22t0.33,-0.72 -0.33,-0.68q-0.33,-0.22 -0.99,-0.22zM377.2,304.66l1.35,0l0,6.67l-1.35,0zM383.43,311.33q-0.16,-0.33 -0.43,-0.7 -0.23,-0.37 -0.53,-0.76 -0.28,-0.39 -0.59,-0.74t-0.63,-0.63l0,2.83l-1.32,0l0,-6.67l1.32,0l0,2.51q0.53,-0.61 1.06,-1.27l0.99,-1.24l1.58,0q-0.61,0.81 -1.22,1.55t-1.29,1.51q0.71,0.66 1.37,1.59t1.29,2.03zM385.81,304.66l1.32,0l0,6.67l-1.32,0z"/>
  <path
      android:pathData="m242.26,297.93 l9.08,-2.44 -2.79,-4.97 15.62,-5.85l0,5.32l4.8,0l0,8.12l-26.18,0Z"
      android:fillColor="#008300"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M266.7,298.1l-19.03,0l19.03,-5.15zM262.51,290.08L254.05,290.08l8.73,-3.32zM251.43,291.47l1.92,3.49 12.65,-3.49z"
      android:fillColor="#fff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m397.7,297.93l-9.08,-2.44 2.79,-4.97 -15.62,-5.85l-0,5.32l-4.8,0l-0,8.12l26.18,0Z"
      android:fillColor="#008300"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M373.26,298.1l19.03,0l-19.03,-5.15zM377.45,290.08L385.92,290.08l-8.73,-3.32zM388.53,291.47l-1.92,3.49 -12.65,-3.49z"
      android:fillColor="#fff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M318.37,168.42A61.79,61.79 0,0 0,297.68 287.63l0,-6.81a55.42,55.42 0,0 1,21.99 -106.12l0.44,0a55.42,55.42 0,0 1,22.34 106.12l0,6.81A61.7,61.7 0,0 0,320.02 168.42z"
      android:strokeWidth="1.05"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="m336.96,274.63 l0.09,9.25c-5.59,0.87 -11.78,6.63 -13.44,7.68s-4.36,3.32 -5.85,3.14c-2.53,-0.35 -0.7,-2.36 -0.7,-2.36s-1.75,2.09 -2.97,1.13c-1.22,-0.79 0.7,-2.36 0.7,-2.36s-0.61,0.87 -1.92,0.44c-1.22,-0.35 -0.35,-1.75 -0.35,-1.75s-3.32,1.75 -3.93,1.13c-0.87,-1.13 0.35,-2.18 0.35,-2.18l7.77,-6.55s-5.06,1.83 0,0c4.28,-1.75 5.5,0.7 7.42,0.52a24.44,24.44 0,0 0,8.9 -4.45c1.48,-1.48 1.05,-3.05 2.18,-3.49s1.75,-0.17 1.75,-0.17z"
      android:strokeWidth="0.79"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m302.92,275.33 l-0.09,8.55c5.24,0.7 11.08,6.28 12.65,7.33 1.48,0.96 4.1,2.62 5.5,2.44 2.27,-0.26 1.75,-1.13 1.75,-1.13s1.05,0.52 2.44,0 0.7,-1.48 0.7,-1.48 2.09,0.61 2.97,-0.52c0.87,-0.96 -0.87,-2.01 -0.87,-2.01s0.87,-0.61 -0.52,0.17 -4.19,-2.44 -6.98,-2.62c-2.88,0 -3.14,2.79 -4.89,1.48 -1.83,-1.4 1.4,-3.67 2.53,-4.01 4.45,-1.48 8.64,0.35 3.23,-1.75 -2.79,-1.05 -4.97,0.96 -6.81,0.87 -1.92,-0.09 -6.63,-2.62 -7.85,-4.01 -1.4,-1.4 -1.05,-2.79 -2.09,-3.23q-1.57,-0.35 -1.75,-0.09z"
      android:strokeWidth="0.7"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m325.96,291.04 l-3.75,-1.57m0.96,3.23 l-4.36,-2.62"
      android:strokeWidth="0.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M295.76,270.88c-10.82,5.24 -11.35,-8.55 -16.23,-8.38 9.16,-6.11 7.68,5.24 16.23,8.38zM291.57,255.69c-5.41,9.86 7.24,11.17 6.81,15.97 6.2,-8.38 -4.36,-7.68 -6.81,-15.97z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M286.34,259.53c-13.35,2.27 -9.69,-9.25 -15.27,-10.21 11.78,-3.49 6.72,5.41 15.27,10.21z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M284.94,245.91c-7.33,8.64 3.23,9.08 2.01,13.61 7.59,-6.98 -0.26,-6.72 -2.01,-13.61zM277.26,247.49c-13.27,-1.92 -4.89,-15.45 -8.9,-18.33 10.91,0.79 3.84,12.65 8.9,18.33z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M282.32,230.38c-12.22,5.85 0.09,12.13 -4.71,17.45 12.04,-0.61 1.4,-8.47 4.71,-17.28zM275.52,230.82c-14.23,-6.46 0.61,-18.68 -1.92,-23.13 9.69,9.86 -5.06,13.53 1.92,23.13z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M284.59,214.41c-12.22,1.57 -6.11,12.83 -8.64,16.84 9.6,-2.18 1.92,-7.85 8.73,-16.84zM278.66,211.01c-6.89,-10.47 4.63,-12.83 5.41,-18.15 3.32,13.79 -4.54,9.34 -5.41,18.15z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M292.45,202.28c-11.35,-3.23 -9.25,8.2 -13.09,10.91 9.6,1.57 7.59,-7.59 13.09,-10.91zM287.21,197.39c-2.27,-9.08 2.27,-7.59 7.42,-11.61 2.79,7.33 -4.1,6.46 -7.42,11.61zM301.7,195.99c-8.73,-6.46 -9.69,2.27 -14.23,3.49 8.38,4.97 8.73,-2.44 14.23,-3.4zM296.46,191.63c0.26,-3.93 5.5,-9.08 10.91,-7.33 -0.26,6.55 -6.55,8.2 -10.91,7.42z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M344.2,270.88c10.82,5.24 11.35,-8.55 16.23,-8.38 -9.16,-6.11 -7.68,5.24 -16.23,8.38zM348.39,255.69c5.41,9.86 -7.24,11.17 -6.81,15.97 -6.2,-8.38 4.36,-7.68 6.81,-15.97z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M353.62,259.53c13.35,2.27 9.69,-9.25 15.27,-10.21 -11.78,-3.49 -6.72,5.41 -15.27,10.21z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M355.02,245.91c7.33,8.64 -3.23,9.08 -2.01,13.61 -7.59,-6.98 0.26,-6.72 2.01,-13.61zM362.7,247.49c13.27,-1.92 4.89,-15.45 8.9,-18.33 -10.91,0.79 -3.84,12.65 -8.9,18.33z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M357.64,230.38c12.22,5.85 -0.09,12.13 4.71,17.45 -12.04,-0.61 -1.4,-8.47 -4.71,-17.28zM364.45,230.82c14.23,-6.46 -0.61,-18.68 1.92,-23.13 -9.69,9.86 5.06,13.53 -1.92,23.13z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M355.37,214.41c12.22,1.57 6.11,12.83 8.64,16.84 -9.6,-2.18 -1.92,-7.85 -8.73,-16.84zM361.3,211.01c6.89,-10.47 -4.63,-12.83 -5.41,-18.15 -3.32,13.79 4.54,9.34 5.41,18.15z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M347.52,202.28c11.35,-3.23 9.25,8.2 13.09,10.91 -9.6,1.57 -7.59,-7.59 -13.09,-10.91zM352.75,197.39c2.27,-9.08 -2.27,-7.59 -7.42,-11.61 -2.79,7.33 4.1,6.46 7.42,11.61zM338.26,195.99c8.73,-6.46 9.69,2.27 14.23,3.49 -8.38,4.97 -8.73,-2.44 -14.23,-3.4zM343.5,191.63c-0.26,-3.93 -5.5,-9.08 -10.91,-7.33 0.26,6.55 6.55,8.2 10.91,7.42z"
      android:strokeWidth="0.35"
      android:fillColor="#008300"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m309.3,180.28 l0.52,3.06 1.63,-0.33q0.65,-0.07 0.85,-0.33t0.2,-0.98l0.2,0l0.46,2.6l-0.2,0l-0.26,-0.65 -0.39,-0.26l-0.72,0l-1.69,0.33 0.39,2.54 0.2,0.65 0.2,0.07l0.39,0l1.3,-0.2 0.91,-0.26 0.52,-0.46q0.33,-0.39 0.52,-1.17l0.26,0l-0.33,1.95 -5.86,1.04 -0.07,-0.2l0.26,0l0.46,-0.26q0.2,-0.13 0.2,-0.33l0,-0.78l-0.91,-4.95q0,-0.72 -0.26,-0.85 -0.26,-0.2 -0.72,-0.13l-0.26,0l0,-0.13l5.86,-1.04 0.33,1.69l-0.2,0l-0.39,-0.78 -0.52,-0.26l-0.78,0zM320.49,183.99l-2.99,0l-0.52,1.24 -0.2,0.65q0,0.2 0.2,0.33t0.72,0.2l0,0.2l-2.41,0l0,-0.2q0.46,0 0.65,-0.2l0.65,-1.17 2.6,-6.38l0.26,0l2.73,6.44q0.33,0.78 0.59,0.98t0.72,0.26l0,0.26l-3.06,0l0,-0.2q0.46,0 0.65,-0.2 0.13,-0.13 0.13,-0.33 0,-0.26 -0.2,-0.78zM320.36,183.53 L319.06,180.41 317.7,183.6zM331.95,179.96 L331.69,182.56l-0.2,0a3.25,3.25 70.7,0 0,-0.65 -1.82,2.6 2.6,114.67 0,0 -1.5,-0.78 2.6,2.6 0,0 0,-1.43 0.13,2.6 2.6,82.86 0,0 -1.24,1.04q-0.46,0.78 -0.65,2.08a4.55,4.55 0,0 0,0 1.82q0.2,0.91 0.78,1.3 0.65,0.65 1.5,0.72 0.65,0.13 1.3,-0.07t1.5,-1.04l0.2,0.13q-0.78,0.91 -1.63,1.3 -0.85,0.33 -1.95,0.13 -1.89,-0.33 -2.6,-1.95a3.9,3.9 55.84,0 1,-0.39 -2.6q0.2,-1.11 0.85,-1.95t1.69,-1.3a3.9,3.9 66.05,0 1,3.64 0.52l0.26,0.2 0.33,-0.07 0.26,-0.39z"/>
  <path
      android:pathData="M317.93,194.34q-1.31,0 -2.53,0.35a11.35,11.35 0,0 1,-6.11 5.06l-0.52,0.17a11.35,11.35 0,0 1,-8.47 0.26,36.65 36.65,0 0,0 -6.63,5.67 11.35,11.35 0,0 1,-1.75 8.29,11.35 11.35,0 0,1 -6.28,5.59q-1.31,4.28 -1.48,8.73c1.75,1.31 3.23,3.84 3.84,6.89l0.17,0.61c0.61,3.32 0,6.46 -1.22,8.29q1.75,4.1 4.36,7.59c2.09,-0.17 4.89,0.87 7.33,2.71l0.44,0.44c2.62,2.18 4.19,4.89 4.36,7.16q4.01,2.09 8.47,3.05c1.57,-1.48 4.36,-2.62 7.51,-2.62l0.61,0c3.4,0 6.37,1.05 8.03,2.62q4.45,-0.96 8.38,-3.05a11.35,11.35 0,0 1,4.36 -7.24,12.22 12.22,0 0,1 7.85,-3.05q2.62,-3.49 4.36,-7.42a11.35,11.35 0,0 1,-1.31 -7.77l0,-0.52q1.22,-5.24 4.1,-7.51a35.78,35.78 0,0 0,-1.48 -8.73,11.35 11.35,0 0,1 -5.93,-5.15l-0.35,-0.52c-1.75,-2.97 -2.27,-6.11 -1.75,-8.29a34.04,34.04 0,0 0,-6.89 -5.85,11.35 11.35,0 0,1 -7.85,0l-0.61,-0.26a12.22,12.22 0,0 1,-6.63 -5.24q-2.09,-0.26 -4.36,-0.26l-2.09,0zM324.91,265.81"
      android:fillColor="#ff0000"/>
  <path
      android:pathData="M350.66,230.03a30.63,30.63 0,1 1,-61.35 0,30.63 30.63,0 0,1 61.35,0z"
      android:strokeWidth="0.35"
      android:fillColor="#fbf014"
      android:strokeColor="#000"/>
  <path
      android:pathData="M348.04,230.03a28.1,28.1 0,1 1,-56.2 0,28.1 28.1,0 0,1 56.2,0z"
      android:strokeWidth="0.35"
      android:fillColor="#fbf014"
      android:strokeColor="#000"/>
  <path
      android:pathData="m311.65,216.24 l9.25,1.66 -1.22,9.25 -7.85,5.41 -4.36,-4.8 0.26,-7.59z"
      android:strokeWidth="0.35"
      android:fillColor="#0087ff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m309.03,223.05 l-3.67,0.17l-0.35,0l0,0.17l-0.09,0l-0.09,0.26 -0.35,0.17l0,0.17l-0.26,0.17 -0.35,0.17l-0.09,0l-0.09,-0.35l-0.52,0l-0.26,0.09l0,-0.44l-0.09,-0.09l0,-0.87l0.17,0l0,-0.79l0.09,-0.17l0,-0.17l0.26,-0.26 0.26,-0.09l0,-0.26l0.09,-0.26 0.09,-0.09 -0.09,-0.17 -0.26,0.17 -0.26,-0.17l0,-0.52l0.26,-0.17 0.09,0.09 0.17,-0.7 0.26,-0.26 -0.17,-0.44 0.17,-0.26 -0.09,-0.35 0.79,-0.26l0,-0.26l0.09,-0.35 0.35,-0.09 0.26,-0.17 0.17,0.44l0,0.26l0.17,0l0.17,-0.35 0.17,-0.09l0,-0.26l0.52,-0.61 0.17,-0.17 0.35,-0.17l0.35,0l0.26,-0.35l0,-0.35l0.17,0l0.17,-0.17 -0.09,-0.26l0,-0.61l-0.17,0l0,-0.17l-0.17,0.26 -0.17,-0.09l0,-0.26l-0.52,0l-0.17,-0.35 -0.17,0.09l0,0.26l-0.17,-0.09l0,-0.35l-0.35,0l0.09,-0.35 0.35,-0.61l0,-0.35l-0.35,-0.35 0.26,-0.7 0.35,-0.35l0,-0.44l-0.26,0.09 0.35,-0.44 0.61,-0.26l0.35,0l0.52,0.35 0.17,-0.09l0.26,0l0.44,-0.35 0.44,0.35l0,0.26l0.35,0l0.09,0.17l0.26,0l0,-0.26l0.61,-0.26 0.87,-0.09 0.79,-0.44 0.44,0.35l0.96,0l1.48,-1.31 4.54,1.05 -0.26,7.85 -4.71,2.62 -0.26,-0.17 -0.09,0.09l-0.09,0l0,-0.44l-0.26,0l0,0.52l-0.35,0l-0.35,-0.17l0,-0.17l0.35,0l0,-0.09l-0.26,-0.09l-0.26,0l0,-0.17l0.26,-0.17l0,-0.09l-0.26,0.17 -0.17,-0.09 -0.17,0.09 0.17,0.26 -0.26,0.17l0,0.17l-0.35,-0.09l0,0.17l0.17,0l-0.52,0.35 -0.17,-0.17l0,-0.09l-0.26,0.17l-0.35,0l0,-0.09l0.35,-0.09 -0.09,-0.09l-0.26,0l0,-0.26l-0.09,0.26l-0.17,0l0,0.44l-0.35,0l-0.09,-0.17s-0.26,0 -0.17,0.17l0,0.26l-0.26,-0.17l-0.17,0l0,0.26l-0.17,0l-0.35,-0.17 0.17,0.26l-0.35,0l-0.17,0.09 0.17,0.17 0.09,0.17 0.17,0.17 -0.09,0.09l-0.09,0l-0.26,0.26l-0.17,0l-0.09,0.26 -0.17,0.09 0.09,0.17l0.09,0l0,0.17l-0.09,0l-0.35,0.35l0,0.09l0.26,0z"
      android:strokeWidth="0.35"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m315.31,223.14 l10.04,5.5 0.09,0.79 -0.35,0.17 0.09,0.35 0.35,0.09 0.09,0.35 3.93,2.79 0.26,-0.17 0.35,0.17 0.09,-0.35 0.26,-0.26 0.44,-0.96 -0.26,-0.09 0.17,-0.35l0.17,0l0.35,-0.44l0,-0.35l-0.17,-0.26 0.26,0.09 0.35,-0.79 0.35,-0.09 0.09,-0.61 0.17,-0.17s-0.17,-0.7 0,-0.96 1.13,-0.26 1.13,-0.26l0.52,-0.61l0,-0.17l0.35,-0.26 -0.35,-0.61 0.7,0.17 0.52,-0.17l0.35,0l0.52,-0.7l0,-0.26l-1.66,-2.09l0,-10.21l0.96,-0.87 1.57,-2.18 -0.44,-0.17 -1.57,0.17 -0.44,-0.52l-0.17,0l-0.09,-0.35 -0.35,-0.17 -2.62,1.22 -0.09,0.44 -0.61,0.52l0,0.35l-0.17,0l-0.17,-0.09 -2.71,-0.61 -0.17,0.17l-0.87,0l-3.23,-2.36l-2.36,0l-0.52,-0.17l0,-0.26l-4.36,0l-0.87,0.87 0.17,1.13l0.44,0l0.17,0.52l0.44,0l-0.09,0.52l-0.09,0l0,0.61l0.17,0.26 0.26,0.17l0,0.44l0.52,0.26 0.09,0.61 0.35,0.17l0,0.26l-0.17,0.35 0.35,0.52 -0.09,0.35 0.17,0.44l0,0.17l-0.09,0.52 -0.26,0.17 -0.09,0.35l-0.17,0l0,0.7l-0.44,0l-0.17,0.26 -0.26,-0.09l0,0.61l-0.26,0.35l-0.26,0l0,0.26l-0.35,0.17 -0.09,0.44l0,0.44l-0.44,0.17l0,0.79l0.26,0.09l0.17,0l-0.09,0.26 0.26,0.09 0.17,0.44 0.35,-0.26l0,-0.26l0.44,0l0.52,-0.26 0.17,0.35 0.17,0.26l-0.35,0l-0.09,0.17l-0.26,0l-0.44,-0.09l0,0.17l0.17,0.17 -0.26,0.17 -0.09,-0.17 -0.26,0.09l-0.17,0l-0.26,-0.17l0,0.35l-0.17,0l-0.17,0.44 0.26,0.26 0.26,0.17 -0.26,0.35zM330.67,242.16 L331.02,241.9l0,-0.17l0.35,0l0.17,-0.44l0,0.44l-0.17,0.17l0,0.26l-0.17,0l0,0.26l-0.09,-0.17zM329.62,237.27l0,-0.61l-0.09,-0.09 0.35,-0.26l0,-0.35l0.44,-0.17l0,0.35l-0.26,0l0,0.52l0.17,0.26l0,0.26l0.26,0.17 0.26,0.35l0,0.26l-0.26,0.09 -0.35,-0.44 -0.35,0.09zM330.93,235.01l0,-0.35l0.17,-0.35 -0.26,-0.7l0.35,0l0.09,0.35l0.17,0l0,-0.26s0.17,-0.09 0.17,0l-0.09,0.79 0.09,0.35 -0.17,0.26 -0.26,0.26z"
      android:strokeWidth="0.35"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m332.94,249.14 l-1.13,0.87 -1.57,0.7 -0.44,0.26l-0.79,0l-0.61,0.17 -0.35,0.17 -0.44,0.44 -0.7,-0.44l-0.87,0l-0.26,0.26l0,0.44l-0.61,0.26l-0.26,0l-0.26,0.17 -0.87,-0.09 -0.35,-0.35 -0.44,0.09 -0.7,0.52 -0.35,-0.09l0,-0.17l-0.26,0l-0.17,0.17 -0.44,-0.09l0,-0.35l-0.26,0l-0.35,-0.52l-0.44,0l-0.09,0.26 -0.35,0.26 -0.35,-0.09l-1.31,0l-0.35,-0.61l-0.35,0l-0.35,-0.87l0,-0.44l0.17,-0.17l0,-0.52l-0.35,-0.26 0.09,-0.87 -0.09,-0.17 -0.09,-0.35 -0.61,-0.61l0,-0.26l-0.7,-0.52l-0.26,0l0,0.52l-0.35,-0.09 -0.17,-0.26 -0.87,0.17 -0.35,-0.44 -0.44,0.09 -0.35,-0.26l-0.44,0l-0.35,-0.35l-0.79,0l-0.17,-0.52l-0.52,0l-0.17,-0.09l-0.61,0l0,-0.26l-0.7,-0.09 -0.35,-0.26l0,-0.44l-0.52,-0.26 -0.44,0.09 -0.17,-0.52 -0.44,-0.35 -0.09,-0.52 -0.61,-0.87l0,-0.35l-0.35,-0.35l0,-0.7l-0.26,-0.35l0,-0.35l0.17,0l0,-0.26l-0.44,-0.35 -0.17,-0.35 -0.7,-0.52l-0.52,0l-0.61,-0.7l0,-0.35l0.26,-0.35l0.35,0l0.09,-0.26l0,-0.26l-0.61,-0.87l0,-0.44l0.26,-0.17 -0.09,-0.52 -0.52,-0.52l0,-0.52l0.09,-0.44l0,-0.17l0.52,-0.17 0.26,-0.17 0.26,-0.26l0,-0.26l0.52,-0.17l0,-0.26l0.44,-0.52l0,-0.26l0.26,-0.44 0.61,-0.26l0,-0.17l0.44,-0.26l0,-0.87l-0.17,-0.09l-0.26,0l-0.26,-0.09l-0.44,0l0,-0.26l0.26,-0.35 -0.17,-0.17 0.26,-0.52 0.87,-0.09l0,-0.61l0.09,-0.26 -0.35,-0.44l0,-0.87l-0.17,-0.26l0,-0.26l-0.44,-0.26 -0.35,-0.61l0.52,0l0.26,-0.26l3.05,0l0.09,0.26l0,0.35l-0.17,0.44 -0.09,0.26l0,0.17l-0.09,0.35l0,0.35l-0.17,0.17l0,0.26l-0.17,0.17l0.26,0l0,0.44l-0.17,0.35l0.7,0l-0.26,0.52 -0.09,0.35 0.09,0.52 0.35,-0.35 0.26,0.26l0,-0.44l0.09,-0.35 0.35,0.17 0.09,-0.17 -0.17,-0.35 0.35,-0.35 0.35,0.44 0.26,0.09 0.17,0.44 0.26,-0.52 0.09,0.35l0.35,0l0,0.35l0.26,0.35 -0.35,0.17l0,0.44l0.26,0l0,-0.44l0.35,0l-0.17,-0.44 -0.09,-0.35 0.35,-0.52l0.35,0l0.17,0.44l0.61,0l0.52,-0.52 0.7,-0.26 -0.17,-0.44l-0.17,0l-0.26,0.26 -0.79,-0.09 -0.26,-0.17 -0.17,0.17l-0.26,0l-0.7,-0.26l0,-0.26l-0.17,-0.09 0.35,-0.17 0.17,0.26l0.17,0l0.26,-0.09 0.35,0.26 0.52,-0.09l0,-0.26l-0.35,-0.09l0,-0.26l0.61,0l0.09,-0.17 0.44,-0.17 -0.26,-0.35l0,-0.17l0.87,0.17 -0.44,-0.35 0.09,-0.35 0.52,0.35l0,-0.17l-0.35,-0.26 0.52,-0.52 10.12,5.5 0.09,0.79 -0.35,0.26l0,0.35l0.44,0l0,0.35l3.93,2.62 0.26,0.44 -0.26,0.26 -0.17,0.35 -0.09,0.61 -0.17,0.35l0,0.52l-0.35,0.35 -0.26,0.61l0,0.44l0.26,0.35 -0.17,0.52 0.44,0.35l0.26,0l0.79,0.87 0.35,0.35 0.17,0.44 -0.17,0.44 -0.26,0.09 -0.26,0.87l0,0.52l0.35,0.35 -0.09,0.35 -0.17,0.52 -0.17,0.52 0.17,0.52 0.17,0.44 0.17,0.17l0,0.35l-0.17,0l0.26,0.44 0.35,0.17l0,0.7l-0.09,0l0,0.17l0.35,0.26 0.17,0.87 -0.17,0.26l0,0.17l0.52,0l0.17,0.44 0.35,-0.09 0.61,0.26 0.26,0.44z"
      android:strokeWidth="0.35"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M301.26,226.98c-0.26,-0.26 0.87,-0.87 0.7,-1.31q0,-1.13 1.4,-1.48l0.52,-0.35c0.26,0.17 0.26,0.61 0.52,0.35s0.87,-1.13 1.31,-0.96c-0.26,0.35 0.26,1.05 0.61,1.31l0.52,1.66q-0.17,0.79 -1.22,0.52c-0.7,-0.26 -0.87,0.26 -1.4,-0.17 -0.09,0.61 0,1.13 -0.79,1.4q-0.79,0 -0.87,-0.26 -0.61,-0.7 -1.05,0 -0.17,-0.17 -0.26,-0.7"
      android:strokeWidth="0.44"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M301.52,227.67c0.44,-0.96 0.96,0.09 1.13,0.09 1.31,0.17 1.31,-0.09 1.57,-1.31 0.7,0.52 0.44,0 1.92,0.35 -0.26,0 -0.87,0.7 -0.44,0.7 -0.35,0.96 0.44,0.96 0.96,0.87 -0.26,0.35 0.44,0.52 -0.26,0.87 -0.44,0.26 -0.44,0 -0.44,0.61l-0.52,0.09c-0.09,0.17 0.26,0.52 0.17,0.52q0,0.17 -0.52,0.7t-1.13,0.87l-0.61,0.17 -0.87,-1.57q0,-0.7 -0.26,-1.4l0,-0.79q-0.44,-0.35 -0.7,-0.87"
      android:strokeWidth="0.44"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
</vector>
