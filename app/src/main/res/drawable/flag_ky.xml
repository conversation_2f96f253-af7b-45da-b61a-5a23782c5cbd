<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#006"/>
  <path
      android:pathData="M0,0h320v240H0z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5zM120,150.5 L123,168 27,240L0,240zM320,0v1.5l-124.5,94 1,-22L295,0zM0,0l119.5,88h-30L0,21z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M120.5,0v240h80V0zM0,80v80h320V80z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,96.5v48h320v-48zM136.5,0v240h48V0z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M555.59,181.83l0,81.39c0,70.58 -33.37,94.8 -78.07,113.97 -44.7,-19.17 -78.42,-43.39 -78.42,-113.97l0,-81.47l156.5,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="8.37"
      android:fillColor="#fff"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M350.55,370.56c2.53,6.62 20.48,15.42 41.39,4.18 14.2,-8.02 23.35,-19.17 32.15,-39.21 2.44,-5.05 3.75,-11.33 -2.09,-15.07 -4.62,-3.4 -12.2,-7.15 -15.51,-10.46q-2.61,-2.44 -4.53,-4.18 1.31,1.13 2.44,2.88c8.37,10.02 -0.35,24.66 -9.58,33.46 -6.71,6.27 -18.73,21.7 -36.34,7.06 -3.75,-3.31 -12.55,10.46 -7.84,21.35zM604.47,370.56c-2.96,6.62 -20.91,15.42 -41.39,4.18 -14.12,-8.02 -23.35,-19.17 -32.5,-39.21 -2.09,-5.05 -3.4,-11.33 2.44,-15.07 4.62,-3.4 12.2,-7.15 15.51,-10.46 1.66,-1.74 2.88,-2.96 4.53,-4.18q-1.31,1.13 -2.44,2.88c-8.37,10.02 0.35,24.66 9.15,33.46 6.71,6.27 19.17,21.7 36.77,7.06 3.75,-3.31 12.11,10.46 7.84,21.35z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#fcea83"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.51,406.46c33.37,0 84.78,-15.07 112.41,-67.7 4.53,-8.71 0,-13.33 -5.49,-16.21a63.61,63.61 0,0 1,-16.73 -12.2c6.71,6.71 5.4,11.33 0.87,18.82 -21.35,35.55 -50.54,53.5 -91.06,53.5s-69.71,-17.95 -91.06,-53.5c-4.53,-7.49 -5.84,-12.11 0.44,-18.73a57.51,57.51 0,0 1,-16.73 12.11c-5.4,2.88 -10.02,7.49 -5.4,16.21 27.53,52.63 79.29,67.7 112.75,67.7z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#fcea83"
      android:strokeColor="#000"/>
  <path
      android:pathData="M400.22,335.88c-0.87,-4.18 -6.19,-2.53 -7.84,2.53a59.25,59.25 0,0 1,-5.93 -9.24c-4.97,-7.49 -5.84,-12.55 1.31,-19.61 3.31,-3.31 10.02,-7.15 14.55,-2.96 9.58,7.93 4.62,20.91 -2.09,29.28zM554.37,335.88c0.87,-4.18 6.71,-2.53 8.37,2.53q3.22,-4.36 5.84,-9.24c4.97,-7.49 5.84,-12.55 -1.74,-19.61 -2.88,-3.31 -9.58,-7.15 -14.55,-2.96 -9.24,7.93 -4.62,20.91 2.09,29.28zM350.55,370.56c-4.18,-6.27 -7.49,-22.66 -8.37,-26.75 -1.22,-3.75 -1.66,-7.15 2.96,-8.71 4.62,-1.74 8.71,4.1 10.8,9.15s5.49,12.46 9.24,21.7c-9.24,-5.4 -16.73,-1.74 -14.64,4.62zM604.47,370.56c3.75,-6.27 7.06,-22.66 8.37,-26.75 0.87,-3.75 1.66,-7.15 -2.96,-8.71 -4.62,-1.74 -8.71,4.1 -10.8,9.15s-5.49,12.46 -9.24,21.7c9.24,-5.4 16.29,-1.74 14.64,4.62z"
      android:strokeLineJoin="round"
      android:strokeWidth="8.37"
      android:fillColor="#c8102e"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M445.36,181.39c-5.4,1.22 -9.15,1.66 -14.2,-2.96 -3.31,-3.31 -2.53,-9.15 0.87,-11.33 3.75,-2.44 7.06,-4.1 11.68,-2.88s16.73,12.55 13.77,12.98c-2.96,0 -7.49,2.88 -12.11,4.18zM458.25,161.35c1.74,-1.31 3.83,-2.09 9.24,-2.61 5.05,-0.35 8.71,-0.35 10.02,3.49 1.74,4.53 2.96,9.93 6.27,12.46 -2.09,1.22 -12.55,2.53 -16.29,0.44 -4.97,-2.53 -6.27,-12.2 -9.15,-13.77zM494.24,161.79c1.66,-1.31 3.75,-1.31 9.15,-0.87s7.49,0.87 8.71,4.62c1.31,4.18 0,9.58 2.61,13.33 -2.61,0.87 -10.89,0.87 -13.85,-2.09 -4.53,-3.31 -4.18,-12.98 -6.62,-15.07z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M519.25,164.23c-3.75,-0.87 -6.27,-1.22 -7.84,0 2,2.53 0.35,12.55 4.53,15.86 2.88,2.09 10.46,2.61 12.11,-2.09 2.09,-4.18 2.09,-10.46 -8.71,-13.77zM476.29,160.04c1.22,-1.22 3.75,-1.66 8.71,-1.66 5.49,0 8.71,0.87 10.46,4.18 1.74,4.18 1.22,9.58 4.18,12.98 -1.22,1.66 -12.55,1.66 -15.86,-0.87 -4.62,-2.96 -5.05,-12.55 -7.49,-14.64zM440.83,163.79c0,-2.09 2.88,-2.44 10.46,-2.88 7.41,-0.87 7.84,-0.44 10.37,4.97 1.22,2.96 2.88,7.93 6.27,9.58 -9.24,2.18 -12.2,3.4 -17.17,0.44a25.27,25.27 0,0 1,-8.71 -9.15c-0.44,-1.22 -0.44,-2.53 -1.31,-2.96z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#073163"
      android:strokeColor="#000"/>
  <path
      android:pathData="M441.61,126.67q-4.44,-0.09 -7.15,-3.31c-2.88,-3.83 -10.8,-5.49 -14.2,-2.09 -1.22,0.87 -2.88,0.87 -4.1,1.22 -2.09,0 -4.62,2.09 -3.83,6.27 1.74,-1.31 2.09,0 3.4,0 0.87,-0.44 0.44,0.44 0.44,1.22 0,2.09 4.53,6.27 8.71,5.84 4.62,0 6.71,1.74 7.49,2.61 1.31,1.13 0.44,0.78 1.31,2.88 1.22,1.22 1.66,1.66 -0.44,3.31a8.45,8.45 135,0 0,0 12.98c5.4,4.1 19.17,10.46 34.68,-1.74 14.99,-11.68 38.34,-4.97 40.08,-0.87 2.09,4.27 2.88,6.71 3.31,8.8 0.44,1.74 2.53,3.4 5.49,2.96 2.88,0 6.19,1.22 7.84,2.53 2.18,1.22 5.05,0.87 4.27,-2.96 -1.74,-11.68 -8.8,-13.33 -9.58,-18.73 4.1,0.35 6.97,0.35 8.71,-1.31 1.22,-1.66 2.88,-4.62 4.62,-5.4 1.22,-0.87 1.22,-1.74 -0.44,-1.31 -2.09,0.44 -3.31,1.31 -7.15,0.87 -19.17,-2.09 -73.02,-16.73 -83.48,-13.77z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#059334"
      android:strokeColor="#000"/>
  <path
      android:pathData="M526.83,140.87a30.5,30.5 0,0 0,-8.02 8.37c-4.1,5.4 -16.64,10.8 -32.15,10.8 -14.99,0 -24.14,-2.44 -30.85,-7.84a53.15,53.15 127.44,0 0,-9.24 -5.93c-4.53,-2.09 -5.4,-6.71 -4.18,-10.46 -1.22,2.09 -2,4.18 -4.53,3.75 -1.74,0 -5.05,0 -4.18,1.74 -0.44,-0.87 -0.44,-2.09 -0.87,-2.61l-0.87,-0.78c1.31,0.87 4.27,-1.66 6.36,-1.66 1.66,0.44 3.75,-1.66 3.31,-4.18l-1.22,-5.4c3.31,-1.74 47.14,6.27 55.94,7.06 8.71,0.44 33.81,4.18 30.5,7.15z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M447.45,125.01c4.18,-4.18 9.15,-6.27 14.2,-5.05l1.22,0c2.61,-2.09 8.37,-2.53 11.76,-0.87l1.66,0c4.97,-1.66 14.55,0 17.95,3.4l1.22,0.87c11.33,-0.87 17.95,4.1 25.88,12.11 0.87,0.87 2.09,2.44 4.18,2.88 2.53,0.44 2.96,1.74 0.87,2.96a33.11,33.11 0,0 0,-8.71 7.84c-3.83,5.05 -15.95,10.11 -31.02,10.11 -14.55,0 -23.35,-2.61 -30.06,-7.58 -3.75,-2.88 -6.62,-4.53 -8.71,-5.84 -2.53,-0.87 -5.84,-4.18 -4.62,-7.49 0.87,-3.31 -1.22,-7.49 -2.88,-11.68 1.22,-0.87 4.53,-2.09 6.19,-1.22z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#059334"
      android:strokeColor="#000"/>
  <path
      android:pathData="M521.34,135.47c-7.84,-8.02 -14.55,-12.98 -25.88,-12.2 0,0.44 0,1.74 -0.44,2.61a57.51,57.51 0,0 1,20.48 15.42c1.74,-1.74 4.62,-3.75 5.84,-5.84zM494.24,122.49c-3.4,-3.31 -12.98,-5.05 -17.95,-3.31 0,1.66 0,4.18 -0.87,5.84 2.96,1.22 5.4,4.18 7.93,7.06 1.74,-0.44 5.84,-1.66 7.93,-2.09a38.34,38.34 0,0 0,2.96 -7.49zM474.64,119.09c-3.4,-1.66 -9.24,-1.22 -11.76,0.87 -0.87,1.74 -1.22,5.05 -1.22,6.71 1.74,0 4.18,1.22 4.97,2.09 2.61,-0.87 5.93,-2.53 7.15,-3.75 0.44,-1.74 1.22,-4.18 0.87,-5.84zM447.45,125.01c4.18,-4.18 9.15,-6.27 14.2,-5.05 -0.87,1.31 -1.22,3.75 -1.22,6.27 -4.62,-0.44 -10.46,0.87 -12.98,2.53z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#000"/>
  <path
      android:pathData="M513.85,142.09a55.77,55.77 56.82,0 0,-19.17 -14.64c0,0.44 -0.87,2.09 -1.31,3.4 2.53,3.75 7.49,12.11 9.58,16.73 2.09,-0.87 7.93,-3.4 10.89,-5.49zM482.13,151.33a155.97,155.97 0,0 0,1.22 -17.17c2.09,-0.44 6.27,-1.66 7.93,-2.09 2.09,3.4 7.15,12.2 9.24,16.29 -5.05,1.22 -13.42,3.31 -18.39,2.96zM467.49,131.29c2.09,-0.87 5.05,-2.61 7.06,-4.18 1.74,1.22 5.05,4.18 6.27,5.84 0,3.75 -1.22,14.64 -1.66,18.3 -3.31,0 -8.71,-0.78 -11.68,-2.44 0.44,-6.27 0.87,-13.33 0,-17.51zM446.58,136.25c0.44,-0.87 0.87,-3.75 0.87,-4.97 3.31,-2.09 11.68,-4.18 17.95,-0.87 0.44,4.18 0,14.64 -0.44,17.17a83.65,83.65 75.93,0 0,-18.3 -11.33z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#000"/>
  <path
      android:pathData="M425.75,128.67c-0.87,0 -1.31,-2.44 -2.96,-2.44 -0.44,0 -1.66,0.44 -2.09,0q0.09,1.39 0.87,2.09c0.44,1.22 2.53,0.44 4.18,0.44z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M425.75,135.82c4.97,0 5.84,-6.27 8.37,-6.62m-5.49,7.06c3.75,1.74 6.71,-7.06 9.58,-6.71m-17.08,-4.1c1.22,0 2.09,-1.74 4.18,0.35q1.48,1.31 2.96,1.31m-0.87,4.53c-1.74,2.61 -4.62,4.18 -6.27,3.4m-0.87,-13.77c-1.22,0.87 -2,2.44 -2.88,3.75m-1.22,2.44l0,2.09"
      android:strokeLineJoin="round"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460.87,121.27c-8.8,-6.71 -19.26,-12.98 -28.84,-14.2a243.98,243.98 0,0 1,28.41 22.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M465.83,114.99c-10.46,-7.15 -22.57,-15.07 -34.24,-15.86a108.05,108.05 111.33,0 1,28.75 24.22m18.82,-20.04a81.04,81.04 86.54,0 1,20.48 -28.06c-4.18,8.8 -8.71,26.75 -10.89,33.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M488.4,101.23A41.83,41.83 0,0 1,509.66 79.01a88.01,88.01 0,0 0,-15.86 30.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M492.15,109.5a74.07,74.07 61.38,0 1,32.94 -27.53c-10.46,10.46 -25.88,29.63 -29.63,37.21m2.53,-0.44c7.49,-4.18 22.57,-10.89 33.37,-8.71 -6.62,2.44 -26.66,11.59 -35.46,20.04"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M496.33,115.78c7.06,-7.49 20.04,-18.73 33.81,-23.35 -10.02,9.58 -31.37,31.37 -34.24,34.68M476.29,111.25c-5.05,-15.51 -12.55,-30.06 -23.79,-34.68a114.15,114.15 0,0 1,18.3 42.61m-4.18,-8.37c-8.28,-10.02 -19.61,-20.04 -32.5,-22.13a78.42,78.42 0,0 1,28.75 33.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M471.32,110.38c-10.46,-18.82 -18.47,-22.13 -23,-25.88 3.75,7.06 12.9,25.88 18.3,33.81"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M450.41,109.16a50.54,50.54 0,0 1,14.99 14.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M450.41,117.87c2.44,2.09 7.06,4.62 11.24,8.37m38.43,-2.09c4.18,-3.31 13.33,-8.71 17.95,-10.46m-20.91,7.58c4.18,-5.49 12.55,-12.98 15.51,-15.51m-3.83,-10.46a250.08,250.08 77.64,0 0,-15.86 19.69m5.05,-23.44a136.8,136.8 61.28,0 0,-10.46 18.39m-22.57,-17.51c2.53,6.71 7.15,17.95 8.71,22.57m-12.46,-15.51c1.74,2.61 5.05,8.37 6.27,12.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.42,97.39c0,-3.75 2.53,-7.84 5.05,-7.84 2.88,0 4.97,4.97 4.97,7.84 0,3.4 -2.53,6.27 -4.97,6.27 -2.61,0 -5.05,-2.44 -5.05,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M470.02,100.79c0.44,-4.18 3.31,-7.49 5.84,-7.15s3.75,5.49 3.31,8.37c-0.44,3.31 -3.31,5.84 -5.4,5.4 -2.09,0 -4.18,-2.88 -3.75,-6.62zM481.26,102.01c-0.87,-3.75 0.87,-7.84 3.4,-8.37 2.44,-0.35 4.97,3.75 5.4,7.15 0.87,3.31 -1.31,6.62 -3.4,6.62 -2,0.44 -4.97,-1.66 -5.4,-5.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.98,105.32c0,-4.1 2.53,-7.84 5.05,-7.84 2.88,0 4.97,4.53 4.97,7.84 0,3.4 -2.44,6.27 -4.97,6.27s-5.05,-2.44 -5.05,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.49,107.41c0,-3.75 2.96,-7.84 5.4,-7.84 2.61,0 5.05,4.97 5.05,7.84 0,3.4 -2.96,6.27 -5.05,6.27 -2.44,0 -5.4,-2.44 -5.4,-6.27zM482.91,105.76c0,-3.75 2.61,-7.84 5.05,-7.84 2.88,0 4.97,4.97 4.97,7.84 0,3.4 -2.44,6.27 -4.97,6.27s-5.05,-2.53 -5.05,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M481.26,109.16c0,-4.18 2.96,-7.93 5.4,-7.93 2.61,0 5.05,4.53 5.05,7.84 0,3.4 -2.53,6.36 -5.05,6.36 -2.44,0 -5.4,-2.61 -5.4,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.64,112.9c0,-4.18 2.44,-7.93 4.97,-7.93 2.96,0 5.05,4.97 5.05,7.84 0,3.49 -2.61,6.36 -5.05,6.36 -2.53,0 -5.05,-2.61 -5.05,-6.27zM464.18,112.9c0,-4.18 2.09,-7.93 4.18,-7.93s3.75,4.97 3.75,7.84c0,3.49 -2.09,6.36 -3.75,6.36 -2.09,0 -4.18,-2.61 -4.18,-6.27zM487.53,112.12c0,-3.83 2.09,-7.58 4.18,-7.58s4.18,4.62 4.18,7.49c0,3.75 -2.53,6.27 -4.18,6.27 -2.09,0 -4.18,-2.53 -4.18,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M490.06,119.09c0.87,-4.1 3.31,-7.49 4.97,-6.97 1.74,0.35 2.09,5.32 1.31,8.28 -0.44,3.75 -2.96,5.84 -4.62,5.4 -1.74,-0.35 -2.53,-3.31 -1.74,-6.62z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M482.13,117.87c0.44,-3.75 2.88,-7.49 4.97,-7.06 2.53,0 3.75,4.97 3.4,7.84 -0.44,3.83 -2.61,6.36 -4.62,5.93 -2.09,0 -4.18,-2.96 -3.75,-6.71zM470.45,119.09c-1.31,-4.1 -3.83,-6.97 -5.49,-6.62s-2.09,5.4 -1.22,8.37c0.87,3.31 3.31,5.4 4.97,4.97 1.31,-0.35 2.61,-3.31 1.74,-6.62z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.49,118.3c0,-3.75 2.09,-7.49 4.18,-7.49 2.53,0 4.18,4.62 4.18,7.49 0,3.75 -2.09,6.27 -4.18,6.27 -1.74,0 -4.18,-2.53 -4.18,-6.27z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M473.76,119.96c0,-4.18 2.53,-7.84 4.97,-7.84 2.96,0 5.05,4.53 5.05,7.84 0,3.4 -2.53,6.27 -5.05,6.27 -2.44,0 -4.97,-2.53 -4.97,-6.27zM475.86,74.04c-0.87,3.31 -3.75,6.27 -1.74,10.89 -3.66,-1.31 -6.97,-1.74 -9.93,1.66 3.75,-0.44 7.06,0.87 8.71,3.31 -3.31,-0.44 -4.97,1.31 -5.84,3.4 2.96,-0.87 5.84,-1.74 7.15,-1.31 -1.31,1.31 -3.4,3.31 -3.75,5.05 2.44,-2.09 5.4,-1.74 7.06,-2.96 -1.22,2.96 1.22,5.84 3.31,8.71 -0.35,-4.1 2.09,-6.97 0.87,-9.15 3.75,0.87 4.18,4.18 9.58,4.18 -2.09,-1.22 -3.31,-5.4 -5.84,-5.4 1.22,-0.44 6.27,-0.87 7.15,0 -0.87,-1.74 -3.83,-3.31 -5.49,-3.31 1.74,-1.74 4.62,-5.05 6.71,-5.93 -2.96,0.44 -7.49,0.87 -9.15,3.4 4.1,-3.4 3.31,-9.58 7.84,-10.46 -2.44,-0.44 -6.19,1.22 -9.15,4.18 1.31,-2.53 1.31,-5.84 2.96,-6.71 -3.4,0.87 -7.15,5.05 -8.37,7.15a15.68,15.68 123.04,0 0,-2.09 -6.71z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.95,80.75c0,1.22 0,3.75 -0.44,4.53m5.84,-4.97c-0.87,0.87 -2.53,3.75 -2.53,6.27"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M485.88,85.28a6.97,6.97 0,0 0,-2.96 3.83"
      android:strokeLineJoin="round"
      android:strokeWidth="0.52"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M350.55,370.56c2.53,6.62 20.48,15.42 41.39,4.18 14.2,-8.02 23.35,-19.17 32.15,-39.21 2.44,-5.05 3.75,-11.33 -2.09,-15.07 -4.62,-3.4 -12.2,-7.15 -15.51,-10.46q-2.61,-2.44 -4.53,-4.18 1.31,1.13 2.44,2.88c8.37,10.02 -0.35,24.66 -9.58,33.46 -6.71,6.27 -18.73,21.7 -36.34,7.06 -3.75,-3.31 -12.55,10.46 -7.84,21.35zM604.47,370.56c-2.96,6.62 -20.91,15.42 -41.39,4.18 -14.12,-8.02 -23.35,-19.17 -32.5,-39.21 -2.09,-5.05 -3.4,-11.33 2.44,-15.07 4.62,-3.4 12.2,-7.15 15.51,-10.46 1.66,-1.74 2.88,-2.96 4.53,-4.18q-1.31,1.13 -2.44,2.88c-8.37,10.02 0.35,24.66 9.15,33.46 6.71,6.27 19.17,21.7 36.77,7.06 3.75,-3.31 12.11,10.46 7.84,21.35z"
      android:strokeWidth="1.22"
      android:fillColor="#fcea83"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.51,406.46c33.37,0 84.78,-15.07 112.41,-67.7 4.53,-8.71 0,-13.33 -5.49,-16.21a63.61,63.61 0,0 1,-16.73 -12.2c6.71,6.71 5.4,11.33 0.87,18.82 -21.35,35.55 -50.54,53.5 -91.06,53.5s-69.71,-17.95 -91.06,-53.5c-4.53,-7.49 -5.84,-12.11 0.44,-18.73a57.51,57.51 0,0 1,-16.73 12.11c-5.4,2.88 -10.02,7.49 -5.4,16.21 27.53,52.63 79.29,67.7 112.75,67.7z"
      android:strokeWidth="1.22"
      android:fillColor="#fcea83"
      android:strokeColor="#000"/>
  <path
      android:pathData="M400.22,335.88c-0.87,-4.18 -6.19,-2.53 -7.84,2.53a59.25,59.25 0,0 1,-5.93 -9.24c-4.97,-7.49 -5.84,-12.55 1.31,-19.61 3.31,-3.31 10.02,-7.15 14.55,-2.96 9.58,7.93 4.62,20.91 -2.09,29.28zM554.37,335.88c0.87,-4.18 6.71,-2.53 8.37,2.53q3.22,-4.36 5.84,-9.24c4.97,-7.49 5.84,-12.55 -1.74,-19.61 -2.88,-3.31 -9.58,-7.15 -14.55,-2.96 -9.24,7.93 -4.62,20.91 2.09,29.28zM350.55,370.56c-4.18,-6.27 -7.49,-22.66 -8.37,-26.75 -1.22,-3.75 -1.66,-7.15 2.96,-8.71 4.62,-1.74 8.71,4.1 10.8,9.15s5.49,12.46 9.24,21.7c-9.24,-5.4 -16.73,-1.74 -14.64,4.62zM604.47,370.56c3.75,-6.27 7.06,-22.66 8.37,-26.75 0.87,-3.75 1.66,-7.15 -2.96,-8.71 -4.62,-1.74 -8.71,4.1 -10.8,9.15s-5.49,12.46 -9.24,21.7c9.24,-5.4 16.29,-1.74 14.64,4.62z"
      android:strokeWidth="1.22"
      android:fillColor="#c8102e"
      android:strokeColor="#000"/>
  <path
      android:pathData="m374.78,332.92 l-3.31,1.74c-0.87,0.35 -0.87,1.22 -0.87,1.66l-0.44,0L368.5,332.57l0.44,0c0,0.35 0.44,0.87 1.22,0.35l7.15,-3.75q1.22,-0.7 0.44,-1.22l0.35,-0.44 2.09,4.18l-0.35,0c-0.44,-0.44 -0.87,-0.87 -1.74,-0.44l-2.88,1.74 1.22,2.44 3.4,-1.22c0.87,-0.44 0.35,-1.31 0.35,-1.74l0.44,0l1.74,3.83c-0.44,-0.44 -0.87,-0.87 -1.74,-0.44l-7.06,3.75q-1.22,0.7 -0.44,1.22l-0.44,0.44 -2.09,-3.75 0.44,-0.44c0.44,0.44 0.87,0.87 1.74,0.44l3.31,-1.74zM381.92,338.41q1.22,-0.52 0.35,-1.74l0.44,0l4.18,6.27 -2.88,2.09l0,-0.35c1.66,-1.31 1.66,-2.96 1.22,-3.4s-0.87,-0.87 -0.87,-1.22c-0.35,0 -0.35,-0.44 -1.22,0.44l-2.96,2.09c0.44,0.35 0.87,1.66 2.96,0.35l0.44,0l-4.62,3.4l0,-0.44c2.09,-1.74 1.22,-2.96 0.87,-3.31l-2.96,2.09c-0.87,0.35 -0.87,0.87 -0.87,1.22 0.44,0 0.44,0.44 0.87,0.87 0.44,0.87 1.74,1.66 3.75,0.87l0.44,0.35 -3.31,1.22 -4.18,-6.62l0.44,0c0.35,0.35 0.87,0.87 1.66,0.35zM388.55,354.27 L385.67,356.71c-0.44,0.44 -0.44,1.31 0,1.74l-0.44,0l-2.96,-2.96 0.44,-0.44c0.44,0.44 0.87,0.87 1.74,0l5.4,-5.4q1.22,-0.52 0.35,-1.74l2.96,3.49q-0.87,-0.78 -1.74,0l-2.44,2.44 2.09,2.09 2.53,-2.09c0.87,-0.87 0.44,-1.22 0,-1.74l0.44,-0.35 2.88,3.31l-0.44,0c-0.35,0 -0.87,-0.35 -1.66,0.44l-5.4,5.05q-1.22,1.13 -0.44,1.66l0,0.44l-2.88,-3.4c0.35,0 1.22,0.44 1.66,0l2.88,-2.88zM394.82,363.07 L396.13,364.29 397.78,360.54s-1.31,0.35 -3.4,1.66zl-0.44,0.35 -0.87,-0.87c-0.35,0.44 -0.78,0.44 -1.22,0.87 -1.22,0.87 -0.87,1.31 -0.35,1.74l0,0.35l-2.09,-2.09l0.35,0l1.74,-0.44 8.28,-4.53 0.44,0.35s-0.44,0.44 -4.18,8.8c0,0.87 -0.35,1.22 0,2.09l-3.31,-2.88 0.44,-0.44q0.87,1.31 1.66,0c0,-0.44 0.44,-1.31 0.44,-1.74l-1.31,-1.22 0.44,-0.44zM398.13,371.35 L398.57,370.91q0.52,0.7 1.74,0l5.4,-7.06 -0.44,-0.44c-0.87,-0.44 -2.09,-0.44 -3.31,0.87l0,-0.44l1.66,-2.09 6.27,4.62 -2.09,2.44l-0.44,0c1.22,-1.22 0.87,-2.88 0,-3.31l0,-0.44l-5.4,7.15c-0.87,0.44 -0.44,1.22 0,1.66l-0.44,0z"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m409.02,373 l-2.09,2.96c-0.44,0.87 0,1.22 0.44,1.74l-0.44,0.35 -3.31,-2.53l0,-0.44q1.13,0.78 1.66,-0.35l4.62,-6.27q0.35,-1.22 -0.44,-1.74l0.44,-0.35 3.31,2.44l0,0.44c-0.87,-0.44 -1.22,-0.44 -1.74,0.44L409.46,372.65l2.53,1.66 2.09,-2.96q0.35,-1.13 -0.44,-1.66l0.44,-0.44 3.31,2.53l0,0.44q-0.87,-0.78 -1.74,0.44l-4.1,6.62c-0.87,0.44 -0.44,0.87 0,1.31l0,0.35l-3.75,-2.44l0.44,0q0.61,0.09 1.22,-0.44l2.09,-3.31zM417.39,384.77 L417.82,384.33q0.87,0.78 1.66,-0.44l3.31,-7.06c0.44,-0.87 0,-1.31 -0.44,-1.74l7.15,3.4 -1.74,3.31 -0.35,-0.44c1.22,-1.66 0,-3.31 -0.44,-3.75 -0.44,0 -0.87,-0.44 -1.22,-0.44 -0.44,0 -0.44,0 -0.87,0.87 -0.44,0.44 -1.66,2.88 -1.66,2.88 0.44,0.44 1.22,1.31 2.96,-0.87l0.35,0l-2.44,5.05 -0.44,-0.35c1.22,-2.61 -0.44,-2.96 -0.87,-2.96l-1.66,3.31c-0.44,0.87 0,1.31 0.44,1.31l0,0.44zM429.5,390.6c-1.22,-0.87 -1.22,-3.4 0,-5.84 0.87,-2.61 2.53,-4.62 4.18,-3.83 1.22,0.44 0.87,2.96 0,5.49 -1.31,2.88 -2.96,4.53 -4.18,4.18l-0.44,0c2.96,1.22 5.05,-0.44 6.27,-3.4 1.22,-2.44 0.87,-5.4 -1.74,-6.62 -2.44,-1.31 -4.97,0.35 -6.19,3.31s-0.87,5.84 1.74,6.71zM442.4,391.39 L444.14,386.42q0.35,-1.22 -0.44,-1.74l2.09,0.44l0,0.44c-0.44,0 -0.87,0 -1.31,0.87l-1.66,4.97c-0.87,2.09 -2.09,3.75 -4.53,2.96 -1.74,-0.44 -2.96,-2.53 -2.09,-4.62l2.09,-5.4q0.35,-1.22 -0.44,-1.74l0,-0.35l3.75,1.66l0,0.44c-0.44,-0.44 -1.22,-0.44 -1.22,0.44L438.21,390.08c-0.44,1.66 -0.44,3.31 0.87,3.75q1.92,0.61 3.22,-2.53zM446.23,385.99q0.09,-0.52 -0.44,-0.44l2.88,0.44 2.61,7.93 1.22,-5.05q0.35,-1.13 -0.44,-1.66l2.44,0.44 -0.35,0.44c-0.44,0 -0.87,0 -1.31,0.87l-2.09,9.15l-0.35,0l-3.4,-10.02 -2.09,6.71c0,1.22 0.44,1.22 0.87,1.66l0,0.44l-2.53,-0.87l0,-0.44c0.44,0.44 1.22,0 1.22,-0.87l2.09,-7.49zM460.43,394.35c-0.87,3.75 -2.61,4.62 -3.4,4.62l-0.87,0s-0.35,-0.44 0,-0.87l1.31,-8.37c0.44,-0.44 0.44,-0.87 0.87,-0.44l0.87,0c0.78,0 1.57,1.74 1.22,5.05l1.22,0.44c0.87,-4.18 -0.87,-5.93 -2.96,-6.27 -1.66,-0.44 -4.18,-0.87 -4.18,-0.87l0,0.44c0.44,0.44 1.31,0.44 0.87,1.74l-1.22,7.41q-0.7,1.31 -1.31,1.31l-0.35,0l4.1,0.87c2.09,0.35 4.18,0 5.05,-4.62zM464.18,390.95c0,-0.87 -0.87,-1.22 -1.31,-1.22l0,-0.44l7.58,0.87 -0.44,3.31c0,-2.09 -1.31,-2.88 -2.09,-2.88q-0.61,-0.52 -1.31,-0.44c-0.35,0 -0.35,0 -0.35,1.22l-0.44,3.4c0.44,0 1.66,0.35 2.53,-2.09l-0.44,5.4l-0.44,0c0,-2.53 -1.22,-2.53 -1.74,-2.53l-0.35,3.75c0,0.87 0,0.87 0.44,0.87l0.87,0.44c1.22,0 2.44,-0.44 3.31,-2.96l0.44,0l-0.87,3.31 -7.93,-0.87c0.87,0 1.22,-0.35 1.74,-1.13zM477.95,396.01c0,3.75 -1.31,4.97 -2.53,4.97 -0.44,-0.35 -0.44,-0.35 -0.87,-0.35l-0.35,-0.44 0.44,-8.71q-0.09,-0.61 0.35,-0.52l0.87,0c0.87,0 2.09,1.31 2.09,5.05l1.66,0c0,-4.18 -2.09,-5.4 -4.18,-5.4 -1.74,-0.44 -4.18,-0.44 -4.18,-0.44l0,0.44c0.44,0 1.31,0.35 1.31,1.22l-0.44,7.93q-0.09,1.31 -0.87,1.22l0,0.44l3.75,0c2.09,0 4.18,-0.87 4.62,-5.4l-1.74,0zM490.06,399.32c0,0.87 0.44,0.87 1.22,0.87l0,0.44l-4.62,0.35c0.44,-0.35 1.31,-0.87 1.31,-1.66l-0.87,-7.84c-0.44,-0.87 -0.87,-0.87 -1.66,-0.87l0,-0.44l4.62,-0.44c-0.44,0.44 -1.31,0.87 -0.87,1.74zM494.24,400.19l0,-0.44q0.78,0 0.87,-1.22l-1.74,-8.71l-0.44,0c-0.87,0.35 -1.66,1.13 -1.22,2.88l-0.44,0l-0.44,-2.96 7.58,-1.22 0.35,2.88l-0.35,0c0,-1.66 -1.31,-2.09 -2.61,-2.09l-0.35,0l1.74,8.71q0,1.31 1.22,1.31l0,0.44zM512.19,391.82 L510.45,386.86c0,-0.87 -0.78,-0.87 -1.22,-0.87l0,-0.44l2.09,-0.87l0,0.44c-0.44,0.44 -0.44,0.44 -0.44,1.31l1.74,5.4c0.35,1.74 0.35,4.18 -2.09,5.05 -1.74,0.35 -3.75,-0.44 -4.18,-2.96l-1.74,-5.4c-0.35,-0.87 -0.78,-0.87 -1.22,-0.87l-0.44,-0.44 4.18,-1.22l0,0.44c-0.35,0.44 -0.87,0.87 -0.35,1.66l1.22,5.4c0.44,1.74 1.74,2.53 2.88,2.53 1.31,-0.44 1.74,-2.09 1.31,-4.18m4.62,-2.96c2,-0.87 1.66,-2 1.22,-3.31 -0.44,-1.22 -1.22,-2.09 -2.09,-1.66 -0.44,0 -0.87,0.44 -0.44,0.87zl0.35,0.44 0.87,3.4q0.87,1.22 1.74,0.35l0,0.44l-3.83,1.74 -0.44,-0.44c0.87,-0.44 0.87,-0.87 0.87,-1.74l-2.96,-7.06c-0.44,-1.22 -0.87,-0.87 -1.66,-0.87l0,-0.35l4.18,-1.74c1.74,-0.35 3.31,0 4.18,1.74q0.52,2.96 -2.09,4.18c-0.44,0 -0.87,0.35 -0.87,0.35z"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M527.18,390.08c-1.22,0.52 -2.88,-1.22 -4.18,-4.1 -1.22,-2.53 -1.22,-5.05 0,-5.4 1.31,-0.44 2.96,1.22 4.18,3.75 1.31,2.53 1.31,4.97 0,5.84 2.61,-0.87 3.4,-3.75 2.09,-6.71 -1.22,-2.53 -3.75,-4.18 -6.27,-3.31a4.88,4.88 45,0 0,-2.09 6.71c1.31,2.88 3.83,4.53 6.27,3.31zM527.61,378.49l-0.78,0l2.44,-1.74 7.58,4.18 -2.61,-4.53c-0.35,-0.87 -1.22,-0.87 -1.57,-0.87l0,-0.44l2.09,-0.87c-0.44,0.52 -0.87,0.87 0,1.74l4.18,8.37 -0.44,0.44 -9.24,-5.05 3.4,6.27c0.35,0.87 1.22,0.87 1.66,0.44l0,0.44l-2.09,1.22l0,-0.44q0.61,-0.61 0,-1.66l-3.31,-6.71zM546.44,379.71l0,-0.44c0,-0.44 0.44,-0.87 0,-1.66l-5.05,-7.06l-0.44,0c-0.87,0.35 -1.22,2.09 0,3.31l-0.35,0.44 -1.74,-2.96 6.27,-4.18 1.74,2.61c-1.31,-1.31 -2.61,-1.31 -3.4,-0.87l-0.44,0.35 5.05,7.15q0.87,1.22 1.74,0.44l0,0.35z"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m551.05,369.69 l2.44,2.96c0.44,0.35 1.31,0.35 1.74,0l0,0.35l-3.4,2.96l0,-0.44q0.61,-0.52 0,-1.66l-4.97,-6.27c-0.44,-0.87 -0.87,-0.44 -1.74,-0.44l3.4,-2.88l0,0.44c0,0.35 -0.44,0.87 0,1.66l2.09,2.44 2.44,-1.66 -2,-2.88c-0.44,-0.87 -1.31,-0.44 -1.74,-0.44l3.4,-2.96l0,0.44q-0.61,0.52 0,1.74l4.97,6.19q0.7,0.7 1.31,0l0.35,0.44 -3.31,2.96 -0.44,-0.44c0.44,-0.44 0.87,-0.87 0.44,-1.74l-2.53,-2.88zM554.8,360.89c-0.44,-0.35 -1.31,-0.35 -1.74,0l0,-0.35l5.49,-5.05 2.53,2.53l-0.44,0c-1.31,-1.74 -2.96,-0.87 -3.75,-0.44 0,0.44 -0.44,0.44 -0.87,0.87q-0.09,0 0.44,1.22l2.53,2.53c0.35,-0.44 1.66,-1.31 0,-3.4l0.35,0l3.4,3.83l0,0.35c-2.09,-2.09 -2.96,-0.87 -3.4,-0.35l2.61,2.44c0.35,0.87 0.78,0.87 0.78,0.87l1.22,-1.22c0.44,-0.44 0.87,-1.74 0,-4.18l0.44,0l1.66,2.88 -5.4,5.4 -0.44,-0.35c0.44,-0.44 0.87,-0.87 0,-1.74zM564.82,349.22c-0.44,0.44 -0.44,1.31 0.35,1.74 0.87,0.78 1.74,0.78 3.4,0 0.87,-0.44 2.44,-0.87 3.75,0.35 2.09,2.09 1.22,3.75 0.87,4.62 -0.44,0.44 -1.31,0.87 -1.74,1.22q-0.52,-0.09 -0.44,0.44l0,0.44l-2.88,-2.53l0.44,0c1.66,0.87 3.31,1.22 4.18,0 0.87,-0.87 0,-1.74 -0.44,-2.09 -0.87,-0.87 -1.74,-0.87 -2.96,0 -1.22,0.44 -2.88,0.44 -4.18,-0.44 -1.66,-1.66 -1.66,-2.88 -0.87,-4.18l1.74,-1.22c0.44,0 0.44,0 0,-0.87l2.96,2.09l-0.44,0c-2.09,-0.87 -3.4,-0.35 -3.75,0.44m3.75,-3.31c-0.87,-0.44 -1.31,0 -1.74,0.44l-0.35,0l4.62,-6.71 2.88,2.09 -0.44,0.44a2.88,2.88 0,0 0,-3.75 0c0,0.44 -0.44,0.87 -0.44,1.22q-0.61,0.09 0.44,0.87c0.44,0.44 2.96,2.09 2.96,2.09 0.44,-0.44 1.22,-1.31 -0.44,-2.96l0,-0.44l4.18,3.4c-2.53,-1.31 -2.96,0 -3.31,0.35l2.88,2.09q1.22,0.52 1.22,0.44 0.09,-0.7 0.87,-1.22c0.44,-0.87 0.87,-2.09 -0.87,-3.75l0.44,-0.44 2.09,2.53 -4.18,6.27l-0.44,0c0.44,-0.87 0.44,-1.31 -0.35,-2.09zM579.81,338.41 L578.59,340.5c-1.74,-1.74 -2.53,-2.96 -2.53,-2.96zl0.87,0c0.44,0 1.22,0.35 1.66,0.35 1.22,0.44 0.87,0.87 0.44,1.74l0.44,0l2.09,-3.83c-0.87,0.87 -1.31,0.44 -2.09,0.44 -8.8,-1.66 -9.58,-2.09 -9.58,-2.09l0,0.44l6.62,7.49c0.44,0.44 0.44,1.31 0.44,1.74l0.44,0l1.22,-2.09 -0.44,-0.44c-0.44,0.87 -0.87,1.22 -1.66,0l-0.87,-0.87 1.31,-2.88zM577.28,329.17c-0.35,0.44 -0.35,1.31 0.87,1.74s1.74,0 2.96,-0.87 2.44,-1.74 4.1,-0.87c2.61,1.31 2.09,3.4 1.74,4.18l-1.31,1.74l0,0.78l-3.31,-1.74c2.09,0.44 3.75,0 4.18,-1.22 0.44,-0.87 -0.44,-1.66 -0.87,-2.09q-1.13,-0.44 -2.88,0.87c-1.31,1.22 -2.61,1.74 -4.18,0.87q-2.79,-1.39 -1.74,-3.4c0,-0.87 0.87,-1.22 1.31,-1.66 0,-0.44 0,-0.44 -0.44,-0.87l0,-0.35l3.4,1.66l-0.44,0c-2.09,-0.44 -3.4,0.44 -3.4,1.22"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M445.36,181.39c-5.4,1.22 -9.15,1.66 -14.2,-2.96 -3.31,-3.31 -2.53,-9.15 0.87,-11.33 3.75,-2.44 7.06,-4.1 11.68,-2.88s16.73,12.55 13.77,12.98c-2.96,0 -7.49,2.88 -12.11,4.18zM458.25,161.35c1.74,-1.31 3.83,-2.09 9.24,-2.61 5.05,-0.35 8.71,-0.35 10.02,3.49 1.74,4.53 2.96,9.93 6.27,12.46 -2.09,1.22 -12.55,2.53 -16.29,0.44 -4.97,-2.53 -6.27,-12.2 -9.15,-13.77zM494.24,161.79c1.66,-1.31 3.75,-1.31 9.15,-0.87s7.49,0.87 8.71,4.62c1.31,4.18 0,9.58 2.61,13.33 -2.61,0.87 -10.89,0.87 -13.85,-2.09 -4.53,-3.31 -4.18,-12.98 -6.62,-15.07z"
      android:strokeWidth="1.22"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M519.25,164.23c-3.75,-0.87 -6.27,-1.22 -7.84,0 2,2.53 0.35,12.55 4.53,15.86 2.88,2.09 10.46,2.61 12.11,-2.09 2.09,-4.18 2.09,-10.46 -8.71,-13.77zM476.29,160.04c1.22,-1.22 3.75,-1.66 8.71,-1.66 5.49,0 8.71,0.87 10.46,4.18 1.74,4.18 1.22,9.58 4.18,12.98 -1.22,1.66 -12.55,1.66 -15.86,-0.87 -4.62,-2.96 -5.05,-12.55 -7.49,-14.64zM440.83,163.79c0,-2.09 2.88,-2.44 10.46,-2.88 7.41,-0.87 7.84,-0.44 10.37,4.97 1.22,2.96 2.88,7.93 6.27,9.58 -9.24,2.18 -12.2,3.4 -17.17,0.44a25.27,25.27 0,0 1,-8.71 -9.15c-0.44,-1.22 -0.44,-2.53 -1.31,-2.96z"
      android:strokeWidth="1.22"
      android:fillColor="#073163"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460.87,121.27c-8.8,-6.71 -19.26,-12.98 -28.84,-14.2a243.98,243.98 0,0 1,28.41 22.13"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M465.83,114.99c-10.46,-7.15 -22.57,-15.07 -34.24,-15.86a108.05,108.05 111.33,0 1,28.75 24.22m18.82,-20.04a81.04,81.04 86.54,0 1,20.48 -28.06c-4.18,8.8 -8.71,26.75 -10.89,33.9"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M488.4,101.23A41.83,41.83 0,0 1,509.66 79.01a88.01,88.01 0,0 0,-15.86 30.5"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M492.15,109.5a74.07,74.07 61.38,0 1,32.94 -27.53c-10.46,10.46 -25.88,29.63 -29.63,37.21m2.53,-0.44c7.49,-4.18 22.57,-10.89 33.37,-8.71 -6.62,2.44 -26.66,11.59 -35.46,20.04"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M496.33,115.78c7.06,-7.49 20.04,-18.73 33.81,-23.35 -10.02,9.58 -31.37,31.37 -34.24,34.68M476.29,111.25c-5.05,-15.51 -12.55,-30.06 -23.79,-34.68a114.15,114.15 0,0 1,18.3 42.61m-4.18,-8.37c-8.28,-10.02 -19.61,-20.04 -32.5,-22.13a78.42,78.42 0,0 1,28.75 33.81"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M471.32,110.38c-10.46,-18.82 -18.47,-22.13 -23,-25.88 3.75,7.06 12.9,25.88 18.3,33.81"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M450.41,109.16a50.54,50.54 0,0 1,14.99 14.55"
      android:strokeWidth="0.52"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M450.41,117.87c2.44,2.09 7.06,4.62 11.24,8.37m38.43,-2.09c4.18,-3.31 13.33,-8.71 17.95,-10.46m-20.91,7.58c4.18,-5.49 12.55,-12.98 15.51,-15.51m-3.83,-10.46a250.08,250.08 77.64,0 0,-15.86 19.69m5.05,-23.44a136.8,136.8 61.28,0 0,-10.46 18.39m-22.57,-17.51c2.53,6.71 7.15,17.95 8.71,22.57m-12.46,-15.51c1.74,2.61 5.05,8.37 6.27,12.55"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.42,97.39c0,-3.75 2.53,-7.84 5.05,-7.84 2.88,0 4.97,4.97 4.97,7.84 0,3.4 -2.53,6.27 -4.97,6.27 -2.61,0 -5.05,-2.44 -5.05,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M470.02,100.79c0.44,-4.18 3.31,-7.49 5.84,-7.15s3.75,5.49 3.31,8.37c-0.44,3.31 -3.31,5.84 -5.4,5.4 -2.09,0 -4.18,-2.88 -3.75,-6.62zM481.26,102.01c-0.87,-3.75 0.87,-7.84 3.4,-8.37 2.44,-0.35 4.97,3.75 5.4,7.15 0.87,3.31 -1.31,6.62 -3.4,6.62 -2,0.44 -4.97,-1.66 -5.4,-5.4z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.98,105.32c0,-4.1 2.53,-7.84 5.05,-7.84 2.88,0 4.97,4.53 4.97,7.84 0,3.4 -2.44,6.27 -4.97,6.27s-5.05,-2.44 -5.05,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.49,107.41c0,-3.75 2.96,-7.84 5.4,-7.84 2.61,0 5.05,4.97 5.05,7.84 0,3.4 -2.96,6.27 -5.05,6.27 -2.44,0 -5.4,-2.44 -5.4,-6.27zM482.91,105.76c0,-3.75 2.61,-7.84 5.05,-7.84 2.88,0 4.97,4.97 4.97,7.84 0,3.4 -2.44,6.27 -4.97,6.27s-5.05,-2.53 -5.05,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M481.26,109.16c0,-4.18 2.96,-7.93 5.4,-7.93 2.61,0 5.05,4.53 5.05,7.84 0,3.4 -2.53,6.36 -5.05,6.36 -2.44,0 -5.4,-2.61 -5.4,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474.64,112.9c0,-4.18 2.44,-7.93 4.97,-7.93 2.96,0 5.05,4.97 5.05,7.84 0,3.49 -2.61,6.36 -5.05,6.36 -2.53,0 -5.05,-2.61 -5.05,-6.27zM464.18,112.9c0,-4.18 2.09,-7.93 4.18,-7.93s3.75,4.97 3.75,7.84c0,3.49 -2.09,6.36 -3.75,6.36 -2.09,0 -4.18,-2.61 -4.18,-6.27zM487.53,112.12c0,-3.83 2.09,-7.58 4.18,-7.58s4.18,4.62 4.18,7.49c0,3.75 -2.53,6.27 -4.18,6.27 -2.09,0 -4.18,-2.53 -4.18,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M490.06,119.09c0.87,-4.1 3.31,-7.49 4.97,-6.97 1.74,0.35 2.09,5.32 1.31,8.28 -0.44,3.75 -2.96,5.84 -4.62,5.4 -1.74,-0.35 -2.53,-3.31 -1.74,-6.62z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M482.13,117.87c0.44,-3.75 2.88,-7.49 4.97,-7.06 2.53,0 3.75,4.97 3.4,7.84 -0.44,3.83 -2.61,6.36 -4.62,5.93 -2.09,0 -4.18,-2.96 -3.75,-6.71zM470.45,119.09c-1.31,-4.1 -3.83,-6.97 -5.49,-6.62s-2.09,5.4 -1.22,8.37c0.87,3.31 3.31,5.4 4.97,4.97 1.31,-0.35 2.61,-3.31 1.74,-6.62z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M467.49,118.3c0,-3.75 2.09,-7.49 4.18,-7.49 2.53,0 4.18,4.62 4.18,7.49 0,3.75 -2.09,6.27 -4.18,6.27 -1.74,0 -4.18,-2.53 -4.18,-6.27z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M473.76,119.96c0,-4.18 2.53,-7.84 4.97,-7.84 2.96,0 5.05,4.53 5.05,7.84 0,3.4 -2.53,6.27 -5.05,6.27 -2.44,0 -4.97,-2.53 -4.97,-6.27zM475.86,74.04c-0.87,3.31 -3.75,6.27 -1.74,10.89 -3.66,-1.31 -6.97,-1.74 -9.93,1.66 3.75,-0.44 7.06,0.87 8.71,3.31 -3.31,-0.44 -4.97,1.31 -5.84,3.4 2.96,-0.87 5.84,-1.74 7.15,-1.31 -1.31,1.31 -3.4,3.31 -3.75,5.05 2.44,-2.09 5.4,-1.74 7.06,-2.96 -1.22,2.96 1.22,5.84 3.31,8.71 -0.35,-4.1 2.09,-6.97 0.87,-9.15 3.75,0.87 4.18,4.18 9.58,4.18 -2.09,-1.22 -3.31,-5.4 -5.84,-5.4 1.22,-0.44 6.27,-0.87 7.15,0 -0.87,-1.74 -3.83,-3.31 -5.49,-3.31 1.74,-1.74 4.62,-5.05 6.71,-5.93 -2.96,0.44 -7.49,0.87 -9.15,3.4 4.1,-3.4 3.31,-9.58 7.84,-10.46 -2.44,-0.44 -6.19,1.22 -9.15,4.18 1.31,-2.53 1.31,-5.84 2.96,-6.71 -3.4,0.87 -7.15,5.05 -8.37,7.15a15.68,15.68 123.04,0 0,-2.09 -6.71z"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M477.95,80.75c0,1.22 0,3.75 -0.44,4.53m5.84,-4.97c-0.87,0.87 -2.53,3.75 -2.53,6.27"
      android:strokeWidth="0.52"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M485.88,85.28a6.97,6.97 0,0 0,-2.96 3.83"
      android:strokeWidth="0.52"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M441.61,126.67q-4.44,-0.09 -7.15,-3.31c-2.88,-3.83 -10.8,-5.49 -14.2,-2.09 -1.22,0.87 -2.88,0.87 -4.1,1.22 -2.09,0 -4.62,2.09 -3.83,6.27 1.74,-1.31 2.09,0 3.4,0 0.87,-0.44 0.44,0.44 0.44,1.22 0,2.09 4.53,6.27 8.71,5.84 4.62,0 6.71,1.74 7.49,2.61 1.31,1.13 0.44,0.78 1.31,2.88 1.22,1.22 1.66,1.66 -0.44,3.31a8.45,8.45 135,0 0,0 12.98c5.4,4.1 19.17,10.46 34.68,-1.74 14.99,-11.68 38.34,-4.97 40.08,-0.87 2.09,4.27 2.88,6.71 3.31,8.8 0.44,1.74 2.53,3.4 5.49,2.96 2.88,0 6.19,1.22 7.84,2.53 2.18,1.22 5.05,0.87 4.27,-2.96 -1.74,-11.68 -8.8,-13.33 -9.58,-18.73 4.1,0.35 6.97,0.35 8.71,-1.31 1.22,-1.66 2.88,-4.62 4.62,-5.4 1.22,-0.87 1.22,-1.74 -0.44,-1.31 -2.09,0.44 -3.31,1.31 -7.15,0.87 -19.17,-2.09 -73.02,-16.73 -83.48,-13.77z"
      android:strokeWidth="1.22"
      android:fillColor="#059334"
      android:strokeColor="#000"/>
  <path
      android:pathData="M526.83,140.87a30.5,30.5 0,0 0,-8.02 8.37c-4.1,5.4 -16.64,10.8 -32.15,10.8 -14.99,0 -24.14,-2.44 -30.85,-7.84a53.15,53.15 127.44,0 0,-9.24 -5.93c-4.53,-2.09 -5.4,-6.71 -4.18,-10.46 -1.22,2.09 -2,4.18 -4.53,3.75 -1.74,0 -5.05,0 -4.18,1.74 -0.44,-0.87 -0.44,-2.09 -0.87,-2.61l-0.87,-0.78c1.31,0.87 4.27,-1.66 6.36,-1.66 1.66,0.44 3.75,-1.66 3.31,-4.18l-1.22,-5.4c3.31,-1.74 47.14,6.27 55.94,7.06 8.71,0.44 33.81,4.18 30.5,7.15z"
      android:strokeWidth="1.22"
      android:fillColor="#000001"
      android:strokeColor="#000"/>
  <path
      android:pathData="M447.45,125.01c4.18,-4.18 9.15,-6.27 14.2,-5.05l1.22,0c2.61,-2.09 8.37,-2.53 11.76,-0.87l1.66,0c4.97,-1.66 14.55,0 17.95,3.4l1.22,0.87c11.33,-0.87 17.95,4.1 25.88,12.11 0.87,0.87 2.09,2.44 4.18,2.88 2.53,0.44 2.96,1.74 0.87,2.96a33.11,33.11 0,0 0,-8.71 7.84c-3.83,5.05 -15.95,10.11 -31.02,10.11 -14.55,0 -23.35,-2.61 -30.06,-7.58 -3.75,-2.88 -6.62,-4.53 -8.71,-5.84 -2.53,-0.87 -5.84,-4.18 -4.62,-7.49 0.87,-3.31 -1.22,-7.49 -2.88,-11.68 1.22,-0.87 4.53,-2.09 6.19,-1.22z"
      android:strokeWidth="1.22"
      android:fillColor="#059334"
      android:strokeColor="#000"/>
  <path
      android:pathData="M521.34,135.47c-7.84,-8.02 -14.55,-12.98 -25.88,-12.2 0,0.44 0,1.74 -0.44,2.61a57.51,57.51 0,0 1,20.48 15.42c1.74,-1.74 4.62,-3.75 5.84,-5.84zM494.24,122.49c-3.4,-3.31 -12.98,-5.05 -17.95,-3.31 0,1.66 0,4.18 -0.87,5.84 2.96,1.22 5.4,4.18 7.93,7.06 1.74,-0.44 5.84,-1.66 7.93,-2.09a38.34,38.34 0,0 0,2.96 -7.49zM474.64,119.09c-3.4,-1.66 -9.24,-1.22 -11.76,0.87 -0.87,1.74 -1.22,5.05 -1.22,6.71 1.74,0 4.18,1.22 4.97,2.09 2.61,-0.87 5.93,-2.53 7.15,-3.75 0.44,-1.74 1.22,-4.18 0.87,-5.84zM447.45,125.01c4.18,-4.18 9.15,-6.27 14.2,-5.05 -0.87,1.31 -1.22,3.75 -1.22,6.27 -4.62,-0.44 -10.46,0.87 -12.98,2.53z"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#000"/>
  <path
      android:pathData="M513.85,142.09a55.77,55.77 56.82,0 0,-19.17 -14.64c0,0.44 -0.87,2.09 -1.31,3.4 2.53,3.75 7.49,12.11 9.58,16.73 2.09,-0.87 7.93,-3.4 10.89,-5.49zM482.13,151.33a155.97,155.97 0,0 0,1.22 -17.17c2.09,-0.44 6.27,-1.66 7.93,-2.09 2.09,3.4 7.15,12.2 9.24,16.29 -5.05,1.22 -13.42,3.31 -18.39,2.96zM467.49,131.29c2.09,-0.87 5.05,-2.61 7.06,-4.18 1.74,1.22 5.05,4.18 6.27,5.84 0,3.75 -1.22,14.64 -1.66,18.3 -3.31,0 -8.71,-0.78 -11.68,-2.44 0.44,-6.27 0.87,-13.33 0,-17.51zM446.58,136.25c0.44,-0.87 0.87,-3.75 0.87,-4.97 3.31,-2.09 11.68,-4.18 17.95,-0.87 0.44,4.18 0,14.64 -0.44,17.17a83.65,83.65 75.93,0 0,-18.3 -11.33z"
      android:strokeWidth="1.22"
      android:fillColor="#00493d"
      android:strokeColor="#000"/>
  <path
      android:pathData="M425.75,128.67c-0.87,0 -1.31,-2.44 -2.96,-2.44 -0.44,0 -1.66,0.44 -2.09,0q0.09,1.39 0.87,2.09c0.44,1.22 2.53,0.44 4.18,0.44z"
      android:strokeWidth="1.22"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M425.75,135.82c4.97,0 5.84,-6.27 8.37,-6.62m-5.49,7.06c3.75,1.74 6.71,-7.06 9.58,-6.71m-17.08,-4.1c1.22,0 2.09,-1.74 4.18,0.35q1.48,1.31 2.96,1.31m-0.87,4.53c-1.74,2.61 -4.62,4.18 -6.27,3.4m-0.87,-13.77c-1.22,0.87 -2,2.44 -2.88,3.75m-1.22,2.44l0,2.09"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M555.59,181.83l0,81.39c0,70.58 -33.37,94.8 -78.07,113.97 -44.7,-19.17 -78.42,-43.39 -78.42,-113.97l0,-81.47l156.5,0z"
      android:strokeWidth="1.22"
      android:fillColor="#fff"/>
  <path
      android:pathData="M477.51,250.66c3.75,0 9.58,-3.31 14.64,-8.37 5.4,-5.84 14.64,-7.84 24.22,0s15.42,4.62 20.48,0c4.97,-4.53 9.15,-6.27 18.73,-6.27L555.58,181.83L399,181.83l0,54.29c9.58,0 13.77,1.74 18.82,6.27 4.97,4.62 10.8,7.93 20.48,0 10.02,-7.84 18.73,-5.84 24.57,0 4.62,5.05 10.46,8.37 14.64,8.37z"
      android:strokeWidth="1.22"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M477.51,292.49c5.05,0 15.07,-5.49 17.51,-6.71 9.24,-5.49 15.86,-0.44 21.35,2.09 5.84,2.44 16.64,5.4 23.35,0s10.46,-5.05 15.07,-5.05c0.87,-6.27 0.87,-12.55 0.87,-19.61l0,-5.05c-5.93,-1.22 -8.8,0.44 -15.07,5.93s-13.42,4.18 -22.66,0.87c-8.71,-3.83 -17.43,-10.46 -26.23,0a20.91,20.91 0,0 1,-14.2 6.97c-6.27,0 -11.68,-4.1 -14.2,-6.97 -9.15,-10.46 -17.51,-3.83 -26.32,0 -9.15,3.31 -16.73,4.53 -22.92,-0.87 -5.84,-5.49 -8.8,-7.15 -15.07,-5.93l0,5.05c0,7.06 0.44,13.33 1.22,19.61 4.62,0 8.02,-0.44 14.64,5.05s17.95,2.44 23.44,0c5.84,-2.53 12.46,-7.58 21.7,-2.09 2.53,1.22 12.11,6.62 17.51,6.62zM477.51,310.44a44.44,44.44 0,0 0,20.48 -6.27c8.37,-4.62 14.2,-2.61 22.57,1.66 8.71,4.18 15.86,2.88 20.91,0 2.44,-1.74 4.53,-5.05 9.93,-3.75 -2.44,10.46 -6.27,19.61 -10.8,27.1a28.75,28.75 0,0 1,-21.78 -1.66c-8.71,-4.18 -19.61,-6.71 -25.44,-2.96a31.37,31.37 0,0 1,-15.86 5.05c-4.62,0 -10.46,-1.74 -15.86,-5.05 -5.84,-3.75 -16.73,-1.22 -25.44,2.96a28.75,28.75 0,0 1,-21.78 1.74q-6.97,-11.5 -10.8,-27.19c5.4,-1.31 7.49,2.09 10.02,3.75a20.91,20.91 0,0 0,20.48 0c8.71,-4.18 14.55,-6.27 22.92,-1.74a44.44,44.44 0,0 0,20.48 6.36m0,66.75a167.3,167.3 0,0 0,44.7 -25.88c-3.4,-1.22 -5.4,-0.44 -8.37,-2.53s-11.68,-2.88 -19.17,1.31a33.98,33.98 0,0 1,-34.24 0c-7.58,-4.18 -16.29,-3.4 -19.17,-1.31s-5.05,1.31 -8.45,2.61a161.2,161.2 0,0 0,44.7 25.79"
      android:strokeWidth="1.22"
      android:fillColor="#0b50a0"/>
  <path
      android:pathData="M466.62,202.22c0.44,0.87 -0.35,2.96 0,3.75 0.87,1.74 0.87,2.61 2.09,3.4l2.96,0c9.15,0 20.04,-3.75 29.19,-3.75 8.8,0 14.2,2.88 19.17,2.88 2.96,0 5.49,-2.09 5.49,-4.18s-3.31,-3.31 -10.46,-3.31c-10.02,0 -15.42,2.53 -23.79,2.53 -12.55,0 -14.64,-4.62 -14.64,-6.71s2.09,-4.97 8.8,-4.97q3.22,0 4.62,-1.31c0.44,-0.87 1.66,-2.53 4.18,-3.75 2.88,-1.74 8.71,0 11.68,-2.09 0,1.74 -2.53,4.18 -4.62,5.05 4.18,-1.31 8.71,-1.31 9.58,2.09 -2.09,-1.31 -5.4,-0.44 -6.62,0 -1.74,0.35 -2.53,0.87 -4.18,1.22 1.66,-0.44 2.53,2.88 4.97,2.88 -2.44,2.09 -6.62,2.09 -10.8,0.44 -1.31,-0.44 -4.62,-1.66 -6.71,-1.66 -4.18,0 -6.71,0.87 -6.71,2.53 0,2.09 2.61,3.31 10.02,3.31 7.58,0 11.76,-2.09 23,-2.09 -0.87,-1.22 0.44,-2.53 -1.22,-3.75 4.18,0 8.71,1.22 10.46,4.18 -0.44,-2.96 2,-5.4 1.22,-7.93 2.53,0.44 4.62,2.88 4.97,4.97 0,-1.22 1.74,-1.22 1.74,-2.44a6.1,6.1 130.64,0 1,3.31 6.62c1.22,-2.09 2.53,-1.22 3.31,-3.75 0.87,1.74 0.44,6.71 -2.53,8.37 2.61,-1.74 6.27,-1.74 8.8,1.22 -2.53,-0.35 -3.75,0.87 -4.97,1.31A38.34,38.34 0,0 1,519.25 211.45c-6.62,-0.44 -7.49,-1.74 -11.68,-1.74 2.53,0.44 7.49,5.49 12.2,5.49q4.36,0.09 6.19,-0.44c1.22,-0.44 3.31,0 4.97,0.87 1.31,1.22 3.83,2.44 5.49,2.09q-0.7,2.35 -2.53,2.88c2.09,1.31 0.87,2.09 2.96,3.75 -0.87,0 -1.74,0.87 -2.96,0.44 1.22,1.22 1.22,2.96 2.53,3.75 -0.44,0 -1.74,0.44 -2.53,0 1.22,1.74 0,3.75 1.22,4.62 1.31,1.74 -1.22,3.75 -3.31,2.09 0.44,1.22 -0.87,2.53 -2.09,2.09s-2.09,-1.74 -1.74,-2.09c0,-0.44 0,-0.44 0.52,-0.87 -0.87,1.31 -2.53,1.31 -3.4,0.87 -0.35,-0.44 -0.87,-0.87 -0.87,-1.22 -2.88,0.87 -4.1,-1.74 -3.66,-2.96s2.88,-1.74 3.75,-1.22c0.87,0.35 1.66,0.87 2.53,0q1.31,-0.78 1.22,-2.09c0,-1.31 -1.22,-5.05 -6.71,-4.62 -6.27,0.44 -16.29,-1.22 -21.26,-2.96 -0.44,-0.35 -0.87,-0.35 -1.31,-0.87 0,1.74 -1.66,2.61 -1.66,4.27 0,2.09 0.87,3.75 3.4,4.53 -1.31,2.09 -3.4,1.74 -4.18,0.44 0,1.31 -1.74,2.09 -1.74,3.75 -0.35,-0.87 -0.87,-1.22 -1.22,-2.09 -1.22,2.96 -4.62,4.62 -6.27,3.83 -2.09,1.66 -4.53,1.22 -5.4,-0.44 -1.22,0.87 -2.96,0.87 -3.75,0 0,1.22 -0.87,0.87 -1.31,2.53 0,0.87 -1.66,1.22 -2.44,0.87 -0.87,-0.52 -1.31,-2.18 -0.44,-2.96q-2,1.31 -3.31,0c0,-0.87 0,-2.09 0.87,-2.61l0.78,0c-0.87,-0.35 -2.09,0 -2.96,-0.35 -0.87,-0.87 -0.87,-1.74 -0.44,-2.53 0.44,-0.44 1.74,-1.22 2.61,-0.44l0.35,0.44q0.09,-1.48 0.87,-1.66c0.87,-0.44 2,-0.44 3.31,0.87 0.87,1.22 1.66,1.22 4.18,0.87 2.09,0 5.4,-2.18 7.84,-4.27 -6.97,-1.22 -2.88,-5.84 -0.78,-7.49 -1.66,-0.44 -4.18,-0.44 -5.84,0.44 -8.71,4.62 -17.95,10.02 -23.79,8.37a5.23,5.23 0,0 0,0.87 2.44c-1.31,0 -3.83,0 -4.62,-1.22 -0.44,0.87 -1.22,2.96 -0.44,4.62 -0.87,-0.87 -3.31,-1.74 -4.62,-2.09 0.87,0.87 0,2.53 0.44,3.75 -1.22,-1.22 -3.31,-2.09 -4.18,-1.22s-2.53,1.22 -3.75,0.87a6.97,6.97 0,0 1,-3.31 2.44c0,-0.87 0,-2.09 -0.44,-2.53 -0.44,1.31 -1.74,2.96 -3.31,3.75 0,-0.87 0,-2.09 -0.44,-2.88 0,1.66 -3.31,2.53 -3.75,3.75q0.52,-2 0.44,-3.31a6.1,6.1 124.54,0 1,-4.18 2.88c0.35,-0.87 0.87,-1.74 0.35,-2.53 -0.35,1.74 -2.88,2.61 -4.97,2.09 0,0.87 -1.74,2.09 -3.75,0.87 -1.31,-0.44 -2.09,-1.74 -1.31,-2.53 -1.66,0 -3.31,-1.66 -2.44,-2.88q0.17,-0.61 0.35,-1.31c-1.22,-0.87 -1.66,-2.53 -0.35,-3.75 0.35,-0.44 1.66,-0.44 2.44,-0.44 0,-3.31 2.96,-3.31 4.18,-0.87 0.87,1.31 3.4,3.4 5.49,2.96 0.87,0 1.22,-0.44 2.09,-0.44a37.47,37.47 0,0 0,10.46 -5.4q0.44,-1.74 0.35,-2.88c-1.66,0.87 -3.31,1.66 -4.18,2.88l0,-1.22c-1.22,0 -3.31,-0.44 -4.18,-0.44 0.44,-0.44 0.87,-2.53 1.74,-2.96 -2.09,0.44 -4.18,0 -5.93,-0.35 0.87,-0.87 0.87,-2.09 1.31,-3.4 -0.87,0.44 -2.96,-0.35 -4.62,0 0.44,-1.22 0.87,-2.88 2.09,-3.31q-2.61,0.52 -5.84,-0.87c1.74,0 2.53,-2.09 3.75,-2.09 -3.31,0 -4.18,-1.66 -5.84,-2.44 1.22,0.35 2.09,-0.87 3.31,-0.87 -1.22,-0.44 -3.31,-2.53 -2.44,-3.75q-0.52,0.87 -1.74,1.74c-0.87,0.35 -2.44,0 -3.75,-1.31 -0.87,-1.74 -0.44,-3.75 1.31,-4.18a2,2 0,0 1,-1.31 -3.4c0.44,-0.87 2.09,-2 2.96,-0.87l1.22,1.74c-0.44,-0.87 -0.44,-2.53 -0.44,-3.75 0.44,-1.22 1.31,-1.22 2.61,-1.74 1.13,0 2,1.31 2,2.61 0,1.22 0.44,2 0.87,3.75l0.35,-1.74c0,-1.66 2.09,-1.22 3.4,-0.87 1.22,0.52 1.66,2.96 0,4.27 -0.87,0.87 -0.44,2.09 0.44,2.88 2.88,4.62 7.84,8.37 12.11,8.8q0.09,-2.61 2.09,-4.62c0,-0.87 0.35,-2.09 0.87,-2.88 0.35,-2.53 1.57,-3.75 3.22,-2.96 -0.35,-0.44 -0.87,-0.87 -0.87,-1.22 -1.22,-1.31 -2.44,-3.4 -1.57,-5.05s3.31,-1.22 4.18,-0.35c-0.44,-2.61 2.44,-4.62 2.44,-5.93 0.44,1.31 1.31,2.96 0.87,4.18 0.44,-1.66 3.31,-3.75 5.4,-4.53 -0.87,1.22 1.74,3.31 1.31,4.53 0.87,-2.09 4.18,-3.31 6.62,-3.31 -0.87,0.87 -0.87,2.53 -0.87,3.31 1.31,0 3.49,-0.35 4.27,-1.66 0,1.74 -0.87,3.4 -1.74,4.18 0.44,-0.44 2.09,-0.44 3.4,-0.44 -1.31,1.31 -0.87,2.61 -2.09,3.4 1.66,-0.87 3.75,-0.87 4.18,0.87 0.87,1.22 -0.44,3.31 -1.31,4.53 -0.87,0.44 -1.66,1.22 -2.09,1.22z"
      android:strokeWidth="1.22"
      android:fillColor="#f9d050"
      android:strokeColor="#000"/>
  <path
      android:pathData="M440.83,207.27c-2.96,1.22 -3.83,4.18 -4.27,5.84"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M440.39,210.15c-4.18,2.61 -5.93,4.62 -5.05,9.24m7.06,3.4c2.61,-0.87 4.18,-1.74 6.27,-5.49"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M447.45,218.95c-0.87,1.74 0,4.62 -1.22,5.84a7.84,7.84 0,0 0,4.18 -5.84"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M448.32,223.57c1.66,-0.87 4.18,-3.4 5.4,-6.27 -0.44,1.22 0,3.31 -0.87,4.62 2.09,-2.09 2.96,-3.4 3.4,-5.49l0.35,1.74c0.87,-0.87 1.74,-1.74 1.74,-3.4m-0.09,1.22l0,2.09c1.74,-1.22 2.18,-2.88 2.18,-4.97"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460,215.64c2.53,-1.74 2.88,-4.18 2.88,-7.93m0,4.18c1.74,-1.31 2.09,-2.09 2.09,-5.93m-5.4,-11.24c-0.87,0 -1.74,0.44 -1.74,1.74 0,0.78 -1.22,2 -1.22,3.31q-0.52,2.44 -2.44,1.66c-1.31,-0.87 -1.31,-0.87 -1.31,-2.09 0,-1.74 2.09,-2.09 2.09,-3.75 0.44,-0.87 0.87,-2.09 1.31,-2.09 -2.09,1.22 -5.05,0.35 -5.49,-2.09"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M463.31,195.94c-0.87,0.44 -1.74,-0.87 -2.88,-0.35 -0.44,0 -1.74,0.35 -2.09,0"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M457.47,198.03c0.44,0.44 2.53,1.31 4.62,0.44 -1.74,0.44 -2.96,-1.66 -1.31,-2.88m-7.06,-1.74c-1.74,0 -2.96,1.31 -2.53,2.61 0.44,1.22 2.09,1.22 3.31,0.35m-3.75,17.95q0.78,-0.87 0.87,-1.74 1.39,0 2.09,-0.78c1.66,0 2.88,-1.22 3.31,-2.44 0.87,0 1.31,-2.09 1.74,-2.96 0.35,0.44 1.22,0 1.66,-1.74 0.87,0.52 2.09,0 2.44,-1.22m-17.08,-2.88c0,0.44 -0.87,1.22 -1.74,1.74 0.44,0.78 0,2.88 -0.78,3.22 0.44,0.87 0,2.09 -0.44,2.96 0,0.44 0.44,1.74 0.44,2.09m4.18,-18.39c-0.87,2.09 -0.44,4.62 1.22,5.05"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M448.32,199.25c-0.44,2.18 -0.44,5.05 -0.87,6.36 -0.44,0.87 -1.22,3.75 2.09,4.53 3.75,0.87 5.84,0.44 7.93,-5.84m6.27,16.29c0,0.44 -0.44,1.31 -1.22,1.31m4.1,-2.09c0,0.87 -0.87,2.09 -1.22,2.44m4.18,-3.31c0,0.87 0,2.09 -0.87,2.53m-24.57,-28.41c0.35,0 0.87,0.87 1.22,1.66 -1.22,-0.87 -2.53,1.22 -1.74,2.53m-0.78,2.88 l-1.66,1.31m0.44,2.44c-0.44,0 -0.87,0.44 -1.22,0.44m-18.82,-7.93c0,-0.44 0,-0.44 0.44,-0.87m-4.18,-0.35c-0.44,0 -0.44,0 -0.87,-0.44m1.22,6.27l0.87,0m-2.53,-2.53a4.36,4.36 0,0 0,-2.44 -1.22m25.44,16.73c-2.96,2.44 -4.18,4.53 -4.62,7.49m7.49,-5.49a9.58,9.58 0,0 1,-3.31 3.83m-12.9,8.71a9.58,9.58 0,0 1,-8.37 -1.22c-0.44,-0.44 -0.87,-0.44 -1.31,-0.87m22.13,-3.66c-0.35,1.57 -0.87,3.66 -1.66,4.1 3.4,-1.74 6.27,-4.18 6.71,-7.93M414.42,230.62c1.31,-0.44 2.61,0.44 3.4,-0.44m69.27,-13.33c0.87,0.44 2.09,0.87 2.53,1.22 0.44,-0.87 2.09,-2.09 3.31,-2.44m-35.46,10.02c-0.44,-1.74 0.44,-3.75 2.96,-4.62m-19.26,11.68c0.44,-0.87 0.44,-2.09 0.87,-2.88m-19.26,4.18c-0.44,0.87 -1.22,1.66 -1.22,2.44m-5.05,-1.66s0,-0.44 0.44,-0.44c0.87,-0.35 2.09,-1.22 2.53,-2.09m53.85,-3.31c-0.87,0.44 -1.22,0 -1.66,-0.44m1.22,2.53s-0.35,0.44 -0.87,0.44m2.96,1.66q-0.7,0.78 -1.22,0.87m13.33,0q1.22,-0.52 1.74,-2.96m-7.15,2.53c1.22,-0.44 2.09,-1.31 3.31,-2.53m8.37,-0.87q-0.09,-1.31 0.44,-2.44m30.5,6.27c-0.87,-0.87 -0.87,-2.09 0.44,-2.96 0.87,-0.44 1.22,-0.87 2.09,-1.22"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M498.77,219.3c-6.62,-2.88 -6.19,-6.62 -11.68,-6.19m41.39,19.26c0.44,0.35 0,1.66 0,2m3.75,-2c-0.44,1.22 -0.44,1.66 -0.44,2.88M500.08,193.07l-3.49,0m-31.63,14.2c0,0.87 1.31,2.53 4.62,2.53 -0.44,0 -0.87,-0.44 -0.87,-0.44m-2.09,-7.15c-0.35,0.44 -0.87,0 -0.87,0 -0.78,-0.87 0.52,-2.44 1.74,-3.31q-1.74,0.52 -2.96,0.44c-0.87,2.53 -3.31,3.75 -4.97,2.09 -0.44,1.66 -2.53,3.75 -4.18,3.31q-2.44,-1.39 -1.74,-3.31c-2,2.44 -4.53,0.87 -5.32,0 -0.44,-0.87 0,-2.96 0.87,-5.49"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M493.37,218.08c-1.22,0.87 -2.53,0 -3.75,1.31 -0.87,0.87 0.44,1.22 1.22,1.22 -0.35,1.31 -0.35,1.74 1.74,1.74 -0.87,1.22 1.66,1.66 1.22,3.31a4.36,4.36 0,0 0,0.87 -4.18c-0.44,-1.31 -2.61,-2.09 -1.31,-3.4"
      android:strokeWidth="1.22"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M455.38,204.74q-2.44,-1.39 -1.74,-3.31 -0.61,1.22 -2,1.22c-1.74,3.75 -3.75,7.15 -6.27,7.15 -2.96,0.35 -2.53,3.31 -0.44,3.31 4.62,0.87 6.71,-0.87 10.46,-8.37m-41.3,-10.02c1.22,-1.22 0.35,-2.53 -0.44,-2.88 -1.31,-0.44 -3.75,0 -5.05,2 1.31,-0.35 2.61,-0.35 2.96,0.44 0.44,0.87 1.22,1.74 2.53,0.44m2.09,-7.49c2.88,0 3.75,0.44 4.1,1.66 0.44,0.87 -0.35,2.09 -1.22,2.09 -1.22,0 -2.53,-0.87 -2.09,-1.22 0.44,-0.87 0.44,-2.09 -0.87,-2.61zM424.45,193.85c2.09,0.44 2.96,-0.87 1.74,-2.44q-0.7,-2.09 -3.83,-1.74c2.09,1.31 0,3.4 2.09,4.18m-15.42,7.15a3.49,3.49 0,0 0,4.18 1.74c1.22,-0.52 1.74,-1.74 1.22,-2.61 -0.87,-1.22 -2.09,-1.66 -2.44,0 -0.87,1.74 -2.09,1.31 -2.96,0.87m6.27,25.44c-0.87,-1.66 -3.31,-1.66 -5.4,0 0.87,-0.44 2.44,0 2.88,0.87s1.31,0.87 1.74,0.87c0.35,-0.44 1.22,-0.87 0.78,-1.74m3.31,-2.44c0,-2.09 -2.88,-2.61 -4.53,-1.74 0.87,0 2.09,0.87 2.09,1.74 0.35,0.87 1.22,1.22 1.66,1.22q1.31,-0.7 0.87,-1.22zM414.42,231.49c-2.44,-0.44 -3.75,2.53 -3.75,4.53 0.44,-1.22 1.74,-2 2.96,-1.66q1.74,0 1.66,-1.22c0.44,-0.87 0,-1.22 -0.87,-1.74zM414.86,238.55c0.87,0.44 1.74,-0.44 2.09,-1.66 0,-0.87 1.74,-0.87 2.09,-0.44 0.87,0.44 0.87,2.09 -0.44,2.96 -0.87,0.44 -2.88,0.87 -3.75,-0.87m58.03,-12.55c-0.87,-0.35 -2.88,-1.22 -4.53,0 1.22,-0.35 2.09,0.44 2.44,1.31 0,0.87 1.31,1.22 1.74,0.44 0.35,-0.44 0.87,-1.31 0.35,-1.74m-3.75,2.96c-1.66,-0.87 -3.75,1.22 -4.18,2.96a6.1,6.1 0,0 1,2.96 -1.31q1.48,0.52 1.66,-0.87 0.78,-0.61 -0.44,-0.78m2.18,3.4c-1.31,0 -2.96,1.66 -2.96,3.31q0.61,-1.39 2.09,-0.87c0.87,0 2.09,-0.44 2.09,-1.22q0,-1.31 -1.31,-1.31zM474.98,235.68c-0.87,-0.44 -2.09,0 -2.09,1.22s0,2.53 -0.35,2.96q1.31,-1.22 2.44,-1.74c0.44,-0.35 0.87,-1.66 0,-2.44m47.23,-3.75c-0.87,-0.87 -2.09,-0.44 -2.53,0.35s-0.87,2.96 0,4.62c0,-0.87 0.87,-2.09 1.74,-2.09 1.22,-0.44 1.57,-2.09 0.78,-2.88m2.53,1.66c-1.74,0.87 -1.74,3.75 -0.44,5.4 0,-0.87 0.44,-2.09 1.22,-2.53 1.31,0 1.74,-1.66 1.31,-2.44 -0.87,-0.87 -1.74,-0.44 -2.09,-0.44m4.97,1.66c-0.87,-0.44 -1.74,0.44 -1.74,1.22 -0.35,1.31 0.52,2.96 1.74,3.75q0.09,-1.39 0.87,-2.09c1.22,-0.87 0.87,-2.44 -0.87,-2.88m3.75,-0.44c-0.87,0 -1.66,1.22 -0.87,2.53q0.52,1.31 2.96,0.87c-0.44,0 -1.22,-1.31 -0.87,-1.74q0.61,-1.48 -1.22,-1.66"
      android:strokeWidth="1.22"
      android:fillColor="#073163"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m417.39,312.88 l20.91,-64.31 20.91,64.31 -54.81,-39.65L472.2,273.23Z"
      android:strokeWidth="1.22"
      android:fillColor="#f9d050"/>
  <path
      android:pathData="m421.57,307.04 l43.39,-31.72l-53.41,0l42.96,31.72 -16.21,-50.89z"
      android:strokeWidth="1.22"
      android:fillColor="#024919"/>
  <path
      android:pathData="m495.9,312.88l20.91,-64.31 20.91,64.31 -54.81,-39.65L550.71,273.23Z"
      android:strokeWidth="1.22"
      android:fillColor="#f9d050"/>
  <path
      android:pathData="m500.08,307.04l43.39,-31.72l-53.41,0l42.96,31.72 -16.21,-50.89z"
      android:strokeWidth="1.22"
      android:fillColor="#024919"/>
  <path
      android:pathData="m456.6,358.36l20.91,-64.31 20.91,64.31 -54.81,-39.65L511.41,318.72Z"
      android:strokeWidth="1.22"
      android:fillColor="#f9d050"/>
  <path
      android:pathData="m460.78,352.53l43.39,-31.72l-53.41,0l42.96,31.72 -16.21,-50.89z"
      android:strokeWidth="1.22"
      android:fillColor="#024919"/>
  <path
      android:pathData="M555.59,181.83l0,81.39c0,70.58 -33.37,94.8 -78.07,113.97 -44.7,-19.17 -78.42,-43.39 -78.42,-113.97l0,-81.47l156.5,0z"
      android:strokeWidth="1.22"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
</vector>
