<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0v480h640V0z"
      android:fillColor="#68bfe5"/>
  <path
      android:pathData="M420.9,193.2v136.4c0,44.6 80.6,71 80.6,71s80.5,-26.4 80.5,-71V193.2z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M490.3,241.2v61.4h-69.4v22.3h69.4v71.5l11.2,4.3 11.1,-4.3V325H582v-22.4h-69.4v-61.4z"
      android:fillColor="#d21034"/>
  <path
      android:pathData="M420.9,193.2H582V246H420.8z"
      android:fillColor="#d21034"/>
  <path
      android:pathData="M424.75,225.29a7.31,9.04 90,1 0,18.08 0a7.31,9.04 90,1 0,-18.08 0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M425.75,221.5a4.06,8.04 90,1 0,16.09 0a4.06,8.04 90,1 0,-16.09 0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M425.75,229.67a4.06,8.04 90,1 0,16.09 0a4.06,8.04 90,1 0,-16.09 0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M425.75,225.29a4.06,8.04 90,1 0,16.09 0a4.06,8.04 90,1 0,-16.09 0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="m578,235.4 l-1.3,-3.6s-3.6,-1.5 -3.9,-3.4c-0.6,-5.1 -2.4,-7 -5.2,-7.8a6,6 0,0 0,-4.5 0.5c-0.8,1.2 -5,1.2 -7.2,1.3 -2.3,0 -2.4,-1 -2.4,-1 -1.7,0 -2.5,-1.3 -2.5,-1.3 -1.1,-2.3 -2,-3.2 -2.6,-3.5 3,0 5.2,-1.3 8.6,-4.8 3.6,-3.8 6.2,-2.4 6.2,-2.4 -1.8,3.6 0.6,3.3 0.6,3.3 4.8,-2.5 0.6,-8.7 -0.8,-8.4 -1.5,0.3 -1.9,-0.7 -1.9,-0.7 0,1.1 -1.6,1.6 -1.6,1.6 1.2,-7.1 -3,-9 -3,-9 -0.6,0.6 -1,1.9 -1,1.9 -1.2,4.3 -5.1,3.6 -9.5,1.5 -4.4,-2 -16,2.2 -19.7,3.7s-10.4,3.7 -16.4,0.4 -7.5,-2.8 -9.9,-2.1c-2.3,0.6 -6.5,1.6 -8,0.8a9,9 0,0 0,-3.4 -0.9c0.4,-0.2 -0.2,-1.2 -0.2,-1.2 -1.9,-2.5 0.2,-4 0.2,-4s-3.4,1 -2,6.8l0.6,0.8s0.4,1.9 1.4,2.4v0.4s0.9,-0.4 1.1,0.8 1.6,0 2.1,-1c0,0 -0.9,5.4 5.7,6.7 0,0 -0.7,-0.9 -0.5,-1.4 0.3,-0.6 -0.8,-1 -0.7,-1.8s-1.6,-1.2 -0.3,-1.9 2,-1.9 5,-0.1c2.9,1.7 5.2,1.3 5.2,1.3s-1.8,-3.6 -0.8,-3.6 5.4,5.4 7.5,5.2c2.2,-0.3 6.4,-2.8 8.5,-1.9 0,0 0.2,-1 8.2,-2.4s18,-5.4 21.7,-1.9c0,0 0.4,2.8 2,3.5 0,0 -0.3,5.8 -10.2,2.6l-3.6,-1s-0.6,-1.7 -19.5,3.2c0,0 -1.1,0.4 -1.9,0.9s-1.7,-0.3 -5.5,1c-3.7,1.5 -17.7,6 -26.3,4.4 -1.9,-0.6 -3.6,-0.3 -3.7,-0.6 0,-0.3 1,0.1 1,0.1q-0.9,-1 -0.9,-0.8c-7.2,-0.8 -6,-4 -6,-4l0.4,1c1.2,-1.6 -0.4,-5.2 -0.4,-5.2h-0.1v-0.3l0.5,-0.4c3.5,-3 7.5,-3.5 7.5,-3.5 -2.4,-3 -7.4,-1.4 -7.4,-1.4 0.1,-3.5 -1.4,-7.7 -1.4,-7.7a12,12 0,0 0,-7.7 4.7c-0.2,-3.7 -4.9,-5.8 -4.9,-5.8 1.8,2.1 0.7,7.2 0.7,7.2h0.2,-0.5l-0.1,1.1h-0.2s-1.7,0.6 -3.7,5.2c0,0 -0.2,1 -1,2v-0.4s-2.3,3 -7.4,0c0,0 -7,-4.1 -9.6,-4 -2.4,0.2 -4,1.3 -3.9,2 0,0 -5.7,-0.4 -7.6,2.8 0,0 -4.3,1.6 -0.6,5 0,0 0.5,1.6 1.5,2.4 0,0 -0.1,-3.2 0.7,-2.3l0.5,0.6c0.1,0.4 0.5,1.6 1.2,2.3l0.6,-0.2c0,-0.8 0.2,-1.8 0.4,-2.2h0.2l1,1.6 1,-0.6c0.6,0 1.3,0.5 1.3,0.5l1.3,-1.1c1.3,0.2 1.7,-2.4 1.7,-2.4a4,4 0,0 0,3.1 -0.5c0.6,2 5.2,4.3 5.2,4.3h0.2l-0.6,0.4c2.1,1.5 6,1.1 6,1.1 -1.6,0.6 -1.4,3.7 -1.4,3.7 -1,0.4 -1,1.3 -1,1.3 3.2,1.3 5.3,0.2 5.3,0.2q0.1,1.4 0,1.2c-1.3,2.7 -1.9,5.4 -1.9,5.4h0.1v0.2s-5.7,2 -8.1,4.7c0,0 -0.3,-0.8 -1,-0.8s-2.8,-1.8 -3.8,-2.1 -2.5,0.3 -3,-0.2c-0.3,-0.4 -0.1,-3.6 -0.8,-4l-0.7,-2c-0.4,1 -2.4,0.8 -2.4,0.8l-0.8,0.6 -0.8,-0.1q-0.3,0.1 -0.5,1l-0.3,0.3v-0.6c-0.3,0.3 -1.1,-0.1 -1.4,-0.1q-0.3,0 -0.5,0.9l-1.9,0.1c-0.3,0.2 0,2.6 0,2.6 -2.3,1.2 -1.2,3.1 -1.2,3.1 1.2,6 11.5,3.3 12.3,4.1s2.6,1 3.5,1c1,0 2.1,2.2 3.4,2.4s0,-1.8 6.1,-3.2 8.2,-4 8.2,-4c1.9,-0.4 2.4,-2.7 2.5,-3.6l0.2,1.4c0.3,2.1 -1.2,3 -1.2,3l5.3,-1.7c2.1,-0.5 1.8,-2.4 1.8,-2.4 2,5.8 5.7,6.2 5.7,6.2 0.7,-2.2 0.6,-2.9 0.6,-2.9h1.7c0,-1.3 -1,-2.2 -1.7,-2.6l2.6,1.5 1,-0.1c0,0.9 1.3,1.7 1.3,1.7l0.2,-1.2c1.2,1.6 3.4,1 3.4,1l-0.2,-0.8c4.7,2.9 12.2,-0.8 12.2,-0.8 2,0.4 2.3,-0.8 2.3,-0.8 2.2,0.3 2.8,-1.3 2.8,-1.3 5.3,-0.4 9.1,-4.2 9.1,-4.2 2.7,-0.2 2.8,-2.1 2.8,-2.1s2.6,0.5 5.2,-2.7c6,-8.5 14.8,-4 14.8,-4l-5.1,0.3c-13.5,-0.1 -7.9,9 -7.9,9h0.8c1.3,0.2 5.7,3.7 4,6.2v0.2c-2.8,3.7 -7.8,0.2 -9.3,-3 -1.4,-3.2 -4.4,-2.5 -4.4,-2.5s-7.5,1.3 -4.4,7.3c0.1,1.5 1,3.2 3.8,4.8 1.5,2.2 6.5,2.4 10.1,1.6C524,244 533,240 533,240c0.7,-2 3,-1.5 5,-4.4 2,-2.8 -2.9,-3.8 -4,-5.4 -1,-1.5 0.3,-1.2 0.3,-1.2 4.4,2.3 4.8,-5.2 4.8,-5.2a7,7 0,0 0,4.3 5.5c2.3,-2.3 0.6,-7.2 0.6,-7.2 5.7,10.2 9.1,8.5 9.1,8.5 -3,-1.8 -1.5,-4.4 -1.5,-4.4 2.7,10.8 14,1.4 14,1.4 -0.6,1.6 0,2.5 1,3.5s2.2,1.4 1.6,3.3c-0.5,1.8 -5.8,1.4 -5.8,1.4s-5,-0.2 -4.6,4c0.3,1 3.6,1.4 5,1.1 0,0 -0.6,0.2 1.3,0.4h2.2c2.1,0 4.5,-0.4 4.5,-0.4 2.9,-0.6 2.4,-1.6 2.4,-1.6 4.5,-0.8 5,-3.8 5,-3.8z"
      android:strokeWidth=".8"
      android:fillColor="#ffd100"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M429.6,216.7s0.3,-0.5 0.7,-0.6q0.6,0 1.2,0.8l0.3,-0.2s-1.2,-1.8 -0.8,-3q0.4,-1 1.6,-1.1v-0.3q-1.5,0.3 -1.8,1.3l-0.2,0.7q0,1 0.5,1.7l-0.8,-0.2a1,1 0,0 0,-0.8 0.6zM570.6,240.7c-3,0.5 -7.5,0.7 -7.9,-0.2 0,-0.2 0,-0.8 2.4,-2.4l-0.2,-0.5q-3.1,2 -2.7,3c0.7,1.8 7.2,0.8 8.5,0.6zM512.2,242.4q-1,-1.6 1,-4.3l-0.5,-0.3q-2,3 -1,5z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M508.1,237.8q0,2.9 3.9,5.1l0.3,-0.5q-3.6,-2.2 -3.6,-4.6c-0.2,-2.5 2.5,-4.5 2.6,-4.5l-0.3,-0.4c-0.2,0 -3,2.1 -2.9,5zM560.1,238.5c-0.8,1.3 -0.5,2.8 -0.5,2.8h0.5c0,-0.1 -0.2,-1.4 0.5,-2.5a3,3 0,0 1,2 -1.2v-0.5q-1.7,0.3 -2.4,1.4zM441,215.1h-0.2v0.2s-0.2,1.6 -1,2h-0.6l-1.3,-0.7q-0.2,-0.3 -0.8,-0.2a2,2 0,0 1,-0.4 -1.4q0.1,-1 1.6,-2l-0.2,-0.3q-1.5,1 -1.7,2.3c0,1 0.5,1.6 0.5,1.6v0.1h0.2q0.3,-0.1 0.6,0.2l1.4,0.8h1c0.7,-0.5 1,-1.9 1,-2.2 3.2,0.7 5.8,-3.3 6,-3.4l-0.3,-0.2s-2.7,4 -5.8,3.2"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M433.1,214.5a3,3 0,0 0,0.4 2q0.5,0.8 1,0.9 0.7,0 1.2,-0.6 0.3,-0.3 0.6,-0.4l0.3,0.2 0.2,-0.2 -0.5,-0.3q-0.5,0 -0.8,0.5t-1,0.5q-0.3,0 -0.8,-0.8a2,2 0,0 1,-0.3 -1.7c0.4,-1.4 2.1,-2.5 2.1,-2.5l-0.1,-0.3c-0.1,0 -1.9,1.2 -2.3,2.7m-4.4,20.7q0.2,-0.8 1.3,-1.4c-0.2,0 0.6,0.6 1.3,0.6l0.5,-0.3c0.2,0.2 1.4,1.3 1.4,2.7q0,0.5 -0.2,0.9l0.3,0.1c0.9,-2.1 -1.4,-4 -1.4,-4h-0.2l-0.4,0.3c-0.6,0 -1.3,-0.6 -1.3,-0.6q-1.4,0.6 -1.6,1.6zM439,229.8c-0.7,0.1 -2.5,0.4 -3.1,1.4q-0.4,0.6 -0.1,1.2l0.2,0.6c0.6,1.6 1.2,3.2 1.9,3.6l0.1,-0.3c-0.6,-0.3 -1.2,-2 -1.7,-3.4l-0.2,-0.6a1,1 0,0 1,0 -1c0.5,-0.7 2.2,-1 3,-1.1 0.4,0 0.5,1.5 0.6,2.4h0.3c-0.1,-1.4 -0.3,-2.8 -1,-2.8"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M433.9,231.4q-0.4,0.6 -0.3,0.9c0,0.1 0.2,4 2,4.8l0.1,-0.3c-1.6,-0.7 -1.8,-4.5 -1.8,-4.6q0,-0.2 0.2,-0.6 0.4,-0.4 1.6,0l0.1,-0.3q-1.3,-0.4 -1.9,0z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M432.4,231.7c-0.8,0.6 -0.7,2.2 -0.7,2.3h0.3s0,-1.6 0.6,-2q0.4,-0.3 1,0l0.1,-0.4q-0.8,-0.3 -1.3,0z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M476.41,210.19s0.86,1.99 0.86,3.52q0,0.4 -0.2,0.66l-0.33,-0.86 -0.2,0.66s-0.4,0.93 0.2,1.93q1.2,1.99 5.98,2.46l0,0.07l-0.27,0.07 -0.13,0.13l0,0.27q0.13,0.4 1.2,0.33l0.47,0.07 0.4,0.13 -0.07,-0.07q1.06,0 2.39,0.53l-0.66,0.13 0.73,0.33c0.07,0 5.78,2.33 8.11,4.19 -0.66,0.6 -3.52,2.66 -8.44,1.46l-0.8,-0.2 0.53,0.66s3.26,3.72 3.92,5.98c-1.13,0 -5.45,0 -6.98,-1.33l-0.33,0.33s1.99,3.06 2.53,5.39c-0.66,0.13 -1.99,0.2 -2.86,-0.93l-0.4,-0.47 -0.2,1.33c-0.4,-0.33 -0.93,-0.73 -0.93,-1.26l0,-0.27l-1.2,0.2 -2.53,-1.53 -0.4,-0.27 -0.27,0.47 0.4,0.2c0.53,0.4 1.46,1.13 1.53,2.19L476.87,236.72l0,0.33c0,0.2 0,0.93 -0.6,2.46 -0.73,-0.13 -3.52,-1.13 -5.19,-5.85l-0.53,0.13l0,0.13c0,0.47 -0.13,1.53 -1.6,1.93l-4.32,1.33a3.32,3.32 0,0 0,0.53 -1.86l0,-0.53l-0.13,-1.73q-0.07,-1.46 0.93,-1.99l-0.27,-0.53c-0.07,0 -6.38,2.59 -9.51,1.53 0.13,-0.66 0.66,-2.79 1.73,-5.05l-0.13,0.13c0.2,-0.13 0.4,-0.4 0.2,-1.46l0,-0.33l-0.33,0.13s-1.99,0.93 -4.92,-0.13q0,-0.47 0.8,-0.93l0.2,-0.07l0,-0.53c0,-0.8 0.07,-2.66 1.2,-3.06l-0.13,-0.53s-3.39,0.27 -5.45,-0.86a7.31,7.31 127.96,0 1,2.99 -1.26l0.2,0l0,-0.27a3.99,3.99 0,0 0,-0.33 -1.53c0.8,0.27 1.73,0.47 2.06,0.2l0.13,-0.07 -0.13,-2.06l-0.53,0l0.07,1.73 -1.99,-0.47 -0.8,-0.27 0.53,0.66a2.66,2.66 0,0 1,0.47 1.6,8.64 8.64,0 0,0 -3.32,1.53l-0.33,0.27 0.33,0.2c1.6,1.06 4.06,1.2 5.32,1.13a5.32,5.32 0,0 0,-0.86 2.99l0,0.27a1.99,1.99 0,0 0,-1 1.46l0,0.2l0.13,0.07a7.31,7.31 0,0 0,5.25 0.33l0,0.6c-1.33,2.79 -1.99,5.52 -1.99,5.52l0,0.27l0.2,0c2.59,1.06 7.11,-0.33 9.18,-1.06q-0.4,0.66 -0.47,1.66l0.2,2.26c0,1.53 -1,2.13 -1.06,2.13l0.2,0.53 5.32,-1.6c1.2,-0.33 1.66,-1.06 1.86,-1.66 2.06,4.92 5.32,5.32 5.52,5.39l0.2,0l0.07,-0.2a9.31,9.31 0,0 0,0.66 -2.66l1.6,-0.07l0,-0.33q0,-0.66 -0.27,-1.2l0.93,0.53 0.86,-0.13c0.2,0.86 1.26,1.6 1.4,1.66l0.33,0.2 0.2,-1.13c1.33,1.13 3.12,0.8 3.26,0.8l0.2,-0.07l0,-0.27c-0.27,-1.8 -1.46,-3.92 -2.19,-5.05 2.33,1.06 6.58,1 6.78,1l0.27,0l0,-0.33a19.95,19.95 0,0 0,-3.72 -5.98c5.45,1 8.38,-1.86 8.51,-1.99l0.2,-0.2 -0.27,-0.13a35.9,35.9 0,0 0,-7.71 -4.19l0.6,-0.13 -0.66,-0.33a8.64,8.64 0,0 0,-3.86 -0.93c-0.66,-0.8 -1,-0.86 -1.06,-0.93 -3.72,-0.4 -5.05,-1.4 -5.58,-2.19l0,-0.2l0.13,-0.2q0.47,-0.66 0.47,-1.73c0,-1.73 -0.86,-3.66 -0.86,-3.72z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M459.45,209.13s-0.4,1.99 -2.13,2.99q-1.99,1.06 -5.05,-0.2l-0.27,-0.07l0,0.27a3.32,3.32 0,0 0,1.33 2.13q1.33,1 3.92,0.8c-0.6,0.66 -2.39,2.93 -2.39,5.19l0,0.33c0.27,0.13 3.06,0.93 4.39,-0.73 0,0.66 0.27,2.99 1.13,3.86q0.73,0.86 0.66,1.8 -0.07,0.86 -0.13,0.93l-0.13,0.4 0.33,-0.27c0.13,-0.07 2.73,-1.99 3.52,-4.45 0,0 0.47,-0.66 1.26,-0.86l0,0.33c0,1 0.4,2.73 3.19,3.79l0.2,0.07l0,-0.66c0,-0.73 0,-1.99 0.73,-2.66a1.99,1.99 0,0 0,0.4 -2.19c0.6,0.13 1.93,0.6 2.46,1.6 0.8,1.46 3.52,0.27 3.66,0.27l-0.07,-0.33s-1.2,0.27 -1.93,-0.2a1.33,1.33 0,0 1,-0.53 -1l-0.13,-1.6q0,-1 -0.47,-1.46c0.33,0 1.06,0 1.33,0.66 0.53,1.06 2.19,1.13 2.26,1.13l0.6,0.07 -0.47,-0.33s-1.46,-0.93 -1.2,-2.19l0.2,-0.93c0.4,-1.66 1.06,-4.39 0.47,-6.12l-0.27,0.13q0.2,0.66 0.2,1.46c0,1.46 -0.47,3.32 -0.66,4.45l-0.27,0.93l0,0.33q0.13,1.33 0.86,1.8c-0.47,-0.07 -1.13,-0.33 -1.33,-0.86 -0.47,-0.93 -1.53,-0.93 -1.99,-0.86l-0.4,0.07 0.27,0.27q0.66,0.33 0.53,1.53l0.13,1.53q0.13,0.8 0.66,1.2 0.66,0.33 1.13,0.33c-0.8,0.2 -1.93,0.33 -2.33,-0.4 -0.73,-1.46 -2.86,-1.8 -2.93,-1.86l-0.33,0l0.13,0.27s0.27,0.4 0.27,0.93q0,0.53 -0.47,1.13c-0.73,0.73 -0.86,2.13 -0.86,2.86l0,0.27c-2.39,-1 -2.79,-2.46 -2.79,-3.32l0,-0.66l0.13,-0.2l-0.27,0a2.66,2.66 61.84,0 0,-1.66 1.2,9.97 9.97,0 0,1 -3.12,4.12l0.07,-0.66q0.07,-0.93 -0.66,-1.93c-1,-0.93 -1.13,-3.99 -1.13,-3.99l0,-0.53l-0.27,0.47c-1.06,1.86 -4.12,0.93 -4.19,0.93 -0.13,-2.33 2.66,-5.39 2.66,-5.45l0.27,-0.27l-0.4,0q-2.66,0.33 -4.12,-0.66a3.32,3.32 0,0 1,-1.06 -1.66q2.99,1.13 5.05,0.07a4.65,4.65 0,0 0,2.26 -3.19c1.99,-4.39 3.66,-5.12 3.66,-5.12l-0.07,-0.27c-0.13,0 -1.86,0.66 -3.86,5.32"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M463.91,202.55l-0.13,0l-1.6,2.19l0.47,0a20.61,20.61 56.1,0 1,13.1 6.12l0.2,0.2 1.53,-1.66 -0.13,-0.2c-4.32,-6.78 -13.36,-6.65 -13.5,-6.65zM464.04,203.15c1.06,0 8.78,0.2 12.77,6.18l-0.86,0.93a21.94,21.94 0,0 0,-12.83 -5.92z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M462.65,207.07a7.31,7.31 0,0 0,-1.4 3.19l0.07,0.27a4.65,4.65 0,0 0,-1.33 3.66q0,1.33 0.4,2.53l0.2,0.47c0.8,2.26 1.53,4.26 5.19,3.06 3.46,-1.06 3.59,-1.33 4.45,-2.79l0.27,-0.33c0.47,-0.8 1,-0.93 1.53,-1.13 0.66,-0.2 1.33,-0.4 1.99,-1.6l0.53,-0.86c0.66,-0.86 1.46,-1.99 1.53,-3.32l-0.27,0a6.65,6.65 0,0 1,-1.46 3.12l-0.66,0.93a2.66,2.66 0,0 1,-1.73 1.46,2.66 2.66,0 0,0 -1.73,1.2l-0.27,0.4c-0.8,1.4 -0.93,1.6 -4.32,2.66s-3.99,-0.6 -4.79,-2.93l-0.2,-0.47c-0.93,-2.53 -0.2,-5.05 1.06,-5.98l0.4,-0.33 -0.6,0.07l0,-0.07c0,-0.47 0.47,-1.73 1.33,-2.99 0.8,-1.06 2.66,-2.06 2.66,-2.13l-0.13,-0.27c-0.13,0.07 -1.93,1.13 -2.73,2.19"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M461.05,216.31q0.86,0.53 2.46,0c0,0.33 0,1.13 0.66,1.66q1.2,1 3.66,0.4l0,-0.33q-2.39,0.6 -3.46,-0.33a1.99,1.99 0,0 1,-0.6 -1.6l0,-0.27l-0.2,0.13q-1.46,0.66 -2.39,0c-1,-0.66 -0.93,-2.46 -0.93,-2.46l-0.27,0s-0.13,1.99 1.06,2.79"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M462.91,213.85q-0.53,0.27 -0.53,0.73l0,0.2c0.13,0.8 1,1.33 1.73,1.53s1.73,0.27 2.06,-0.2q0.27,-0.27 0,-1.13a2.46,2.46 135,0 0,-3.19 -1.13zM464.24,216.04c-0.66,-0.2 -1.46,-0.66 -1.53,-1.33 0,0 -0.07,-0.47 0.33,-0.6a2.19,2.19 135,0 1,2.79 1.06q0.27,0.47 0.07,0.66c-0.2,0.33 -0.93,0.4 -1.66,0.2m-0.27,1.8c-0.2,1.66 0,1.73 0.07,1.73l0.13,0l0.13,-0.07 0.93,-1.33 -1.2,-0.53l0,0.2zM464.31,218.11 L464.77,218.31 464.24,219.04zM461.52,216.51 L461.45,218.04 462.78,216.44 461.45,216.31zM461.78,216.64l0.4,0l-0.4,0.53z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M460.59,215.98s1,1.93 1.2,2.66q0.2,0.66 1.33,1.13t1.8,-0.07l0.86,-0.73q0.33,-0.47 0.66,-0.33l0,-0.33q-0.53,0 -0.86,0.4l-0.86,0.66c-0.53,0.4 -1.13,0.27 -1.46,0.13q-1.06,-0.4 -1.2,-1c-0.2,-0.66 -1.13,-2.59 -1.2,-2.66zM461.91,210.26a3.32,3.32 0,0 0,-1.6 1.86l0.27,0.07s0.47,-1.2 1.4,-1.66a1.99,1.99 0,0 1,1.6 0l0.07,-0.27q-0.93,-0.4 -1.8,0zM469.1,213.72c0.13,0 2.13,1.6 1,3.46l0.2,0.2c1.33,-2.19 -1,-3.92 -1,-3.99zM462.98,213.98s-0.33,1.13 1.33,1.53c0,0 -1.53,0 -1.66,-0.4 -0.07,-0.4 0,-1.26 0.33,-1.13m4.99,1.33q0,0.2 -0.2,0.2l-0.2,-0.13q0,-0.2 0.2,-0.2t0.2,0.2zM468.1,216.11q0,0.13 -0.13,0.13l-0.2,-0.13 0.2,-0.2q0.13,0 0.13,0.2"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M466.9,215.71m-0.2,0a0.2,0.2 45,1 1,0.4 0a0.2,0.2 45,1 1,-0.4 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M467.43,216.78a0.2,0.2 45,1 1,-0.4 0q0,-0.13 0.2,-0.13zM462.71,211.92a0.2,0.2 45,0 1,-0.4 0,0.2 0.2,45 0,1 0.33,0z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M462.65,212.65m-0.2,0a0.2,0.2 45,1 1,0.4 0a0.2,0.2 45,1 1,-0.4 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M461.65,212.32m-0.2,0a0.2,0.2 45,1 1,0.4 0a0.2,0.2 45,1 1,-0.4 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m462.11,213.39 l-0.13,0.2a0.2,0.2 45,0 1,0 -0.4zM465.31,207s2.33,1.46 0.07,4.19c0,0 1.13,-0.86 1.2,-2.26s-0.8,-2.53 -1.33,-1.93zM474.28,210.93c-0.8,0.4 -1.86,-0.33 -2.39,-1s-1.66,-0.86 -2.39,0.4c-0.66,1.2 -1.66,1.66 -1.66,1.66s1,-0.53 1.99,-1.66 1.6,-0.2 2.59,0.33c0.93,0.53 1.13,0.66 1.8,0.33l0,-0.07z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M474.08,211.12a1.33,1.33 0,0 1,-1.46 -0.07c-0.66,-0.53 -1.13,-1.2 -1.86,-0.8 -0.8,0.4 -2.19,1.46 -2.19,1.46s2.33,-1.6 2.79,-1.2c0.4,0.33 1.46,1.2 2.06,0.93z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M472.29,212.06c-0.73,-0.53 -1.46,0.66 -2.06,0.53 -0.66,-0.13 -1.13,-1.2 -1.13,-1.2s0.53,1.06 1.33,0.86c0.86,-0.27 1.4,-0.8 1.93,-0.4s-0.07,0.2 -0.07,0.2"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M471.16,212.25a1.33,1.33 0,0 1,-1.6 -1.26s0.47,1.2 1.73,1z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M471.09,211.32q0.2,0.53 -0.33,0.53 -0.73,0 -0.66,-0.53c0,-0.33 0.93,-0.27 1,0m-5.19,-1.99a0.66,0.66 0,0 1,-0.47 0.4q-0.4,0 -0.33,-0.4c0.07,-0.4 0,-0.47 0.33,-0.47s0.47,0.2 0.47,0.47"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M464.24,207.67s2.53,0 1.33,3.12c0,0 0.93,-1.53 0.33,-2.66 -0.66,-1.06 -1.53,-0.8 -1.53,-0.8s-0.27,0.2 -0.13,0.33"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M464.24,208.93q0,0.93 1.2,1.93l0.2,-0.27q-1.13,-0.86 -1.13,-1.66c0,-0.8 0.4,-0.86 0.4,-0.86l-0.2,-0.2s-0.47,0.4 -0.53,1.06z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M464.77,209.46q0.13,0.53 0.8,0.8l0.13,-0.27q-0.53,-0.2 -0.66,-0.66c-0.13,-0.53 0.27,-1.13 0.27,-1.13l-0.27,-0.2s-0.47,0.8 -0.27,1.46m3.39,1.73s0.47,1 -0.2,2.06l-0.66,1.06s0.8,-1.53 0.47,-2.13 0.4,-1 0.4,-1m9.71,10.5c2.53,0.47 8.31,4.99 8.31,4.99l0.33,-0.4c-0.2,-0.2 -5.92,-4.65 -8.58,-5.12zM480.13,228.01c0.13,2.33 2.66,3.72 2.66,3.79l0.33,-0.47s-2.33,-1.33 -2.46,-3.32zM473.95,228.54c0,2.53 2.39,5.58 2.53,5.72l0.4,-0.33s-2.39,-3.06 -2.39,-5.39zM465.9,228.81q0.53,1.26 0.27,1.46 -0.33,0.33 -0.4,0.27l0.07,0.53q0.4,0 0.8,-0.53 0.4,-0.66 -0.27,-1.99z"/>
  <path
      android:pathData="M429.6,216.3s0.1,1.7 1.4,2.7c0,0 -0.1,-1.8 0.3,-2.6 0,0 -0.7,-0.8 -1.7,-0.1zM432.2,217.3s0.3,1.6 1.2,2.3l0.6,-0.1s0.2,-2 0.5,-2.2 -1.3,0.7 -2.3,0z"
      android:strokeWidth=".2"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="m434.5,217.3 l1,1.6 1.2,-0.7 -0.2,-1.8s-0.7,-0.2 -2,0.9zM439.2,217.6 L437.9,218.7s-0.7,-0.5 -1.2,-0.5l0.2,-1.7s1,-0.7 2.3,1.1zM429.9,233.6s-0.2,-2.4 0,-2.5c0,0 1,0.3 1.4,0 0,0 0.5,1.8 0.4,2.6 -0.2,0.8 -1.1,0.5 -1.8,0zM435.9,231.2s-0.1,-2 0.2,-2.3c0,0 2,0.3 2.4,-0.8l0.7,2s-2.1,-0.3 -3.4,1z"
      android:strokeWidth=".2"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M433.8,231.6s0.3,-2.1 0.7,-2.2c0,0 1,0.3 1.3,-0.1v2.6s0,-1.1 -2,-0.3zM431.5,231.9s0.5,-1.9 0.8,-1.9 1,0.5 1.4,0.2v1.4s-1.6,-0.4 -1.9,1.9z"
      android:strokeWidth=".2"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M461.7,270.4s5.2,4.9 3.9,9.2c0,0 3.3,-7.3 -0.3,-9.5 -3.6,-2 -3.7,-0.5 -3.6,0.3z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M455,275.1s-1.3,2 0.2,3.3c0,0 -1.7,2.4 -0.4,4.4 0,0 -1,2.1 -0.8,4.4 0,0 -0.8,3.5 1,5.1 0,0 2,1 3,0.1 0,0 1.7,-2.6 0.6,-5.3 0,0 1.2,-2.3 -0.2,-5.2 0,0 0.4,-1.6 -0.7,-3.5 0,0 0.7,-1.2 0.1,-2.2 0,0 -2.5,-2.7 -2.8,-1z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M456.5,269s-6.2,-12 -10.6,-12.5 -4.6,-4.8 -4.6,-4.8 0.5,2 4,2 10.2,5 11,8.7c0.7,3.7 1.5,3.2 1.5,3.2z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M456.5,268.1s2.3,-15.1 12.2,-15.4c9.9,-0.2 6.6,0.8 9,-0.8 0,0 -6.6,3.4 -10.7,4 -4.2,0.4 -7.6,8.5 -7.4,10 0,1.5 -2.3,4.4 -3.4,3.4s0.3,-1.2 0.3,-1.2z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M458.8,273.8s2.3,-6 6.1,-4c0,0 -3,-5.8 -8.4,-0.8 0,0 2.4,3.3 2.3,4.8zM451.3,267.8s3.2,13.4 -1.8,17.5c0,0 0.2,-6.9 -2.9,-11 -3,-4.2 3,-8.7 4.7,-6.5z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M451.7,270.1s5.9,5.7 6.2,7.6c0.4,1.8 1.8,-5.7 -1,-7.8s-5.7,-2.5 -5.7,-2.5l0.5,2.8z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M440.8,285s0.5,2.3 2,2.8c0,0 0,1.7 1.4,2.2 0,0 -0.7,1.5 0.4,2.4 0,0 -0.2,3 0.8,3.5 0,0 2.7,-0.1 3.6,-0.9 0,0 0.5,-1.9 -1,-3.8 0,0 0.3,-1.8 -1.2,-2.8 0,0 0.5,-2.5 -1.4,-3.4 0,0 -0.3,-2 -1.4,-3 0,0 -2.2,3.1 -3.2,3z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M443.7,282s-7.7,-2.8 -12,1.2c0,0 6.6,1 7.4,2.3s4.9,-2.2 4.6,-3.5z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="m441.3,286.8 l-1.4,-1c-0.7,-0.6 -7.2,-2.3 -8.1,4 0,0 -2.4,-6.3 1,-7 3.5,-1 6.2,1 8.5,4m-5,-12.8s2.3,5.2 3.2,5.4 1.5,-0.6 1.5,-0.6 -3.1,-4.6 -4.8,-4.8zM434.5,266.1s-2,-6.6 -4.4,-4.8 -1.8,6.5 -1.8,6.5 -4.7,-5 -2,-8.8 8.2,0.4 8.2,7.1z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M439.9,279.8s0,1.3 0.9,1.6 2.5,1 2.5,0.3 -1.3,-3.7 -1.8,-3.3 -1.4,0.4 -1.6,1.4z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M438.4,272s-0.3,-5.7 3.5,-7.9c0,0 0.7,0.2 1.1,-1 0,0 -3,7.3 -2.1,11l-0.1,0.4"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M439.1,265s6.8,-14 15,-9.1c0,0 -8.6,1.7 -11.7,7.9l-3.1,4.6z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M435.5,270.1s-2,-9.4 -3.3,-10.6c-1.2,-1.3 -3.3,-2.6 -5.9,-0.5 0,0 4.4,-3.9 9,-0.6 4.7,3.2 3.7,8.4 3.7,8.4l-0.5,6.5 -3.1,-2.9"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M442.8,281.3s-8.3,-15.7 -13.7,-4.5c0,0 -1,2.9 0.1,4 0,0 -3,-2.3 -1.6,-7.8s10.5,-3 13.2,1.5 3.3,6.8 3,7c-0.4,0.3 -1,-0.2 -1,-0.2zM466.1,285.8s6,-12.4 9.5,-6.8c0,0 1,2.8 -0.5,4 0,0 -2.4,-3 -4.5,0.6 -2.2,3.5 -1.3,3.2 -1.3,3.2s-2.2,0.6 -3.2,-1z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475,283s3.3,1.7 2.2,5.2c0,0 4.4,-8.4 -1.6,-9.2 0,0 1.1,2.7 -0.5,4zM475.5,274.4s7.9,2.3 8.1,5.7c0,0 1.4,-6.6 -3.1,-8.6 0,0 -5.4,1 -5,3z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M479,264c3.4,-6.6 7.6,2.1 7.6,2.1s1.5,-6.9 -2.7,-8.1c-4.3,-1.3 -7,14.7 -7,14.7l-5,1s1.3,-13.8 8.8,-15h1"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M473,268s-4.8,-9.8 -8,-10.8 1.7,-2.5 3.7,-0.6 5.8,4.3 5.4,9.3zM468,281.4s1.4,-6.6 5.2,-8.6 7.3,-1.3 7.3,-1.3 -4.5,1 -5.3,3.3 -2,2.4 -2,2.4z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M466,285.8s-2,4 -1.6,4.6c0,0 -2.2,3.3 -1.3,5 0,0 2.2,1.7 3.2,1 0,0 2.6,-3.7 1.9,-5.7 0,0 1.8,-2.4 1.1,-3.9 0,0 -2.6,0.1 -3.2,-1z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M455,278.6c0.2,0.1 1.5,0.7 2.3,0.4l0.6,-0.5 -0.5,-0.2c-0.3,0.6 -1.7,0.1 -2.1,-0.1l-0.2,0.4zM454.9,282.6 L454.7,283.1c0.1,0 2.7,0.5 3.9,-0.6l-0.4,-0.4c-1,0.9 -3.3,0.4 -3.4,0.4zM454.2,287 L453.9,287.4s3,2.1 4.9,-0.2l-0.5,-0.3c-1.5,1.9 -4,0.2 -4.1,0zM442.7,287.6 L442.8,287.9c0.1,0 2.6,-1 2.8,-2.9h-0.3c-0.1,1.6 -2.5,2.6 -2.6,2.6m1.4,2.3v0.3c0.2,0 2.5,-0.2 2.9,-1.8h-0.3c-0.4,1.3 -2.5,1.5 -2.6,1.5m0.5,2.4v0.3c0.1,0 2.7,0.1 3.6,-1.3l-0.3,-0.2c-0.8,1.3 -3.3,1.2 -3.3,1.2m19.7,-1.7s1.1,1.2 2.4,1.1a2,2 0,0 0,1.7 -0.8l-0.4,-0.4q-0.6,0.7 -1.3,0.7c-1,0 -2,-1 -2,-1z"
      android:strokeWidth=".8"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m542,299.8 l-0.3,0.3 0.4,-0.3zM551.3,297.5q-0.2,-0.2 -0.3,-0.2l-0.4,-0.8h-0.3l-0.7,-1.4v-0.7s-0.2,0 -0.3,-0.2v-1.6l-0.1,-0.5 -0.3,-1.1v-1q0,-0.5 -0.1,-1l0.1,-0.9v-5.4l-0.1,-1 -0.1,-0.4v-1.6,2.6l-0.2,-4.3 -3.4,-1.6s-0.3,6.2 -0.2,7c0,0.9 0.4,6.7 0.2,7.4l-0.5,4.3s-1.6,1.2 -2.2,2.4c-0.7,1.3 -0.5,1.7 -0.7,1.7l1.5,-0.2 -1.1,0.8 0.7,0.1 0.2,0.2 0.2,-0.2c0.3,0 0.5,0.4 0.7,0.3l0.4,-0.2 0.2,-0.4q0.2,0 0.4,0h0.7q0.5,-0.2 0.9,-0.7 0.3,-0.3 0.7,-0.3 0.4,0.1 1,0.3 0.3,0 0.5,-0.2l0.4,-0.6 0.8,-0.1 0.4,0.1h0.6l0.3,0.2 0.2,-0.4z"
      android:strokeWidth=".3"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M544.5,299.7h0.6v0.2h0.2l0.3,0.2h0.3l0.4,0.1q0.3,0.1 0.6,-0.1t0.6,0.2q0.1,0.2 0.2,0l0.1,-0.1 0.3,-0.1 0.3,-0.1 0.4,-0.2 0.4,0.2h0.2l0.7,-0.5q0.4,-0.1 0.7,0h0.3q0.1,0 0.4,0 0,-0.1 0.2,-0.3l0.7,0.4c0.1,0.1 0.7,0.2 0.6,-0.2s-0.4,-0.1 -0.5,-0.4v-0.6l-0.3,-0.4q-0.2,0 -0.4,0l-0.2,-0.6 -0.3,0.2q-0.2,0 -0.3,-0.3c-0.3,-0.3 -0.4,0.2 -0.7,0.1q-0.3,-0.2 -0.5,-0.7t-0.5,-0.4q-0.2,0.3 0,0.7h-0.5q-0.2,0 -0.3,0.2 -0.2,0.7 -0.7,0.6 -0.2,0 -0.4,0l-0.5,0.2h-0.4l-0.4,-0.4a0.1,0.1 0,0 0,-0.2 0l-0.1,-0.2q-0.2,0 -0.3,0.3l-0.1,0.5 -0.3,0.4 -0.4,0.1c-0.2,0 -0.4,0.3 -0.4,0 -0.2,-0.4 0.3,-1.1 -0.2,-1q-0.1,0 -0.3,0.2v0.7c-0.1,0.3 -0.7,0.8 -0.6,1 0,0.3 0.4,0 0.4,0l0.2,0.4c0.2,0.3 0.7,-0.4 0.7,0m-2.1,-2.7v-0.2l-0.4,-0.1q-0.3,0.1 -0.5,0.4c-0.2,0.4 -0.2,1 -0.6,1h-0.5v1s-0.2,0 -0.2,0.2q0,0.4 0.4,0.2h1l0.5,-0.1q0.3,0 0.5,-0.2l0.2,-0.4v-0.4q0.1,-0.1 0.3,-0.4c0.1,-0.7 -0.6,-0.2 -0.5,-0.9"
      android:strokeWidth=".3"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M550.8,277.9q-0.1,1.5 -1.4,1.6 -1.2,-0.1 -1.4,-1.6 0.2,-1.5 1.4,-1.6 1.3,0.1 1.4,1.6zM546.3,275.9q-0.1,1.5 -1.4,1.6c-1.3,0.1 -1.4,-0.7 -1.4,-1.6q0.2,-1.6 1.4,-1.6c1.2,0 1.4,0.7 1.4,1.6z"
      android:strokeWidth=".5"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M548.7,276.5q-0.1,1.6 -1.4,1.6 -1.3,-0.1 -1.4,-1.6 0.1,-1.5 1.4,-1.6 1.3,0.1 1.4,1.6z"
      android:strokeWidth=".5"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M546.4,278q-0.1,1.5 -1.4,1.6 -1.3,-0.1 -1.4,-1.6 0.1,-1.5 1.4,-1.6 1.3,0.1 1.4,1.6z"
      android:strokeWidth=".5"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M548.5,279.3q-0.1,1.4 -1.4,1.6 -1.2,-0.1 -1.4,-1.6 0.2,-1.6 1.4,-1.7 1.3,0.1 1.4,1.6z"
      android:strokeWidth=".5"
      android:strokeColor="#000"/>
  <path
      android:pathData="M549.2,261.5s13.9,-19.4 28.8,0.7c0,0 0.1,2 -1.3,4.2l-0.3,-1.7 -1,0.3 -0.2,-1 -1.6,-0.1 0.5,-2.2 -1.7,1.4 0.2,-1.5 -2,2 0.3,-4.3 -2.2,3.7 0.3,-2.5 -0.8,1.1 -0.4,-1.5 -0.7,1.5 -1,-0.7v1l-1.8,-1.5 -0.6,2 -0.9,-2.2 -0.2,1.5 -0.9,-1.7 -0.1,1.9 -2.9,-1.3 0.6,1.8 -1.3,-1 0.2,1 -1.9,-0.4 0.2,1.1 -2,-0.4 0.3,1 -2.7,-0.3 1,1 -0.3,0.6s15,-5 16,13.8l-2.7,1.3 0.8,-1.5 -0.8,-0.5 -2.3,-1.7 0.7,-2.4 -0.8,0.3 -0.1,-1.4 -0.4,0.2 -0.9,-1.3 -0.2,0.5 -0.6,-1.7 -0.7,1.2 -0.3,-2.8 -1,1.8 -0.2,-1 -0.3,0.6 -1.3,-1.5 -0.4,1 -0.5,-0.8 -0.3,1 -1.4,-1.4v1l-1,-0.7 -0.3,0.5 -2.1,-1.1 -0.1,1 -1.2,-1 -0.3,1s13.6,7.8 9.4,20.4l-0.8,1.6 -0.2,-4.2 -1.3,2.2 0.9,-4.9 -0.9,1 -0.1,-2 -1.1,2 -0.4,-2.2 -0.5,0.6 -0.8,-3.2 -0.6,1.3 -1,-3.6 -0.8,1.5 -0.5,-3.1 -0.5,1.9 -1.5,-2.8 -0.2,1.1 -0.9,-2.2 -0.5,0.7s-2.5,-0.1 -3.3,-0.7 -2.8,3.7 -2.8,3.7l0.2,-2.6 -0.9,1.8v-1.8l-1.6,5.3 -0.4,-3.8 -0.8,3.3 -0.4,-1 -0.3,1.3 -0.5,-0.5 -0.4,1.5 -0.8,-0.6v2l-0.8,-1 -0.6,3 -0.4,-1.2v1.3l-0.6,-0.7 -0.6,3 -1,-1.3v2.2l0.3,1.6 -1,-0.5 0.4,2.6s-3.9,-7.2 0.8,-14.2c4.6,-7 10.9,-9 10.9,-9l-0.7,-0.6v-0.6l-3.8,1.9v-1.1l-1,0.8 -0.2,-1.2 -0.6,1.1 -0.1,-0.5 -1,1.4 -0.4,-2 -0.6,2.4 -0.6,-1.7 -1.3,3.5V270l-1,2.8 -0.4,-3 -0.7,3.4v-3l-1.2,3 -0.7,-3 -0.5,3 -0.8,-2.3 -0.7,3.4 -1.2,-3 -0.6,4.5 -0.7,-1.8 -0.2,3.3 -0.8,-2 0.8,4.2 -1,-1.6 0.3,2.6 -1.6,-2.9v3.4l-0.8,-1.1 -0.4,1.3s-0.3,-13 8.6,-15.6 14.6,0.8 14.6,0.8l-3.4,-2.8 0.4,-0.9h-1.2l0.5,-1 -2.7,0.1 0.8,-1 -2,0.3v-0.6l-1.3,1 0.5,-1.2 -1.7,1.2 -0.1,-1.7 -1,1.4 -0.4,-1.5 -4,4 1,-4.1 -0.7,0.9 -0.1,-1 -1.1,1.8 -0.3,-1.2 -0.5,1.5 -0.4,-1.8 -0.8,1.8 -1,-1.6 -0.3,2.1 -0.6,-1.1 -0.8,1.6 -1,-1v1l-2,-0.9 0.4,1.5 -1.5,-1.2 -0.4,2 -0.8,-1 -0.7,2.5 -0.6,-2.4 -0.6,1.9s7.8,-20.2 27.6,-7c0,0 2.2,2.1 2.7,3.6s0.1,0.2 0.1,0.2l-0.4,-13.9 0.8,6 0.5,-2 -0.1,3 2,-4.3s-2.4,7.2 -1.5,9.9c0,0 0.9,-8.4 3.9,-9.4l-2.8,6 0.9,-0.1 -0.6,1.2h0.7l-0.9,2 -0.5,1 0.5,1z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m547.1,269.6 l0.4,0.4s1.3,-1.4 3.4,0.4l0.3,-0.4c-2.5,-2.1 -4,-0.4 -4,-0.4zM546.5,262.8v0.5l0.9,-0.4 -0.7,1.2 1.5,-0.5 -1.8,2.3 0.4,0.3 2.9,-3.7 -1.8,0.6 0.8,-1.3 -1.6,0.6 0.3,-1.9h-0.5z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m543.9,263 l0.6,1.3 -1,-0.4 2.9,5.2 0.4,-0.5 -0.6,1.5 0.5,0.2 1.2,-3.2 -0.4,-0.3 -1,1.3 -1.8,-3.1 0.8,0.3 -0.8,-1.7 1.1,0.6v-1.9h-0.5v1l-1.8,-1.1zM542,269.4 L542.2,269.9q1.2,-0.5 2,-0.2 0.7,0.6 0.7,1l0.5,-0.2s-0.2,-0.9 -1,-1.2q-0.9,-0.5 -2.4,0.1m2.3,5.4 l-0.5,-2.8h0.6l0.4,2.7zM548.1,274.4 L547.4,272.2 547.9,272.1 548.6,274.1zM549.5,275.9 L549,273.8 549.5,273.7 550.1,275.8z"/>
  <path
      android:pathData="M442.6,340.2s-2.4,-1 -3.5,-0.2c-1,0.7 -0.9,1.9 -1.8,1.8 0,0 3.6,0.8 4.3,-0.1l1,-1.2m0.4,0.7s-1.6,2 -1,3.3c0.5,1.2 1.7,1.3 1.4,2.2 0,0 1.5,-3.3 0.8,-4.3s-1,-1.1 -1,-1.1m-2.8,-5.8s-2.5,0.8 -2.8,2c-0.2,1.4 0.6,2.1 -0.1,2.7 0,0 3.2,-1.8 3.1,-3v-1.5m1.2,0.7s1.2,-2.3 0.5,-3.4c-0.8,-1.2 -2,-1 -1.8,-2 0,0 -1,3.5 0,4.4l1.1,1m-2.2,-0.9s0,-2.6 -1,-3.2c-1.2,-0.7 -2.2,-0.1 -2.5,-1 0,0 0.7,3.6 1.8,4l1.5,0.3m4.4,4.2s2.7,-1 3,2 -0.2,2 -0.2,2 -1.4,-3.2 -3,-3.7z"
      android:strokeWidth=".5"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m443,341.3 l0.2,2.2h0.4l-0.3,-2.2 1,-4.9h-0.4l-1,4.9z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m440.5,340.6 l-0.1,0.3c1.3,0.5 3,-1 3.1,-1l-0.2,-0.3s-1.7,1.3 -2.8,1m3.4,-2.9 l-6,-3 0.2,-0.5 6,3z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m439.5,337 l-0.4,-0.4 1.2,-1.2 0.4,0.4zM441.6,336.4 L440.3,333.6 440.8,333.4 442.1,336.2zM443.4,339.8v0.6c1.7,-0.1 2,1 2,1l0.6,-0.2c0,-0.3 -0.7,-1.5 -2.6,-1.4"/>
  <path
      android:pathData="M471.2,341c2,-1 2.4,-5 2.4,-5 -0.5,1.4 -4.2,2.9 -4.2,2.9 2.7,-1.7 6,-9.4 6,-9.4 -0.8,2.7 -8,5 -12.6,7.6s-2.2,10 -2.2,10c-1.3,-0.9 -3.8,-4.8 -4.3,-8.3s-1.8,-4.9 -5,-5.3c-3.1,-0.4 -5,3.2 -5,3.2l-4.7,2.4 4.8,0.2s3.4,1.1 3.4,3.8 -3,13.3 2,19.3c3.3,4.1 16,6.5 16,6.5"
      android:strokeWidth=".7"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M461.9,349.3s1,-4.8 4.1,-4.6 4,-3.6 5.9,-4.1c1.9,-0.6 11.5,-3.2 12.5,-7.8 0,0 -0.9,7.6 -9.7,11.4 0,0 7.1,-2.3 8,-3.9 0,0 -3.2,6.8 -10.9,7.9 0,0 7.2,-0.6 8.2,-2.5 0,0 -3.2,4.2 -8.5,4.8 0,0 5,2 6.2,0.3 0,0 -2.7,3.2 -6,3.4l1,0.2s-3.2,3.8 -6.7,1.3 -3.6,0.6 -3.6,0.6"
      android:strokeWidth=".7"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M469,356.5s4.1,6.8 4.5,10.4c0,0 7.8,8.5 10.7,9 0,0 -1.4,0.6 -4,-1.5 0,0 2.2,2.2 2.6,2.3s-1.7,-0.2 -2,-0.8l1.4,1.5 -0.5,-0.2s-1,-0.1 -1.6,-0.5c-0.5,-0.4 -0.1,1 -0.1,1s-1.2,-0.3 -1.6,-0.7 0.1,0.9 0.1,0.9l-1,-0.3 -0.8,0.5s0,0.5 -0.5,0c-0.5,-0.3 -1.4,0 -1.4,0s-0.6,0.6 -1,0.2c-0.3,-0.4 -1.5,1.6 -1.5,1.6l-5,-12.3"
      android:strokeWidth=".7"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m473.9,378.7 l-6.4,-12.2 0.6,-0.4 6.4,12.3zM479.5,377.9 L468.5,365.6 469.1,365.1 480.1,377.4zM482,376.9L469.5,365l0.5,-0.5 12.5,11.7z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M450.55,337.38s-0.8,-1.46 -1.53,-0.86q-1,0.73 -1.2,0.6s0.66,0 1.13,-0.33q0.66,-0.66 1.6,0.66zM446.16,336.72 L446.62,337.65 446.09,339.18 446.49,339.31 446.96,337.65 446.49,336.58z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M450.41,337.18s-0.66,0.66 -1.46,0.53l-0.86,-0.2l1.2,0c0.47,0 1.06,-0.2 1.13,-0.33"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M450.21,337.32s-0.8,-0.8 -1.33,-0.53c0,0 0.93,-0.33 1.46,0.47z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M450.08,337.18s-0.86,0.6 -1.8,0l-0.07,-0.13l0.13,0s1.13,0.47 1.73,0.13"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M449.35,337.05m-0.27,0a0.27,0.27 0,1 1,0.53 0a0.27,0.27 135,1 1,-0.53 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M450,359.6s-0.7,1.2 -0.3,2c0.3,0.7 2,1 2.1,2s0.8,1 1.5,1l0.9,-0.3s-3.1,-1.6 -4,-4.3z"/>
  <path
      android:pathData="M549.4,334.8s5.6,-7.4 10.6,-5.5 8.1,3 8.1,3 0.1,4.9 -2.5,4.9 -5.2,-5 -7.6,-3.5a13,13 0,0 0,-4.5 4.2c-0.1,0.7 -6.3,0.8 -4.1,-3.1z"
      android:strokeWidth=".3"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.2,370.4s-1.6,1.6 -0.9,2 2.1,0 2.1,-0.6 -1,-1.6 -1.2,-1.4z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M527,372s-3,-1.7 -5.2,2.2c-2.3,3.8 -4.1,10.1 -5.4,11 -1.3,0.8 12.8,-6.2 13,-9.2 0,0 0.9,-3.8 -0.6,-3.7s-1.6,0.2 -1.9,-0.4z"
      android:strokeWidth=".5"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M527.3,372.4s-3,0.7 -3.6,4.9 -4.7,6.3 -5,6.5m49.4,-51.6s-4.4,1.6 -3.7,4.7"
      android:strokeWidth=".3"
      android:fillColor="#964b36"
      android:strokeColor="#000"/>
  <path
      android:pathData="M533.7,341.3s0.2,-10.6 8.9,-10.2c1.6,0.2 0.8,2.1 0.8,2.1s2,-1.3 2.8,0.8c0,0 2.7,-1.2 3,1.5 0,0 3,-0.4 2.6,2 0,0 2,-0.5 2,1.2 0,0 2.3,-0.9 2,1.5 0,0 2.6,-1.7 2.3,1.4 0,0 2.6,-1.9 3.5,1.2 1,3.2 -2.6,9.8 -6.1,11.5"
      android:strokeWidth=".3"
      android:fillColor="#fff200"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M557.9,342s1.1,8.2 -3.5,11.3l0.1,0.2c4.9,-3 3.7,-11.5 3.7,-11.6h-0.3zM552.2,351 L552.4,351.2c3,-2.9 3.7,-10.6 3.7,-11h-0.3s-0.7,8 -3.6,10.8m-2.7,-1.9h0.3a26,26 0,0 1,4.1 -10.3l-0.2,-0.1s-3.8,5.5 -4.2,10.4m-2.2,-1.4h0.3c0.5,-5.7 4.4,-10 4.4,-10.1l-0.2,-0.2s-4,4.5 -4.5,10.3m-3,-2h0.3c0.3,-6.7 4.8,-10 4.8,-10l-0.2,-0.3s-4.6,3.4 -5,10.4zM541.5,344.1h0.3c-0.2,-6.2 4.5,-10 4.6,-10l-0.2,-0.2s-5,3.8 -4.7,10.2m-5,-2.3h0.3a10,10 0,0 1,6.8 -8.4v-0.3a11,11 0,0 0,-7.1 8.7"/>
  <path
      android:pathData="M528.6,354.2s-1.5,-9.9 4.4,-13c0,0 1.1,-0.2 1.7,1.2 0,0 3.1,-1.3 3.2,1.3 0,0 1.6,-1.2 2,0.5 0,0 3.3,-0.7 2.7,1.8 0,0 2.8,-0.8 2.5,1.6 0,0 2.8,-1 2.4,1 0,0 3,-0.6 2.2,1.8 0,0 3.7,-0.4 2.6,2.1 0,0 2.7,-0.7 2,1.4 0,0 3.6,0.7 2,3a24,24 0,0 1,-9.8 7"
      android:strokeWidth=".3"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M525.4,364.1s-2.4,-8.2 2.2,-10.3c0,0 0.9,-0.2 1,0.6 0,0 1,-1.3 1.8,-0.1 0,0 2.3,-1.1 2.9,0.6 0,0 1.5,-1.3 2.3,0.6 0,0 1.5,-0.6 1.5,1 0,0 2,-1 2,1 0,0 2,-0.8 1.5,1.2 0,0 3,0.5 2.1,1.9 0,0 2.7,0.7 1.7,1.8 0,0 2.8,-0.3 1.3,1.6 0,0 2.2,-0.7 1,1.7s-5.2,5 -8,5.4"
      android:strokeWidth=".3"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.3,370.6s-5.1,-5 -2.7,-6.4c0,0 1,0.3 1.5,1.4 0,0 1.2,-1.8 2,0 0,0 1.8,-0.6 1.8,0.7l1.4,1s1.7,-0.1 1.4,1c0,0 1.9,-0.3 1.8,0.7 0,0 1.6,0.1 1.5,1.2 0,0 2.8,0.5 1.5,1.9 0,0 -4.8,1 -8.3,-0.4 0,0 -1.7,-0.6 -1.9,-1.1z"
      android:strokeWidth=".3"
      android:fillColor="#00a651"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M545.9,362.4v0.3a36,36 0,0 0,8.6 -8.7l-0.3,-0.2c0,0.1 -5.4,7.5 -8.3,8.6m-1.8,-1.3 l0.2,0.3c2.6,-1.5 7.9,-8.5 8,-8.8l-0.1,-0.2c-0.1,0.1 -5.5,7.2 -8,8.7zM542.6,360.5 L542.8,360.7a48,48 0,0 0,7 -10.3l-0.2,-0.1s-4.2,7.9 -7,10.2m-1.3,-1.8h0.3c0.5,-3.8 6,-9.9 6,-10l-0.3,-0.1c-0.2,0.2 -5.5,6.2 -6,10.1m-10.9,-4.7h0.3c-0.4,-5.5 4.1,-11.5 4.2,-11.5l-0.3,-0.2s-4.7,6.2 -4.2,11.7m2.9,0.5h0.3c-0.8,-4.4 4.4,-10.6 4.4,-10.7l-0.2,-0.2c-0.2,0.3 -5.3,6.4 -4.5,10.9m4.4,1.6h0.3c-0.2,-5 4.6,-10 4.7,-10l-0.2,-0.3c0,0.1 -5,5.2 -4.8,10.3m-10.5,9h0.3c-1.8,-4.7 1.2,-10.6 1.3,-10.6l-0.3,-0.2s-3.1,6.1 -1.3,10.9zM529.2,365.5 L529.5,365.4c-1.6,-4.7 1,-11 1,-11l-0.2,-0.2s-2.7,6.5 -1.1,11.3m8.3,4.7v0.2c5,-1.7 8.3,-6.3 8.3,-6.3l-0.3,-0.2s-3.2,4.5 -8,6.3m-1,-1.1v0.3c4,-1.8 7.7,-6.8 7.7,-6.8l-0.2,-0.2s-3.7,5 -7.5,6.7m-1,-0.4 l0.2,0.3c3,-2.3 7,-8 7,-8l-0.2,-0.2s-4,5.7 -7,7.9m-5,-2.9h0.4a21,21 0,0 1,2.5 -10.9l-0.3,-0.1s-3.2,6 -2.5,11zM532.7,367.2 L532.9,367.4c3.2,-2.9 4.3,-10.6 4.3,-11h-0.3s-1,8 -4.2,10.8m2.2,0.8 l0.2,0.3a17,17 0,0 0,5.7 -9.5h-0.3s-1.2,6 -5.6,9.3zM535.4,354.6v0.9l-3.4,11 0.3,0.1 3.4,-11c-0.5,-5.4 4.2,-11.3 4.3,-11.3l-0.3,-0.2s-4.3,5.3 -4.3,10.5"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M539,357.3c0,0.2 -1.1,7.7 -5.2,10.5l0.2,0.3c4.2,-2.9 5.2,-10.3 5.2,-10.7 1.8,-2.1 6,-9.7 6,-9.7l-0.2,-0.2s-4.2,7.6 -6,9.8m-12,8.3c0,0.1 0.6,4 1.4,5.2l0.3,-0.1c-0.8,-1.3 -1.5,-5.1 -1.5,-5.2l-0.3,0.1zM528.9,365.6 L528.8,370.9h0.3v-5.3z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m529,371 l0.3,0.1c1.3,-2.8 1.7,-4.8 1.7,-4.8h-0.3s-0.4,1.9 -1.7,4.7m0.3,0.1 l0.2,0.3a7,7 0,0 0,3 -4h-0.3s-0.6,2.4 -2.9,3.7m0.5,0.2v0.3c2.4,-0.2 4,-3.1 4,-3.3h-0.2s-1.6,2.8 -3.8,3"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M530,371.5v0.3c3.9,-0.3 5.6,-2.6 5.6,-2.7l-0.2,-0.1s-1.7,2.2 -5.4,2.5"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M530.3,371.5v0.3c0.1,0 5,1.1 6.7,-1.4l-0.3,-0.2c-1.5,2.4 -6.4,1.4 -6.4,1.3"/>
  <path
      android:pathData="M420.9,193.2v136.4c0,44.6 80.6,71 80.6,71s80.5,-26.4 80.5,-71V193.2z"
      android:strokeWidth="2.8"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M0,0h320v240H0z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0z"
      android:fillColor="#FFF"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5zM120,150.5 L123,168 27,240L0,240zM320,0v1.5l-124.5,94 1,-22L295,0zM0,0l119.5,88h-30L0,21z"
      android:fillColor="#C8102E"/>
  <path
      android:pathData="M120.5,0v240h80V0zM0,80v80h320V80z"
      android:fillColor="#FFF"/>
  <path
      android:pathData="M0,96.5v48h320v-48zM136.5,0v240h48V0z"
      android:fillColor="#C8102E"/>
</vector>
