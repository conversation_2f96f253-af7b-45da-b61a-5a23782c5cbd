<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M-0.14,0L641.5,0l0,481.28L-0.14,481.28z"/>
    <path
        android:pathData="M664.44,481.28L-23.08,481.28L-23.08,0l687.52,0z"
        android:fillColor="#ba0000"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M336.94,353.44c0.19,-0.56 0.56,-6.39 0.38,-6.39s-8.84,-10.25 -8.65,-10.25 11.09,2.44 11.09,2.07 4.42,-10.81 4.42,-11l5.26,12.69 10.81,4.7 -7.52,6.3 1.6,12.22c0,0.28 -7.52,-7.14 -7.52,-7.14l-8.37,0.94s-1.13,-3.76 -1.5,-4.14z"
        android:strokeWidth="2.07"
        android:fillColor="#ffef00"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M278.38,194.49c-7.24,3.76 -34.78,35.25 -38.54,40.14a72.38,72.38 121.19,0 1,-15.79 22c-6.86,5.17 -10.62,12.22 -9.68,18.8 0,8.46 4.61,14.1 8.27,19.83q3.48,4.14 8.27,4.7c6.49,0.75 7.05,2.82 10.34,3.95 12.6,17.3 31.58,30.08 45.12,40.14a63.92,63.92 0,0 1,16.92 11.66c3.95,7.71 3.2,15.04 2.63,18.8L295.86,412.66c-1.79,10.34 7.33,8.08 7.8,6.02 4.04,-5.36 10.15,-1.6 18.8,-32.24l12.03,-16.07s4.61,-1.88 4.61,-2.35c7.05,-8.46 1.69,-13.16 -2.35,-14.76l-8.65,-3.2s-10.15,-10.15 -10.62,-10.15c-4.79,-14.01 -28.48,-43.8 -33.93,-47.94 -3.76,-4.04 -5.64,-5.64 -9.21,-7.9 -5.55,-2.63 -7.33,-3.57 -10.72,-4.89 -2.82,-1.13 -0.85,-4.23 0.94,-5.64 18.8,-10.25 33.56,-21.53 51.51,-32.99l2.82,-1.88 -6.49,-36.85 -29.89,-10.53q-2.26,1.6 -4.14,3.2z"
        android:strokeWidth="2.44"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M303.38,388.31c0,-0.38 18.8,-3.76 18.8,-3.76l-2.35,6.96 -18.52,5.64z"
        android:fillColor="#ffec00"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M254.78,318.19a35.72,35.72 0,0 1,20.3 -19.18"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M302.72,388.5c1.22,-0.28 6.58,-1.88 8.08,-2.16q2.54,-0.47 4.89,-1.32c1.88,-0.38 3.01,-0.75 4.89,-1.03l4.7,-1.32M299.34,400.06l2.35,-1.88c1.03,-0.38 4.7,-1.88 6.11,-2.16l4.42,-1.32 4.32,-1.13q2.07,-0.85 4.14,-1.32"
        android:strokeWidth="2.26"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M306.86,320.92l-0.19,0c0.75,0 0.28,0 -0.85,1.32 -0.38,1.03 -3.76,3.48 -6.39,3.48l-4.42,0.28 -1.22,-0.28m12.88,50.29l1.32,0q2.16,0.09 3.95,0.28c1.41,0 2.73,0.38 4.04,0.38q2.16,0.09 4.42,0.38 2.16,0.19 4.42,0.28c1.32,0.09 2.07,0.28 3.48,0.28l-3.48,-0.28c1.32,0.09 2.07,0.28 3.48,0.28m-30.74,31.02 l2.16,-1.88c0.94,-0.28 4.32,-1.88 5.64,-2.07l3.95,-1.41 4.04,-0.94q1.88,-0.94 3.76,-1.41m5.45,-38.73c-0.75,2.16 0.28,2.73 0.66,3.67a11.28,11.28 0,0 0,5.36 3.2c1.13,0.28 1.88,0.56 3.2,1.13q1.03,0.09 1.88,0.47m-116.94,-89.11l0.28,0c-0.85,0 -0.28,0.09 1.03,-0.94 0.94,-1.03 1.6,-1.5 2.44,-2.54m13.54,22.56c0.19,0 15.6,-7.71 17.2,-9.4l3.48,-2.73q1.32,-0.75 2.44,-1.6 1.13,-1.32 2.16,-2.54c0.94,-0.85 0.47,-1.69 1.32,-2.73l1.22,-3.57m19.55,11.47c0.09,0.56 -0.19,2.26 -0.19,3.57 0,1.5 -1.97,6.11 -4.32,7.52"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m253.84,289.33 l6.3,1.97c4.7,1.69 14.48,7.9 15.51,8.74 0.94,0.75 2.82,1.5 3.38,2.35q1.6,1.41 2.73,3.01 1.5,1.69 2.35,3.2a106.22,106.22 0,0 1,12.6 23.03c0.66,0.66 0.94,1.6 1.6,2.63 0.56,1.32 1.22,1.88 1.88,2.91 0.94,0.66 2.07,1.88 3.2,2.44 1.22,1.03 2.35,1.41 3.29,2.16 1.22,0.56 15.32,9.4 15.98,9.87 1.32,1.03 5.17,5.08 2.44,8.08 -1.13,0.94 -2.26,2.35 -3.2,2.82q-1.69,1.5 -3.67,2.07c-6.3,1.88 -9.59,1.22 -10.81,1.22l-1.32,0m-83.47,-108.01c1.88,0.75 1.13,0.19 2.73,0.94 1.13,0.47 1.88,0.47 2.82,0.94 1.22,0.28 4.42,0.94 5.92,2.44 1.13,0.94 1.88,1.88 3.2,2.54a20.68,20.68 0,0 0,4.32 2.26q2.35,1.03 4.7,1.22l7.33,0 -3.76,0 3.76,0"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M222.26,275.7c3.95,-0.28 10.62,0.94 10.81,0.94l8.84,-0.19c4.7,-0.38 5.64,-2.16 6.39,-3.48 1.79,-2.63 2.91,-3.57 4.32,-5.64 2.07,-1.5 5.08,2.16 5.17,2.16 7.52,7.14 1.5,15.79 1.13,16.07 -3.76,3.48 -4.61,3.67 -6.86,1.5 -2.26,-2.73 -2.91,-3.95 -4.79,-4.89 -3.67,-1.69 -11.09,-0.38 -11.28,-0.38l-3.85,1.5c-1.88,0.66 -3.01,2.26 -6.11,2.82 -3.29,0.19 -4.32,-0.09 -5.92,-2.73 -2.16,-3.29 -1.13,-7.33 2.16,-7.71z"
        android:strokeWidth="2.07"
        android:fillColor="#ffe606"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M431.69,113.46c-0.56,0 -5.92,2.63 -5.83,2.82 0.09,0.09 -4.51,12.69 -4.61,12.6 -0.09,-0.19 -3.29,-11 -3.67,-10.81s-11.56,1.5 -11.66,1.6l8.46,-10.81 -1.22,-11.75 9.21,3.57 9.87,-7.52c0.19,-0.09 -2.54,10.15 -2.54,10.15l4.89,6.77s-2.82,2.82 -2.91,3.38z"
        android:strokeWidth="2.07"
        android:fillColor="#ffef00"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M321.9,242.43c6.67,4.42 47.66,13.07 53.86,13.91a72.38,72.38 0,0 1,26.88 3.01c7.8,3.38 15.79,3.2 21.15,-0.94 7.33,-3.95 9.96,-10.72 13.16,-16.92a11.28,11.28 0,0 0,0 -9.4c-2.44,-6.02 -0.94,-7.52 -1.5,-10.9 8.84,-19.55 10.72,-42.3 12.78,-59.13 -0.19,-9.87 0.85,-17.39 1.88,-20.4a30.08,30.08 0,0 1,15.04 -11.47l38.26,-9.96c9.96,-3.57 3.48,-10.34 1.41,-9.78 -6.58,-0.94 -6.39,-8.08 -37.32,-0.66l-19.93,-2.54s-3.76,-3.1 -4.23,-2.82c-10.81,-1.97 -12.31,4.98 -11.75,9.21l1.5,9.21s-3.85,13.72 -3.67,14.1c-9.87,11.09 -24.44,46.62 -25.38,53.49 -0.47,6.39 -2.82,5.17 -2.26,11.47 0,4.89 -0.56,1.88 1.03,11.84 0.47,3.01 -3.29,2.82 -5.36,1.88 -18.05,-11.28 -35.16,-18.71 -54.05,-28.67l-2.91,-1.5 -28.95,23.69 5.45,31.21q2.44,1.22 4.79,2.07z"
        android:strokeWidth="2.44"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M478.6,125.58a352.5,352.5 70.98,0 1,-12.5 -14.48l7.14,-1.41 13.91,13.44z"
        android:fillColor="#ffec00"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M441.28,202.29a35.72,35.72 0,0 1,-26.7 -8.27"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M479.07,126.05c-0.94,-0.85 -4.89,-4.7 -5.83,-6.02l-3.57,-3.57c-1.22,-1.41 -2.16,-2.26 -3.38,-3.76l-3.38,-3.38m27.82,14.1c-0.28,0 -1.88,-0.66 -2.82,-1.13 -0.75,-0.85 -3.95,-3.2 -4.79,-4.32l-3.29,-3.2c-1.13,-1.22 -1.97,-1.88 -3.1,-3.29l-3.2,-2.82"
        android:strokeWidth="2.26"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m418.06,155.57 l0.09,0.28c-0.38,-0.75 -0.19,-0.28 1.6,0 1.13,-0.19 4.89,1.6 6.11,3.85 0.75,1.03 1.88,2.63 2.44,3.76q0.19,0.66 0.38,1.22m37.6,-35.91 l-0.19,-0.19 -0.56,-0.94 -1.69,-3.67q-0.75,-1.88 -1.6,-3.67 -0.94,-1.97 -1.88,-3.95c-0.75,-1.41 -1.13,-2.63 -1.88,-4.14 -0.66,-1.13 -0.75,-1.88 -1.5,-3.1l1.5,3.1c-0.66,-1.13 -0.75,-1.88 -1.5,-3.1m42.11,11.56 l-2.63,-0.94c-0.75,-0.66 -3.76,-2.82 -4.7,-3.76l-3.1,-2.82c-0.94,-1.22 -1.79,-1.88 -2.82,-3.1l-3.01,-2.54m-36.57,14.1c2.35,-0.38 2.35,-1.5 2.91,-2.35 0.47,-0.94 0.85,-3.29 0.19,-6.2l-0.56,-3.29q-0.47,-1.13 -0.47,-1.88m-20.4,145.51 l-0.09,-0.19c0.38,0.75 0.19,0.28 -1.5,-0.47l-3.29,-0.94m13.07,-22.84c-0.09,-0.19 -14.38,-9.78 -16.64,-10.34 -1.32,-0.66 -2.54,-0.94 -4.04,-1.69l-2.63,-1.32c-1.22,-0.19 -2.26,-0.66 -3.29,-0.66 -1.13,-0.38 -1.79,0.38 -3.01,0.19l-3.76,0.66m0.47,-22.65c0.38,-0.38 2.07,-0.94 3.2,-1.6 1.32,-0.66 6.3,-1.22 8.65,0.09"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m416.56,217.33 l-1.41,-6.58a94,94 81.16,0 1,0 -17.67c0.19,-1.22 0,-3.2 0.47,-4.23q0.47,-1.88 1.22,-3.76 0.85,-2.07 1.6,-3.67c0.66,-3.76 11.28,-18.89 12.03,-19.65l1.88,-2.54q0.56,-1.32 1.5,-2.82c0.94,-1.03 1.13,-1.88 1.69,-3.01 0.09,-1.03 0.56,-2.73 0.56,-3.95 0.28,-1.6 0,-2.82 0.28,-3.85 -0.19,-1.41 0.66,-18.05 0.75,-18.8 0.28,-1.69 1.88,-7.05 5.83,-6.11 1.41,0.47 3.1,0.75 4.14,1.41 1.32,0.38 2.44,1.41 3.48,2.16 4.7,4.61 5.73,7.71 6.39,8.84l0.66,1.13M404.24,259.44l-0.09,-0.19q0.47,0.94 -0.28,-1.03c-0.09,-1.22 -0.47,-2.82 -0.66,-3.95 -0.28,-1.22 -0.85,-2.54 -0.66,-3.29 -0.47,-1.22 -0.19,-2.16 0,-3.38 0.28,-1.5 0.47,-2.54 0.47,-3.95 0.19,-1.88 0,-3.01 0.09,-4.7 -0.47,-1.41 -0.56,-2.54 -1.22,-3.95l-1.88,-3.76 -1.88,-3.29 1.88,3.2 -1.88,-3.2"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M420.13,251.54c-2.16,-3.38 -4.42,-9.78 -4.51,-9.87l-4.42,-7.71c-2.63,-3.76 -4.7,-3.76 -6.2,-3.76 -3.2,-0.28 -4.61,-0.94 -6.96,-1.03 -2.44,-1.13 -0.66,-5.55 -0.75,-5.64 2.54,-9.96 13.16,-9.02 13.54,-8.84 4.79,1.6 5.36,2.26 4.61,5.26 -1.22,3.29 -1.88,4.51 -1.88,6.58 0.28,3.95 5.17,9.87 5.17,9.96l3.2,2.63c1.5,1.32 3.48,1.5 5.45,4.04 1.79,2.73 2.07,3.76 0.56,6.39 -1.88,3.57 -5.83,4.61 -7.8,1.97z"
        android:strokeWidth="2.07"
        android:fillColor="#ffe606"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M171.69,158.58c0.47,0.38 5.45,3.48 5.64,3.29s13.16,-3.2 12.97,-3.01 -7.33,8.74 -6.96,8.93 7.52,8.84 7.71,8.93l-13.63,-1.13 -9.21,7.33 -2.07,-9.59L154.58,169.2c-0.19,-0.09 9.87,-3.38 9.87,-3.38l3.01,-7.8s3.85,0.66 4.42,0.56z"
        android:strokeWidth="2.07"
        android:fillColor="#ffef00"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M339.76,179.82c0,-8.18 -15.23,-47.19 -17.86,-52.73a72.38,72.38 77.23,0 1,-12.22 -24.16c-1.41,-8.46 -5.92,-15.04 -12.22,-17.3 -7.43,-3.85 -14.57,-2.35 -21.43,-1.69 -3.38,0.66 -6.3,2.54 -7.9,5.17 -3.76,5.36 -5.73,4.89 -8.37,7.24 -21.15,3.29 -41.17,14.19 -56.4,21.62a63.92,63.92 58.2,0 1,-18.14 9.59,30.08 30.08,114.21 0,1 -17.86,-6.2l-29.23,-26.6c-8.46,-6.3 -10.53,2.82 -8.93,4.23 2.82,6.02 -3.29,9.68 19.93,31.58l8.74,18.05s-0.47,4.89 0,5.08c4.14,10.15 10.9,7.61 14.1,4.79l6.86,-6.3s13.63,-4.32 13.82,-4.7c14.66,2.16 52.45,-4.98 58.66,-7.99 5.64,-3.1 5.92,-0.47 10.81,-4.42 4.04,-2.73 1.88,-0.56 9.4,-7.43 2.26,-1.88 4.04,1.22 4.51,3.48 0.38,21.34 3.57,39.67 5.55,60.91l0.38,3.29 35.72,11.28 23.12,-21.62 -0.94,-5.26z"
        android:strokeWidth="2.44"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M156.18,112.61c0.28,0.28 -5.26,18.42 -5.26,18.42l-5.08,-5.17 3.57,-19.08z"
        android:fillColor="#ffec00"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M240.87,101.9c6.77,7.52 8.93,19.93 7.71,26.88"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M156.46,111.95c-0.28,1.22 -1.41,6.77 -1.88,8.18l-1.13,4.98 -1.32,4.89q-0.28,2.35 -0.94,4.7m-3.48,-31.02c0.09,0.28 0.47,1.88 0.56,2.91 -0.19,1.13 -0.47,5.08 -0.94,6.49l-0.85,4.51 -0.94,4.32 -0.75,4.32"
        android:strokeWidth="2.26"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m214.46,146.92 l0.09,-0.28c-0.38,0.75 0,0.28 -0.75,-1.32 -0.85,-0.94 -1.32,-4.98 -0.19,-7.33l1.88,-4.04q0.28,-0.47 0.75,-0.94M165.67,121.26l-0.09,0.19 -0.56,0.94q-1.03,1.88 -2.07,3.48c-0.56,1.22 -1.6,2.16 -2.16,3.38l-2.26,3.76q-1.32,1.88 -2.44,3.76l-1.88,3.01 1.88,-3.01 -1.88,3.01m-13.35,-41.64c0.19,0.19 0.66,1.79 0.75,2.82 -0.19,0.94 -0.38,4.7 -0.75,5.92l-0.66,4.14c-0.38,1.5 -0.47,2.54 -0.94,4.04l-0.47,3.95M170.56,141.94c-1.6,-1.79 -2.54,-1.13 -3.57,-1.22a11.28,11.28 0,0 0,-5.36 3.29q-0.94,1.22 -2.44,2.35c-0.47,0.75 -0.85,0.94 -1.32,1.41M290.88,84.98l-0.09,0.28c0.38,-0.75 0.09,-0.38 0.47,1.41 0.47,1.22 0.47,2.07 0.94,3.2m-26.23,1.69c0,0.19 -0.28,17.39 0.47,19.65 0.19,1.41 0.56,2.54 0.75,4.32q0.09,1.41 0.38,2.82c0.56,1.13 0.66,2.26 1.32,3.1 0.28,1.22 1.22,1.32 1.79,2.35 0.66,0.85 1.88,2.26 2.63,2.82m-19.27,12.13c-0.56,-0.19 -1.88,-1.32 -3.01,-1.88 -1.32,-0.75 -4.61,-4.61 -4.7,-7.33"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m266.91,114.3 l-4.7,4.7a88.36,88.36 53.05,0 1,-14.85 9.68c-1.13,0.56 -2.54,1.88 -3.76,1.97q-1.88,0.75 -3.76,1.03a106.22,106.22 54.04,0 1,-26.98 1.41l-3.2,-0.19q-1.32,0.19 -3.1,0.19c-1.5,-0.09 -2.26,0.19 -3.48,0.28 -0.94,0.56 -2.54,1.03 -3.67,1.79q-1.97,0.94 -3.38,1.88c-0.94,0.85 -15.42,9.21 -16.17,9.59 -1.5,0.66 -6.86,2.35 -8.27,-1.5q-0.75,-2.44 -1.03,-4.23c-0.47,-1.32 -0.19,-2.82 -0.19,-4.14 1.32,-6.49 3.38,-9.02 3.95,-10.15l0.66,-1.13M308.74,101.52l-0.09,0.28q0.56,-0.94 -0.75,0.75 -1.5,1.22 -2.91,2.82c-0.94,0.75 -1.69,1.88 -2.44,2.26q-1.13,1.22 -2.82,1.88a18.8,18.8 55.04,0 0,-3.57 1.79c-1.6,0.75 -2.54,1.6 -3.95,2.44 -0.94,1.13 -1.88,1.88 -2.63,3.2q-1.13,1.69 -2.16,3.67l-1.69,3.29 1.69,-3.29 -1.69,3.29m11,68.71 l0.28,-0.47q-0.66,1.41 0.09,-0.85c0,-1.69 0.47,-3.85 0.47,-5.45q0.47,-2.54 0.56,-5.17 0,-2.82 -0.38,-5.64 -0.19,-2.54 -0.28,-5.26 0,-2.44 -0.56,-4.7l-0.38,-4.51q0,-1.69 -0.47,-3.2l-1.13,-4.79 -0.94,-3.85c-0.19,-1.5 -0.94,-3.38 -1.32,-4.61a13.16,13.16 0,0 1,-1.03 -3.2q-0.94,-1.6 -1.22,-3.38l-1.5,-3.48 -1.32,-3.38q-0.75,-1.88 -1.41,-3.76 -0.56,-0.75 -0.75,-1.32"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M293.61,92.59a55.46,55.46 0,0 1,-5.83 9.12l-3.95,7.9c-1.69,4.32 -0.66,6.11 0.09,7.33 1.6,2.82 1.88,4.32 3.01,6.39 0.47,2.63 -4.23,3.57 -4.23,3.67 -9.78,3.38 -14.76,-6.02 -14.76,-6.49 -1.32,-4.89 -1.13,-5.64 1.88,-6.67 3.38,-0.85 4.7,-0.94 6.39,-2.07 3.29,-2.44 5.55,-9.68 5.64,-9.78l0.38,-4.14c0.28,-1.88 -0.56,-3.76 0.38,-6.77 1.32,-3.01 2.07,-3.76 5.08,-4.04 3.95,-0.28 7.05,2.44 5.92,5.55z"
        android:strokeWidth="2.07"
        android:fillColor="#ffe606"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M280.82,187.72c-1.5,-1.41 27.64,10.53 31.68,10.62 5.45,-1.97 28.11,-21.06 28.11,-21.06 0.19,1.88 1.03,6.67 3.2,7.71 -8.74,7.05 -16.64,13.91 -25.38,20.96 0.56,11.28 -1.41,23.22 4.23,35.72 0,0 -6.77,0.19 -6.77,0 -6.02,-6.02 -8.18,-34.97 -8.18,-34.97l-28.76,-12.78c1.41,-0.94 2.44,-3.67 1.88,-6.3z"
        android:strokeWidth="2.07"
        android:fillColor="#ffef00"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M308.55,217.42c0.47,-0.38 -5.26,2.63 -6.77,3.38 -26.41,14.1 -40.04,34.78 -40.89,35.53l-2.07,3.2 -2.16,2.82 -3.38,4.42c-0.19,0.56 0.38,-0.28 0.19,0.19m73.23,-66.36a74.26,74.26 0,0 0,6.11 4.42c24.63,17.11 49.16,19.83 50.29,20.21q1.5,0.09 3.76,0.47 1.79,0.19 3.57,0.47c1.32,0.19 4.14,0.75 5.45,1.13 0.56,-0.19 -0.47,-0.28 0,-0.38"
        android:strokeWidth="2.07"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
  </group>
</vector>
