<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v240H0z"
      android:fillColor="#002b7f"/>
  <path
      android:pathData="M0,240h640v240H0z"
      android:fillColor="#ce1126"/>
  <path
      android:pathData="m173.12,97.84 l-1.52,50l-50.72,0c-6.24,-12.16 -11.36,-22.4 -11.36,-36.4 0,-11.68 9.2,-20.96 22.4,-20.96 14,0 29.44,4.72 41.2,7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.6,100.16l0,28.8m4,-31.76l0,38.4m4.16,-40.4l0,45.76m4,-46.72l0,35.92m4.08,-36.08l0,36.24m4.08,-37.6l0,37.6m4.08,-37.6l0,37.6m4.08,-37.2l0,37.6m4.08,-37.2l0,37.52m4.08,-35.2l0,37.6m4,-37.2l0,42.32m4.16,-42.32l0,37.6m4,-37.6l0,37.6"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.12,94.4c8.64,1.36 27.52,10.24 25.36,22.16 -3.04,16.96 -12.96,10.16 -26.08,7.76l-9.92,3.28c-3.6,3.6 -8.8,6.96 -12.32,2.56l-5.92,0l0,23.04l65.12,0L177.36,97.6z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#000001"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.24,90.4m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.36,88.24m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.32,86m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M143.28,84.48m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.24,83.6m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.44,84.08m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.96,86.96m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.84,92.24m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.8,99.52m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.32,108.16m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.4,117.04m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.16,125.76m-3.92,0a3.92,3.92 0,1 1,7.84 0a3.92,3.92 0,1 1,-7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m172,95.6 l-0.4,5.2c-9.84,-1.6 -23.76,-7.04 -36.8,-7.04 -12,0 -21.28,4.8 -21.28,16.96 0,11.92 5.04,22.8 11.76,33.84l-6.96,3.2c-6.24,-12 -11.44,-22.4 -11.44,-36.32 0,-11.68 9.2,-23.2 25.04,-23.2 14,0 28.32,4.8 40.08,7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m182.08,97.84l1.52,50l50.72,0c6.24,-12.16 11.36,-22.4 11.36,-36.4 -0,-11.68 -9.2,-20.96 -22.4,-20.96 -14,0 -29.44,4.72 -41.2,7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M239.6,100.16l-0,28.8m-4,-31.76l-0,38.4m-4.16,-40.4l-0,45.76m-4,-46.72l-0,35.92m-4.08,-36.08l-0,36.24m-4.08,-37.6l-0,37.6m-4.08,-37.6l-0,37.6m-4.08,-37.2l-0,37.6m-4.08,-37.2l-0,37.52m-4.08,-35.2l-0,37.6m-4,-37.2l-0,42.32m-4.16,-42.32l-0,37.6m-4,-37.6l-0,37.6"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M214.08,94.4c-8.64,1.36 -27.52,10.24 -25.36,22.16 3.04,16.96 12.96,10.16 26.08,7.76l9.92,3.28c3.6,3.6 8.8,6.96 12.32,2.56l5.92,0l-0,23.04l-65.12,0L177.84,97.6z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#000001"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.96,90.4m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.84,88.24m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M202.88,86m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.92,84.48m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M220.96,83.6m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M229.76,84.08m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M238.24,86.96m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M245.36,92.24m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M250.4,99.52m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M252.88,108.16m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M252.8,117.04m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M251.04,125.76m3.92,0a3.92,3.92 0,1 0,-7.84 0a3.92,3.92 0,1 0,7.84 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m183.2,95.6l0.4,5.2c9.84,-1.6 23.76,-7.04 36.8,-7.04 12,0 21.28,4.8 21.28,16.96 -0,11.92 -5.04,22.8 -11.76,33.84l6.96,3.2c6.24,-12 11.44,-22.4 11.44,-36.32 -0,-11.68 -9.2,-23.2 -25.04,-23.2 -14,0 -28.32,4.8 -40.08,7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m177.6,42.48 l-4.16,7.76 4.16,7.68 4.08,-7.68L177.6,42.4zM177.6,62 L173.44,69.68 177.6,77.44 181.6,69.6zM162.88,60l6.48,4.08 6.48,-4.08 -6.4,-4.16zM179.28,60 L185.76,64.08 192.32,60 185.76,55.84z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,60.08m-3.12,0a3.12,3.12 0,1 1,6.24 0a3.12,3.12 0,1 1,-6.24 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,80m-8.4,0a8.4,8.4 0,1 1,16.8 0a8.4,8.4 0,1 1,-16.8 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.44,71.92l0,5.28a50.4,50.4 124.56,0 0,-5.92 0.64m16.16,0a49.6,49.6 62.58,0 0,-6 -0.64l0,-5.28m-10.24,10.16a48.8,48.8 0,0 1,8.16 -0.72q4.16,0 8,0.72"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.44,94.16c-0.8,13.6 -2.4,27.6 -7.76,37.68l8.64,-3.52c3.04,-11.44 3.68,-26.24 4.56,-33.28zM185.76,94.16 L180.24,95.04c0.96,7.04 1.6,21.84 4.56,33.28l8.64,3.52c-5.28,-10.08 -6.88,-24 -7.68,-37.68"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,123.76c-10.4,0 -18,4.8 -18.4,17.28 -2.56,-4.48 -13.2,-18.4 -23.2,-16.56 -5.92,1.12 -11.2,9.36 -10.24,18.72 -4.96,-14.08 -19.28,-16.4 -29.84,-8 9.36,7.6 13.52,30.24 21.36,40l120.56,0c7.84,-9.76 12,-32.4 21.36,-40 -10.48,-8.4 -24.88,-6.08 -29.76,8 0.8,-9.36 -4.4,-17.6 -10.4,-18.72 -9.92,-1.84 -20.56,12.08 -23.12,16.56 -0.4,-12.4 -8,-17.28 -18.4,-17.28z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.6"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M237.68,175.2c0,4.48 -26.88,9.36 -60.08,9.36s-60.16,-4.8 -60.16,-9.36c0,-4.4 26.96,-6.8 60.16,-6.8 33.12,0 60.08,2.4 60.08,6.8"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#000001"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,91.52m-2.8,0a2.8,2.8 0,1 1,5.6 0a2.8,2.8 0,1 1,-5.6 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,97.6m-2.96,0a2.96,2.96 45,1 1,5.92 0a2.96,2.96 45,1 1,-5.92 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,104.16m-3.28,0a3.28,3.28 0,1 1,6.56 0a3.28,3.28 0,1 1,-6.56 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,111.44m-3.76,0a3.76,3.76 0,1 1,7.52 0a3.76,3.76 0,1 1,-7.52 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,119.52m-4.08,0a4.08,4.08 0,1 1,8.16 0a4.08,4.08 0,1 1,-8.16 0"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176,127.36q-0.8,0 -0.8,1.12l0.48,3.44c0.16,1.2 0.8,0.8 1.04,0 0.16,-0.96 0.08,-1.6 0.16,-3.28q-0.16,-1.28 -0.96,-1.28zM179.2,127.36q-0.8,0 -0.88,1.28c0,1.68 0,2.32 0.16,3.2s0.8,1.28 1.04,0l0.4,-3.36c0.16,-0.8 -0.32,-1.12 -0.72,-1.12m-6.16,0.48a0.8,0.8 0,0 0,-0.8 0.8q0.08,1.2 0.32,3.04c0.16,1.2 1.04,0.8 1.12,0q0.16,-1.04 0,-3.12 0,-0.72 -0.48,-0.8l-0.16,0zM181.92,127.84q-0.4,0 -0.56,0.8l0.16,3.04c0,0.8 0.96,1.2 1.04,0 0,-1.2 0.32,-2.16 0.32,-2.96q-0.08,-0.8 -0.8,-0.88zM169.6,129.2q-0.8,0.24 -0.64,1.04 0.4,1.6 0.48,3.2c0,0.88 0.72,0.56 0.8,0l0.08,-3.2q-0.08,-1.2 -0.8,-1.04zM185.28,129.2q-0.4,0.08 -0.48,1.04l0.16,3.2c0,0.56 0.8,0.88 0.8,0q0,-1.6 0.4,-3.2 0.08,-0.8 -0.56,-1.04l-0.24,0zM138.08,129.36q-0.8,0.08 -0.48,1.2c0.48,1.12 1.04,1.76 1.52,2.88s1.12,0.4 0.88,-0.32l-1.12,-2.96q-0.4,-0.8 -0.8,-0.8m79.04,0q-0.48,0 -0.8,0.8c-0.72,1.52 -0.96,2.24 -1.2,2.96 -0.16,0.8 0.48,1.36 0.96,0.32l1.44,-2.88q0.32,-1.12 -0.4,-1.2m-75.84,0q-0.64,0.24 -0.4,1.04 0.8,1.44 1.44,3.04c0.32,0.88 1.12,0.64 0.8,-0.16l-0.8,-2.96q-0.56,-1.12 -1.04,-0.96m72.4,0q-0.48,0.08 -0.8,0.96c-0.56,1.6 -0.64,2.08 -0.88,2.96 -0.32,0.8 0.48,1.04 0.8,0.16l1.44,-3.04q0.24,-0.8 -0.32,-1.04zM145.28,130.16q-0.8,0.24 -0.48,1.52 0.56,1.36 1.28,2.96c0.48,1.12 1.2,0.88 1.04,0.16q-0.24,-1.04 -0.96,-3.6 -0.4,-1.2 -0.88,-1.04m64.64,0q-0.56,-0.08 -0.96,1.04 -0.8,2.56 -0.88,3.6c-0.24,0.8 0.56,0.96 0.96,-0.16q0.8,-1.6 1.28,-2.96 0.32,-1.28 -0.4,-1.52m-74.56,0.56c-0.48,0 -0.8,0.8 -0.56,1.36 0.4,0.8 0.72,1.12 1.12,2.08 0.48,0.96 0.96,0.24 0.8,-0.4q-0.4,-0.96 -0.64,-2.24 -0.24,-0.88 -0.8,-0.8zM219.84,130.72q-0.48,-0.08 -0.8,0.8l-0.56,2.24c-0.16,0.64 0.32,1.36 0.8,0.4s0.64,-1.2 1.12,-2.08c0.24,-0.56 -0.16,-1.28 -0.56,-1.36m-53.6,2.24c-0.56,0 -1.04,0.64 -0.8,1.2q0.4,1.28 0.88,2.96c0.32,1.04 1.04,0.56 0.96,-0.16l-0.4,-3.2q-0.24,-0.8 -0.72,-0.8zM188.96,132.96q-0.48,0 -0.64,0.8l-0.48,3.2c0,0.72 0.72,1.2 0.96,0.16l0.96,-2.96c0.16,-0.56 -0.32,-1.2 -0.8,-1.2m-11.36,0.96c-0.64,0 -0.72,0.8 -0.72,1.36a9.6,9.6 0,0 1,-1.6 5.12c-0.8,0.96 -1.84,0.72 -2.8,0.16l-2.64,-1.76c-1.12,-0.8 -1.92,-0.32 -0.64,1.52 3.68,5.28 7.68,9.76 7.68,18.4q0,1.44 0.72,1.36c0.72,-0.08 0.64,-0.32 0.64,-1.44 0,-8.56 4,-13.04 7.68,-18.4 1.28,-1.76 0.48,-2.24 -0.56,-1.44l-2.64,1.76c-1.04,0.56 -2.08,0.8 -2.8,-0.16a9.6,9.6 0,0 1,-1.6 -5.12c-0.08,-0.56 -0.16,-1.36 -0.8,-1.36zM149.36,132.64l-0.16,0q-0.32,0.08 -0.24,0.8l0.88,3.12c0.24,0.8 1.12,0.56 0.96,-0.4l-0.48,-2.48q-0.32,-0.96 -0.96,-1.04m56.48,0q-0.64,0 -0.96,1.04c-0.32,1.12 -0.32,1.44 -0.48,2.4 -0.24,1.04 0.72,1.2 0.88,0.48l0.96,-3.2q0,-0.64 -0.32,-0.72zM132.56,132.96q-0.88,0.16 -0.32,1.44c0.48,0.96 1.36,2.08 1.76,2.8s1.12,0.16 0.64,-0.64 -0.64,-1.84 -1.28,-3.04a0.8,0.8 0,0 0,-0.8 -0.56m90,0q-0.4,0 -0.8,0.56 -0.72,1.76 -1.2,3.04c-0.48,0.8 0.16,1.28 0.56,0.64s1.28,-1.84 1.76,-2.8c0.48,-0.8 0.16,-1.44 -0.32,-1.44M107.2,135.6c-0.48,0 -0.64,0.96 -0.16,1.6l2.08,2.16c0.64,0.64 0.8,-0.16 0.48,-0.8l-1.44,-2.16q-0.56,-0.8 -0.96,-0.8m140.72,0q-0.4,0 -0.88,0.8l-1.52,2.16c-0.32,0.64 -0.16,1.44 0.56,0.8l2.08,-2.16c0.48,-0.64 0.32,-1.6 -0.24,-1.6m-136.64,0.8q-0.8,0 -0.4,1.04l1.52,3.04c0.4,0.8 1.6,1.04 1.12,-0.16l-1.04,-3.04q-0.48,-0.8 -1.2,-0.8zM243.92,136.4q-0.8,0 -1.28,0.88l-0.96,3.04c-0.48,1.2 0.72,0.96 1.12,0.16q0.72,-1.36 1.44,-3.04 0.48,-1.04 -0.32,-1.04m-140.48,0.32c-0.64,0 -0.64,0.56 -0.08,0.96q1.12,0.8 2.4,1.84c1.04,0.8 1.36,0 0.8,-0.64s-0.96,-1.28 -2.32,-2q-0.48,-0.16 -0.8,-0.16m148.24,0q-0.32,0 -0.8,0.16c-1.28,0.72 -1.84,1.36 -2.4,2 -0.48,0.72 -0.08,1.44 0.88,0.64q1.28,-1.04 2.4,-1.84c0.64,-0.4 0.56,-0.96 0,-0.96zM164,137.28q-0.64,0 -0.56,0.96l0.8,2.88c0.16,0.88 1.2,0.88 1.04,0q-0.16,-1.2 -0.56,-3.12 -0.16,-0.72 -0.64,-0.8l-0.08,0zM191.04,137.28q-0.4,0 -0.64,0.8 -0.4,1.84 -0.56,3.12c-0.16,0.8 0.88,0.8 1.04,-0.08l0.8,-2.88q0.16,-0.88 -0.48,-0.96zM139.84,135.68q-0.48,0.08 -0.16,1.36a20.8,20.8 0,0 1,1.28 7.2q-0.4,1.28 -1.52,0.8l-2.24,-1.36c-0.64,-0.4 -1.36,0.08 -0.4,0.88 4.8,4 8.24,8.56 9.6,14.08 0.16,1.12 0.88,1.28 0.72,0 -1.12,-6.96 -1.36,-12.72 0.32,-16.24 0.64,-1.36 0,-2.64 -0.96,-0.48 -0.72,1.44 -1.76,2 -2.64,0.8a38.4,38.4 0,0 1,-3.2 -6.16q-0.24,-0.8 -0.64,-0.88l-0.08,0zM215.12,135.68q-0.4,0 -0.64,0.8c-0.56,1.6 -2.32,4.96 -3.2,6.24s-1.92,0.64 -2.56,-0.8c-1.04,-2.16 -1.6,-0.88 -1.04,0.48 1.68,3.52 1.44,9.28 0.4,16.24 -0.24,1.28 0.48,1.12 0.8,0a25.6,25.6 86.6,0 1,9.44 -14.08c0.96,-0.8 0.24,-1.28 -0.4,-0.8l-2.24,1.2q-1.2,0.64 -1.52,-0.8c-0.16,-0.96 0.16,-3.12 1.36,-7.2q0.16,-1.04 -0.24,-1.28zM115.52,137.6c-0.4,0 -0.8,0.32 -0.56,0.96 0.32,1.12 0.96,2.24 1.2,2.88s0.96,0.4 0.8,-0.56l-0.4,-2.48q-0.16,-0.8 -1.04,-0.8m124.08,0q-0.8,0 -1.04,0.8l-0.32,2.48c-0.16,0.96 0.56,1.28 0.8,0.56 0.16,-0.64 0.8,-1.76 1.12,-2.88q0.24,-0.96 -0.56,-0.96m-109.04,-1.36c-0.48,-0.08 -0.56,1.2 -0.16,1.76l2.24,2.48c0.64,0.8 1.04,0.08 0.64,-0.56l-2.08,-3.04q-0.4,-0.64 -0.64,-0.64m94,0q-0.24,0 -0.64,0.64l-2.08,3.04c-0.4,0.64 0,1.36 0.72,0.56l2.16,-2.48c0.4,-0.56 0.4,-1.84 -0.16,-1.76m-105.6,3.2q-0.56,-0.08 -0.4,0.8 0.24,1.28 0.64,2.48c0.24,0.8 0.8,0.72 0.8,-0.08l-0.24,-2.56a0.8,0.8 0,0 0,-0.8 -0.72zM236.16,139.44a0.8,0.8 0,0 0,-0.8 0.64l-0.24,2.56c0,0.8 0.64,0.96 0.8,0.08l0.72,-2.4q0.08,-1.04 -0.48,-0.96zM129.44,140.56c-0.48,0 -0.88,0.56 -0.32,1.28 0.88,1.2 1.92,2 2.32,2.4s1.2,0.32 0.64,-0.56l-1.84,-2.8q-0.24,-0.48 -0.8,-0.32m96,0q-0.32,0 -0.48,0.32l-1.92,2.8c-0.56,0.88 0.32,1.04 0.72,0.64s1.36,-1.28 2.32,-2.48c0.56,-0.8 0.08,-1.28 -0.4,-1.28zM112.88,143.84q-0.56,0 0,1.04a13.6,13.6 52.62,0 1,2.64 5.12c0.08,1.12 -0.4,1.12 -0.88,1.12 -1.52,0 -2.24,-1.12 -3.52,-1.44 -1.36,-0.24 -1.6,0.4 -0.64,1.12a42.4,42.4 85.8,0 1,11.76 11.04c0.8,1.52 1.84,2 1.28,0.64a19.2,19.2 0,0 1,-1.92 -10.08c0.4,-2.24 0.96,-3.68 0.88,-4.96 0,-1.2 -0.8,-1.12 -1.04,0l-0.8,2.24c-0.32,0.4 -1.44,0.64 -2.4,-0.72a38.4,38.4 104.09,0 0,-4.48 -4.64q-0.4,-0.4 -0.8,-0.48zM242.24,143.84q-0.4,0 -0.88,0.48a38.4,38.4 0,0 0,-4.4 4.64c-1.04,1.36 -2.16,1.12 -2.4,0.8l-0.8,-2.32c-0.32,-1.12 -1.04,-1.2 -1.12,0 0,1.28 0.48,2.72 0.88,4.96s0,5.76 -1.84,10.08c-0.64,1.36 0.48,0.8 1.28,-0.64a42.4,42.4 0,0 1,11.68 -11.04c0.96,-0.72 0.8,-1.36 -0.56,-1.12s-2.08,1.44 -3.52,1.44c-0.48,0 -1.04,0 -0.96,-1.12s1.44,-3.52 2.64,-5.12q0.64,-1.12 0,-1.04"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#000001"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m120.08,170.16 l1.6,4.88m1.6,-5.84 l1.44,4.96m2.08,-5.6 l0.96,5.12m2.24,-5.68 l1.04,5.04m3.04,-5.44 l0.8,5.12m3.6,-5.12 l0.88,5.04m3.44,-6.48 l0.64,5.12m3.36,-4.88 l0.64,5.12m3.28,-5.6 l0.64,5.12m3.2,-5.6 l0.4,5.2m3.84,-5.2 l0.32,5.2m3.52,-5.44 l0.24,5.2m3.92,-5.44 l0.24,5.12m4.56,-5.28 l0.24,5.2m63.2,0.16 l-1.6,4.88m-1.6,-5.84 l-1.36,4.96m-2.08,-5.6 l-0.96,5.12m-2.24,-5.68 l-1.04,5.04m-3.2,-5.44 l-0.8,5.12m-3.52,-5.12 l-0.88,5.12m-3.36,-6.56 l-0.64,5.12m-3.44,-4.88 l-0.64,5.12m-3.2,-5.6 l-0.72,5.12m-3.2,-5.6 l-0.4,5.2m-3.84,-5.2 l-0.32,5.2m-3.44,-5.44 l-0.24,5.2m-4,-5.44 l-0.16,5.12m-4.64,-5.28 l-0.16,5.2m-5.76,-5.6l0,5.76"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2"
      android:fillColor="#ffd83d"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
</vector>
