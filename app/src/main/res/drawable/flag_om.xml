<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M0,0h640v480H0z"/>
    <path
        android:pathData="M-3.3,-21.6H699v553H-3.3z"
        android:fillColor="#ef2d29"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M174.6,317.3h535.7V525H174.6z"
        android:fillColor="#009025"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M174.6,-35.4h564.9v190h-565z"
        android:fillColor="#fff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M117.06,69.59L140,69.59A2.09,2.24 90,0 1,142.24 71.68L142.24,82.58A2.09,2.24 90,0 1,140 84.67L117.06,84.67A2.09,2.24 90,0 1,114.81 82.58L114.81,71.68A2.09,2.24 90,0 1,117.06 69.59z"
        android:strokeWidth="0.26"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M117.65,70.23L139.42,70.23A1.93,2.12 90,0 1,141.55 72.16L141.55,82.13A1.93,2.12 90,0 1,139.42 84.07L117.65,84.07A1.93,2.12 90,0 1,115.53 82.13L115.53,72.16A1.93,2.12 90,0 1,117.65 70.23z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m127.63,73.98 l1.31,0.05 0.05,0.6 1.08,0.55 0.84,-0.7 1.01,0.55 -0.95,0.6 0.23,0.83 1.19,-0.05L132.4,77.51l-0.95,-0.04 -0.49,0.69 1.08,0.78 -0.84,0.64 -0.91,-0.69 -1.31,0.27 0.07,1.01 -1.43,0.1 -0.19,-0.98 -1.19,-0.5 -0.66,0.69 -1.01,-0.51 0.59,-0.78 -0.72,-0.5L123.44,77.7l-0.05,-1.42 1.01,0.09 0.72,-0.83 -0.84,-0.64 1.08,-0.73 0.77,0.59 1.31,-0.19z"
        android:strokeWidth="0.15"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M126.55,76.97a1.23,1.45 90,1 0,2.9 0a1.23,1.45 90,1 0,-2.9 0z"
        android:strokeWidth="0.17"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m119.04,70.85 l-2.12,1.88m3.53,-1.88 l-2.12,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88L126.78,72.73m3.51,-1.88L128.17,72.73m3.53,-1.88L129.56,72.73m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m-3.51,-1.88L126.78,72.73m9.13,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m0,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.12,1.88M128.17,70.85l2.1,1.88m-3.53,-1.88 l2.12,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88M128.17,70.85l2.1,1.88m-9.13,-1.88 l2.08,1.88m-3.51,-1.88 l2.12,1.88m-3.53,-1.88L120.43,72.73m-3.53,-1.88L119.04,72.73m0,8.8 l-2.1,1.9M120.43,81.53l-2.12,1.9M121.86,81.53l-2.12,1.9M123.25,81.53l-2.1,1.9M124.66,81.53l-2.1,1.9M126.07,81.53l-2.1,1.9M127.48,81.53 L125.39,83.43M128.89,81.53 L126.78,83.43M130.29,81.53 L128.17,83.43m3.51,-1.9L129.56,83.43m3.57,-1.9 l-2.12,1.9M134.52,81.53l-2.1,1.9M128.89,81.53 L126.78,83.43m9.13,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m0,-1.9 l2.1,1.9M136.63,81.53l2.1,1.9M135.22,81.53l2.1,1.9M133.81,81.53l2.1,1.9M132.4,81.53l2.1,1.9M130.99,81.53l2.12,1.9M129.56,81.53l2.12,1.9M128.17,81.53l2.1,1.9M126.78,81.53l2.1,1.9M125.37,81.53l2.1,1.9M123.96,81.53l2.1,1.9M122.55,81.53l2.1,1.9M128.19,81.53l2.1,1.9m-9.13,-1.9 l2.08,1.9M119.74,81.53l2.12,1.9M118.33,81.53l2.12,1.9M116.94,81.53 L119.04,83.43"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M24.86,68.99L47.8,68.99A2.09,2.24 90,0 1,50.04 71.08L50.04,81.98A2.09,2.24 90,0 1,47.8 84.07L24.86,84.07A2.09,2.24 90,0 1,22.61 81.98L22.61,71.08A2.09,2.24 90,0 1,24.86 68.99z"
        android:strokeWidth="0.26"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M25.45,69.63L47.22,69.63A1.93,2.12 90,0 1,49.35 71.56L49.35,81.53A1.93,2.12 90,0 1,47.22 83.47L25.45,83.47A1.93,2.12 90,0 1,23.33 81.53L23.33,71.56A1.93,2.12 90,0 1,25.45 69.63z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m35.43,73.38 l1.31,0.05 0.05,0.6 1.08,0.55 0.84,-0.7 1.01,0.55 -0.95,0.6 0.23,0.83 1.19,-0.05L40.2,76.91l-0.95,-0.04 -0.49,0.69 1.08,0.78 -0.84,0.64 -0.91,-0.69 -1.31,0.27 0.07,1.01 -1.43,0.1 -0.19,-0.98 -1.19,-0.5 -0.66,0.69 -1.01,-0.51 0.59,-0.78 -0.72,-0.5L31.24,77.1l-0.05,-1.42 1.01,0.09 0.72,-0.83 -0.84,-0.64 1.08,-0.73 0.77,0.59 1.31,-0.19z"
        android:strokeWidth="0.15"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M34.35,76.37a1.23,1.45 90,1 0,2.9 0a1.23,1.45 90,1 0,-2.9 0z"
        android:strokeWidth="0.17"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m26.84,70.25 l-2.12,1.88m3.53,-1.88 l-2.12,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88L34.58,72.13m3.51,-1.88L35.97,72.13m3.53,-1.88L37.36,72.13m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m-3.51,-1.88L34.58,72.13m9.13,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m3.51,-1.88 l-2.1,1.88m0,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.12,1.88M35.97,70.25l2.1,1.88m-3.53,-1.88 l2.12,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88m-3.51,-1.88 l2.1,1.88M35.97,70.25l2.1,1.88m-9.13,-1.88 l2.08,1.88m-3.51,-1.88 l2.12,1.88m-3.53,-1.88L28.23,72.13m-3.53,-1.88L26.84,72.13m0,8.8 l-2.1,1.9M28.23,80.93l-2.12,1.9M29.66,80.93l-2.12,1.9M31.05,80.93l-2.1,1.9M32.46,80.93l-2.1,1.9M33.87,80.93l-2.1,1.9M35.28,80.93 L33.19,82.83M36.69,80.93 L34.58,82.83M38.09,80.93 L35.97,82.83m3.51,-1.9L37.36,82.83m3.57,-1.9 l-2.12,1.9M42.32,80.93l-2.1,1.9M36.69,80.93 L34.58,82.83m9.13,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m3.51,-1.9 l-2.1,1.9m0,-1.9 l2.1,1.9M44.43,80.93l2.1,1.9M43.02,80.93l2.1,1.9M41.61,80.93l2.1,1.9M40.2,80.93l2.1,1.9M38.79,80.93l2.12,1.9M37.36,80.93l2.12,1.9M35.97,80.93l2.1,1.9M34.58,80.93l2.1,1.9M33.17,80.93l2.1,1.9M31.76,80.93l2.1,1.9M30.35,80.93l2.1,1.9M35.99,80.93l2.1,1.9m-9.13,-1.9 l2.08,1.9M27.54,80.93l2.12,1.9M26.13,80.93l2.12,1.9M24.74,80.93 L26.84,82.83"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M65.68,100.56c-34.13,33.47 -48.69,37.7 -41.84,40.4s28.82,-14.58 51.68,-37.51 36.86,-43.36 30.02,-46.06 -3.54,7.59 -39.85,43.16z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m36.99,124.91 l11.97,3.14c-11.25,10.7 -23.92,15.08 -27.43,13.2s6.33,-6.28 15.48,-16.32z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m36.99,126.16 l9.86,2.51c-10.96,10.29 -21.81,13.21 -24.61,11.94 -2.82,-1.25 5.86,-4.78 14.75,-14.45z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M73.64,99.13c-0.03,0.16 -0.11,0.88 -0.18,1.17 -0,0.4 -0.04,0.7 -0.08,1.01 -0.11,0.27 -0.1,0.52 -0.43,0.62a1.24,1.4 90,0 1,-0.7 0.55,3.48 3.91,90 0,1 -0.88,0.47 1.49,1.68 90,0 1,-1.05 0.07q-0.43,-0.3 -0.78,-0.55c-0.06,-0.25 -0.11,-0.61 -0.18,-0.86a4.61,4.1 0,0 1,0.18 1.02c-0.03,0.37 -0.18,0.55 -0.35,0.84"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m70.47,104.33 l-0.08,-0.07c0.21,0.19 0.13,0.1 -0.35,-0.16q-0.49,-0.2 -1.13,-0.3l-1.23,0c-0.48,0 -0.8,0.06 -1.13,0.15 -0.25,0.22 -0.62,0.35 -0.88,0.55a1.37,1.54 90,0 0,-0.52 0.62c-0.24,0.21 -0.39,0.53 -0.62,0.7 -0.1,0.36 -0.29,0.35 -0.43,0.62 0.42,0.02 0.74,0.07 0.96,0.31 0.36,0.16 -0.31,-0.16 -0.45,-0.31 -0.28,-0.07 -0.35,-0.15 -0.78,-0.15 -0.39,-0.12 -0.64,0.09 -1.05,0.15 -0.24,0.12 -0.31,0.16 -0.62,0.16"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M65.76,108.19c-0.18,0 -0.99,0.14 -1.31,0 -0.36,-0.09 -0.56,-0.25 -0.78,-0.55 -0.11,-0.21 -0,0.37 -0,0.62 -0.04,0.46 -0.2,0.46 -0.45,0.77 -0.25,0.15 -0.52,0.35 -0.78,0.47a2.23,2.52 90,0 1,-0.78 0.38q-0.45,0.16 -0.78,0.38c-0.29,0.19 -0.28,0.41 -0.45,0.71 0.04,0.37 0.11,0.6 0.35,0.77 0.11,0.26 0.34,0.52 0.53,0.7 0.21,0.37 0.46,0.53 -0.08,0.62 -0.31,0.12 -0.48,0.02 -0.8,0"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M60.22,113.6c-0.22,-0.05 -0.98,-0.27 -1.31,-0.38l-1.23,0c-0.46,0.04 -0.56,0.16 -0.95,0.31 -0.27,0.24 -0.43,0.4 -0.8,0.55 -0.18,0.21 -0.56,0.58 -0.7,0.86a3.23,3.63 90,0 0,-0.17 0.99c-0,0.46 0.14,0.55 0.17,0.94a2.36,2.66 90,0 0,0.53 0.93q0.04,0.12 0.17,0.15"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M56.1,118.96c-0.04,0 -0.12,0.2 -0.25,0.36 -0.29,0.29 -0.32,0.31 -0.74,0.31 -0.4,-0.02 -0.35,-0.16 -0.63,-0.24 -0.13,-0.2 -0.29,-0.35 -0.44,-0.62 -0.1,-0.16 -0.19,-0.4 -0.3,-0.55 -0.12,-0.19 -0.26,-0.32 -0.38,-0.49 -0.12,-0.05 -0.16,-0.1 -0.32,-0.13 0.36,0.06 0.4,0.19 0.63,0.37l0.31,0.55c0.04,0.26 0.12,0.47 0.13,0.79 -0,0.34 -0,0.58 -0.13,0.86 -0.15,0.15 -0.25,0.3 -0.44,0.43a3.63,3.67 90,0 0,-0.43 0.49l-0.57,0.36c-0.18,0.11 -0.39,0.22 -0.67,0.24 -0.37,-0.03 -0.48,-0.13 -0.74,-0.24 -0.24,-0.17 -0.42,-0.27 -0.63,-0.43a2.06,2.08 90,0 1,-0.44 -0.49c-0.33,-0.08 -0.55,-0.05 -0.74,0.13a1.67,1.69 90,0 0,-0.43 0.43c-0.1,0.05 -0.28,0.2 -0.38,0.24"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M49.24,121.55c0.1,0.04 0.47,0.29 0.62,0.38 0.1,0.33 0.26,0.5 0.31,0.86q0.11,0.49 0.09,1.06c-0.09,0.39 -0.25,0.67 -0.39,0.98 -0.25,0.17 -0.37,0.29 -0.69,0.46q-0.44,0.09 -1,0.09c-0.25,0.07 -0.69,0.11 -0.93,0 -0.35,-0.05 -0.6,-0.18 -0.85,-0.31 -0.28,-0.16 -0.46,-0.32 -0.76,-0.46q-0.26,-0.32 -0.62,-0.68c-0.2,-0.26 -0.33,-0.4 -0.47,-0.68q0.21,0.46 0.38,0.92c0.19,0.32 0.28,0.44 0.31,0.83 0.12,0.32 0.09,0.71 0.09,1.08 -0.04,0.24 -0.05,0.71 -0.16,0.92l-0,0.23"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M37.86,128.41l-0,0.12q-0.04,-0.46 0.14,0.44c0.22,0.25 0.79,0.32 1.23,0.21 0.4,-0.3 0.4,-0.53 1.11,-0.66 0.71,0.04 0.99,0.21 1.39,0.44a1.77,1.98 90,0 1,0.99 0.76c0.14,0.37 0.22,0.64 -0.26,0.78a2.66,2.98 90,0 1,-1.39 0.44c-0.56,0 -0.97,-0.05 -1.23,0.23 -0.4,0.35 -0.36,0.57 -0.36,1.21 -0.14,0.2 -0.16,0.78 -0.26,1.01a1.95,2.18 90,0 1,-1.11 0.21c-0.14,-0.35 -0.28,-0.96 -0.38,-1.33 -0.1,-0.39 -0.12,-0.96 -0.38,-1.22a3.02,3.37 90,0 0,-0.85 -0.78,1.42 1.59,90 0,0 -0.75,-0.43c0.5,0.21 0.56,0.46 0.62,0.99 -0.42,0.18 -0.54,0.32 -1.23,0.34 -0.69,-0.05 -0.85,-0.21 -1.11,-0.57 -0.06,0.57 -0.26,0.75 -0.4,1.24 -0.24,0.32 -0.34,0.66 -0.6,1.1 0.1,0.53 0.34,0.55 0.36,1.21 0.16,0.44 0.16,0.85 0.5,1.12 0.16,0.18 0.28,0.2 0.64,0.21 -0.71,0 -1.15,-0.07 -1.63,-0.32q-0.5,-0.34 -0.99,-0.67a4.26,4.76 90,0 0,-1.49 -0.67c-0.58,0 -0.99,-0.05 -1.23,0.23q-0.52,0.35 -0.75,1.12c-0.1,0.57 -0.16,0.96 0.14,1.33 0.2,0.43 0.26,0.59 -0,1.1 -0.3,0.35 -0.71,0.41 -1.39,0.55q-0.71,-0.04 -1.23,-0.21"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M104.66,70.38c15.14,-26.2 25.17,-48.52 22.44,-49.79 -2.77,-1.28 -17.29,18.98 -32.43,45.17z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M103.66,69.94c14.6,-25.27 24.66,-46.64 22.47,-47.65 -2.21,-1.02 -15.86,18.69 -30.46,43.96"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M101.15,56.75c-1.78,2.95 -3.66,6.34 -5.48,9.49L103.68,69.94c1.82,-3.15 3.82,-6.49 5.48,-9.49z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M101.85,57.77c-1.79,2.97 -3.01,5.22 -4.85,8.39l5.98,2.76c1.82,-3.15 3.18,-5.38 4.85,-8.39z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M106.19,61.2l-4.01,-1.85l-3.55,6.15l4.01,1.85zM106.19,61.2 L98.63,65.5m4.01,1.85 l-0.45,-8m22.08,-29.43l-4.01,-1.85"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M123.99,26.03m1.11,-0.28a1.01,1.15 76.52,1 0,-2.23 0.56a1.01,1.15 76.52,1 0,2.23 -0.56"
        android:strokeWidth="1.3"
        android:fillColor="#ef0000"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M98.72,99.96c34.13,33.47 48.69,37.7 41.84,40.4s-28.82,-14.58 -51.68,-37.51 -36.86,-43.36 -30.02,-46.06 3.54,7.59 39.85,43.16z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m127.41,124.31 l-11.97,3.14c11.25,10.7 23.92,15.08 27.43,13.2s-6.33,-6.28 -15.48,-16.32z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m127.41,125.46 l-9.86,2.51c10.96,10.29 21.81,13.21 24.61,11.94 2.82,-1.25 -5.86,-4.78 -14.75,-14.45z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M90.76,98.43c0.03,0.16 0.11,0.88 0.18,1.17 0,0.4 0.04,0.7 0.08,1.01 0.11,0.27 0.1,0.52 0.43,0.62a1.24,1.4 90,0 0,0.7 0.55,3.48 3.91,90 0,0 0.88,0.47 1.49,1.68 90,0 0,1.05 0.07q0.43,-0.3 0.78,-0.55c0.06,-0.25 0.11,-0.61 0.18,-0.86a4.1,4.61 90,0 0,-0.18 1.02c0.03,0.37 0.18,0.55 0.35,0.84"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m93.93,103.63 l0.08,-0.07c-0.21,0.19 -0.13,0.1 0.35,-0.16q0.49,-0.2 1.13,-0.3l1.23,0c0.48,0 0.8,0.06 1.13,0.15 0.25,0.22 0.62,0.35 0.88,0.55a1.37,1.54 90,0 1,0.52 0.62c0.24,0.21 0.39,0.53 0.62,0.7 0.1,0.36 0.29,0.35 0.43,0.62 -0.42,0.02 -0.74,0.07 -0.96,0.31 -0.36,0.16 0.31,-0.16 0.45,-0.31 0.28,-0.07 0.35,-0.15 0.78,-0.15 0.39,-0.12 0.64,0.09 1.05,0.15 0.24,0.12 0.31,0.16 0.62,0.16"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M98.64,107.59c0.18,0 0.99,0.14 1.31,0 0.36,-0.09 0.56,-0.25 0.78,-0.55 0.11,-0.21 0,0.37 0,0.62 0.04,0.46 0.2,0.46 0.45,0.77 0.25,0.15 0.52,0.35 0.78,0.47a2.23,2.52 90,0 0,0.78 0.38q0.45,0.16 0.78,0.38c0.29,0.19 0.28,0.41 0.45,0.71 -0.04,0.37 -0.11,0.6 -0.35,0.77 -0.11,0.26 -0.34,0.52 -0.53,0.7 -0.21,0.37 -0.46,0.53 0.08,0.62 0.31,0.12 0.48,0.02 0.8,0"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M104.18,113c0.22,-0.05 0.98,-0.27 1.31,-0.38l1.23,0c0.46,0.04 0.56,0.16 0.95,0.31 0.27,0.24 0.43,0.4 0.8,0.55 0.18,0.21 0.56,0.58 0.7,0.86a3.23,3.63 90,0 1,0.17 0.99c0,0.46 -0.14,0.55 -0.17,0.94a2.36,2.66 90,0 1,-0.53 0.93q-0.04,0.12 -0.17,0.15"
        android:strokeWidth="0.24"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M108.4,118.36c0.04,0 0.12,0.2 0.25,0.36 0.29,0.29 0.32,0.31 0.74,0.31 0.4,-0.02 0.35,-0.16 0.63,-0.24 0.13,-0.2 0.29,-0.35 0.44,-0.62 0.1,-0.16 0.19,-0.4 0.3,-0.55 0.12,-0.19 0.26,-0.32 0.38,-0.49 0.12,-0.05 0.16,-0.1 0.32,-0.13 -0.36,0.06 -0.4,0.19 -0.63,0.37l-0.31,0.55c-0.04,0.26 -0.12,0.47 -0.13,0.79 0,0.34 0,0.58 0.13,0.86 0.15,0.15 0.25,0.3 0.44,0.43a3.63,3.67 90,0 1,0.43 0.49l0.57,0.36c0.18,0.11 0.39,0.22 0.67,0.24 0.37,-0.03 0.48,-0.13 0.74,-0.24 0.24,-0.17 0.42,-0.27 0.63,-0.43a2.06,2.08 90,0 0,0.44 -0.49c0.33,-0.08 0.55,-0.05 0.74,0.13a1.67,1.69 90,0 1,0.43 0.43c0.1,0.05 0.28,0.2 0.38,0.24"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M115.16,120.85c-0.1,0.04 -0.47,0.29 -0.62,0.38 -0.1,0.33 -0.26,0.5 -0.31,0.86q-0.11,0.49 -0.09,1.06c0.09,0.39 0.25,0.67 0.39,0.98 0.25,0.17 0.37,0.29 0.69,0.46q0.44,0.09 1,0.09c0.25,0.07 0.69,0.11 0.93,0 0.35,-0.05 0.6,-0.18 0.85,-0.31 0.28,-0.16 0.46,-0.32 0.76,-0.46q0.26,-0.32 0.62,-0.68c0.2,-0.26 0.33,-0.4 0.47,-0.68q-0.21,0.46 -0.38,0.92c-0.19,0.32 -0.28,0.44 -0.31,0.83 -0.12,0.32 -0.09,0.71 -0.09,1.08 0.04,0.24 0.05,0.71 0.16,0.92l0,0.23"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M126.54,127.81l0,0.12q0.04,-0.46 -0.14,0.44c-0.22,0.25 -0.79,0.32 -1.23,0.21 -0.4,-0.3 -0.4,-0.53 -1.11,-0.66 -0.71,0.04 -0.99,0.21 -1.39,0.44a1.77,1.98 90,0 0,-0.99 0.76c-0.14,0.37 -0.22,0.64 0.26,0.78a2.66,2.98 90,0 0,1.39 0.44c0.56,0 0.97,-0.05 1.23,0.23 0.4,0.35 0.36,0.57 0.36,1.21 0.14,0.2 0.16,0.78 0.26,1.01a1.95,2.18 90,0 0,1.11 0.21c0.14,-0.35 0.28,-0.96 0.38,-1.33 0.1,-0.39 0.12,-0.96 0.38,-1.22a3.02,3.37 90,0 1,0.85 -0.78,1.42 1.59,90 0,1 0.75,-0.43c-0.5,0.21 -0.56,0.46 -0.62,0.99 0.42,0.18 0.54,0.32 1.23,0.34 0.69,-0.05 0.85,-0.21 1.11,-0.57 0.06,0.57 0.26,0.75 0.4,1.24 0.24,0.32 0.34,0.66 0.6,1.1 -0.1,0.53 -0.34,0.55 -0.36,1.21 -0.16,0.44 -0.16,0.85 -0.5,1.12 -0.16,0.18 -0.28,0.2 -0.64,0.21 0.71,0 1.15,-0.07 1.63,-0.32q0.5,-0.34 0.99,-0.67a4.26,4.76 90,0 1,1.49 -0.67c0.58,0 0.99,-0.05 1.23,0.23q0.52,0.35 0.75,1.12c0.1,0.57 0.16,0.96 -0.14,1.33 -0.2,0.43 -0.26,0.59 0,1.1 0.3,0.35 0.71,0.41 1.39,0.55q0.71,-0.04 1.23,-0.21"
        android:strokeWidth="0.23"
        android:fillColor="#00000000"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M59.74,69.78c-15.14,-26.2 -25.17,-48.52 -22.44,-49.79 2.77,-1.28 17.29,18.98 32.43,45.17z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M60.74,69.34c-14.6,-25.27 -24.66,-46.64 -22.47,-47.65 2.21,-1.02 15.86,18.69 30.46,43.96"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M63.25,56.15c1.78,2.95 3.66,6.34 5.48,9.49L60.72,69.34c-1.82,-3.15 -3.82,-6.49 -5.48,-9.49z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M62.55,57.17c1.79,2.97 3.01,5.22 4.85,8.39l-5.98,2.76c-1.82,-3.15 -3.18,-5.38 -4.85,-8.39z"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M58.21,60.6l4.01,-1.85l3.55,6.15l-4.01,1.85zM58.21,60.6 L65.77,64.9m-4.01,1.85 l0.45,-8m-22.08,-29.43l4.01,-1.85"
        android:strokeWidth="1.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M40.41,25.43m-1.11,-0.28a1.01,1.15 103.48,1 1,2.23 0.56a1.01,1.15 103.48,1 1,-2.23 -0.56"
        android:strokeWidth="1.3"
        android:fillColor="#ef0000"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.85,56.43c0,22 0.13,30.29 -1.48,31.42 -1.53,1.22 -44.22,0 -44.22,6.28s35.14,12.6 45.7,12.6q21.12,0 21.09,-18.88L92.94,56.44l-21.09,0z"
        android:strokeWidth="0.28"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,62.71l0,3.14a63.71,56.96 0,0 0,21.08 0l0,-3.14a63.71,56.96 0,0 1,-21.08 0z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,56.43l0,3.14a63.71,56.96 0,0 0,21.08 0l0,-3.14a63.71,56.96 0,0 1,-21.08 0z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,59.57l0,3.14a63.71,56.96 0,0 0,21.08 0l0,-3.14a63.71,56.96 0,0 1,-21.08 0z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,65.85l0,3.14a63.71,56.96 0,0 0,21.08 0l0,-3.14a63.71,56.96 0,0 1,-21.08 0z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.85,61.34a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M75.68,61.68a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M79.53,62.03a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M83.36,62.03a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M87.19,61.68a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M91.03,61.34a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,72.13 L92.93,89.11m-18.97,-16.98 l18.99,15.08M76.76,72.77l16.2,12.58m-14.09,-12.6 l14.09,10.7M80.99,72.77l11.95,8.8M83.09,72.77l9.84,6.92m-7.74,-6.92 l7.74,5.02M87.34,72.77l5.64,3.76M89.42,72.77l3.53,2.52M91.55,72.77l1.39,1.24m-21.08,0L92.93,91.01m0,-18.86L69.71,90.99m21.1,-18.86 l-20.38,16.36m17.59,-15.72 l-16.18,12.56m14.05,-12.56 l-14.05,10.68M83.8,72.77l-11.95,8.8m9.84,-8.8 l-9.84,6.92m7.74,-6.92 l-7.74,5.02M77.45,72.77l-5.62,3.76m3.51,-3.76 l-3.51,2.52m1.39,-2.52 l-1.39,1.24m21.1,0L69.71,92.91m23.22,-16.98L69.71,94.77m23.22,-16.98L69.71,96.65m23.22,-16.98L69.71,98.57m23.22,-16.98L69.71,100.43m23.22,-16.98L69.71,102.31m23.22,-16.98L69.71,104.19m23.22,-16.96L69.71,106.09m23.22,-16.98 l-21.81,17.6M92.93,90.99l-19.69,15.72m18.97,-13.2 l-15.48,12.58M91.55,96.01l-12.66,10.06m11.25,-6.92 l-6.35,5.02m-11.93,-28.28 l20.38,16.34m-20.38,-14.46 l20.38,16.36M71.86,79.69l19.69,15.7m-19.69,-13.84 l19.69,15.74m-19.69,-13.84 l18.97,15.08m-19.69,-13.82 l18.99,15.08M71.14,86.61l17.59,13.84m-18.28,-12.6 l17.59,13.84M69.71,89.09l16.87,13.2m-16.87,-11.32 l16.18,12.58M69.71,92.87l14.09,11.3"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.86,69.01l0,3.14a63.71,56.96 0,0 0,21.08 0l0,-3.14a63.71,56.96 0,0 1,-21.08 0z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m69.73,94.77 l12.66,10.06m-12.66,-8.16 l11.25,8.78m-11.25,-6.92 l9.84,7.56m-9.84,-5.68 l7.05,5.68M69.71,102.31l5.64,4.4M69.71,104.19l3.51,2.52"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.85,70.78a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M75.68,71.12a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M79.53,71.45a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M83.36,71.45a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M87.19,71.12a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M91.03,70.78a0.68,0.96 90,1 0,1.93 0a0.68,0.96 90,1 0,-1.93 0z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m41.61,101.69 l25.31,-13.2m-26.72,12.56 l23.92,-12.56m-24.61,11.3 l21.79,-11.3M38.79,98.55l18.99,-10.06M37.36,97.29l17.59,-8.8m-16.87,6.92 l11.95,-6.3M37.36,94.13l9.13,-5.02m-9.13,3.14 l6.35,-3.14m0,13.2 l23.9,-12.58M45.84,102.93 L68.32,90.99m-20.38,12.58 l21.1,-11.32m-18.99,11.94 l17.59,-9.42M52.84,104.19l15.48,-8.16m-13.38,8.8 l13.36,-6.92m-11.25,7.54 l10.56,-5.66m-7.74,5.68 l8.44,-4.42m-5.62,5.04L68.32,102.93m-2.78,3.16 l3.51,-1.88"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M70.43,87.87l-3.51,0a56.96,63.71 90,0 0,-0 18.84l3.51,0a56.96,63.71 90,0 1,-0 -18.84z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m28.94,96.65 l8.44,-6.28m-8.44,1.88 l8.44,7.54"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="m41.61,89.1 l-4.22,0.63c-0.94,3.99 -0.94,7.34 -0,11.33l4.22,1.25c-0.94,-3.98 -0.94,-9.22 -0,-13.2z"
        android:strokeWidth="0.17"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M92.94,57.67l0,-3.18c-3.18,0.04 -5.29,-1.86 -5.29,-3.76l-10.53,0.02c0,1.88 -2.08,3.78 -5.26,3.78l0,3.14z"
        android:strokeWidth="0.3"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M77.85,46.99l9.05,0L86.9,50.77L77.85,50.77zM78.62,43.21l7.52,0l0,3.78l-7.54,0zM78.62,39.44l7.52,0l0,3.76l-7.54,0zM79.38,34.41L85.39,34.41l0,5.04l-6.01,0zM79.12,30.01l6.55,0l0,4.4l-6.55,0z"
        android:strokeWidth="0.26"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M74.02,25.92a5.98,8.72 90,1 0,17.43 0a5.98,8.72 90,1 0,-17.43 0z"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M87.1,25.6c0,1.65 2.17,4.4 2.17,4.4 -1.58,1.1 -4.14,1.9 -6.54,1.9s-5.39,-0.46 -6.54,-1.9c0,0 2.17,-2.73 2.17,-4.38s-2.17,-3.78 -2.17,-3.78a9.76,11.28 90,0 1,6.56 -1.88c2.38,0 4.92,0.8 6.52,1.88 0,0 -2.17,2.13 -2.17,3.78z"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M85.65,25.6c0,1.65 2.17,5.04 2.17,5.04 -1.58,1.06 -2.67,1.24 -5.09,1.24s-3.94,0.2 -5.09,-1.24c0,0 2.17,-3.37 2.17,-5.04s-2.17,-4.4 -2.17,-4.4c1.58,-1.06 2.67,-1.24 5.09,-1.24 2.4,0 3.51,0.18 5.09,1.24 0,0 -2.17,2.75 -2.17,4.4z"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M74.02,26.23m-2.17,0a1.88,2.17 90,1 1,4.35 0a1.88,2.17 90,1 1,-4.35 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M91.46,26.23m-2.17,0a1.88,2.17 90,1 1,4.35 0a1.88,2.17 90,1 1,-4.35 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M82.75,26.23m-2.17,0a1.88,2.17 90,1 1,4.35 0a1.88,2.17 90,1 1,-4.35 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M82.75,19.94m-2.17,0a1.88,2.17 90,1 1,4.35 0a1.88,2.17 90,1 1,-4.35 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M76.38,55.79m-2.25,0a1.88,2.25 90,1 1,4.5 0a1.88,2.25 90,1 1,-4.5 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M88.41,55.79m-2.25,0a1.88,2.25 90,1 1,4.5 0a1.88,2.25 90,1 1,-4.5 0"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M77.12,50.75l10.54,0l-5.27,5.66z"
        android:strokeWidth="0.25"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M82.38,55.15m-3,0a2.51,3 90,1 1,6 0a2.51,3 90,1 1,-6 0"
        android:strokeWidth="0.34"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M71.69,73.95L86.16,73.95A0.66,0.85 90,0 1,87.02 74.61L87.02,77.07A0.66,0.85 90,0 1,86.16 77.73L71.69,77.73A0.66,0.85 90,0 1,70.84 77.07L70.84,74.61A0.66,0.85 90,0 1,71.69 73.95z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M91.94,70.81c-1.95,0 -3.51,1.47 -3.51,3.28l0,2.86a3.19,3.57 90,0 0,3.51 3.28,3.19 3.57,90 0,0 3.51,-3.28l0,-2.84a3.19,3.57 90,0 0,-3.51 -3.3zM91.94,72.05c-1.17,0 -2.1,1.19 -2.1,2.64l0,1.65c0,1.46 0.93,2.63 2.1,2.63s2.1,-1.17 2.1,-2.63l0,-1.65c0,-1.46 -0.93,-2.63 -2.1,-2.63zM73.66,71.43c-1.19,0 -2.1,1.17 -2.1,2.64l0,2.91c0,1.46 0.93,2.63 2.1,2.63s2.1,-1.17 2.1,-2.63L75.76,74.06c0,-1.47 -0.93,-2.64 -2.1,-2.64zM73.66,70.19a3.19,3.57 90,0 0,-3.51 3.28l0,4.12a3.19,3.57 90,0 0,3.51 3.28c1.95,0 3.51,-1.47 3.51,-3.28l0,-4.12a3.19,3.57 90,0 0,-3.51 -3.3z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M68.02,72.69c-1.17,0 -2.1,1.17 -2.1,2.63l0,0.69c0,1.46 0.93,2.63 2.1,2.63 1.19,0 2.12,-1.17 2.12,-2.63l0,-0.69c0,-1.46 -0.95,-2.63 -2.12,-2.63zM68.02,71.45a3.19,3.57 90,0 0,-3.51 3.28l0,1.88a3.19,3.57 90,0 0,3.51 3.28c1.95,0 3.53,-1.47 3.53,-3.28l0,-1.88a3.19,3.57 90,0 0,-3.53 -3.3z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M59.61,70.95c-2.14,0 -3.82,1.31 -3.82,2.93l0,3.27c0,1.61 1.71,2.93 3.82,2.93s3.79,-1.31 3.79,-2.95l0,-3.25c0,-1.63 -1.71,-2.93 -3.79,-2.93zM59.61,69.57a3.57,6.43 90,0 0,-6.36 3.65L53.25,77.82a6.43,3.57 0,0 0,6.32 3.69c3.54,0 6.36,-1.65 6.36,-3.69l0,-4.6a3.57,6.43 90,0 0,-6.32 -3.67z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M105.32,71.58c-2.14,0 -3.82,1.31 -3.82,2.93l0,3.27c0,1.61 1.71,2.93 3.82,2.93s3.79,-1.31 3.79,-2.95l0,-3.25c0,-1.63 -1.71,-2.93 -3.79,-2.93zM105.32,70.19a3.57,6.43 90,0 0,-6.36 3.65L98.96,78.44a6.43,3.57 0,0 0,6.32 3.69c3.54,0 6.36,-1.65 6.36,-3.69l0,-4.6a3.57,6.43 90,0 0,-6.32 -3.67z"
        android:strokeWidth="0.24"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M93.81,73.95L101.33,73.95A0.66,0.46 90,0 1,101.78 74.61L101.78,77.07A0.66,0.46 90,0 1,101.33 77.73L93.81,77.73A0.66,0.46 90,0 1,93.35 77.07L93.35,74.61A0.66,0.46 90,0 1,93.81 73.95z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M85.87,73.33L90.27,73.33A0.87,0.26 90,0 1,90.53 74.2L90.53,77.5A0.87,0.26 90,0 1,90.27 78.37L85.87,78.37A0.87,0.26 90,0 1,85.61 77.5L85.61,74.2A0.87,0.26 90,0 1,85.87 73.33z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M62.66,73.33L67.07,73.33A0.87,0.26 90,0 1,67.33 74.2L67.33,77.5A0.87,0.26 90,0 1,67.07 78.37L62.66,78.37A0.87,0.26 90,0 1,62.41 77.5L62.41,74.2A0.87,0.26 90,0 1,62.66 73.33z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M47.44,72.69L56.25,72.69A1.08,0.52 90,0 1,56.77 73.77L56.77,77.89A1.08,0.52 90,0 1,56.25 78.97L47.44,78.97A1.08,0.52 90,0 1,46.92 77.89L46.92,73.77A1.08,0.52 90,0 1,47.44 72.69z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
    <path
        android:pathData="M108.61,73.33L116.77,73.33A1.08,0.5 90,0 1,117.27 74.41L117.27,78.53A1.08,0.5 90,0 1,116.77 79.61L108.61,79.61A1.08,0.5 90,0 1,108.12 78.53L108.12,74.41A1.08,0.5 90,0 1,108.61 73.33z"
        android:strokeWidth="0.23"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#ef2d28"/>
  </group>
</vector>
