<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0Z"
      android:fillColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m1.3,-0.08 l637.39,480.09m-637.39,0L638.69,-0.08"/>
  <path
      android:pathData="M0,-0.08l0,28.92L280.49,239.97 0,451.19l0,28.72l44,0L319.99,269.68l275.79,210.34L639.99,480.01L639.99,450.9L359.49,239.97 639.99,28.74l0,-28.72l-44,0L319.99,210.06 44,-0.08Z"
      android:fillColor="#cf142b"/>
  <path
      android:pathData="M364.2,65c10.8,44 4.8,98.2 -44.2,128.4 -49,-30.2 -55,-84.4 -44.2,-128.4 11.6,-10.5 64.2,-17 88.4,0z"
      android:strokeWidth=".5"
      android:fillColor="#cf142b"
      android:strokeColor="#800000"/>
  <path
      android:pathData="M295.1,91a7,7 0,0 1,-2.4 -0.4,6 6,0 0,1 -1.4,-2l-2.7,0.2a7,7 0,0 1,-2.1 -1.2,4 4,0 0,0 2.2,-0.9l-2.5,-0.5q-1.2,-0.6 -2.1,-1.3 1.3,-0.1 2.1,-0.8l-2.4,-0.3 -1.7,-1.3q1.3,0 2.2,-1l-2.3,-0.1 -2.4,-1.6a5,5 0,0 0,2.6 -0.8l-0.6,-1c-0.6,-1.2 -1.8,-0.7 -2.7,-1.1a2,2 0,0 1,-1.4 -1.8c0,-0.9 0.4,-2.3 2.4,-2 -1,-1.6 0.1,-2.7 1.2,-3.2s2,-0.2 2.7,1.5l1.4,-0.8a2,2 0,0 1,1.8 0.6v1.2c-0.3,0.6 -1,1 -1,1.8 -0.3,0.8 0.5,2.4 0.5,2.4s4,3.2 5.7,4.8l2.8,3h4.1l0.3,6.6H295z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M284.2,69.2a2,2 0,0 1,0.7 2.4q0.3,0.5 1,0.4a1,1 0,0 0,0.7 -1.3c-0.4,-0.3 -0.5,-1.1 -2.4,-1.6zM279.7,69.7 L278.6,69.8a2,2 0,0 1,1.6 2q0.6,0.2 1,-0.1a1,1 0,0 0,0.2 -1.5c-0.4,0 -0.8,-0.5 -1.6,-0.5zM278.4,73.9c-0.5,0.1 -1.2,-0.4 -2.7,0.8a2,2 0,0 1,2.3 1q0.5,0 0.9,-0.4a1,1 0,0 0,-0.5 -1.4z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="M317.6,94c-0.6,-0.2 -1.5,-1 -2.2,-1a2,2 0,0 0,-1.7 1,2 2,0 0,0 0.4,1.5l1.3,0.8a4,4 0,0 0,-1 1.6,2 2,0 0,0 0.6,1.1q0.4,0.8 1.2,0.8l1.5,-0.1 0.1,1.1a2,2 0,0 0,2.1 1.1,1 1,0 0,0 1.2,-0.6c0.3,-0.4 0.3,-1.4 0.7,-1.9l1.9,-1.6q1,0.7 0.6,2l1.6,-1.4 0.5,-1.8a2,2 0,0 1,0.8 2l1.6,-1.2 0.8,-2q1,1.1 0.5,2.3a6,6 0,0 0,1.7 -1.6l0.4,-1.6q2.6,0 2.8,-1.4c0,-0.5 -0.6,-1.3 -1.9,-1.6l-0.6,-1.2v-1.1a5,5 0,0 1,1.5 -2l2.8,-1.5 -0.6,-2.9 -6.5,-1 -7.4,1.8c1.4,0.6 4.3,2.7 4.1,3.5 0,0.3 -0.7,0.6 -1,1q-0.2,0.5 0.2,1c0.2,0.4 1,0.5 1.3,0.9q0.3,0.6 0,1c-0.5,0.7 -2.2,1 -3,1.3l-4,2.1a6,6 0,0 1,-2.2 -0.4z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M333.1,85.1s-0.1,2.4 -1.1,3a2,2 0,0 1,-1.4 0.1,2 2,0 0,1 -1,-1l-0.3,-1.1 -2.3,-0.7q-1.8,0 -3.6,0.6a8,8 0,0 1,2.7 -3.2l3.9,-0.3z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M343.6,69.4c1.1,-0.1 3.4,-0.6 4.2,-1.3l1.4,-1.4q-0.2,3.3 -3.8,4.7a14,14 0,0 1,-6.2 0.8l-3.8,-0.8c1.4,1.4 0.7,2.4 -1,3 0,0 0.2,-1.3 -0.2,-1.5q-1.7,-0.4 -3.5,0.8a7,7 0,0 1,-3 0.6,8 8,0 0,1 -3.6,-0.5c-0.8,-0.2 -2.3,-1.3 -2.8,-1.2 -1.4,0.2 -3,0.4 -3.7,1.4q-0.3,0.5 0,1.2a4,4 0,0 0,2.6 1.5q4.2,0.9 8.4,0.3l9.6,-1.9c3,-0.5 6.8,-1.4 9,-1.9a34,34 0,0 1,8.9 -1c5.3,0.1 7,2.8 6.9,5.6 -0.2,2.8 -3.3,4.1 -6,5 -4.8,1.4 -11.4,0.6 -15.6,0.4l2,1.6c0.8,0.6 1.2,1.8 2,2 1.3,0.4 3.2,-0.4 4.5,-0.4 1.9,-0.2 5,0 5,0l1.3,-0.8h1.2q1,0.3 0.8,2 0.3,0.4 1,0.6h1.7l1.6,0.8 -1.5,0.5 -0.7,0.7q1,0 2,0.3l1.2,1 -1.3,0.2 -0.8,0.6s1.6,-0.1 2.2,0.3l1.1,1.2h-1.3l-0.8,0.4 1.6,1.2q0.5,0.7 0.6,1.6l-1.1,-0.6h-0.8l0.2,2.2c0,0.6 1,1.2 1,1.6a1,1 0,0 1,-0.1 1.2,2 2,0 0,1 -2.2,0.7l-1,-0.6 -0.9,1c-0.5,0.4 -0.6,0.4 -1.3,0.4a2,2 0,0 1,-1.3 -0.5l-0.7,-1.6 -1.6,0.3a2,2 0,0 1,-1.5 -0.7,2 2,0 0,1 0.3,-1.8c0.4,-0.4 1.6,-0.5 2.2,-0.8a5,5 0,0 0,1.6 -1.3l-3.8,-3 -1.6,-2.1s-3.4,1 -4.7,1a26,26 0,0 1,-6.4 0c-4.8,0 -8,-6.6 -12.3,-8.2a5,5 0,0 0,-3.3 0c-5,1 -10.4,6.8 -15.3,8q-3.2,0.6 -6.4,0.7a6,6 0,0 1,-1.3 1.7,7 7,0 0,1 -2.6,0.5l-1,2.3a7,7 0,0 1,-2 1.3,3 3,0 0,0 0,-2.3l-1.5,2 -2.2,1.1c0.2,-0.6 0.5,-1.3 0,-2.1l-1.2,1.8q-1,0.7 -2,1a2,2 0,0 0,0 -2.3l-1.2,1.9 -2.6,1.3a4,4 0,0 0,0.4 -2.4h-1.2c-1.5,0 -1.6,1.3 -2.3,1.9a3,3 0,0 1,-2.4 0.4c-0.8,-0.4 -1.7,-1.4 -0.5,-3 -2,0.2 -3,-1 -3,-2.2 0,-1 0.3,-1.9 2.4,-1.8l-0.2,-1.4a1.4,1.4 0,0 1,1.5 -1.2q0.6,0 1.2,0.6c0.4,0.4 0.5,1.1 1.2,1.6 0.8,0.6 2.7,0.5 2.7,0.5a50,50 0,0 1,6.2 -2.8c1.3,-0.4 5,-1 5,-1V81a87,87 0,0 1,13 1.1l5.2,-0.3s8.4,-1.3 12.4,-1.7q3,-0.3 6.1,-0.2c3,0.2 5.6,0.9 8.6,1q3.2,0.5 6.4,0.3c3.5,-0.2 9.7,0 11.7,-3a2,2 0,0 0,-0.6 -2.6,3 3,0 0,0 -3,-0.8q-1.4,0.2 -1.7,1.2a1,1 0,0 0,0.3 1.1q0.3,0.6 1.1,0.9a4,4 0,0 1,-2.2 -0.3,6 6,0 0,1 -1.4,-2.2 2,2 0,0 0,-0.5 1.5q0,1 0.7,1.6a4,4 0,0 1,-2.4 -0.8,7 7,0 0,1 -0.9,-2.2 2,2 0,0 0,-0.8 1.6q0.1,0.8 0.5,1.7a5,5 0,0 1,-2.2 -1.2,6 6,0 0,1 -0.7,-2.2l-9,1.8 -9.5,1.6a24,24 0,0 1,-11.3 -0.8c-1.6,-0.8 -2.4,-1.3 -2.8,-2.7a3,3 0,0 1,1 -2.7,10 10,0 0,1 5,-2.6 76,76 0,0 1,8.8 -1.6,34 34,0 0,1 9.5,0.4c1.4,0.2 3,-0.1 3.3,-0.6 0.4,-0.4 1,-0.8 0.2,-1.8q1.2,0 1.8,1.2c0.3,0.4 0,1.6 0,1.6z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M343.6,69.4c1.1,-0.1 3.4,-0.6 4.2,-1.3l1.4,-1.4q-0.2,3.3 -3.8,4.7a14,14 0,0 1,-6.2 0.8l-3.8,-0.8c1.4,1.4 0.7,2.4 -1,3 0,0 0.2,-1.3 -0.2,-1.5q-1.7,-0.4 -3.5,0.8a7,7 0,0 1,-3 0.6,8 8,0 0,1 -3.6,-0.5c-0.8,-0.2 -2.3,-1.3 -2.8,-1.2 -1.4,0.2 -3,0.4 -3.7,1.4q-0.3,0.5 0,1.2a4,4 0,0 0,2.6 1.5q4.2,0.9 8.4,0.3l9.6,-1.9c3,-0.5 6.8,-1.4 9,-1.9a34,34 0,0 1,8.9 -1c5.3,0.1 7,2.8 6.9,5.6 -0.2,2.8 -3.3,4.1 -6,5 -4.8,1.4 -11.4,0.6 -15.6,0.4l2,1.6c0.8,0.6 1.2,1.8 2,2 1.3,0.4 3.2,-0.4 4.5,-0.4 1.9,-0.2 5,0 5,0l1.3,-0.8h1.2q1,0.3 0.8,2 0.3,0.4 1,0.6h1.7l1.6,0.8 -1.5,0.5 -0.7,0.7q1,0 2,0.3l1.2,1 -1.3,0.2 -0.8,0.6s1.6,-0.1 2.2,0.3l1.1,1.2h-1.3l-0.8,0.4 1.6,1.2q0.5,0.7 0.6,1.6l-1.1,-0.6h-0.8l0.2,2.2c0,0.6 1,1.2 1,1.6a1,1 0,0 1,-0.1 1.2,2 2,0 0,1 -2.2,0.7l-1,-0.6q-0.3,0.6 -0.9,1a2,2 0,0 1,-1.3 0.5,2 2,0 0,1 -1.3,-0.6l-0.7,-1.6 -1.6,0.3a2,2 0,0 1,-1.5 -0.7,2 2,0 0,1 0.3,-1.8c0.4,-0.4 1.6,-0.5 2.2,-0.8a5,5 0,0 0,1.6 -1.3l-3.8,-3 -1.6,-2.1s-3.4,1 -4.7,1a26,26 0,0 1,-6.4 0c-4.8,0 -8,-6.6 -12.3,-8.2a5,5 0,0 0,-3.3 0c-5,1 -10.4,6.8 -15.3,8q-3.2,0.6 -6.4,0.7a6,6 0,0 1,-1.3 1.7,7 7,0 0,1 -2.6,0.5l-1,2.3a7,7 0,0 1,-2 1.3,3 3,0 0,0 0,-2.4l-1.5,2 -2.2,1.2c0.2,-0.6 0.5,-1.3 0,-2.1l-1.2,1.8q-1,0.7 -2,1a2,2 0,0 0,0 -2.3l-1.2,1.9 -2.6,1.3a4,4 0,0 0,0.4 -2.4h-1.2c-1.5,0 -1.6,1.3 -2.3,1.9a3,3 0,0 1,-2.4 0.4c-0.8,-0.4 -1.7,-1.4 -0.5,-3 -2,0.2 -3,-1 -3,-2.2 0,-1 0.3,-1.9 2.4,-1.8l-0.2,-1.4a1.4,1.4 0,0 1,1.5 -1.2q0.6,0 1.2,0.6c0.4,0.4 0.5,1.1 1.2,1.6 0.8,0.6 2.7,0.5 2.7,0.5a50,50 0,0 1,6.2 -2.8c1.3,-0.4 5,-1 5,-1V81a87,87 0,0 1,13 1.1l5.2,-0.3s8.4,-1.3 12.4,-1.7q3,-0.3 6.1,-0.2c3,0.2 5.6,0.9 8.6,1q3.2,0.5 6.4,0.3c3.5,-0.2 9.7,0 11.7,-3a2,2 0,0 0,-0.6 -2.6,3 3,0 0,0 -3,-0.8q-1.4,0.2 -1.7,1.2 0,0.7 0.3,1 0.4,0.6 1.1,1s-1.7,0 -2.4,-0.4a5,5 0,0 1,-1.2 -2.1,2 2,0 0,0 -0.5,1.5q0,1 0.7,1.6a4,4 0,0 1,-2.4 -0.8,7 7,0 0,1 -0.9,-2.2 2,2 0,0 0,-0.8 1.6q0.1,0.8 0.5,1.7a5,5 0,0 1,-2.2 -1.2,6 6,0 0,1 -0.7,-2.2l-9,1.8 -9.5,1.6a22,22 0,0 1,-11.3 -1c-1.6,-0.6 -2.4,-1.1 -2.8,-2.5a3,3 0,0 1,1 -2.8,10 10,0 0,1 5,-2.5 76,76 0,0 1,8.8 -1.6,34 34,0 0,1 9.4,0.4c1.4,0.2 3,-0.1 3.4,-0.6s1,-0.8 0.1,-1.8a2,2 0,0 1,1.8 1.2c0.3,0.4 0,1.6 0,1.6z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M307.6,82.3c1,0.7 2,-0.1 2.2,-0.2L309,81l-0.2,-2c0,-1.6 0.6,-1.9 0.6,-1.8l1.6,-0.7a2,2 0,0 0,0.7 -1.5l-0.2,-1.9c-1.6,0 -2,0.1 -2.4,0.6l-0.2,-1.6 -0.6,-1.2 -0.5,-0.6q-0.1,0.7 -0.6,1.1l-0.6,-2.1 -1.2,-1 -1.5,0.2 -1.6,1.6 0.1,-1.3q-0.3,0 -0.8,0.2l-1.1,0.7 -1,1.2c0,-0.7 -0.4,-1 -1.7,-1.9l-1.2,1.6a2,2 0,0 0,-0.2 1.6q0.4,0.8 1,1.5c0,-0.1 0.4,0.5 -0.5,1.8l-1.3,1.5 -1.3,0.5c0.3,0.9 0.4,1.3 1.8,1.5 -3,2.2 -3.4,5.7 -1,8.7l0.9,-1.5a4,4 0,0 0,1 2.6c0.2,0.3 0.4,2 0.8,2.4l1,1s0.3,-1.3 0.9,-1.5l2,-0.6c0.9,-0.3 2.1,-1.9 2.1,-1.9l-0.2,2c4,-1.4 5,-4.2 4,-7.9"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M299.8,77.4a8,8 0,0 1,-0.8 4l2.5,1 2.7,0.4c-0.3,-1.1 0.8,-3.2 1.3,-3.8h-1.9l-0.8,-0.8 -1.6,0.3z"
      android:fillColor="#504416"/>
  <path
      android:pathData="M300.2,78a3,3 0,0 0,0 2l0.8,-1.6zM304,79.2 L303.8,81a3,3 0,0 0,1 -1.7zM300.2,80.5a3,3 0,0 0,-1 1.7h0.9zM303.5,81.4 L302.8,83 303.4,83.4a3,3 0,0 0,0.1 -2m-3,-8.6q-0.7,0 -1,0.6 0,0.8 1,1.2a1.4,1.4 0,0 0,1.7 -0.7q0.1,-0.7 -1.2,-1zM305.9,74.4q-0.6,-0.1 -0.8,0.4a1.4,1.4 0,0 0,1.1 1.4q1.3,0.3 1.6,-0.5 0,-0.9 -1,-1.2z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m300.5,72.6 l-0.5,0.1a1,1 0,0 0,-0.6 0.6q0,0.4 0.2,0.9 0.4,0.4 0.9,0.6 1.4,0.3 1.8,-0.8a1,1 0,0 0,-0.2 -0.8l-1,-0.4zM300.5,73h0.5l0.8,0.4q0.4,0.2 0.3,0.5a1.2,1.2 0,0 1,-1.5 0.7l-0.8,-0.6v-0.6l0.3,-0.4zM305.9,74.2h-0.2a1,1 0,0 0,-0.7 0.5,1.4 1.4,0 0,0 1.2,1.6q0.6,0.3 1,0 0.5,0 0.6,-0.6a1,1 0,0 0,-0.1 -0.8l-1,-0.5zM305.9,74.5 L306.7,74.6q0.5,0.1 0.8,0.4 0.2,0.3 0.1,0.7l-0.5,0.5h-0.9a1.2,1.2 0,0 1,-1 -1.4q0,-0.2 0.5,-0.3z"
      android:fillColor="#d1b948"/>
  <path
      android:pathData="M300.6,71.7a1,1 0,0 0,-1 0.2q-0.2,0.5 -0.2,0.7h0.4l0.7,0.1 1.3,0.6h1.5l-1.2,-0.7c-0.5,-0.2 -1,-0.8 -1.6,-1zM307.6,73.7q-0.9,-0.1 -1.6,0l-1.4,-0.1 1.2,0.8 1.5,0.2 0.6,0.2 0.4,0.3 0.3,-0.6a1,1 0,0 0,-1 -0.8m-8.6,7.7c-1.3,3.5 4,5.2 4.8,1.4a11,11 0,0 1,-4.8 -1.4"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="m299,81.1 l-0.2,0.3a3,3 0,0 0,0 2.4,2.8 2.8,0 0,0 5.4,-0.8v-0.4h-0.3a11,11 0,0 1,-4.8 -1.3zM299.1,81.5A14,14 0,0 0,304 83a3,3 0,0 1,-1.1 1.7,4 4,0 0,1 -2.2,0.2 4,4 0,0 1,-1.6 -1.4,3 3,0 0,1 0,-1.9z"
      android:fillColor="#806600"/>
  <path
      android:pathData="M301.3,78.4a15,15 0,0 0,-0.8 7.2c1.6,-1.8 2.4,-4.2 3,-6.4q-0.2,-0.3 -0.5,-0.4l-0.4,-0.6 -0.7,0.4 -0.6,-0.1z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="m305.4,68.2 l-1.5,0.3 -1.6,1.6v-1.3q-0.3,0 -0.7,0.2l-1.2,0.7 -1,1.1c0.4,0 1.6,-1 2,-0.9s0.2,1 0.5,1.1c0.5,0 1,-0.8 1.6,-1.2a3,3 0,0 1,1.6 -0.8,2 2,0 0,1 1,1.6q0.4,1.3 0.9,1.9c0.3,0.1 0.6,-0.8 1,-0.8 0.5,0.2 0.7,1.7 1.1,2l-0.2,-1.6 -0.7,-1.2 -0.5,-0.6q-0.1,0.6 -0.6,1.1l-0.5,-2.1zM298.9,73.9c-0.6,1 0,2 1.2,2.1l-0.4,1.1 0.6,1.3 1.2,0.4 1,-0.4 0.9,1 1,0.2 1.3,-0.8 0.5,-0.9h0.8l0.8,-0.4 0.2,-0.9a2,2 0,0 1,-2.6 0.4,2 2,0 0,1 -0.5,1.4 1,1 0,0 1,-1 0.2,2 2,0 0,1 -1.2,-1q-0.7,0.5 -1.2,0.5a1.4,1.4 0,0 1,-1 -1,2 2,0 0,1 0.5,-1.4c-2.4,-0.7 -1.6,-1.4 -2.2,-1.8zM297.6,74.2 L295.6,77 294.3,77.5c0.3,0.9 0.4,1.3 1.8,1.5 -3,2.2 -3.4,5.7 -1,8.7l0.9,-1.5a4,4 0,0 0,1 2.6c0.2,0.3 0.4,2 0.8,2.4l1,1s0.3,-1.3 0.9,-1.5l2,-0.6c0.9,-0.3 2.1,-1.9 2.1,-1.9l-0.1,2c4,-1.4 5,-4.2 3.9,-7.9 1,0.7 2,-0.1 2.2,-0.2a3,3 0,0 1,-0.8 -1.2l-0.2,-2 -0.2,-1.1 -0.5,1.7v1.8a5,5 0,0 1,-1.9 -1l0.8,4q0,1.3 -0.4,2.7a7,7 0,0 1,-1.7 1.7l0.3,-2.4c0,-0.8 -0.4,-3 -0.4,-3l-0.8,3 -1.3,2c0.3,-2.1 0.2,-2.2 -0.1,-3 0,0 -0.5,2.6 -1.5,3.7l-1.6,0.8 -0.8,0.6 -0.6,-1.8 -1.2,-2.4c-0.3,-1.2 1.3,-3.6 1.3,-3.6q-0.9,0.2 -1.9,1.4l-1.3,1.8 -0.5,-2.4a5,5 0,0 1,0.7 -2.2,22 22,0 0,1 3.4,-3.3 5,5 0,0 1,-3 0s1.3,-1.3 1.4,-1.6a4,4 0,0 0,0.6 -2zM302.8,75.4h-1.3v0.6l0.6,0.2 0.8,1 1.7,-0.5 0.3,-0.4 -1.6,-0.9z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M307.6,82.3c1,0.7 2,-0.1 2.2,-0.2L309,81l-0.2,-2c0,-1.6 0.6,-1.9 0.6,-1.8l1.6,-0.7a2,2 0,0 0,0.7 -1.5l-0.2,-1.9c-1.6,0 -2,0.1 -2.4,0.6l-0.2,-1.6 -0.6,-1.2 -0.5,-0.6q-0.1,0.7 -0.6,1.1a4,4 0,0 0,-1.8 -3.2q-1.9,0.1 -3.2,1.8l0.2,-1.2 -0.8,0.2 -1.1,0.7 -1,1.2c0,-0.7 -0.4,-1 -1.7,-1.9l-1.2,1.6a2,2 0,0 0,-0.2 1.6q0.4,0.8 1,1.5c0,-0.1 0.4,0.5 -0.5,1.8l-1.3,1.5 -1.3,0.5c0.3,0.9 0.4,1.3 1.8,1.5 -3,2.2 -3.4,5.7 -1,8.7l0.9,-1.5a4,4 0,0 0,1 2.6c0.2,0.3 0.4,2 0.8,2.4l1,1s0.3,-1.3 0.9,-1.5l2,-0.6c0.9,-0.3 2.1,-1.9 2.1,-1.9l-0.2,2c4,-1.4 5,-4.2 4,-7.9z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M297.8,70.2a3,3 0,0 0,-0.6 1.6l1,1.7 0.4,-2.4c0,-0.4 -0.8,-1 -0.8,-1ZM299.9,72.3 L299.6,72.4q-0.3,0.2 -0.6,0.6l0.8,-0.4h0.3q0.6,0.5 1.2,0.7h1.9l-0.2,0.8s-0.7,0.2 -1,0.5l-0.3,0.4 -0.3,0.3v0.8h0.2q0.7,0.2 0.8,1l0.2,0.2h0.7q0.5,-0.5 1.3,-0.3h0.1l0.1,-0.1q0.3,-0.2 0.2,-0.6L305,76l-0.1,-0.6 -0.4,-1 0.1,-0.9a5,5 0,0 0,1.6 1.2l1.4,0.1 0.3,0.2 0.5,0.6q0,-0.5 -0.2,-0.8 -0.2,-0.2 -0.5,-0.2 -0.7,-0.1 -1.3,-0.2a4,4 0,0 1,-1.5 -1l-0.3,-0.2 -0.1,0.2 -0.3,1.2 0.4,1.2 0.1,0.7v0.1a2,2 0,0 0,-1.7 0.5h-0.1a3,3 0,0 0,-1.2 -1.3v-0.3l0.1,-0.2 0.4,-0.3q0.5,-0.4 1,-0.8 0.3,-0.5 0.3,-1v-0.4h-0.3a4,4 0,0 1,-1.8 0.2l-1.2,-0.6h-0.3zM298.9,73.7a2,2 0,0 0,-0.3 1.7q0.5,0.6 1.2,0.8l-0.1,0.2 -0.2,0.8q0.2,0.8 0.8,1.4a2,2 0,0 0,1.3 0.4l0.8,-0.4 0.6,0.8a2,2 0,0 0,1.3 0.3,2 2,0 0,0 1.8,-1.5v-0.1a2,2 0,0 0,1.4 -0.2q0.6,-0.7 0.7,-1.6c-0.4,0.3 0,0.8 -1,1.4a1,1 0,0 1,-1.1 0l-0.4,-0.3v1l-0.3,0.5q-0.5,0.5 -1.2,0.5l-0.8,-0.2 -0.6,-0.8v-0.5l-0.3,0.3 -0.5,0.3 -0.5,0.1 -0.9,-0.3a2,2 0,0 1,-0.8 -1.2l0.2,-0.5 0.2,-0.4 0.2,-0.4h-0.4a1,1 0,0 1,-1 -0.6c-0.6,-0.6 0,-1 -0.1,-1.5m12,0.2s-1,0 -1.3,0.4l-0.8,2.3 1.6,-1a3,3 0,0 0,0.5 -1.7"
      android:fillColor="#806600"/>
  <path
      android:pathData="M301,73.1a0.6,0.6 0,0 0,-0.2 1.1,0.6 0.6,0 1,0 0.2,-1zM306.4,74.7a0.6,0.6 0,0 0,-0.2 1,0.6 0.6,0 1,0 0.2,-1"
      android:fillColor="#2b2200"/>
  <path
      android:pathData="M280.2,91.5c-0.5,0.3 -1.2,-0.2 -2.7,1a2,2 0,0 1,2.4 1q0.5,0 0.8,-0.6a1,1 0,0 0,-0.5 -1.4zM314.1,93.1c-0.5,0.3 -1.2,-0.2 -2.7,1a2,2 0,0 1,2.4 1,1 1,0 0,0 0.8,-0.5 1,1 0,0 0,-0.5 -1.5zM277.7,95.1c-0.4,0.3 -1.2,0.2 -2.1,1.9a2,2 0,0 1,2.6 0q0.5,-0.3 0.5,-1a1,1 0,0 0,-1 -1zM314.7,97.7c-0.4,0.4 -1.2,0.3 -1.9,2a2,2 0,0 1,2.5 -0.2q0.6,-0.2 0.5,-0.8a1,1 0,0 0,-1.2 -1zM280.1,99l-0.6,0.2c-0.2,0.5 -0.9,0.8 -0.8,2.7a2,2 0,0 1,2.2 -1.3q0.4,-0.5 0,-1a1,1 0,0 0,-0.8 -0.6zM353.9,99.3h-0.5c-0.1,0.5 -1,0.8 -1.1,2.7a2,2 0,0 1,2.4 -1q0.2,-0.6 0.2,-1.1a1,1 0,0 0,-1 -0.6zM318.7,100.5 L318.1,100.8c0,0.5 -0.8,0.8 -0.5,2.7a2,2 0,0 1,2 -1.4q0.3,-0.7 0,-1a1,1 0,0 0,-0.8 -0.6zM362.5,100.7a1,1 0,0 0,-1 0.6c0.2,0.5 -0.3,1.2 0.8,2.7a2,2 0,0 1,1.1 -2.3,1 1,0 0,0 -0.5,-1zM357.7,101.3q-0.3,0 -0.7,0.4c0,0.4 -0.6,1 0.1,2.8a2.4,2.4 0,0 1,1.7 -2,1 1,0 0,0 -0.3,-1z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="M295.2,127a6,6 0,0 1,-2.2 -0.3,6 6,0 0,1 -1.5,-1.8H289l-2,-1a4,4 0,0 0,2.1 -1l-2.4,-0.3 -2,-1.3c0.7,-0.2 1.5,-0.2 2,-0.8l-2.1,-0.3 -1.8,-1.2a3,3 0,0 0,2.2 -1l-2.3,-0.2 -2.2,-1.5a4,4 0,0 0,2.4 -0.6l-0.5,-1c-0.6,-1.2 -1.8,-0.8 -2.6,-1.1a2,2 0,0 1,-1.3 -1.6c-0.1,-0.9 0.3,-2.2 2.3,-2 -1,-1.4 0,-2.5 1,-3q1.6,-0.8 2.6,1.4l1.4,-0.8a1.4,1.4 0,0 1,1.6 0.6q0.3,0.6 0,1.2 -0.5,0.6 -1,1.6c-0.2,0.8 0.6,2.4 0.6,2.4s3.8,3 5.5,4.6l2.6,2.7h3.8l0.3,6.3z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M284.8,106.3a2,2 0,0 1,0.6 2.3q0.4,0.5 1,0.4a1,1 0,0 0,0.6 -1.2c-0.4,-0.4 -0.4,-1 -2.2,-1.5zM280.6,106.8h-1.2a2,2 0,0 1,1.6 2,1 1,0 0,0 1,0 1,1 0,0 0,0 -1.5c-0.2,0 -0.6,-0.5 -1.4,-0.5zM279.3,110.8c-0.5,0 -1.1,-0.3 -2.6,0.8a2,2 0,0 1,2.2 1q0.7,0 0.9,-0.5a1,1 0,0 0,-0.5 -1.3z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="M316.6,130c-0.6,-0.2 -1.4,-1.1 -2.1,-1.1a2,2 0,0 0,-1.6 1,2 2,0 0,0 0.4,1.4l1.2,0.9 -0.8,1.4a1,1 0,0 0,0.5 1.1c0.4,0.5 0.4,0.7 1.2,0.8l1.2,-0.1q0,0.6 0.3,1a2,2 0,0 0,1.9 1q0.8,0 1.2,-0.5c0.2,-0.4 0.2,-1.4 0.6,-1.8l1.8,-1.6c0.8,0.5 0.7,1.1 0.6,1.9l1.5,-1.2 0.5,-1.9a2,2 0,0 1,0.8 2l1.6,-1.2 0.7,-1.7a2,2 0,0 1,0.4 2,6 6,0 0,0 1.6,-1.6l0.5,-1.4q2.4,-0.1 2.6,-1.3c0,-0.5 -0.6,-1.3 -1.8,-1.5l-0.7,-1.2v-1.1a5,5 0,0 1,1.5 -1.8l2.7,-1.6 -0.6,-2.6 -6.2,-1 -7,1.7c1.3,0.6 4.1,2.6 4,3.4 -0.2,0.2 -0.9,0.5 -1.1,0.8q-0.2,0.7 0.2,1c0.3,0.5 1,0.6 1.2,0.9a1,1 0,0 1,0 1c-0.4,0.7 -2,0.9 -2.8,1.2l-3.8,2a5,5 0,0 1,-2.2 -0.4z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M331.4,121.4s-0.1,2.3 -1,2.8a2,2 0,0 1,-1.5 0.3,2 2,0 0,1 -0.8,-1.1l-0.3,-1 -2.3,-0.7a13,13 0,0 0,-3.3 0.6,8 8,0 0,1 2.4,-3l3.8,-0.3z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M341.4,106.5c1,-0.1 3.2,-0.6 4,-1.2l1.2,-1.3q0,3 -3.6,4.4a13,13 0,0 1,-5.9 0.7l-3.5,-0.8c1.3,1.5 0.6,2.4 -1,3 0,0 0.3,-1.3 -0.2,-1.5 -1,-0.4 -2.2,0.2 -3.4,0.8a7,7 0,0 1,-2.8 0.6,9 9,0 0,1 -3.4,-0.5c-0.8,-0.2 -2.2,-1.3 -2.7,-1.2 -1.3,0.3 -2.8,0.4 -3.5,1.4a1,1 0,0 0,0 1.2,4 4,0 0,0 2.5,1.3q4,0.9 8,0.3c2.8,-0.4 6.2,-1.2 9,-1.7l8.8,-2a33,33 0,0 1,8.4 -0.7c5,0 6.6,2.5 6.5,5.1 -0.2,2.8 -3.2,4 -5.8,4.8 -4.6,1.4 -10.8,0.6 -14.7,0.5l1.9,1.4c0.7,0.6 1,1.7 2,2 1.1,0.3 3,-0.4 4.2,-0.5l4.8,0.1q0.4,-0.5 1.2,-0.8h1q1,0.3 0.8,1.9l0.9,0.5c0.6,0.2 1.3,0 1.7,0.2l1.5,0.6 -1.5,0.6 -0.6,0.6 2,0.3q0.5,0.3 1,1H359l-0.8,0.7s1.6,-0.1 2.2,0.3l1,1 -1.2,0.2 -0.8,0.3 1.6,1.1 0.5,1.6 -1,-0.6h-0.8l0.2,2.1c0,0.5 0.8,1.1 1,1.5q0.3,0.8 -0.2,1.2a2,2 0,0 1,-2 0.6l-1,-0.6 -0.8,1q-0.5,0.5 -1.3,0.3a2,2 0,0 1,-1.3 -0.4l-0.6,-1.5 -1.5,0.3a2,2 0,0 1,-1.4 -0.7,2 2,0 0,1 0.2,-1.7c0.4,-0.5 1.6,-0.5 2.2,-0.8l1.4,-1.2 -3.5,-2.9 -1.6,-2 -4.5,1a25,25 0,0 1,-6 0c-4.6,0 -7.6,-6.3 -11.7,-7.8a5,5 0,0 0,-3.2 0c-4.8,1 -9.9,6.4 -14.6,7.6a44,44 0,0 1,-6 0.7,5 5,0 0,1 -1.3,1.6q-1.2,0.5 -2.4,0.5l-1,2.1a7,7 0,0 1,-2 1.3q0.5,-1.2 0,-2.2l-1.3,1.9 -2,1.1c0,-0.6 0.3,-1.3 0,-2l-1.3,1.7q-0.8,0.7 -1.9,1a2,2 0,0 0,0 -2.2l-1.1,1.7 -2.4,1.3q0.5,-1.2 0.3,-2.2H286c-1.3,0 -1.4,1.1 -2.1,1.7a3,3 0,0 1,-2.2 0.4c-0.9,-0.4 -1.7,-1.4 -0.6,-3 -1.9,0.3 -2.8,-0.9 -2.8,-2 0,-0.9 0.3,-1.8 2.2,-1.7l-0.2,-1.3a1.4,1.4 0,0 1,1.5 -1q0.6,-0.1 1.1,0.4c0.4,0.4 0.5,1 1.2,1.6a6,6 0,0 0,2.6 0.4,48 48,0 0,1 5.8,-2.6c1.3,-0.4 4.8,-1 4.8,-1v-8.7h3.1c2.6,0 9.3,1 9.3,1l4.8,-0.3s8,-1.2 12,-1.6a39,39 0,0 1,5.7 -0.2c3,0.1 5.4,0.8 8.1,1q3.2,0.3 6.2,0.2c3.3,-0.1 9.2,0 11,-3a2,2 0,0 0,-0.4 -2.3,3 3,0 0,0 -3,-0.8c-0.6,0.1 -1.5,0.6 -1.5,1.2q-0.1,0.6 0.2,1.1l1,0.8a4,4 0,0 1,-2 -0.3,6 6,0 0,1 -1.3,-2.1 2,2 0,0 0,-0.5 1.4,2 2,0 0,0 0.7,1.6 4,4 0,0 1,-2.3 -0.8,7 7,0 0,1 -0.9,-2.1 2,2 0,0 0,-0.8 1.5q0.1,0.8 0.6,1.6a5,5 0,0 1,-2 -1.1,6 6,0 0,1 -0.9,-2.1l-8.5,1.6a101,101 0,0 1,-9 1.6q-6.7,0.9 -10.8,-0.8 -2.1,-0.7 -2.6,-2.4a3,3 0,0 1,0.8 -2.7,9 9,0 0,1 4.8,-2.4 73,73 0,0 1,8.4 -1.6,32 32,0 0,1 9,0.5c1.4,0.1 2.8,-0.2 3.2,-0.6s1,-0.8 0.2,-1.8q1.1,0 1.6,1.2c0.3,0.4 0,1.6 0,1.6z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M341.4,106.5c1,-0.1 3.2,-0.6 4,-1.2l1.2,-1.3q0,3 -3.6,4.4a13,13 0,0 1,-5.9 0.7l-3.5,-0.8c1.3,1.5 0.6,2.4 -1,3 0,0 0.3,-1.3 -0.2,-1.5 -1,-0.4 -2.2,0.2 -3.4,0.8a7,7 0,0 1,-2.8 0.6,9 9,0 0,1 -3.4,-0.5c-0.8,-0.2 -2.2,-1.3 -2.7,-1.2 -1.3,0.3 -2.8,0.4 -3.5,1.4a1,1 0,0 0,0 1.2,4 4,0 0,0 2.5,1.3q4,0.9 8,0.3c2.8,-0.4 6.2,-1.2 9,-1.7l8.8,-2a33,33 0,0 1,8.4 -0.7c5,0 6.6,2.5 6.5,5.1 -0.2,2.8 -3.2,4 -5.8,4.8 -4.6,1.4 -10.8,0.6 -14.7,0.5l1.9,1.4c0.7,0.6 1,1.7 2,2 1.1,0.3 3,-0.4 4.2,-0.5l4.8,0.1q0.4,-0.5 1.2,-0.8h1q1,0.3 0.8,1.9l0.9,0.5c0.6,0.2 1.3,0 1.7,0.2l1.5,0.6 -1.5,0.6 -0.6,0.6 2,0.3q0.5,0.3 1,1H359l-0.8,0.7s1.6,-0.1 2.2,0.3l1,1 -1.2,0.2 -0.8,0.3 1.6,1.1 0.5,1.6 -1,-0.6h-0.8l0.2,2.1c0,0.5 0.8,1.1 1,1.5q0.3,0.8 -0.2,1.2a2,2 0,0 1,-2 0.6l-1,-0.6 -0.8,1a2,2 0,0 1,-1.3 0.5,2 2,0 0,1 -1.3,-0.6l-0.6,-1.5 -1.5,0.3a2,2 0,0 1,-1.4 -0.7,2 2,0 0,1 0.2,-1.7c0.4,-0.5 1.6,-0.5 2.2,-0.8l1.4,-1.2 -3.5,-2.9 -1.6,-2 -4.5,1a25,25 0,0 1,-6 0c-4.6,0 -7.6,-6.3 -11.7,-7.8a5,5 0,0 0,-3.2 0c-4.8,1 -9.9,6.4 -14.6,7.6a44,44 0,0 1,-6 0.7,5 5,0 0,1 -1.3,1.6 7,7 0,0 1,-2.4 0.5l-1,2.1a7,7 0,0 1,-2 1.3q0.5,-1.2 0,-2.2l-1.3,1.9 -2,1.1c0,-0.6 0.3,-1.3 0,-2l-1.3,1.7q-0.8,0.7 -1.9,1a2,2 0,0 0,0 -2.2l-1.1,1.7 -2.4,1.3q0.5,-1.2 0.3,-2.2H286c-1.3,0 -1.4,1.1 -2.1,1.7a3,3 0,0 1,-2.2 0.4c-1,-0.4 -1.7,-1.4 -0.6,-3 -1.9,0.3 -2.8,-0.9 -2.8,-2 0,-0.9 0.3,-1.8 2.2,-1.7l-0.2,-1.3a1.4,1.4 0,0 1,1.5 -1q0.6,-0.1 1.1,0.4c0.4,0.4 0.5,1 1.2,1.6a6,6 0,0 0,2.6 0.4,48 48,0 0,1 5.8,-2.6c1.3,-0.4 4.8,-1 4.8,-1v-8.7h3.1c2.6,0 9.3,1 9.3,1l4.8,-0.3s8,-1.2 12,-1.6a39,39 0,0 1,5.7 -0.2c3,0.1 5.4,0.8 8.1,1q3.2,0.3 6.2,0.2c3.3,-0.1 9.2,0 11,-3a2,2 0,0 0,-0.4 -2.3,3 3,0 0,0 -3,-0.8c-0.6,0.1 -1.5,0.6 -1.5,1.2a1,1 0,0 0,0.2 1l1,0.9a5,5 0,0 1,-2.2 -0.4,4 4,0 0,1 -1.1,-2 2,2 0,0 0,-0.5 1.4,2 2,0 0,0 0.7,1.6 4,4 0,0 1,-2.3 -0.8,7 7,0 0,1 -0.9,-2.1 2,2 0,0 0,-0.8 1.5q0.1,0.8 0.6,1.6a5,5 0,0 1,-2 -1.1,6 6,0 0,1 -0.9,-2.1l-8.5,1.6a101,101 0,0 1,-9 1.6q-6.7,0.9 -10.8,-0.8 -2.1,-0.7 -2.6,-2.4a3,3 0,0 1,0.8 -2.7,9 9,0 0,1 4.8,-2.4 73,73 0,0 1,8.4 -1.6,32 32,0 0,1 9,0.5c1.4,0.1 2.8,-0.1 3.2,-0.6s1,-0.8 0.2,-1.8q1.1,0 1.6,1.2c0.3,0.4 0,1.6 0,1.6z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M307,118.8c1,0.6 2,-0.2 2.3,-0.2a3,3 0,0 1,-0.8 -1.2l-0.3,-1.8c0,-1.6 0.5,-1.8 0.6,-1.6l1.4,-0.8q0.6,-0.5 0.8,-1.3l-0.2,-1.9c-1.5,0 -1.8,0.2 -2.2,0.6l-0.3,-1.5 -0.5,-1.2 -0.6,-0.5q0,0.7 -0.6,1l-0.4,-2a1.6,1.6 0,0 0,-2.6 -0.8l-1.6,1.5 0.2,-1.2 -0.8,0.2 -1,0.7 -1.1,1.1q0.2,-0.8 -1.6,-1.7l-1.1,1.6a2,2 0,0 0,-0.1 1.4l0.8,1.4c0.1,0 0.5,0.4 -0.4,1.7l-1.2,1.4q-0.6,0.5 -1.3,0.5c0.4,0.8 0.5,1.2 1.8,1.4 -3,2 -3.2,5.4 -1,8.3q0.3,-0.8 0.9,-1.4a4,4 0,0 0,0.8 2.5c0.3,0.3 0.5,1.8 0.8,2.3l1,1s0.3,-1.3 0.9,-1.5l2,-0.6a7,7 0,0 0,2 -1.7l-0.3,1.8q5.5,-2.1 3.7,-7.5"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M299.6,114a8,8 0,0 1,-0.8 4l2.6,1 2.4,0.2c-0.2,-1 0.8,-3 1.2,-3.6h-1.7l-0.8,-0.8 -1.5,0.3 -1.4,-1z"
      android:fillColor="#504416"/>
  <path
      android:pathData="M300,114.7a3,3 0,0 0,0 1.9l0.7,-1.6zM303.6,115.8 L303.4,117.5a3,3 0,0 0,1 -1.6h-0.8zM300,117a3,3 0,0 0,-0.9 1.6l0.7,0.1zM303.2,118 L302.4,119.5 303.1,119.8a3,3 0,0 0,0 -1.9zM300.3,109.8q-0.7,0 -0.9,0.4 -0.1,0.8 1,1.3a1.3,1.3 0,0 0,1.6 -0.6q0,-0.8 -1.1,-1zM305.5,111.2q-0.5,0 -0.8,0.4a1.3,1.3 0,0 0,1 1.4q1.3,0.4 1.5,-0.5 0.3,-0.8 -1,-1.1l-0.8,-0.2z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M300.3,109.6h-0.4a1,1 0,0 0,-0.5 0.6q-0.1,0.5 0.1,0.8 0.3,0.5 0.8,0.6a1.4,1.4 0,0 0,1.8 -0.8,1 1,0 0,0 -0.3,-0.6l-1,-0.5zM300.3,109.8 L300.8,109.9 301.6,110.3a0.4,0.4 0,0 1,0.2 0.5,1 1,0 0,1 -1.4,0.6l-0.7,-0.4a1,1 0,0 1,-0.2 -0.7l0.4,-0.4zM305.5,111.1h-0.2a1,1 0,0 0,-0.7 0.5,1.4 1.4,0 0,0 1.2,1.6h1q0.4,-0.3 0.6,-0.7a1,1 0,0 0,-0.3 -0.8l-0.8,-0.4zM305.5,111.4 L306.2,111.5q0.4,0 0.8,0.4l0.1,0.6 -0.5,0.4h-0.8a1,1 0,0 1,-1 -1.2q0,-0.4 0.5,-0.3z"
      android:fillColor="#d1b948"/>
  <path
      android:pathData="M300.4,108.6a1,1 0,0 0,-1 0.3c-0.2,0.1 -0.2,0.6 -0.2,0.6h0.6l0.5,0.2 1.3,0.5h1.4l-1.2,-0.6c-0.4,-0.2 -0.8,-0.9 -1.4,-1m6.6,2c-0.4,-0.2 -1,0.1 -1.5,0l-1.3,-0.1 1.2,0.8 1.4,0.2 0.6,0.2 0.4,0.2 0.2,-0.5a1,1 0,0 0,-1 -0.8m-8,7.4c-1.4,3.2 3.8,4.8 4.6,1.3a11,11 0,0 1,-4.6 -1.3"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M298.8,117.6v0.3a3,3 0,0 0,0 2.3,2.6 2.6,0 0,0 5,-0.8v-0.3h-0.2a10,10 0,0 1,-4.6 -1.3l-0.2,-0.1zM299,118.1a13,13 0,0 0,4.5 1.2,3 3,0 0,1 -1,1.7 3,3 0,0 1,-2 0.2,3 3,0 0,1 -1.6,-1.4 3,3 0,0 1,0 -1.7z"
      android:fillColor="#806600"/>
  <path
      android:pathData="M301.1,115.2a14,14 0,0 0,-0.8 6.7c1.6,-1.6 2.4,-4 2.9,-6.1l-0.5,-0.4 -0.3,-0.5 -0.7,0.3z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="m305,105.4 l-1.4,0.2 -1.6,1.5v-1.2q-0.3,0 -0.6,0.2l-1.2,0.7 -1,1c0.6,0 1.7,-1 2,-0.8 0.4,0 0,1 0.5,1s0.9,-0.6 1.5,-1.1q0.9,-0.8 1.6,-0.7a2,2 0,0 1,0.9 1.4q0.3,1.3 0.8,1.8c0.3,0.2 0.6,-0.8 1,-0.7s0.6,1.6 1,1.9l-0.2,-1.5 -0.6,-1.2 -0.5,-0.5q-0.1,0.6 -0.6,1l-0.5,-2zM298.7,110.9c-0.5,0.8 0.2,1.7 1.3,1.9l-0.5,1 0.7,1.2 1,0.4 1.1,-0.4 0.7,1 1,0.2 1.3,-0.8 0.5,-0.8h0.8l0.6,-0.4 0.2,-0.8q-1,1 -2.4,0.4a2,2 0,0 1,-0.4 1.3,1 1,0 0,1 -1,0.2 2,2 0,0 1,-1.1 -0.9q-0.7,0.4 -1.1,0.4a1.4,1.4 0,0 1,-1 -1,2 2,0 0,1 0.4,-1.3c-2.4,-0.7 -1.6,-1.3 -2,-1.6zM297.5,111.1 L295.7,113.7q-0.6,0.5 -1.3,0.5c0.4,0.8 0.5,1.2 1.8,1.4 -3,2 -3.2,5.4 -1,8.3q0.3,-0.8 0.9,-1.4a4,4 0,0 0,0.8 2.5c0.3,0.3 0.5,1.8 0.8,2.3l1,1s0.3,-1.3 0.9,-1.5l2,-0.6a7,7 0,0 0,2 -1.7l-0.3,1.8c3.9,-1.3 4.8,-4 3.7,-7.5 1,0.6 2,-0.2 2.3,-0.2a3,3 0,0 1,-0.8 -1.2l-0.3,-1.8v-1.1l-0.6,1.6v1.7l-1.8,-0.8q0.5,1.8 0.8,3.7 0,1.3 -0.5,2.5a7,7 0,0 1,-1.6 1.7l0.3,-2.3 -0.3,-2.8 -0.8,2.7 -1.2,2c0.1,-2 0,-2.2 -0.2,-2.9 0,0 -0.5,2.5 -1.3,3.6l-1.6,0.7 -0.7,0.6 -0.6,-1.7 -1.2,-2.2c-0.3,-1.2 1.2,-3.6 1.2,-3.6q-0.9,0.2 -1.7,1.4l-1.3,1.6 -0.5,-2.2q0,-1.2 0.7,-2.1c0.6,-1 3.3,-3 3.3,-3a4,4 0,0 1,-2.8 0l1.2,-1.7a4,4 0,0 0,0.5 -1.9m5,1.1h-1.2v0.5l0.5,0.3 0.8,1 1.6,-0.5 0.3,-0.4 -1.5,-0.8z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M307,118.8c1,0.6 2,-0.2 2.3,-0.2a3,3 0,0 1,-0.8 -1.2l-0.3,-1.8c0,-1.6 0.5,-1.8 0.6,-1.6l1.4,-0.8q0.6,-0.5 0.8,-1.3l-0.2,-1.9c-1.5,0 -1.8,0.2 -2.2,0.6l-0.3,-1.5 -0.5,-1.2 -0.6,-0.5q0,0.7 -0.6,1a4,4 0,0 0,-1.6 -3q-1.8,0.1 -3,1.7v-1.2l-0.6,0.2 -1,0.7 -1.1,1.1q0.2,-0.8 -1.6,-1.7l-1.1,1.6a2,2 0,0 0,-0.1 1.4l0.8,1.4c0.1,0 0.5,0.4 -0.4,1.7l-1.2,1.4q-0.6,0.5 -1.3,0.5c0.4,0.8 0.5,1.2 1.8,1.4 -3,2 -3.2,5.4 -1,8.3q0.3,-0.8 0.9,-1.4a4,4 0,0 0,0.8 2.5c0.3,0.3 0.5,1.8 0.8,2.3l1,1s0.3,-1.3 0.9,-1.5l2,-0.6a7,7 0,0 0,2 -1.7l-0.3,1.8q5.5,-2.1 3.7,-7.5z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M297.7,107.2a3,3 0,0 0,-0.5 1.6l0.9,1.6 0.5,-2.2c-0.1,-0.4 -0.8,-1 -0.8,-1zM299.7,109.2 L299.4,109.4q-0.4,0 -0.4,0.4l0.7,-0.2h0.2l1.2,0.6h1.9l-0.3,0.8 -0.9,0.4 -0.4,0.4 -0.2,0.4v0.6h0.2a1,1 0,0 1,0.8 0.9l0.2,0.2 0.2,0.1h0.4a1,1 0,0 1,1.2 -0.3h0.1l0.1,-0.1 0.3,-0.6v-0.4l-0.2,-0.4c0,-0.4 -0.3,-1 -0.3,-1l0.1,-0.8 1.5,1.1 1.3,0.1q0.3,0 0.3,0.2l0.4,0.5a1,1 0,0 0,-0.1 -0.8l-0.5,-0.2 -1.3,-0.1 -1.4,-1 -0.2,-0.2 -0.1,0.2a2,2 0,0 0,-0.4 1.2l0.4,1 0.2,0.7 -0.2,0.3a2,2 0,0 0,-1.5 0.4h-0.1a3,3 0,0 0,-1.2 -1.3v-0.3l0.2,-0.1q0,-0.3 0.3,-0.3 0.6,-0.4 0.9,-0.8t0.4,-1v-0.3h-0.3l-1.6,0.1q-0.6,-0.2 -1.2,-0.5h-0.3zM298.8,110.6a2,2 0,0 0,-0.2 1.6q0.4,0.6 1,0.8v0.1q-0.2,0.5 -0.2,0.8 0.1,0.9 0.8,1.3 0.5,0.5 1.2,0.4l0.8,-0.2 0.5,0.6q0.6,0.4 1.3,0.3a2,2 0,0 0,1.6 -1.4v-0.2a2,2 0,0 0,1.4 -0.1,2 2,0 0,0 0.6,-1.5c-0.3,0.3 0,0.8 -0.8,1.3q-0.6,0.3 -1.1,0l-0.4,-0.3v0.9l-0.3,0.5a2,2 0,0 1,-1 0.6,1 1,0 0,1 -1,-0.3l-0.4,-0.7 -0.2,-0.5 -0.2,0.3 -0.4,0.2 -0.5,0.2q-0.6,0 -0.8,-0.3a2,2 0,0 1,-0.7 -1.2v-0.5q0,-0.3 0.3,-0.4l0.1,-0.3h-0.4a1,1 0,0 1,-1 -0.5c-0.5,-0.7 0,-1 0,-1.5m11.4,0.2s-1,0 -1.2,0.4l-0.8,2 1.6,-0.7q0.4,-0.9 0.4,-1.6z"
      android:fillColor="#806600"/>
  <path
      android:pathData="M300.8,110a0.5,0.5 0,0 0,-0.2 1.1,0.5 0.5,0 1,0 0.1,-1zM305.9,111.6a0.5,0.5 0,0 0,-0.1 1,0.5 0.5,0 1,0 0.1,-1"
      android:fillColor="#2b2200"/>
  <path
      android:pathData="M281,127.6c-0.5,0.2 -1.2,-0.2 -2.5,1a2,2 0,0 1,2.2 0.8,1 1,0 0,0 0.8,-0.5 1,1 0,0 0,-0.5 -1.3zM313.3,129.1c-0.5,0.2 -1.1,-0.2 -2.5,1a2,2 0,0 1,2.2 0.9q0.7,0 0.8,-0.6a1,1 0,0 0,-0.5 -1.3zM278.6,130.9c-0.4,0.3 -1.2,0.2 -2,1.8a2,2 0,0 1,2.4 0q0.6,-0.2 0.6,-0.8a1,1 0,0 0,-1 -1zM313.8,133.4c-0.3,0.4 -1,0.4 -1.8,2a2,2 0,0 1,2.4 -0.2,1 1,0 0,0 0.6,-0.8 1,1 0,0 0,-1.2 -1zM281,134.8 L280.5,135c-0.2,0.4 -0.9,0.8 -0.8,2.5a2,2 0,0 1,2 -1.2,1 1,0 0,0 0.1,-1 1,1 0,0 0,-0.8 -0.5zM351.2,135h-0.4c-0.2,0.6 -1,0.8 -1,2.6a2,2 0,0 1,2.1 -1q0.4,-0.5 0.3,-1a1,1 0,0 0,-1 -0.6zM317.8,136.2 L317.2,136.5c0,0.5 -0.8,0.8 -0.5,2.6a2,2 0,0 1,2 -1.4,1 1,0 0,0 0,-1 1,1 0,0 0,-0.9 -0.5zM359.4,136.4a1,1 0,0 0,-1 0.6c0.2,0.4 -0.2,1 0.9,2.5a2,2 0,0 1,1 -2.1,1 1,0 0,0 -0.5,-1zM355,136.9 L354.2,137.3c0,0.5 -0.6,1 0.1,2.6a2,2 0,0 1,1.6 -1.7q0.1,-0.7 -0.2,-1z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="m303.1,158.7 l-1.7,-0.3 -1.2,-1.4h-2l-1.6,-0.8a3,3 0,0 0,1.6 -0.8l-1.9,-0.3 -1.6,-1c0.6,-0.2 1.2,-0.1 1.6,-0.7l-1.7,-0.2 -1.5,-1a2,2 0,0 0,1.8 -0.7l-1.8,-0.1 -1.7,-1.2a4,4 0,0 0,1.8 -0.5l-0.4,-0.8c-0.4,-1 -1.4,-0.7 -2,-0.9a2,2 0,0 1,-1.1 -1.4c0,-0.7 0.3,-1.7 1.8,-1.6 -0.8,-1 0,-2 0.9,-2.4 0.8,-0.2 1.5,-0.1 2,1.2l1.2,-0.7a1,1 0,0 1,1.4 0.6v0.9q-0.6,0.5 -0.8,1.3a4,4 0,0 0,0.4 1.9s3,2.4 4.4,3.7c0.8,0.7 2,2.2 2,2.2h3l0.3,5z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M294.8,142q0.8,1 0.5,2a1,1 0,0 0,0.8 0.2,1 1,0 0,0 0.5,-1c-0.4,-0.2 -0.4,-0.8 -1.8,-1.1zM291.4,142.5h-0.8a2,2 0,0 1,1.2 1.6,1 1,0 0,0 0.8,-0.1 1,1 0,0 0,0 -1.1c-0.3,0 -0.5,-0.4 -1.2,-0.4zM290.4,145.7a3,3 0,0 0,-2 0.6,2 2,0 0,1 1.7,0.8q0.4,0.1 0.6,-0.4a1,1 0,0 0,-0.3 -1z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="M318,161c-0.6,-0.1 -1.3,-0.8 -1.8,-0.8a2,2 0,0 0,-1.3 0.8,1 1,0 0,0 0.3,1.2l1,0.6 -0.7,1.2q0,0.5 0.4,0.9 0.3,0.5 0.9,0.6l1.1,-0.1q0,0.5 0.2,0.8a2,2 0,0 0,1.6 0.8q0.5,0.2 0.9,-0.4 0.1,-0.6 0.5,-1.4l1.4,-1.3q0.8,0.6 0.5,1.5l1.2,-1q0.4,-0.6 0.4,-1.4 0.8,0.7 0.6,1.6l1.2,-1 0.6,-1.4q0.7,0.7 0.4,1.6l1.3,-1.2 0.3,-1.2q2,0 2.1,-1 0.1,-0.8 -1.4,-1.2l-0.5,-1v-0.8a4,4 0,0 1,1.2 -1.5l2.2,-1.3 -0.5,-2.1 -5,-0.8 -5.7,1.4c1,0.4 3.4,2 3.2,2.7q-0.2,0.3 -0.8,0.8 -0.2,0.2 0.1,0.8 0.6,0.2 1,0.6a1,1 0,0 1,0 0.8c-0.3,0.5 -1.6,0.7 -2.3,1 -1,0.4 -3,1.6 -3,1.6l-1.7,-0.4z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M328,154.2s0,1.9 -0.7,2.3q-0.6,0.4 -1.2,0.1a1,1 0,0 1,-0.7 -0.8l-0.2,-0.8 -1.8,-0.5q-1.4,0 -2.7,0.5a6,6 0,0 1,2 -2.5l3,-0.3 2.4,2z"
      android:strokeWidth=".2"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M333.8,142.2c0.8,0 2.2,-0.4 2.7,-1l0.8,-1a4,4 0,0 1,-2.3 3.6,7 7,0 0,1 -4,0.5l-2.4,-0.5c1,1 0.5,1.8 -0.6,2.4 0,0 0.2,-1 -0.2,-1.2q-1,-0.4 -2.1,0.5 -0.9,0.5 -2,0.6a5,5 0,0 1,-2.2 -0.4c-0.5,-0.3 -1.4,-1 -1.7,-1 -0.8,0.2 -2,0.3 -2.4,1v1a3,3 0,0 0,1.6 1.1q2.7,0.7 5.3,0.2 3.1,-0.6 6.1,-1.4c2,-0.4 4.3,-1 5.8,-1.5a18,18 0,0 1,5.6 -0.6c3.2,0 4.4,2 4.3,4.1 -0.2,2.2 -2.2,3.2 -3.9,3.8 -3,1.1 -7.2,0.5 -9.7,0.4l1.3,1.2c0.4,0.4 0.6,1.4 1.2,1.6 0.8,0.2 2,-0.3 2.8,-0.4h3.2l0.8,-0.6h0.8q0.5,0.2 0.4,1.5l0.6,0.5h1.1l1,0.6 -0.9,0.4 -0.5,0.6 1.3,0.1 0.8,0.8 -0.8,0.1 -0.6,0.6s1,-0.1 1.5,0.2l0.6,0.8h-0.8l-0.5,0.4 1,0.9q0.3,0.6 0.3,1.3l-0.6,-0.5 -0.6,-0.1 0.2,1.8c0,0.4 0.6,0.8 0.7,1.2q0.2,0.5 -0.2,1 -0.4,0.7 -1.3,0.4l-0.7,-0.4 -0.4,0.8q-0.4,0.3 -1,0.2l-0.7,-0.4 -0.5,-1.2 -1,0.3q-0.6,0 -1,-0.6a1,1 0,0 1,0.2 -1.3q0.6,-0.5 1.5,-0.6l1,-1 -2.5,-2.4 -1,-1.6s-2,0.8 -2.9,0.8a14,14 0,0 1,-4 0c-3.1,0 -5.1,-5 -7.8,-6.2a3,3 0,0 0,-2.1 0c-3.2,0.7 -6.6,5.1 -9.6,6l-4,0.6 -0.9,1.3 -1.6,0.3 -0.6,1.8 -1.3,1a3,3 0,0 0,0 -1.7l-1,1.4 -1.3,1a2,2 0,0 0,0 -1.6l-0.8,1.3 -1.3,0.8a2,2 0,0 0,0 -1.7l-0.7,1.3 -1.6,1a3,3 0,0 0,0.2 -1.8h-0.8c-1,0 -1,1 -1.4,1.5a2,2 0,0 1,-1.5 0.3q-1.1,-0.4 -0.4,-2.3a1.6,1.6 0,0 1,-1.9 -1.6q-0.1,-1.4 1.5,-1.5v-1q0.1,-1 0.8,-0.9 0.5,0 0.8,0.4t0.8,1.2a3,3 0,0 0,1.7 0.4l4,-2.1 3.1,-0.8v-7q1,0.1 2,0 3.2,0.3 6.2,0.9l3.2,-0.3s5.4,-1 7.8,-1.2q2,-0.4 3.9,-0.3c1.9,0.2 3.6,0.8 5.4,0.8q2,0.4 4,0.3c2.3,-0.2 6.2,0 7.4,-2.4a2,2 0,0 0,-2.3 -2.6q-0.8,0.3 -1,1 0,0.5 0.2,0.8t0.7,0.8a2,2 0,0 1,-1.4 -0.4l-0.9,-1.6a2,2 0,0 0,-0.3 1.2q0,0.6 0.4,1.2a2,2 0,0 1,-1.5 -0.7l-0.6,-1.6q-0.4,0.5 -0.4,1.1 0,0.8 0.3,1.4a3,3 0,0 1,-1.4 -1l-0.5,-1.6 -5.6,1.3 -6,1.3q-4.4,0.6 -7.2,-0.7a3,3 0,0 1,-1.6 -2,3 3,0 0,1 0.5,-2.1 6,6 0,0 1,3.2 -2l5.5,-1.2a18,18 0,0 1,6 0.3q1.2,0.3 2,-0.4a1,1 0,0 0,0.2 -1.4q0.8,0.1 1,1z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="m337.4,140.2 l-1.6,2.1a7,7 0,0 1,-3 1.1,11 11,0 0,1 -3,-0.2l-2.8,-0.9 0.8,1.8 -1.6,-0.1 -1.6,0.8q-1.4,0.2 -2.4,-0.2c-0.8,-0.1 -1,-0.4 -1.7,-0.3l-0.7,0.3 2.1,1.2 2,0.3 1.3,-0.3 1.4,-0.8 1.2,-0.1 0.3,0.5v0.8l0.8,-0.8 0.1,-0.5 -0.2,-1 1.6,0.3 1.8,0.2 2,-0.4 1.8,-0.8 0.9,-1.1zM342.6,145.6 L341.3,146.1a2,2 0,0 0,-0.6 1.7l-0.8,-2.1c-0.8,0.8 -0.8,1.8 -1,2.7l-0.8,-2.5a5,5 0,0 0,-1.4 2.3,5 5,0 0,1 -0.5,-2h-0.2c-2.6,0.4 -8.2,2.3 -12.6,2.7 -2.4,0.2 -5.2,0 -6.7,-1a3,3 0,0 1,-1.3 -2l-0.2,1 0.8,1.3 1.6,1 3,0.7 3.4,-0.4 2,-0.1 10,-2.4v1.3l0.8,0.8 0.8,0.5 -0.2,-1.1v-0.8l0.4,-0.5 0.5,1.4 0.7,0.6 1,0.2 -0.5,-0.7v-0.9l0.3,-0.8 0.5,1.3 0.5,0.5h1.3l-0.8,-0.7 -0.2,-0.8 0.6,-0.7 1.1,-0.3 1,0.4 0.6,0.7 0.2,1 -0.1,0.8c1.7,-1.6 0,-3.6 -2,-3.6zM346,147.2a5,5 0,0 1,-1.1 2.8c-3,3.3 -9.8,2.2 -13,1.1l-1.4,-0.2 -0.8,-0.3 -2,-0.3 -3.9,0.3c-2.4,0.2 -7.8,1.2 -7.8,1.2l-3.2,0.3a46,46 0,0 0,-6.2 -0.8h-2v1.1l2.8,0.6h-0.4l-1.9,3.6 -0.4,-1.6 -0.1,2.5 -0.4,0.7 -2.4,0.6 -2.4,1 -1.8,1.4 -1.8,-0.5 -0.8,-1.3q0.2,1 0.7,2c0.5,0.5 2,0.6 2,0.6l3.3,-2 1.6,-0.6a4,4 0,0 0,1.6 1.2c0,-1.6 1,-1.6 2.4,-2.4v1s2.7,-1 3.4,-1.8a5,5 0,0 0,1.1 -1.9,5 5,0 0,0 0.1,-2.1h1.8c2.7,-0.1 5.6,-1.3 8.2,-1.6 2,-0.4 4.5,-0.8 6.4,-0.4 1.6,0.3 2.2,1 3.6,2 1.2,0.8 2.4,3.2 4,3.2 0,0 0.9,0 1.4,-0.2q1,-0.2 2,-0.5l2.4,0.2c0.7,-1 1,-1 1.2,-0.8q0.6,0.4 0.1,1.3a2,2 0,0 0,1.9 0.8q-0.7,0.6 -1,1.3a3,3 0,0 1,1.7 0.3l-0.8,1.2 1.6,0.3q-0.7,0.3 -0.8,0.8l1.3,1q-0.8,0 -1,0.3l0.9,1.7 -0.2,-1.6 0.6,0.2q0.4,0 0.6,0.5l-0.2,-1.3 -1,-0.9 0.4,-0.3h0.8l-0.6,-0.9c-0.4,-0.3 -1.5,-0.2 -1.5,-0.2l0.6,-0.6h0.8l-0.8,-0.9 -1.3,-0.1 0.5,-0.6 0.9,-0.4 -1,-0.6h-1.1l-0.6,-0.5q0.1,-1.2 -0.4,-1.5h-0.8q-0.6,0.2 -0.8,0.7h-3.2c-0.8,0 -2,0.5 -2.8,0.3 -0.6,-0.2 -0.8,-1.2 -1.2,-1.6l-1.3,-1.2h2.9l3,0.2h1.8c2.2,-0.6 4.7,-1.2 5.7,-3.8v-2zM323.6,152.3a6,6 0,0 0,-2.4 0.5,30 30,0 0,0 -5.1,3.5l-3.2,2a18,18 0,0 1,-4.6 0.6s-0.4,1.6 -1,1.9c-0.4,0.3 -1.5,-0.3 -1.5,-0.3l-0.1,1.3 -0.5,1.4 -0.3,-1 -0.8,-1 -0.4,1.4 -0.9,1.4 -0.2,-0.8 -0.8,-1.2 -0.4,1.4 -0.5,1 -0.2,-0.5 -0.8,-1.2 -0.7,1.5 -0.6,0.8 -0.2,-1.2a1,1 0,0 0,-1 -0.3c-1,0.2 -1.5,1.2 -2.1,2.1q-0.4,0.4 -0.4,0v-0.7a1,1 0,0 0,-0.7 -0.3c-0.4,0.3 0.8,-1.2 0.8,-1.2l-1.6,-0.1c-0.4,-0.1 0.1,0 0.1,-0.8s-0.8,-0.8 -0.5,-0.8l1.3,0.1s-0.4,-0.4 -0.2,-0.8c0,-0.1 0.5,-0.4 0.6,-0.8a1,1 0,0 0,-0.5 -1c-0.1,0.4 -0.8,0.2 -0.8,0.7v1.1q-1.7,0.1 -1.5,1.5a1.6,1.6 0,0 0,2 1.6c-0.9,1.2 -0.3,2 0.3,2.3a2,2 0,0 0,1.5 -0.3c0.5,-0.5 0.5,-1.5 1.4,-1.5h0.8a4,4 0,0 1,-0.2 1.9l1.6,-1 0.8,-1.4a2,2 0,0 1,0 1.7l1.2,-0.8 0.8,-1.4a2,2 0,0 1,0 1.7l1.3,-1 1,-1.4a3,3 0,0 1,0 1.7l1.3,-1 0.6,-1.7q0.8,0 1.6,-0.4l0.8,-1.3 4,-0.5c3.2,-1 6.5,-5.4 9.7,-6.2a3,3 0,0 1,2 0c2.8,1.3 4.9,6.4 7.8,6.4 1.4,0 2.1,0.3 4,0l3,-0.9 1,1.6 2.4,2.4 -1,1c-0.3,0.2 -1.1,0.2 -1.4,0.6a1,1 0,0 0,-0.1 1.3q0.3,0.4 1,0.6l0.9,-0.3 0.4,1.2q0.4,0.4 0.8,0.5 0.7,0.1 1,-0.4l0.4,-0.8q0.3,0.4 0.7,0.5a1,1 0,0 0,1.3 -0.5c0.3,-0.3 0,-1.3 -0.1,-1.7v1.2a0.8,0.8 0,0 0,-1.5 0,1 1,0 0,1 -0.7,-0.8l-0.2,1q0,0.4 -0.3,0.6c0,-1 -0.7,-1 -1.2,-0.8v-1l-1,0.5c-0.3,0 0,-0.2 -0.2,-0.5 0,-0.3 -0.6,-0.2 -0.4,-0.3l1.5,-0.8 1,-1.4 -2.5,-2.4 -1.5,-2.4a26,26 0,0 1,-4.8 1,7 7,0 0,1 -3.4,-0.4c-1.8,-0.9 -2.8,-3.1 -4.3,-4.3q-1.4,-1.4 -3,-1.9z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M333.8,142.2c0.8,0 2.2,-0.4 2.7,-1l0.8,-1a4,4 0,0 1,-2.3 3.6,7 7,0 0,1 -4,0.5l-2.4,-0.5c1,1 0.5,1.8 -0.6,2.4 0,0 0.2,-1 -0.2,-1.2q-1,-0.4 -2.1,0.5 -0.9,0.5 -2,0.6a5,5 0,0 1,-2.2 -0.4c-0.5,-0.3 -1.4,-1 -1.7,-1 -0.8,0.2 -2,0.3 -2.4,1v1a3,3 0,0 0,1.6 1.1q2.7,0.7 5.3,0.2 3.1,-0.6 6.1,-1.4c2,-0.4 4.3,-1 5.8,-1.5a18,18 0,0 1,5.6 -0.6c3.2,0 4.4,2 4.3,4.1 -0.2,2.2 -2.2,3.2 -3.9,3.8 -3,1.1 -7.2,0.5 -9.7,0.4l1.3,1.2c0.4,0.4 0.6,1.4 1.2,1.6 0.8,0.2 2,-0.3 2.8,-0.4h3.2l0.8,-0.6h0.8q0.5,0.2 0.4,1.5l0.6,0.5h1.1l1,0.6 -0.9,0.4 -0.5,0.6 1.3,0.1 0.8,0.8 -0.8,0.1 -0.6,0.6s1,-0.1 1.5,0.2l0.6,0.8h-0.8l-0.5,0.4 1,0.9q0.3,0.6 0.3,1.3l-0.6,-0.5 -0.6,-0.1 0.2,1.8c0,0.4 0.6,0.8 0.7,1.2q0.2,0.5 -0.2,1 -0.4,0.7 -1.3,0.4l-0.7,-0.4 -0.4,0.8a1,1 0,0 1,-1 0.3l-0.7,-0.5 -0.5,-1.2 -1,0.3q-0.6,0 -1,-0.6a1,1 0,0 1,0.2 -1.3q0.6,-0.5 1.5,-0.6l1,-1 -2.5,-2.4 -1,-1.6s-2,0.8 -2.9,0.8a14,14 0,0 1,-4 0c-3.1,0 -5.1,-5 -7.8,-6.2a3,3 0,0 0,-2.1 0c-3.2,0.7 -6.6,5.1 -9.6,6l-4,0.6 -0.9,1.3 -1.6,0.3 -0.6,1.8 -1.3,1a3,3 0,0 0,0 -1.7l-1,1.4 -1.3,1a2,2 0,0 0,0 -1.6l-0.8,1.3 -1.3,0.8a2,2 0,0 0,0 -1.7l-0.7,1.3 -1.6,1a3,3 0,0 0,0.2 -1.8h-0.8c-1,0 -1,1 -1.4,1.5a2,2 0,0 1,-1.5 0.3q-1.1,-0.4 -0.4,-2.3a1.6,1.6 0,0 1,-1.9 -1.6q-0.1,-1.3 1.5,-1.5v-1q0.1,-1 0.8,-0.9 0.5,0 0.8,0.4t0.8,1.2a3,3 0,0 0,1.7 0.4l4,-2.1 3.1,-0.8v-7q1,0.1 2,0 3.2,0.3 6.2,0.9l3.2,-0.3s5.4,-1 7.8,-1.2q2,-0.4 3.9,-0.3c1.9,0.2 3.6,0.8 5.4,0.8q2,0.4 4,0.3c2.3,-0.2 6.2,0 7.4,-2.4a2,2 0,0 0,-2.3 -2.6q-0.8,0.3 -1,1 0,0.4 0.2,0.8l0.7,0.8a3,3 0,0 1,-1.6 -0.4,4 4,0 0,1 -0.7,-1.6 2,2 0,0 0,-0.3 1.2q0,0.6 0.4,1.2a2,2 0,0 1,-1.5 -0.7l-0.6,-1.6q-0.4,0.5 -0.4,1.1 0,0.8 0.3,1.4a3,3 0,0 1,-1.4 -1l-0.5,-1.6 -5.6,1.3 -6,1.3q-4.4,0.6 -7.2,-0.7a3,3 0,0 1,-1.6 -2,3 3,0 0,1 0.5,-2.1 6,6 0,0 1,3.2 -2l5.5,-1.2a18,18 0,0 1,6 0.3q1.2,0.3 2,-0.4a1,1 0,0 0,0.2 -1.4q0.8,0.1 1,1 0.2,0.5 0,1.1z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#806600"/>
  <path
      android:pathData="M310.2,152.2c0.8,0.4 1.6,-0.2 1.8,-0.3l-0.6,-0.8 -0.3,-1.6c0,-1.2 0.5,-1.3 0.5,-1.3l1.1,-0.5q0.5,-0.5 0.7,-1.1l-0.2,-1.5c-1.2,0 -1.5,0 -1.8,0.4l-0.2,-1.2 -0.5,-1 -0.4,-0.3q0,0.5 -0.4,0.8l-0.5,-1.6a1.4,1.4 0,0 0,-2 -0.7l-1.2,1.2v-1l-0.5,0.2 -0.9,0.6 -0.8,0.9q0.2,-0.6 -1.3,-1.4l-0.9,1.2a2,2 0,0 0,-0.1 1.2l0.7,1.1s0.4,0.4 -0.3,1.5l-1,1 -1,0.4c0.2,0.8 0.3,1 1.3,1.2 -2.4,1.6 -2.5,4.3 -0.8,6.6l0.8,-1.2q0,1.2 0.7,2c0.2,0.3 0.3,1.6 0.6,2l0.7,0.7s0.4,-1 0.8,-1.1l1.6,-0.5a6,6 0,0 0,1.6 -1.5l-0.2,1.6c3.1,-1.2 3.8,-3.2 3,-6"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M304.2,148.3a6,6 0,0 1,-0.6 3.1l2,0.8 2,0.3c-0.2,-0.8 0.6,-2.4 1,-3h-1.5l-0.6,-0.6 -1.1,0.3 -1.2,-0.8z"
      android:fillColor="#504416"/>
  <path
      android:pathData="M304.6,148.8a2,2 0,0 0,0 1.5l0.5,-1.2zM307.4,149.8 L307.3,151.1q0.5,-0.6 0.7,-1.3zM304.6,150.8a2,2 0,0 0,-0.8 1.2h0.6l0.2,-1.3zM307.1,151.4 L306.5,152.6 307,152.9a2,2 0,0 0,0 -1.5zM304.8,144.8q-0.6,-0.1 -0.7,0.4 -0.2,0.7 0.8,1a1,1 0,0 0,1.2 -0.5q0,-0.6 -0.8,-0.8zM309,146q-0.6,0 -0.7,0.3a1,1 0,0 0,0.8 1.1q1,0.3 1.2,-0.4 0.3,-0.7 -0.8,-1z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M304.8,144.8h-0.3a1,1 0,0 0,-0.5 0.5l0.2,0.6q0.2,0.4 0.6,0.5a1,1 0,0 0,1.4 -0.6l-0.2,-0.6 -0.8,-0.4zM304.8,145h0.4l0.6,0.4q0.3,0 0.2,0.3 -0.4,0.7 -1.1,0.5l-0.6,-0.4 -0.1,-0.5 0.3,-0.3zM309,146h-0.2a1,1 0,0 0,-0.6 0.3,1 1,0 0,0 1,1.3h0.7a1,1 0,0 0,0.5 -0.6l-0.2,-0.5 -0.6,-0.4zM309,146.2h0.5q0.4,0 0.7,0.4v0.4l-0.4,0.4h-0.6a1,1 0,0 1,-0.8 -1q0,-0.2 0.4,-0.2z"
      android:fillColor="#d1b948"/>
  <path
      android:pathData="m304.8,144 l-0.8,0.2v0.4h0.3l0.5,0.2 1,0.5 1.2,-0.1 -1,-0.5c-0.4,-0.1 -0.7,-0.7 -1.2,-0.8zM310.2,145.5L308,145.5l1,0.6 1,0.1 0.6,0.2 0.3,0.2 0.1,-0.4a1,1 0,0 0,-0.8 -0.7m-6.5,6c-1,2.6 3.1,4 3.7,1a8,8 0,0 1,-3.7 -1"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M303.6,151.2v0.2a2,2 0,0 0,0 1.8,2.2 2.2,0 0,0 4,-0.6v-0.3h-0.2a8,8 0,0 1,-3.6 -1zM303.8,151.5a11,11 0,0 0,3.6 1,3 3,0 0,1 -0.8,1.4 3,3 0,0 1,-1.6 0.1,3 3,0 0,1 -1.3,-1 2,2 0,0 1,0 -1.5z"
      android:fillColor="#806600"/>
  <path
      android:pathData="M305.4,149.2a12,12 0,0 0,-0.6 5.4,12 12,0 0,0 2.4,-5l-0.5,-0.2 -0.2,-0.4 -0.6,0.2z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="m308.6,141.4 l-1.2,0.1 -1.2,1.2v-1l-0.5,0.2 -1,0.6 -0.8,0.8c0.4,0 1.3,-0.8 1.6,-0.7 0.3,0 0.1,0.8 0.4,0.8q0.4,0 1.2,-0.8 0.8,-0.7 1.2,-0.6 0.6,0.3 0.8,1.1 0.2,1.2 0.6,1.5c0.3,0.1 0.5,-0.7 0.8,-0.5 0.4,0 0.5,1.3 0.9,1.4l-0.2,-1.2 -0.5,-1 -0.4,-0.3q-0.1,0.5 -0.5,0.8l-0.4,-1.6zM303.6,145.8q-0.6,1.1 0.9,1.6l-0.3,0.8 0.4,1 1,0.2 0.8,-0.3 0.6,0.8 0.8,0.2 1,-0.7 0.4,-0.7h0.6l0.5,-0.2 0.3,-0.7a2,2 0,0 1,-2.1 0.3l-0.3,1a1,1 0,0 1,-0.8 0.2l-0.8,-0.7q-0.6,0.4 -1,0.3a1,1 0,0 1,-0.8 -0.8,1 1,0 0,1 0.3,-1c-1.8,-0.5 -1.2,-1.1 -1.6,-1.3zM302.6,146 L301,148 300,148.4c0.3,0.8 0.4,1 1.4,1.2 -2.4,1.6 -2.5,4.3 -0.8,6.6l0.8,-1.2q0,1.2 0.7,2c0.2,0.3 0.3,1.6 0.6,2l0.7,0.7s0.4,-1 0.8,-1.1l1.6,-0.5a6,6 0,0 0,1.6 -1.5l-0.2,1.6c3.1,-1.2 3.8,-3.2 3,-6 0.8,0.4 1.6,-0.2 1.8,-0.3l-0.6,-0.8 -0.3,-1.6v-0.8l-0.5,1.3v1.3l-1.4,-0.7 0.6,3q0,1 -0.4,2l-1.2,1.4 0.2,-1.8 -0.3,-2.3 -0.7,2.2 -1,1.6c0.2,-1.6 0.2,-1.7 0,-2.4a9,9 0,0 1,-1.1 2.9l-1.2,0.6 -0.6,0.5 -0.5,-1.4 -1,-1.8c-0.2,-0.9 1,-2.8 1,-2.8q-0.6,0.1 -1.3,1.1l-1.1,1.3 -0.4,-1.8a3,3 0,0 1,0.5 -1.6,17 17,0 0,1 2.7,-2.6 3,3 0,0 1,-2.3 0.1l1,-1.3q0.5,-0.7 0.5,-1.6zM306.6,146.8h-1v0.5l0.4,0.1 0.6,0.8 1.4,-0.4 0.2,-0.3 -1.2,-0.6z"
      android:fillColor="#ffd700"/>
  <path
      android:pathData="M310.2,152.2c0.8,0.4 1.6,-0.2 1.8,-0.3l-0.6,-0.8 -0.3,-1.6c0,-1.2 0.5,-1.3 0.5,-1.3l1.1,-0.5q0.5,-0.5 0.7,-1.1l-0.2,-1.5c-1.2,0 -1.5,0 -1.8,0.4l-0.2,-1.2 -0.5,-1 -0.4,-0.3q0,0.5 -0.4,0.8a2,2 0,1 0,-3.7 -1v-1l-0.5,0.1 -0.9,0.6 -0.8,0.9q0.2,-0.6 -1.3,-1.4l-0.9,1.2a2,2 0,0 0,-0.1 1.2l0.7,1.1s0.4,0.4 -0.3,1.5l-1,1 -1,0.4c0.2,0.8 0.3,1 1.3,1.2 -2.4,1.6 -2.5,4.3 -0.8,6.6l0.8,-1.2q0,1.2 0.7,2c0.2,0.3 0.3,1.6 0.6,1.9l0.7,0.8s0.4,-1 0.8,-1.1l1.6,-0.6 1.6,-1.4 -0.2,1.6c3.1,-1.2 3.8,-3.2 3,-6z"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#806600"/>
  <path
      android:pathData="m302.7,142.9 l-0.5,1.2 0.8,1.3q0.3,-0.9 0.4,-1.8c0,-0.3 -0.7,-0.8 -0.7,-0.8zM304.3,144.5h-0.2q-0.3,0 -0.4,0.5 0.3,0 0.5,-0.3h0.3l1,0.5h1.4l-0.2,0.6 -0.7,0.4 -0.2,0.3 -0.3,0.3v0.6h0.1q0.6,0.2 0.6,0.8h0.1l0.2,0.1h0.5a1,1 0,0 1,1 -0.2v-0.1l0.3,-0.4v-0.3l-0.1,-0.4c0,-0.3 -0.4,-0.8 -0.4,-0.8q0,-0.4 0.2,-0.7 0.5,0.6 1.2,0.8 0.5,0.3 1,0.2h0.2l0.4,0.6v-0.7l-0.5,-0.1 -1,-0.1 -1.1,-0.8 -0.2,-0.2 -0.2,0.2 -0.1,1 0.2,0.7 0.1,0.6v0.2a2,2 0,0 0,-1.3 0.4,2 2,0 0,0 -1,-1v-0.3l0.1,-0.1 0.3,-0.3 0.7,-0.5 0.3,-0.9v-0.2h-1.6q-0.3,-0.2 -0.9,-0.3h-0.2zM303.5,145.5a1,1 0,0 0,-0.1 1.3q0.3,0.5 0.8,0.6v0.2l-0.1,0.6q0,0.7 0.5,1t1,0.4l0.7,-0.2 0.4,0.4q0.5,0.4 1,0.4a1.4,1.4 0,0 0,1.3 -1.2v-0.1q0.6,0 1.2,-0.2 0.4,-0.4 0.4,-1.1c-0.3,0.2 0,0.6 -0.6,1h-0.9l-0.3,-0.3v0.7l-0.2,0.4a1,1 0,0 1,-1 0.5l-0.6,-0.1 -0.4,-0.6v-0.4l-0.3,0.2 -0.3,0.2h-0.4l-0.7,-0.2 -0.6,-0.8 0.1,-0.5 0.2,-0.3 0.1,-0.2h-0.3a1,1 0,0 1,-0.8 -0.5c-0.4,-0.5 0,-0.8 0,-1.2zM312.7,145.7s-0.8,0 -1,0.3l-0.6,1.7 1.3,-0.7z"
      android:fillColor="#806600"/>
  <path
      android:pathData="M305.1,145.1a0.4,0.4 0,0 0,0 0.8,0.4 0.4,0 1,0 0,-0.8m4.2,1.2a0.4,0.4 0,0 0,-0.1 0.8,0.4 0.4,0 1,0 0,-0.8z"
      android:fillColor="#2b2200"/>
  <path
      android:pathData="M293.8,159.2c-0.3,0 -0.8,-0.2 -1.6,0.8a1.4,1.4 0,0 1,1.4 0.6,1 1,0 0,0 0.6,-0.4 1,1 0,0 0,-0.4 -1zM315.2,160.4a3,3 0,0 0,-2 0.8,2 2,0 0,1 1.8,0.6 1,1 0,0 0,0.7 -0.4,1 1,0 0,0 -0.5,-1zM292.2,161.8c-0.2,0.3 -0.8,0.2 -1.2,1.5a1.4,1.4 0,0 1,1.6 0,1 1,0 0,0 0.4,-0.7 0.7,0.7 0,0 0,-0.8 -0.8zM315.6,163.8c-0.2,0.4 -0.8,0.3 -1.4,1.6a2,2 0,0 1,1.9 -0.1,1 1,0 0,0 0.4,-0.7 1,1 0,0 0,-0.8 -0.8zM293.9,165 L293.5,165.1a3,3 0,0 0,-0.5 2q0.5,-0.9 1.3,-1l0.1,-0.7zM340.3,165.1 L340.1,165.2a3,3 0,0 0,-0.7 2,1.4 1.4,0 0,1 1.4,-0.8q0.2,-0.4 0.2,-0.8a1,1 0,0 0,-0.7 -0.5zM318.8,166.1 L318.3,166.2a3,3 0,0 0,-0.4 2.2,2 2,0 0,1 1.6,-1.1 1,1 0,0 0,0 -0.8,1 1,0 0,0 -0.7,-0.4zM345.8,166.2q-0.4,0 -0.6,0.4 -0.1,1.3 0.5,2.1 -0.1,-1 0.7,-1.7l-0.3,-0.8zM342.8,166.6 L342.3,167a3,3 0,0 0,0.1 2,2 2,0 0,1 1,-1.4 1,1 0,0 0,-0.1 -0.8z"
      android:strokeWidth=".2"
      android:fillColor="#377bc8"
      android:strokeColor="#2c5aa0"/>
  <path
      android:pathData="M378.7,21.3V21l0.8,-1.6a9,9 0,0 0,-6.9 2,18 18,0 0,0 -2.8,3.7 12,12 0,0 0,-0.8 3.2l-1.9,-1.6 -1.3,-0.4a5,5 0,0 0,-3.2 1.5,6 6,0 0,0 -1.5,3c-0.4,1.6 0,3.2 -1,4.6l-1.6,1.8a8,8 0,0 0,2.7 0.6,4 4,0 0,0 2.8,-1.5c0.6,-0.8 0.9,-1.9 1.8,-2.5a1,1 0,0 1,1.2 -0.2c0.5,0.2 0.6,1.2 0.4,1.6l-1,3.4 -1.8,-0.4 -1.4,0.8 -0.6,0.3h0l1.2,1.8 0.5,0.2h0l-0.4,0.2 -3.3,1.6 -0.4,0.2h0l-2.4,0.1 -1.6,-0.2 -2.4,-0.8 -0.7,-0.4 -1.3,-1.4a12,12 0,0 1,-2.1 -4.3,6 6,0 0,0 5,-5.7v-0.8l-0.1,-0.4 -0.3,-1 -0.3,-0.6 -0.6,-1 -0.4,-0.4a5,5 0,0 0,-3.5 -1.7h-1.3a6,6 0,0 0,-3.2 1.6,6 6,0 0,0 -1.4,2.2l-0.2,0.8 -0.1,1.3h0a6,6 0,0 0,3.2 5.2l-2.7,2.6a15,15 0,0 1,-8.7 4,14 14,0 0,1 -10.6,-3 12,12 0,0 1,-2.3 -2.8h1.3l1.6,-1.6h0.1l-0.5,-0.4 -1.2,-1h-0.5l0.4,-3.8c0,-0.6 0.4,-1.6 1,-1.6a2,2 0,0 1,1.4 0.8c0.8,1 0.4,2.1 0.8,3.2a5,5 0,0 0,2.4 2.6q1.7,0.5 3.3,0.4l-0.9,-2.4c-0.6,-1.8 0.5,-3.2 0.8,-5a6,6 0,0 0,-0.2 -3.6,6 6,0 0,0 -3,-2.8h-1.6l-2.8,0.8s0.8,-2.4 0.5,-3.6a15,15 0,0 0,-1.6 -4.8,23 23,0 0,0 -3.5,-3.7 23,23 0,0 0,-3.5 3.7,15 15,0 0,0 -1.6,4.8c-0.2,1.2 0.5,3.6 0.5,3.6l-2.8,-0.8H311a6,6 0,0 0,-3 2.8,6 6,0 0,0 -0.2,3.6c0.3,1.8 1.4,3.2 0.8,5l-1,2.4q1.7,0.1 3.3,-0.4a5,5 0,0 0,2.4 -2.6c0.5,-1 0.2,-2.3 1,-3.2q0.4,-0.8 1.2,-0.8c0.7,0 1.1,1 1.1,1.6l0.4,3.7h-0.5l-1.6,1.6 1.6,1.5h1.3a12,12 0,0 1,-2.4 2.8,14 14,0 0,1 -10.6,3 15,15 0,0 1,-8.6 -4l-2.6,-2.6a6,6 0,0 0,3.4 -5.2h0a6,6 0,0 0,-1.8 -4.2,6 6,0 0,0 -3.3,-1.6l-0.3,-0.2h-1a5,5 0,0 0,-3.4 1.8,7 7,0 0,0 -1.3,2l-0.3,1v1.2a6,6 0,0 0,4.7 5.7,12 12,0 0,1 -2.1,4.3l-1.4,1.4 -0.8,0.4 -2.3,0.8 -1.6,0.2 -2.3,-0.1h0l-0.4,-0.1 -3.4,-1.7 -0.3,-0.2h0l0.5,-0.2 1.2,-1.8h0l-0.6,-0.3 -1.4,-0.8 -1.7,0.4 -1.1,-3.3c-0.2,-0.5 -0.1,-1.5 0.4,-1.7a1,1 0,0 1,1.2 0.2c0.8,0.6 1.2,1.8 1.8,2.5a4,4 0,0 0,2.8 1.5,8 8,0 0,0 2.7,-0.6l-1.6,-1.8c-1.1,-1.4 -0.6,-3 -1,-4.7a6,6 0,0 0,-1.5 -3,5 5,0 0,0 -3.2,-1.4l-1.3,0.4 -2,1.6a12,12 0,0 0,-0.7 -3.2,18 18,0 0,0 -2.8,-3.7 9,9 0,0 0,-6.8 -2l0.7,1.6v0.3h0.1l8.2,18.4 -1.1,0.2 -1.2,2 1.9,1 2.4,-0.6q1.1,3 2,5.8l-2,0.2h0c-0.4,0.5 -0.6,0.5 -0.6,1h0q0,0.6 0.4,1h0l0.3,0.2h0l0.5,0.3h1l0.2,1.3 -0.1,-0.1v2l-1.2,2.6L274,59l0.8,2.1 0.6,-0.4 0.1,0.7h-0.3l0.5,1h0l-0.7,0.2q-0.5,0.4 -0.5,1h0q0,0.4 0.3,1h0l0.8,0.5q5.8,-0.8 12.2,-1.3l4,-0.3q10.6,-0.6 21.3,-0.6h13.6c4.3,0 13.3,0 21.4,0.6l2.2,0.2 2.3,0.1 5.3,0.5 6.4,0.8h0l0.3,-0.2 0.7,-0.7v-0.6h0q0,-0.6 -0.5,-1h0l-0.7,-0.1 0.2,-0.9h0l0.2,-0.9 0.5,0.4 0.8,-2 2,-2.4 -1.2,-2.8V52h-0.2l0.3,-1.3 0.9,0.1h0q0.3,0 0.4,-0.2 0.8,-0.6 0.8,-1.3h0q0,-0.5 -0.6,-1h0l-2.3,-0.2 2.2,-5.9 2.5,0.7 2,-1 -1.2,-2 -1.2,-0.2 8.3,-18.3h0.1z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#ffd700"
      android:strokeColor="#806600"
      android:strokeLineCap="round"/>
</vector>
