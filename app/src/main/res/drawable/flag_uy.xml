<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M266,53.3h374v53.4L266,106.7zM266,160h374v53.3L266,213.3zM0,266.7h640L640,320L0,320zM0,373.3h640v53.4L0,426.7z"
      android:fillColor="#0038a8"/>
  <path
      android:pathData="m127.43,159.41 l8.8,13.2c-36.37,26.4 -14.37,41.65 -39.89,49.87 15.84,-15.25 -2.64,-16.72 10.85,-49.28"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M120.98,163.22c-19.95,32.85 -7.04,51.04 -24.64,59.55"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l17.6,0L133.3,230.1 115.7,133.3l17.6,0l0,96.8"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m110.69,147.61l-3.11,15.56c-44.39,-7.05 -39.62,19.29 -63.47,7.05 21.99,0.41 9.96,-13.69 42.52,-27.17"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M103.43,145.75c-37.34,9.13 -41.07,31.11 -59.53,24.68"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l12.45,12.45L64.85,201.75 120.85,120.85l12.45,12.45l-68.45,68.45"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m107.19,127.43l-13.2,8.8c-26.4,-36.37 -41.65,-14.37 -49.87,-39.89 15.25,15.84 16.72,-2.64 49.28,10.85"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M103.38,120.98c-32.85,-19.95 -51.04,-7.04 -59.55,-24.64"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l-0,17.6L36.5,133.3 133.3,115.7l-0,17.6l-96.8,0"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m118.99,110.69l-15.56,-3.11c7.05,-44.39 -19.29,-39.62 -7.05,-63.47 -0.41,21.99 13.69,9.96 27.17,42.52"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M120.85,103.43c-9.13,-37.34 -31.11,-41.07 -24.68,-59.53"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l-12.45,12.45L64.85,64.85 145.75,120.85l-12.45,12.45l-68.45,-68.45"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m139.17,107.19l-8.8,-13.2c36.37,-26.4 14.37,-41.65 39.89,-49.87 -15.84,15.25 2.64,16.72 -10.85,49.28"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M145.62,103.38c19.95,-32.85 7.04,-51.04 24.64,-59.55"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l-17.6,-0L133.3,36.5 150.9,133.3l-17.6,-0l-0,-96.8"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m155.91,118.99l3.11,-15.56c44.39,7.05 39.62,-19.29 63.47,-7.05 -21.99,-0.41 -9.96,13.69 -42.52,27.17"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M163.17,120.85c37.34,-9.13 41.07,-31.11 59.53,-24.68"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l-12.45,-12.45L201.75,64.85 145.75,145.75l-12.45,-12.45l68.45,-68.45"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m159.41,139.17l13.2,-8.8c26.4,36.37 41.65,14.37 49.87,39.89 -15.25,-15.84 -16.72,2.64 -49.28,-10.85"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M163.22,145.62c32.85,19.95 51.04,7.04 59.55,24.64"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l0,-17.6L230.1,133.3 133.3,150.9l0,-17.6l96.8,-0"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="m147.61,155.91l15.56,3.11c-7.05,44.39 19.29,39.62 7.05,63.47 0.41,-21.99 -13.69,-9.96 -27.17,-42.52"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M145.75,163.17c9.13,37.34 31.11,41.07 24.68,59.53"
      android:strokeWidth="1.76"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3l12.45,-12.45L201.75,201.75 120.85,145.75l12.45,-12.45l68.45,68.45"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:pathData="M133.3,133.3m-32.27,0a32.27,32.27 0,1 1,64.53 0a32.27,32.27 0,1 1,-64.53 0"
      android:strokeWidth="1.76"
      android:fillColor="#fcd116"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M157.06,120.39c-2.05,2.35 -3.23,-1.76 -10.56,-1.76S137.99,123.03 136.82,122.15s6.16,-6.16 8.51,-6.45 9.09,2.05 11.73,4.69m-8.51,2.64c2.05,1.76 0.29,5.57 -1.76,5.57S140.93,125.09 142.69,122.74"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M138.87,125.67c0.29,-3.52 3.23,-4.11 7.92,-4.11s6.75,3.52 8.51,4.4c-2.05,0 -3.81,-2.93 -8.51,-2.93s-4.69,0 -7.92,2.93m0.88,0.59c1.17,-1.76 2.64,1.76 5.87,1.76s4.99,-0.88 7.04,-2.35 -2.93,3.52 -6.16,3.52 -7.63,-1.76 -6.75,-2.93"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M149.73,128.31c3.81,-2.05 1.47,-4.99 0,-5.57 0.59,0.59 2.93,3.52 0,5.57M133.3,145.91c1.76,0 2.35,-0.59 4.69,-0.59s7.92,3.23 11.15,2.05c-6.75,2.64 -4.11,0.88 -15.84,0.88l-1.47,0m18.48,1.76c-1.17,-2.05 -0.88,-1.47 -3.23,-4.69 2.35,1.76 2.93,2.64 3.23,4.69M133.3,152.95c7.33,0 6.16,-1.47 15.84,-5.57 -7.04,0.88 -8.51,3.23 -15.84,3.23l-1.47,0m1.47,-8.51c2.05,0 2.64,-1.47 4.99,-1.47s5.57,0.88 7.04,2.05c0.29,0.29 -0.88,-2.35 -3.23,-2.64S140.63,135.94 137.99,135.35c0,1.17 0.88,0.88 1.17,2.64 0,1.47 -2.64,1.47 -3.23,0 0.59,2.35 -1.17,2.35 -2.64,2.35"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M109.54,120.39c2.05,2.35 3.23,-1.76 10.56,-1.76S128.61,123.03 129.78,122.15s-6.16,-6.16 -8.51,-6.45 -9.09,2.05 -11.73,4.69m8.51,2.64c-2.05,1.76 -0.29,5.57 1.76,5.57S125.67,125.09 123.91,122.74"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M127.73,125.67c-0.29,-3.52 -3.23,-4.11 -7.92,-4.11s-6.75,3.52 -8.51,4.4c2.05,0 3.81,-2.93 8.51,-2.93s4.69,0 7.92,2.93m-0.88,0.59c-1.17,-1.76 -2.64,1.76 -5.87,1.76s-4.99,-0.88 -7.04,-2.35 2.93,3.52 6.16,3.52 7.63,-1.76 6.75,-2.93"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M116.87,128.31c-3.81,-2.05 -1.47,-4.99 -0,-5.57 -0.59,0.59 -2.93,3.52 -0,5.57M133.3,145.91c-1.76,0 -2.35,-0.59 -4.69,-0.59s-7.92,3.23 -11.15,2.05c6.75,2.64 4.11,0.88 15.84,0.88l1.47,0m-18.48,1.76c1.17,-2.05 0.88,-1.47 3.23,-4.69 -2.35,1.76 -2.93,2.64 -3.23,4.69M133.3,152.95c-7.33,0 -6.16,-1.47 -15.84,-5.57 7.04,0.88 8.51,3.23 15.84,3.23l1.47,0m-1.47,-8.51c-2.05,0 -2.64,-1.47 -4.99,-1.47s-5.57,0.88 -7.04,2.05c-0.29,0.29 0.88,-2.35 3.23,-2.64S125.97,135.94 128.61,135.35c-0,1.17 -0.88,0.88 -1.17,2.64 -0,1.47 2.64,1.47 3.23,0 -0.59,2.35 1.17,2.35 2.64,2.35"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M133.3,155.59c-1.47,0 -5.28,0.88 0,0.88s1.47,-0.88 0,-0.88"/>
</vector>
