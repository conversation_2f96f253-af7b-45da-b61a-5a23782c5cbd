<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M256,0h384v480H256z"
      android:fillColor="#ff0000"/>
  <path
      android:pathData="M0,0h256v480H0z"
      android:fillColor="#060"/>
  <path
      android:pathData="M339.5,306.2c-32.3,-1 -180,-93.2 -181,-108l8.1,-13.5c14.7,21.3 165.7,111 180.6,107.8z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.9,182.8c-2.9,7.8 38.6,33.4 88.4,63.8s92.9,49 96,46.4l1.5,-2.8q-0.9,1.6 -4.3,0.6c-13.5,-3.9 -48.6,-20 -92.1,-46.4 -43.6,-26.4 -81.4,-50.7 -87.3,-61a6,6 0,0 1,-0.6 -3.1h-0.2l-1.2,2.2zM340.2,306.6q-0.7,1.3 -3.5,0.8c-12,-1.3 -48.6,-19.1 -91.9,-45 -50.4,-30.2 -92,-57.6 -87.4,-64.8l1.2,-2.2 0.2,0.1c-4,12.2 82.1,61.4 87.2,64.6 49.8,30.8 91.8,48.9 95.5,44.2z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M256.2,207.2c32.2,-0.3 72,-4.4 95,-13.6l-5,-8c-13.5,7.5 -53.5,12.5 -90.3,13.2 -43.4,-0.4 -74.1,-4.5 -89.5,-14.8l-4.6,8.6c28.2,12 57.2,14.5 94.4,14.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M352.5,193.8c-0.8,1.3 -15.8,6.4 -37.8,10.2a381,381 0,0 1,-58.6 4.3,416 416,0 0,1 -56.2,-3.6c-23.1,-3.6 -35,-8.6 -39.5,-10.4l1.1,-2.2c12.7,5 24.7,8 38.7,10.2A412,412 0,0 0,256 206a392,392 0,0 0,58.3 -4.3c22.5,-3.7 34.8,-8.4 36.6,-10.5zM348.1,185.7c-2.4,2 -14.6,6.3 -36,9.7a388,388 0,0 1,-55.8 4c-22,0 -40.1,-1.6 -53.8,-3.6 -21.8,-2.8 -33.4,-8 -37.6,-9.4l1.3,-2.2c3.3,1.7 14.4,6.2 36.5,9.3a385,385 0,0 0,53.6 3.4,384 384,0 0,0 55.4,-4c21.5,-3 33.1,-8.4 34.9,-9.8zM150.3,246c19.8,10.7 63.9,16 105.6,16.4 38,0.1 87.4,-5.8 105.9,-15.6l-0.5,-10.7c-5.8,9 -58.8,17.7 -105.8,17.4s-90.7,-7.6 -105.3,-17v9.5"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M362.8,244.5v2.5c-2.8,3.4 -20.2,8.4 -42,12a434,434 0,0 1,-65.4 4.4,400 400,0 0,1 -62,-4.3 155,155 0,0 1,-44.4 -12v-2.9c9.7,6.4 35.9,11.2 44.7,12.6 15.8,2.4 36.1,4.2 61.7,4.2 26.9,0 48.4,-1.9 65,-4.4 15.7,-2.3 38,-8.2 42.4,-12.1m0,-9v2.5c-2.8,3.3 -20.2,8.3 -42,11.9a434,434 0,0 1,-65.4 4.5,414 414,0 0,1 -62,-4.3 155,155 0,0 1,-44.4 -12v-3c9.7,6.5 36,11.2 44.7,12.6a408,408 0,0 0,61.7 4.3c26.9,0 48.5,-2 65,-4.5 15.7,-2.2 38,-8.1 42.4,-12m-107,68.8c-45.6,-0.2 -84.7,-12.4 -93,-14.4l6,9.4a250,250 0,0 0,87.4 14.3c34.7,-1 65,-3.7 86.3,-14.1l6.2,-9.8c-14.5,6.9 -64,14.6 -93,14.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m344.9,297.3 l-2.8,4c-10,3.6 -26,7.4 -32.6,8.4a296,296 0,0 1,-53.7 5c-40.4,-0.6 -73.5,-8.5 -89,-15.3l-1.3,-2.1 0.2,-0.4 2.1,0.9a287,287 0,0 0,88.2 14.5c18.8,0 37.5,-2.1 52.6,-4.8 23.2,-4.7 32.6,-8.2 35.5,-9.8l0.7,-0.4zM350.2,288.5 L348.2,292c-5.4,2 -20,6.2 -41.3,9.2 -14,1.9 -22.7,3.8 -50.6,4.3a347,347 0,0 1,-94.2 -14L161,289a390,390 0,0 0,95.4 14c25.5,-0.5 36.4,-2.4 50.3,-4.3 24.8,-3.8 37.3,-8 41,-9.1v-0.2l2.6,-1z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M350.8,237.6c0.1,30 -15.3,57 -27.6,68.8a99,99 0,0 1,-67.8 28.2c-30.3,0.5 -58.8,-19.2 -66.5,-27.9a101,101 0,0 1,-27.5 -67.4c1.8,-32.8 14.7,-55.6 33.3,-71.3a100,100 0,0 1,64.2 -22.7,98 98,0 0,1 71,35.6c12.5,15.2 18,31.7 20.9,56.7M255.6,135a106,106 0,0 1,106 105.2,105.6 105.6,0 1,1 -211.4,0c-0.1,-58 47.3,-105.2 105.4,-105.2"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.9,134.5c58.2,0 105.6,47.4 105.6,105.6S314.1,345.7 256,345.7s-105.6,-47.4 -105.6,-105.6S197.8,134.5 256,134.5zM152.6,240c0,56.8 46.7,103.3 103.3,103.3S359.2,296.8 359.2,240s-46.7,-103.3 -103.3,-103.3S152.6,183.2 152.6,240"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M256,143.3a97,97 0,0 1,96.7 96.7,97 97,0 0,1 -96.7,96.8c-53,0 -96.7,-43.6 -96.7,-96.8a97,97 0,0 1,96.7 -96.7M161.6,240c0,52 42.6,94.4 94.4,94.4s94.4,-42.5 94.4,-94.4 -42.6,-94.4 -94.4,-94.4a95,95 0,0 0,-94.4 94.4"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M260.3,134h-9.1v212.3h9z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M259.3,132.8h2.3v214.7h-2.2L259.4,132.8zM250.3,132.8h2.4v214.7h-2.3z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M361.6,244.2v-7.8l-6.4,-6 -36.3,-9.6 -52.2,-5.3 -63,3.2 -44.8,10.6 -9,6.7v7.9l22.9,-10.3 54.4,-8.5h52.3l38.4,4.2 26.6,6.4z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M256,223.8c24.9,0 49,2.3 68.3,6 19.8,4 33.7,9 38.5,14.5v2.8c-5.8,-7 -24.5,-12 -39,-15 -19,-3.6 -43,-6 -67.9,-6 -26.1,0 -50.5,2.6 -69.3,6.2 -15,3 -35.1,9 -37.6,14.8v-2.9c1.3,-4 16.3,-10 37.3,-14.3 18.9,-3.7 43.3,-6.1 69.6,-6.1zM256,214.7a383,383 0,0 1,68.3 6c19.8,4 33.7,9 38.5,14.6v2.7c-5.8,-6.9 -24.5,-12 -39,-14.9 -19,-3.7 -43,-6 -67.9,-6a376,376 0,0 0,-69.2 6.2c-14.5,2.7 -35.4,8.9 -37.7,14.7v-2.8c1.4,-4 16.6,-10.3 37.3,-14.3 19,-3.7 43.3,-6.2 69.7,-6.2m-0.6,-46.2c39.3,-0.2 73.6,5.5 89.3,13.5l5.7,10c-13.6,-7.4 -50.6,-15 -94.9,-14 -36.1,0.3 -74.7,4 -94,14.4l6.8,-11.4c15.9,-8.3 53.3,-12.5 87.1,-12.5"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M256,176.7a354,354 0,0 1,61.3 4.3c16,3 31.3,7.4 33.5,9.8l1.7,3c-5.3,-3.4 -18.6,-7.3 -35.6,-10.5s-38.7,-4.3 -61,-4.2c-25.3,-0.1 -45,1.2 -61.8,4.2a109,109 0,0 0,-33.3 10.3l1.7,-3.1c6,-3 15.3,-6.7 31.1,-9.6 17.5,-3.2 37.4,-4.1 62.4,-4.2m0,-9c21.4,-0.2 42.6,1 59.1,4a96,96 0,0 1,30.6 10l2.5,4c-4.2,-4.7 -20,-9.2 -34.1,-11.6 -16.4,-2.9 -36.7,-4 -58.1,-4.2a361,361 0,0 0,-59.5 4.4,97 97,0 0,0 -29.6,9.1l2.2,-3.3c5.8,-3 15.2,-5.8 27,-8.1a357,357 0,0 1,59.9 -4.4zM308.4,284a276,276 0,0 0,-52.5 -4c-65.5,0.8 -86.6,13.5 -89.2,17.3l-5,-8c16.8,-12 52.4,-18.8 94.6,-18.2q32.9,0.5 56.6,5l-4.5,8"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.6,278.9c18.2,0.3 36,1 53.3,4.2l-1.2,2.2c-16,-3 -33.2,-4 -52,-4 -24.3,-0.2 -48.7,2.1 -70,8.2 -6.7,1.9 -17.8,6.2 -19,9.8l-1.2,-2c0.4,-2.2 7,-6.6 19.6,-10 24.4,-7 47.2,-8.3 70.5,-8.4m0.8,-9.2a327,327 0,0 1,57.3 5l-1.3,2.3a299,299 0,0 0,-56 -4.9c-24.2,0 -49.9,1.8 -73.3,8.6 -7.5,2.2 -20.6,7 -21,10.7l-1.2,-2.2c0.2,-3.4 11.5,-7.9 21.7,-10.8 23.5,-6.9 49.3,-8.6 73.8,-8.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m349.4,290.5 l-7.8,12.3 -22.7,-20.1 -58.6,-39.5 -66.2,-36.3 -34.3,-11.7 7.3,-13.6 2.5,-1.3 21.3,5.3 70.4,36.3 40.6,25.6L336,272l13.9,16z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.6,195.5c6,-4 50.2,15.6 96.6,43.6 46.1,28 90.3,59.6 86.3,65.5l-1.3,2.1 -0.6,0.5c0.1,-0.1 0.8,-1 0,-3.1 -2,-6.5 -33.4,-31.5 -85.3,-62.9 -50.7,-30.1 -92.9,-48.3 -97,-43.1zM351,290.4c3.8,-7.6 -37.2,-38.5 -88.1,-68.6 -52,-29.5 -89.6,-46.9 -96.5,-41.7L165,183q-0.1,0 0.4,-0.5c1.2,-1 3.3,-1 4.2,-1 11.8,0.2 45.5,15.7 92.8,42.8 20.8,12 87.6,55 87.3,67 0,1 0.1,1.2 -0.3,1.8l1.7,-2.6z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ff0"
      android:fillType="evenOdd"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.64,251.77a62.93,62.93 0,0 0,18.67 44.48,62.93 62.93,99.4 0,0 44.59,18.77 62.93,62.93 0,0 0,44.8 -18.56,62.93 62.93,0 0,0 18.56,-44.59l0,-84.48l-126.61,-0.21z"
      android:strokeWidth="0.75"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M195.2,251.87a59.73,59.73 0,0 0,17.92 42.67,60.8 60.8,119.49 0,0 42.88,17.92 60.8,60.8 119.77,0 0,42.88 -17.71,59.73 59.73,0 0,0 17.81,-42.67l0,-82.13L195.2,169.95l0,81.92m97.07,-57.28l0,52.16l-0.11,5.44a35.2,35.2 115.28,0 1,-10.67 25.6,36.27 36.27,61.46 0,1 -25.6,10.67c-10.03,0 -18.88,-4.27 -25.49,-10.88a36.27,36.27 85.13,0 1,-10.67 -25.6l0,-57.6z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M202.88,191.39c0.11,-5.87 4.27,-7.25 4.27,-7.25 0.11,0 4.59,1.49 4.59,7.36z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m199.25,184.25 l-0.75,6.72l4.48,0c0,-5.55 4.27,-6.4 4.27,-6.4 0.11,0 4.27,1.17 4.37,6.4l4.48,0l-0.85,-6.83zM198.19,191.07l18.13,0q0.53,0 0.64,0.75 0,0.85 -0.64,0.85l-18.13,0q-0.53,0 -0.64,-0.85 0,-0.75 0.75,-0.75z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M204.8,190.97c0,-3.52 2.45,-4.48 2.45,-4.48s2.45,1.07 2.45,4.48L204.8,190.97m-6.19,-9.6l17.39,0q0.53,0.11 0.64,0.85 0,0.53 -0.64,0.64l-17.39,0q-0.53,0 -0.64,-0.75 0,-0.53 0.64,-0.64zM199.04,182.97L215.47,182.97q0.53,0 0.64,0.75t-0.64,0.75l-16.53,0q-0.64,0 -0.64,-0.75t0.64,-0.75zM204.37,171.66l1.28,0l0,0.85l0.96,0l0,-0.85l1.39,0l0,0.96l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.69,0q-0.53,0 -0.64,-0.53zM209.28,174.54 L209.6,181.37l-4.59,0l0.32,-6.93l3.95,0"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M203.73,177.74l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M215.04,177.74l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M198.72,174.97l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M210.03,174.97l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M206.83,176.67c0,-0.64 0.96,-0.64 0.96,0l0,1.71l-0.96,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M201.17,179.02c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M212.48,179.02c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M202.88,240.78c0.11,-5.87 4.27,-7.25 4.27,-7.25 0.11,0 4.59,1.49 4.59,7.36z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m199.25,233.63l-0.75,6.72l4.48,0c0,-5.55 4.27,-6.4 4.27,-6.4 0.11,0 4.27,1.17 4.37,6.4l4.48,0l-0.85,-6.83zM198.19,240.46l18.13,0q0.53,0 0.64,0.75 0,0.85 -0.64,0.85l-18.13,0q-0.53,0 -0.64,-0.85 0,-0.75 0.75,-0.75z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M204.8,240.35c0,-3.52 2.45,-4.48 2.45,-4.48s2.45,1.07 2.45,4.48L204.8,240.35m-6.19,-9.6l17.39,0q0.53,0.11 0.64,0.85 0,0.53 -0.64,0.64l-17.39,0q-0.53,0 -0.64,-0.75 0,-0.53 0.64,-0.64zM199.04,232.35L215.47,232.35q0.53,0 0.64,0.75t-0.64,0.75l-16.53,0q-0.64,0 -0.64,-0.75t0.64,-0.75zM204.37,221.05l1.28,0l0,0.85l0.96,0l0,-0.85l1.39,0l0,0.96l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.69,0q-0.53,0 -0.64,-0.53zM209.28,223.93L209.6,230.75l-4.59,0l0.32,-6.93l3.95,0"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M203.73,227.13l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M215.04,227.13l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M198.72,224.35l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M210.03,224.35l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M206.83,226.06c0,-0.64 0.96,-0.64 0.96,0l0,1.71l-0.96,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M201.17,228.41c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M212.48,228.41c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M222.13,292.25c-4.09,-4.21 -2.14,-8.14 -2.14,-8.14 0.08,-0.08 4.29,-2.2 8.45,1.93z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m214.5,289.79l4.24,5.26l3.16,-3.18c-3.94,-3.91 -1.53,-7.54 -1.53,-7.54 0.08,-0.08 3.84,-2.2 7.62,1.41l3.16,-3.18l-5.45,-4.2zM218.6,295.36l12.78,-12.87q0.38,-0.38 0.98,0.07 0.61,0.6 0.15,1.06l-12.78,12.87q-0.38,0.38 -1.06,-0.15 -0.53,-0.53 -0,-1.06z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M223.18,290.59c-2.5,-2.48 -1.45,-4.9 -1.45,-4.9s2.49,-0.99 4.91,1.42L223.18,290.59m-11.17,-2.37l12.25,-12.34q0.45,-0.3 1.06,0.15 0.38,0.38 0,0.91l-12.25,12.34q-0.38,0.38 -0.98,-0.07 -0.38,-0.38 -0,-0.91zM213.45,289.04L225.02,277.38q0.38,-0.38 0.98,0.07t0.08,0.98l-11.65,11.73q-0.45,0.45 -0.98,-0.07t-0.08,-0.98zM209.18,277.29l0.9,-0.91l0.61,0.6l0.68,-0.68l-0.61,-0.6l0.98,-0.98l0.68,0.68l0.68,-0.68l-0.76,-0.75l0.9,-0.91l1.51,1.5q0.45,0.45 0.08,0.83l-3.31,3.33q-0.38,0.38 -0.83,0.08zM214.68,275.84L219.75,280.42l-3.23,3.25l-4.69,-5.11l2.78,-2.8"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M213.04,282.03l2.57,2.56l-3.01,3.03l-2.57,-2.56z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M221.01,274l2.57,2.56l-3.01,3.03l-2.57,-2.56z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M207.54,283.63l0.9,-0.91l0.76,0.75l0.68,-0.68l-0.76,-0.75l0.9,-0.91l0.76,0.75l0.68,-0.68l-0.76,-0.75l0.9,-0.91l1.51,1.5q0.45,0.45 0.08,0.83l-3.23,3.25a1.07,1.07 89.81,0 1,-0.91 0z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M215.51,275.61l0.9,-0.91l0.76,0.75l0.68,-0.68l-0.76,-0.75l0.9,-0.91l0.76,0.75l0.68,-0.68l-0.76,-0.75l0.9,-0.91l1.51,1.5q0.45,0.45 0.08,0.83l-3.23,3.25a1.07,1.07 89.81,0 1,-0.91 0z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M214.47,279.08c-0.45,-0.45 0.22,-1.13 0.68,-0.68l1.21,1.2l-0.68,0.68z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M212.15,284.74c-0.45,-0.45 0.15,-1.06 0.6,-0.61l0.91,0.9l-0.6,0.61z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M220.12,276.72c-0.45,-0.45 0.15,-1.06 0.6,-0.61l0.91,0.9l-0.6,0.61z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M251.63,191.39c0.11,-5.87 4.27,-7.25 4.27,-7.25 0.11,0 4.59,1.49 4.59,7.36z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m248,184.25l-0.75,6.72l4.48,0c0,-5.55 4.27,-6.4 4.27,-6.4 0.11,0 4.27,1.17 4.37,6.4l4.48,0l-0.85,-6.83zM246.93,191.07l18.13,0q0.53,0 0.64,0.75 0,0.85 -0.64,0.85l-18.13,0q-0.53,0 -0.64,-0.85 0,-0.75 0.75,-0.75z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M253.55,190.97c0,-3.52 2.45,-4.48 2.45,-4.48s2.45,1.07 2.45,4.48L253.55,190.97m-6.19,-9.6l17.39,0q0.53,0.11 0.64,0.85 0,0.53 -0.64,0.64l-17.39,0q-0.53,0 -0.64,-0.75 0,-0.53 0.64,-0.64zM247.79,182.97L264.21,182.97q0.53,0 0.64,0.75t-0.64,0.75l-16.53,0q-0.64,0 -0.64,-0.75t0.64,-0.75zM253.12,171.66l1.28,0l0,0.85l0.96,0l0,-0.85l1.39,0l0,0.96l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.69,0q-0.53,0 -0.64,-0.53zM258.03,174.54L258.35,181.37l-4.59,0l0.32,-6.93l3.95,0"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M252.48,177.74l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M263.79,177.74l0,3.63l-4.27,0l0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M247.47,174.97l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M258.77,174.97l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,1.07l0.96,0l0,-1.07l1.28,0l0,2.13q0,0.64 -0.53,0.64l-4.59,0a1.07,1.07 0,0 1,-0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M255.57,176.67c0,-0.64 0.96,-0.64 0.96,0l0,1.71l-0.96,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M249.92,179.02c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M261.23,179.02c0,-0.64 0.85,-0.64 0.85,0l0,1.28l-0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M308.91,191.39c-0.11,-5.87 -4.27,-7.25 -4.27,-7.25 -0.11,0 -4.59,1.49 -4.59,7.36z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m312.53,184.25l0.75,6.72l-4.48,0c-0,-5.55 -4.27,-6.4 -4.27,-6.4 -0.11,0 -4.27,1.17 -4.37,6.4l-4.48,0l0.85,-6.83zM313.6,191.07l-18.13,0q-0.53,0 -0.64,0.75 -0,0.85 0.64,0.85l18.13,0q0.53,0 0.64,-0.85 -0,-0.75 -0.75,-0.75z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M306.99,190.97c-0,-3.52 -2.45,-4.48 -2.45,-4.48s-2.45,1.07 -2.45,4.48L306.99,190.97m6.19,-9.6l-17.39,0q-0.53,0.11 -0.64,0.85 -0,0.53 0.64,0.64l17.39,0q0.53,0 0.64,-0.75 -0,-0.53 -0.64,-0.64zM312.75,182.97L296.32,182.97q-0.53,0 -0.64,0.75t0.64,0.75l16.53,0q0.64,0 0.64,-0.75t-0.64,-0.75zM307.41,171.66l-1.28,0l-0,0.85l-0.96,0l-0,-0.85l-1.39,0l-0,0.96l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.69,0q0.53,0 0.64,-0.53zM302.51,174.54L302.19,181.37l4.59,0l-0.32,-6.93l-3.95,0"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M308.05,177.74l-0,3.63l4.27,0l-0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M296.75,177.74l-0,3.63l4.27,0l-0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M313.07,174.97l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.59,0a1.07,1.07 0,0 0,0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M301.76,174.97l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.59,0a1.07,1.07 0,0 0,0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M304.96,176.67c-0,-0.64 -0.96,-0.64 -0.96,0l-0,1.71l0.96,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M310.61,179.02c-0,-0.64 -0.85,-0.64 -0.85,0l-0,1.28l0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M299.31,179.02c-0,-0.64 -0.85,-0.64 -0.85,0l-0,1.28l0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M308.91,240.78c-0.11,-5.87 -4.27,-7.25 -4.27,-7.25 -0.11,0 -4.59,1.49 -4.59,7.36z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m312.53,233.63l0.75,6.72l-4.48,0c-0,-5.55 -4.27,-6.4 -4.27,-6.4 -0.11,0 -4.27,1.17 -4.37,6.4l-4.48,0l0.85,-6.83zM313.6,240.46l-18.13,0q-0.53,0 -0.64,0.75 -0,0.85 0.64,0.85l18.13,0q0.53,0 0.64,-0.85 -0,-0.75 -0.75,-0.75z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M306.99,240.35c-0,-3.52 -2.45,-4.48 -2.45,-4.48s-2.45,1.07 -2.45,4.48L306.99,240.35m6.19,-9.6l-17.39,0q-0.53,0.11 -0.64,0.85 -0,0.53 0.64,0.64l17.39,0q0.53,0 0.64,-0.75 -0,-0.53 -0.64,-0.64zM312.75,232.35L296.32,232.35q-0.53,0 -0.64,0.75t0.64,0.75l16.53,0q0.64,0 0.64,-0.75t-0.64,-0.75zM307.41,221.05l-1.28,0l-0,0.85l-0.96,0l-0,-0.85l-1.39,0l-0,0.96l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.69,0q0.53,0 0.64,-0.53zM302.51,223.93L302.19,230.75l4.59,0l-0.32,-6.93l-3.95,0"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M308.05,227.13l-0,3.63l4.27,0l-0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M296.75,227.13l-0,3.63l4.27,0l-0,-3.63z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M313.07,224.35l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.59,0a1.07,1.07 0,0 0,0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M301.76,224.35l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,1.07l-0.96,0l-0,-1.07l-1.28,0l-0,2.13q-0,0.64 0.53,0.64l4.59,0a1.07,1.07 0,0 0,0.64 -0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M304.96,226.06c-0,-0.64 -0.96,-0.64 -0.96,0l-0,1.71l0.96,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M310.61,228.41c-0,-0.64 -0.85,-0.64 -0.85,0l-0,1.28l0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M299.31,228.41c-0,-0.64 -0.85,-0.64 -0.85,0l-0,1.28l0.85,0z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M289.66,292.25c4.09,-4.21 2.14,-8.14 2.14,-8.14 -0.08,-0.08 -4.29,-2.2 -8.45,1.93z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m297.28,289.79l-4.24,5.26l-3.16,-3.18c3.94,-3.91 1.53,-7.54 1.53,-7.54 -0.08,-0.08 -3.84,-2.2 -7.62,1.41l-3.16,-3.18l5.45,-4.2zM293.19,295.36l-12.78,-12.87q-0.38,-0.38 -0.98,0.07 -0.61,0.6 -0.15,1.06l12.78,12.87q0.38,0.38 1.06,-0.15 0.53,-0.53 0,-1.06z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M288.61,290.59c2.5,-2.48 1.45,-4.9 1.45,-4.9s-2.49,-0.99 -4.91,1.42L288.61,290.59m11.17,-2.37l-12.25,-12.34q-0.45,-0.3 -1.06,0.15 -0.38,0.38 -0,0.91l12.25,12.34q0.38,0.38 0.98,-0.07 0.38,-0.38 0,-0.91zM298.34,289.04L286.77,277.38q-0.38,-0.38 -0.98,0.07t-0.08,0.98l11.65,11.73q0.45,0.45 0.98,-0.07t0.08,-0.98zM302.61,277.29l-0.9,-0.91l-0.61,0.6l-0.68,-0.68l0.61,-0.6l-0.98,-0.98l-0.68,0.68l-0.68,-0.68l0.76,-0.75l-0.9,-0.91l-1.51,1.5q-0.45,0.45 -0.08,0.83l3.31,3.33q0.38,0.38 0.83,0.08zM297.11,275.84L292.04,280.42l3.23,3.25l4.69,-5.11l-2.78,-2.8"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M298.74,282.03l-2.57,2.56l3.01,3.03l2.57,-2.56z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M290.78,274l-2.57,2.56l3.01,3.03l2.57,-2.56z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M304.24,283.63l-0.9,-0.91l-0.76,0.75l-0.68,-0.68l0.76,-0.75l-0.9,-0.91l-0.76,0.75l-0.68,-0.68l0.76,-0.75l-0.9,-0.91l-1.51,1.5q-0.45,0.45 -0.08,0.83l3.23,3.25a1.07,1.07 90.19,0 0,0.91 0z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M296.28,275.61l-0.9,-0.91l-0.76,0.75l-0.68,-0.68l0.76,-0.75l-0.9,-0.91l-0.76,0.75l-0.68,-0.68l0.76,-0.75l-0.9,-0.91l-1.51,1.5q-0.45,0.45 -0.08,0.83l3.23,3.25a1.07,1.07 90.19,0 0,0.91 0z"
      android:strokeWidth="0.53"
      android:fillColor="#ff0"
      android:strokeColor="#000"/>
  <path
      android:pathData="M297.32,279.08c0.45,-0.45 -0.22,-1.13 -0.68,-0.68l-1.21,1.2l0.68,0.68z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M299.64,284.74c0.45,-0.45 -0.15,-1.06 -0.6,-0.61l-0.91,0.9l0.6,0.61z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M291.67,276.72c0.45,-0.45 -0.15,-1.06 -0.6,-0.61l-0.91,0.9l0.6,0.61z"
      android:strokeWidth="0.53"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M248.11,242.59a8.53,8.53 0,0 0,2.35 6.08,7.47 7.47,0 0,0 5.65,2.56q3.41,-0.21 5.65,-2.56a8.53,8.53 73.71,0 0,2.35 -6.08l0,-11.52l-16,0z"
      android:fillColor="#039"/>
  <path
      android:pathData="M251.84,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M256.21,239.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M251.84,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M248.11,214.86a8.53,8.53 0,0 0,2.35 6.08,7.47 7.47,0 0,0 5.65,2.56q3.41,-0.21 5.65,-2.56a8.53,8.53 73.71,0 0,2.35 -6.08l0,-11.52l-16,0z"
      android:fillColor="#039"/>
  <path
      android:pathData="M251.84,207.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,207.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M256.21,211.98m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M251.84,216.46m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,216.46m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M225.92,242.59a8.53,8.53 0,0 0,2.35 6.08,7.47 7.47,0 0,0 5.65,2.56q3.41,-0.21 5.65,-2.56a8.53,8.53 0,0 0,2.35 -6.08l0,-11.52l-16,0z"
      android:fillColor="#039"/>
  <path
      android:pathData="M229.65,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M238.51,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M234.03,239.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M229.65,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M238.51,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M270.29,242.59a8.53,8.53 0,0 0,2.35 6.08,7.47 7.47,0 0,0 5.65,2.56q3.41,-0.21 5.65,-2.56a8.53,8.53 0,0 0,2.35 -6.08l0,-11.52l-16,0z"
      android:fillColor="#039"/>
  <path
      android:pathData="M274.03,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M282.88,235.45m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M278.4,239.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M274.03,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M282.88,244.19m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M248.11,270.11a8.53,8.53 0,0 0,2.35 6.08,7.47 7.47,0 0,0 5.65,2.56q3.41,-0.21 5.65,-2.56a8.53,8.53 73.71,0 0,2.35 -6.08l0,-11.52l-16,0z"
      android:fillColor="#039"/>
  <path
      android:pathData="M251.84,262.97m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,262.97m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M256.21,267.23m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M251.84,271.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
  <path
      android:pathData="M260.69,271.71m-1.6,0a1.6,1.6 0,1 1,3.2 0a1.6,1.6 0,1 1,-3.2 0"
      android:fillColor="#fff"/>
</vector>
