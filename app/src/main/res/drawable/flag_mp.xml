<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M0,0h640v480H0z"/>
    <path
        android:pathData="M-160,0h960v480h-960z"
        android:fillColor="#0071bc"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M370,365.7s15.2,-16.1 29.8,-13c4.4,-17 21,-18.4 21,-18.4s-1.7,-18.7 19.5,-22.8c0.6,-12.7 13.5,-23.3 13.5,-23.3s-2.6,-19.7 10.8,-24.3c-8.2,-15.6 2.8,-27 2.7,-27.3 -0.2,-0.3 -14,-24.8 -1.6,-31.8 -13,-11 -8.8,-23.4 -8.8,-23.4s-14.2,-3.7 -9.1,-20.7c-11.7,-2 -14,-20.9 -14,-20.9s-18,3.7 -20.2,-12c-12,1.9 -13.2,-9.8 -13.4,-9.8 -0.1,0 -23.5,6.3 -28.2,-11.4 -9.1,4.7 -13,-3.7 -13,-3.7s-13,6.5 -20.8,-6c-15.5,9.7 -24.2,-0.4 -24.2,-0.4s-20.3,13.4 -27,3c-12,12.2 -22.8,7 -22.8,7s-9,16 -23.7,12c-3.3,14.9 -20.2,15.2 -20.2,15.2s1.8,13.1 -18.4,16.7c-2.6,15.5 -13.2,18 -13.2,18s1,15.2 -8.5,20c3.4,8.5 -5.4,18.9 -5.4,18.9s9.4,11.7 -3.4,24.9c12.3,3.2 3.4,24.4 3.4,24.4s17,7.6 6.3,21.7c12.2,4 8.4,13.6 8.5,19.3 7.8,3.5 14.6,1.8 11.3,17.4 23.8,2.7 12.6,15.8 12.6,15.8s11,0.5 6.7,9.3c20.3,-0.5 23.3,15.2 23.3,15.2s18.8,-5 21.3,2.3 -8.2,56.7 -8.2,56.7 -16,0 -27.8,-12.8c-27.8,-0.7 -21.6,-19 -22,-19 -0.5,0 -9.3,3.4 -13.8,-11.9 -18.6,3.8 -18,-11.8 -18.2,-12s-8.3,-3.8 -5.3,-11.5c-19.1,1.7 -17.7,-17 -17.7,-17 -4.2,-1.7 -5.7,-5.6 -5.1,-9.5 -2.5,-0.9 -19.5,-1.2 -10.7,-23.6 -16.2,-9.8 -6.1,-21 -6.1,-21s-22.4,-11.6 -5.1,-25c-13,-19 1,-28.5 1,-28.5s-17.9,-18.1 0.3,-31c-3,-27.5 13.4,-34.1 13.4,-34.1s-8.6,-22.4 14.8,-31.8c1.5,-22.7 17.9,-23.3 17.9,-23.3s0.4,-18 26.2,-16.2c5,-16.1 22.5,-12.9 22.5,-12.9s5.4,-19.5 29.3,-10.1c12.1,-23.7 30.2,-11.6 30.2,-11.6s11.4,-7.4 17,-5c7.3,-12.5 22.8,-0.6 32.7,1.9 3.7,-1.5 16.6,-11.2 26.5,1 13.2,-8.5 24.5,7 24.5,7s18.1,-9.2 26.6,11.8c38.5,-3.5 33,21.7 33,21.7s30.2,-6.9 23.6,23.3c27.6,1.9 24.8,20 24.8,20s17.4,13.2 9.8,25c15.5,0.8 9.8,15.7 9.8,15.7s11.9,4.6 2,23c22.7,18.1 4.4,36.2 4.3,36 -0.2,-0.1 12.1,14.6 1,31.6 3.4,27 -10.2,33.8 -10.2,33.8s3.5,17.5 -11.7,22.4c-0.3,20.5 -19.7,23 -19.7,23s5.2,9 -14,18.3c-0.8,13.7 -20.8,14.8 -20.8,14.8s-1.9,25.2 -27.4,18.4c-6.1,20.2 -36,14.2 -36.6,14 -0.5,-0.3 -5.7,-43.4 -5.7,-43.5z"
        android:strokeWidth="1.9"
        android:fillColor="#fff"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M185,124s6.4,5.6 5.2,14 -6,19.9 -5,28.5c0.6,3.5 0.3,9.7 0.3,9.7s-5.7,-8.2 -5.8,-17 6.2,-17.1 6.2,-23.6 -1.1,-11.7 -1,-11.5zM181.8,126.6s-8.4,8 -9.5,15.7c-0.8,4 -1,22.7 -1.2,31 -0.2,2.5 -1.2,12.5 -1.4,16.2 -0.3,3.6 6.1,-6.2 6.5,-14.3 -0.2,-8.3 -0.2,-25.8 0.6,-28.5 0.8,-3.4 0.6,-7.1 1.8,-10.5z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.9"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M366,100.5s10.8,-5 14,-14.4 -9.5,-9.4 -11.1,-6.4c-1.7,3 1.4,10.3 0.6,12.3s-5.2,6.6 -3.5,8.4z"
        android:strokeWidth="1.9"
        android:fillColor="#ffd200"
        android:fillType="evenOdd"
        android:strokeColor="#ef8a10"/>
    <path
        android:pathData="M269.8,103.9s7,-9.7 5.2,-19.4c-1.9,-9.6 -12.8,-3.6 -12.8,-0.2s6.2,8.3 6.5,10.4 -1.3,8.4 1,9.1zM250.7,109.6s7,-9.7 5.2,-19.3 -12.8,-3.7 -12.8,-0.3 6.3,8.4 6.6,10.5 -1.3,8.3 1,9.1zM229.6,116.1s7,-9.6 5.2,-19.3c-1.9,-9.7 -12.8,-3.7 -12.8,-0.3s6.2,8.4 6.5,10.5 -1.3,8.3 1,9.1zM214.6,131s7.1,-9.7 5.3,-19.3 -12.8,-3.7 -12.8,-0.3 6.3,8.4 6.5,10.5 -1.3,8.3 1,9.1zM199,145.4s7,-9.7 5.2,-19.4 -12.8,-3.6 -12.8,-0.2 6.3,8.3 6.6,10.4 -1.3,8.4 1,9.2z"
        android:strokeWidth="2.5"
        android:fillColor="#ffd200"
        android:fillType="evenOdd"
        android:strokeColor="#ef8a10"/>
    <path
        android:pathData="M235,61.7s-9.4,7.4 -10.2,17.2 11.3,7 12.2,3.7c1,-3.3 -3.7,-9.7 -3.4,-11.8 0.3,-2 3.5,-7.7 1.4,-9zM266.3,52.1s-9.3,7.4 -10.2,17.2 11.3,7 12.3,3.7 -3.8,-9.8 -3.5,-11.9 3.5,-7.7 1.4,-9zM149.6,136.6s2,11.8 10.2,17.3 11.5,-6.6 9.1,-9 -10.3,-1.4 -12,-2.6 -5,-6.8 -7.3,-5.7zM134,176.6s0,12 7.3,18.7 12.4,-4.7 10.4,-7.4 -10,-3 -11.4,-4.5c-1.5,-1.6 -4,-7.5 -6.3,-6.7zM170,221.4s-4.2,-11.2 -13.3,-15c-9.1,-3.6 -10,8.8 -7.2,10.7 2.9,1.8 10.4,-0.7 12.3,0.2s6.3,5.6 8.3,4zM162.6,202s7.2,-9.5 5.6,-19.1 -12.7,-4 -12.8,-0.6c0,3.4 6,8.5 6.3,10.6s-1.5,8.3 0.9,9.2zM388.2,110.1s10.8,-5 14,-14.3 -9.5,-9.4 -11.1,-6.5c-1.7,3 1.4,10.4 0.6,12.3s-5.2,6.7 -3.5,8.5zM421,126.5s6.4,-10.1 3.9,-19.6 -13,-2.8 -12.8,0.6c0.2,3.3 6.8,7.9 7.2,10s-0.7,8.4 1.7,9zM510.6,219.5s-4.3,-11.1 -13.4,-14.9c-9.1,-3.7 -10,8.8 -7.2,10.6 2.9,1.9 10.5,-0.7 12.3,0.2 2,1 6.3,5.7 8.3,4.1zM466.8,226.5s0.2,-12 7.5,-18.6 12.4,4.9 10.4,7.6c-2.1,2.7 -10,2.8 -11.6,4.3 -1.4,1.5 -4,7.4 -6.3,6.6zM459.8,99.7s-11.3,-4.3 -20,0c-9,4.4 -0.2,13.4 3,12.5s6.4,-8.3 8.4,-9.1 8.4,-1 8.5,-3.4zM434,78.3s-11.1,-4.3 -20,0c-8.8,4.4 -0.1,13.4 3.2,12.5 3.2,-1 6.4,-8.3 8.3,-9.1s8.4,-1 8.5,-3.4zM396,57.9s-9.5,7.3 -10.4,17c-1,9.8 11.2,7.1 12.2,3.9 1,-3.3 -3.7,-9.8 -3.4,-11.9s3.6,-7.6 1.6,-9zM371.2,51.9s-4.4,11.1 -0.1,20 13.3,0.2 12.4,-3 -8.2,-6.5 -9,-8.4 -0.9,-8.5 -3.3,-8.6zM300.7,94.9s7.7,-9.2 6.6,-19c-1.1,-9.7 -12.5,-4.5 -12.7,-1.1 -0.3,3.4 5.6,8.8 5.7,10.9s-1.9,8.2 0.4,9.1z"
        android:strokeWidth="1.9"
        android:fillColor="#ffd200"
        android:fillType="evenOdd"
        android:strokeColor="#ef8a10"/>
    <path
        android:pathData="M342.8,114.4s18.5,1.2 19.4,11.8 -4.4,17.1 -4.4,17.1 2.3,23 -15.8,29.7c-19.4,2.3 -49.9,0.5 -49.9,0.5s-9,2.2 -12.6,-16.3a305,305 0,0 1,-4.6 -31.6s1.8,-10.4 14.9,-11c13,-0.7 52.8,0 53,-0.2z"
        android:strokeWidth="1.9"
        android:fillColor="#8c8a8c"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M357.7,143.2s-10,12.4 -9,17.5"
        android:strokeWidth="1.9"
        android:fillColor="#00000000"
        android:strokeColor="#000"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M344.3,172.3c2.5,2.1 4.8,4.4 5.3,12l1,15.4 13,116.6 11.4,84.3 0.8,13s-2.7,9.7 -10.4,10.5c-5.5,11.6 -35.7,15 -38.7,14.9 -2.7,-0.2 -12.3,-4.2 -18.1,-3.7s-16,4.4 -20.5,3.8 -14.8,-4.2 -16.6,-11.6c-12.7,-4 -14.7,-13.7 -14.7,-13.7l13.7,-97.5 13.3,-123.4s1.6,-16.4 8.2,-18.9c6.9,-0.6 42.4,0.7 52.3,-1.7z"
        android:strokeWidth="1.9"
        android:fillColor="#8c8a8c"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="m274.4,341.1 l-2.7,86.3m86.5,-74.9 l7.6,71.2"
        android:strokeWidth="1.9"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:strokeWidth="1"
        android:pathData="m136.6,296.3 l-0.3,1m358.7,-4.1 l0.3,1"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M127,179.8s5.6,7.9 6.7,12.4c1,4.5 2.9,13.4 5.3,17 2.5,3.7 16.6,23.9 17.4,32.3 0.1,5.6 -5.9,15 -14.7,15.2 -5.6,-0.2 -19.8,-3.8 -20.4,-16 -0.6,-12.3 4.3,-12.5 5,-20 0.5,-7.4 0.7,-40.7 0.7,-40.9z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.9"
        android:fillColor="#00000000"
        android:strokeColor="#6b18b5"/>
    <path
        android:strokeWidth="1"
        android:pathData="M224.5,72.8s-6,0.4 -10.4,2.9c-3.5,2.1 -9.5,5.3 -13.6,11 -10.6,8.2 -26.5,12 -31.5,19 -3,4.6 -2.8,15.7 4.7,20.5 4.8,2.7 19,7 25.8,-3.1s2.7,-13 6,-19.6a117,117 0,0 1,19 -30.7zM367,58s-4.4,-4 -9.3,-5.5c-4,-1 -10.6,-0.6 -17.5,-1.3 -13.3,-1.8 -27,-11.4 -35.3,-10.2 -5.4,1 -13.3,9 -11.5,17.6 1.3,5.4 7.3,16.2 19.4,14.1s12,-4.8 19,-7c17.4,-9.7 35,-7.7 35.2,-7.7zM508.4,195.4c1,-2.3 -0.9,-6.6 -2.2,-11.5 -1.2,-4 -3.1,-10 -7.7,-15.5 -5.4,-12.2 -4.5,-29 -10,-35.4 -3.7,-4 -14.6,-6.6 -21,-0.4 -3.9,4 -11.4,16.6 -3.2,25.7s11.8,5.7 17.4,10.5c13.5,9 20,25 26.7,26.6zM503.5,302c1.9,-2.5 5.2,-7.4 4.8,-11.5 -0.3,-3.2 1.5,-13 1.7,-17.3 1.2,-13.3 9.5,-24.2 7.2,-35.7 -1.3,-5.4 -9.2,-13 -17.8,-10.8 -5.2,1.6 -17,8 -14.3,20s6.6,11.7 9.1,18.7c7.3,14.5 2.7,31.7 9.3,36.6z"
        android:strokeLineJoin="round"
        android:fillColor="#00000000"
        android:strokeColor="#6b18b5"/>
    <path
        android:pathData="M405.5,386.8s-2.3,4.4 6.8,8.2m11.3,-18.4s10,-2.5 16.8,0m13.2,-25.4c0,0.2 -1.4,4.5 6.6,10.8m-0.8,-22s5.9,4.3 15.8,4m-75.4,8.6s0.3,11.5 -5.6,16.8m25.4,-34.5s9.7,2.4 17.7,-1.6m44.6,-15.3s2.1,3 12.9,2.5m-54.9,-9.3c0.3,0.2 12.7,4.2 20.5,0.6m4.2,-47.8s0.5,-0.7 4.3,12.7m5.2,-50.4s-0.5,7.1 -6.2,10.2m42.9,-39.4s-8.4,6.8 -6.8,9.5m-34.7,-14.4s4.3,7.3 -2.8,12.3M448.2,161s7.5,-0.3 10.8,6.1m-24.8,-27.3h5.7m24,-26c0,0.1 1.5,1.5 -1.4,4.5m-52,-3.8s4,6.2 3.3,13.9m19.8,-35.1c1.8,-0.2 7.7,-2.8 7.7,-2.8m-33.4,-22c-0.1,0 -2.2,7.7 1.6,10.5m-11.1,25c0,8.4 6.6,6.3 2,14.3M382.2,97a57,57 0,0 0,-10.4 9.4m-16.4,-13.7s4.4,4.3 3.3,10.2m-16,-13.7s-2.2,6.6 -5.1,8.3m-18,-10c-0.1,0.3 -3.6,7.8 -6,9.1m-25.6,-9c0.2,0 3.5,7 -0.8,12m-6.5,-48c0.2,0.2 0.7,5.4 -2.6,10m-28.7,1.9c0,0.1 1.4,5.9 -7.8,7.8m-9.9,45.2c0.2,0.3 8.9,1.7 8.9,1.7M210,136.6c0.2,-0.2 10.6,-3.3 10.6,-3.3m-27.4,16.3c0.3,0 7.3,1.3 9,0.4m-14,6s2.3,1.3 0.4,13m-34.2,-43.5 l4.2,6.2m-18.5,26c0,-0.2 4.2,6.2 9.8,7.4m23,25.2a49,49 0,0 0,8 -2M170,213.8c0.2,0 -0.1,-4.3 5.2,-7.3M163,224.7c0,0.2 5,6.4 8.5,7.3m-11.5,4.7c0.6,-0.2 6,-4.9 12.2,-4.5m-13.9,20.6s0.2,5.4 16.2,4m-14.6,15c0,-0.2 4.7,-10.8 14.2,-14.8m-1.4,28.4s1.4,-4.6 9.4,-7.7m-0.5,25.5s5.7,-4.7 7.8,-5.3m-0.7,19.3s3.5,3.6 12.2,-2.7m-54,5.6c1,-0.2 13.7,-3.1 18,3.5m-13,7s13.4,-1.8 15.1,-0.4m32.7,3.1s-1.8,3.7 14.7,-2.4m5.8,9c-3,5 0.7,9 -3.2,11.3m-47,-4.3c0.5,-0.4 6,-2.3 5.2,-10.3m-0.2,22.3c0.7,-0.2 9.9,-4.2 11.8,-2.1m6.3,13.7s1.2,-8.2 3.8,-9m10,21.3s7.7,-0.1 11,-2.6m3.8,-25c0.2,0.2 7.1,3.8 22,0.2m-16.4,33s2,10.6 2,13"
        android:strokeWidth="1.9"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M494,225.44c-0.41,-3.32 -2.12,-6.47 -4.25,-7.89q-3.87,-2.21 -6.76,0.84c-2.89,3.05 -2.76,5.4 -2.34,8.71q0.84,5.45 4.27,8.05 4.03,2.19 6.76,-0.84c2.73,-3.03 2.92,-5.42 2.34,-8.71z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M492.6,207.74c-0.41,-3.32 -2.12,-6.47 -4.25,-7.89q-3.87,-2.21 -6.76,0.84c-2.89,3.05 -2.76,5.4 -2.34,8.71q0.84,5.45 4.27,8.05 4.03,2.19 6.76,-0.84c2.73,-3.03 2.92,-5.42 2.34,-8.71z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M488.88,188.78c-0.7,-3.27 -2.68,-6.26 -4.93,-7.48q-4.05,-1.86 -6.66,1.44c-2.61,3.3 -2.27,5.62 -1.57,8.88q1.32,5.35 4.97,7.64 4.21,1.83 6.66,-1.44c2.45,-3.26 2.43,-5.65 1.57,-8.88z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M482.46,169.24c-1.51,-2.98 -4.18,-5.37 -6.67,-5.98q-4.39,-0.77 -6.07,3.08c-1.68,3.85 -0.77,6.01 0.75,8.99q2.64,4.84 6.75,6.13 4.54,0.69 6.07,-3.08c1.54,-3.78 0.91,-6.08 -0.75,-8.99z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M475.46,153.34c-1.51,-2.98 -4.18,-5.37 -6.67,-5.98q-4.39,-0.77 -6.07,3.08c-1.68,3.85 -0.77,6.01 0.75,8.99q2.64,4.84 6.75,6.13 4.54,0.69 6.07,-3.08c1.54,-3.78 0.91,-6.08 -0.75,-8.99z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M465.55,137.5c-1.76,-2.84 -4.62,-5 -7.16,-5.39q-4.44,-0.39 -5.79,3.59c-1.35,3.98 -0.25,6.05 1.51,8.89q3.04,4.6 7.24,5.53 4.58,0.31 5.79,-3.59c1.21,-3.89 0.39,-6.14 -1.51,-8.89z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M454.47,122.1c-2.03,-2.66 -5.08,-4.53 -7.64,-4.67q-4.46,0.04 -5.41,4.13c-0.96,4.09 0.33,6.05 2.36,8.71q3.47,4.28 7.75,4.81 4.59,-0.14 5.41,-4.13c0.83,-3.99 -0.2,-6.15 -2.36,-8.71z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M441.25,107.87c-2.39,-2.33 -5.69,-3.74 -8.24,-3.52q-4.4,0.68 -4.76,4.87c-0.35,4.19 1.21,5.94 3.6,8.27q4.06,3.74 8.36,3.63 4.52,-0.8 4.76,-4.87c0.24,-4.07 -1.09,-6.05 -3.6,-8.27z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M426.47,94.78c-2.59,-2.1 -6.01,-3.2 -8.53,-2.74q-4.32,1.09 -4.29,5.29c0.03,4.2 1.75,5.8 4.34,7.91q4.38,3.35 8.66,2.85 4.42,-1.21 4.29,-5.29c-0.14,-4.08 -1.65,-5.93 -4.34,-7.91z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M410.75,84.31c-2.91,-1.64 -6.45,-2.16 -8.87,-1.29q-4.08,1.79 -3.35,5.93c0.73,4.14 2.69,5.43 5.59,7.08q4.88,2.57 9.01,1.37 4.16,-1.93 3.35,-5.93c-0.81,-4 -2.61,-5.57 -5.59,-7.08z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M393.17,75c-3.05,-1.37 -6.62,-1.56 -8.95,-0.48q-3.9,2.15 -2.8,6.21c1.1,4.05 3.17,5.16 6.21,6.54q5.09,2.12 9.1,0.55 3.97,-2.3 2.8,-6.21c-1.17,-3.91 -3.1,-5.31 -6.21,-6.54z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M375.92,68.04c-3.13,-1.17 -6.71,-1.12 -8.96,0.12q-3.75,2.41 -2.38,6.38c1.37,3.97 3.51,4.94 6.64,6.11q5.22,1.77 9.12,-0.07 3.81,-2.56 2.38,-6.38c-1.43,-3.82 -3.45,-5.09 -6.64,-6.11z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M358.39,62.6c-3.21,-0.92 -6.78,-0.6 -8.92,0.81q-3.55,2.69 -1.88,6.54c1.67,3.86 3.88,4.66 7.09,5.58q5.34,1.37 9.08,-0.76 3.6,-2.85 1.88,-6.54c-1.72,-3.7 -3.83,-4.81 -7.09,-5.58z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M339.16,59.28c-3.31,-0.42 -6.79,0.45 -8.69,2.17q-3.1,3.2 -0.85,6.76c2.25,3.55 4.55,4.01 7.86,4.42q5.49,0.53 8.86,-2.15 3.12,-3.37 0.85,-6.76c-2.27,-3.39 -4.53,-4.17 -7.86,-4.42z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M320.08,58.22c-3.34,-0.13 -6.73,1.03 -8.48,2.91q-2.81,3.46 -0.27,6.8c2.54,3.35 4.87,3.6 8.21,3.74q5.51,0.06 8.64,-2.9 2.82,-3.62 0.27,-6.8c-2.55,-3.19 -4.86,-3.77 -8.21,-3.74z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M302.58,58.82c-3.34,-0.13 -6.73,1.03 -8.48,2.91q-2.81,3.46 -0.27,6.8c2.54,3.35 4.87,3.6 8.21,3.74q5.51,0.06 8.64,-2.9 2.82,-3.62 0.27,-6.8c-2.55,-3.19 -4.86,-3.77 -8.21,-3.74z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M282.18,61.24c-3.26,0.74 -6.23,2.75 -7.42,5.02q-1.81,4.07 1.52,6.64c3.33,2.57 5.64,2.2 8.9,1.46q5.34,-1.39 7.58,-5.06 1.77,-4.23 -1.52,-6.64c-3.29,-2.41 -5.68,-2.36 -8.9,-1.46z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M263.6,65.89c-3.18,1.01 -5.98,3.26 -6.98,5.62q-1.47,4.21 2.06,6.49c3.53,2.28 5.81,1.73 8.99,0.72q5.21,-1.82 7.14,-5.67 1.42,-4.36 -2.06,-6.49c-3.48,-2.13 -5.86,-1.88 -8.99,-0.72z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M246.65,73.21c-3.04,1.38 -5.55,3.95 -6.26,6.41q-0.96,4.35 2.82,6.2c3.78,1.85 5.97,1.03 9.01,-0.36q4.95,-2.43 6.41,-6.48 0.89,-4.5 -2.82,-6.2c-3.71,-1.7 -6.04,-1.17 -9.01,0.36z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M229.73,81.86c-2.91,1.64 -5.19,4.41 -5.69,6.92q-0.58,4.42 3.34,5.94c3.92,1.52 6.04,0.51 8.95,-1.12q4.73,-2.84 5.84,-7 0.5,-4.56 -3.34,-5.94c-3.84,-1.38 -6.12,-0.65 -8.95,1.12z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M213.66,92.75c-2.72,1.94 -4.68,4.94 -4.91,7.5q-0.1,4.46 3.96,5.54c4.06,1.08 6.06,-0.15 8.77,-2.09q4.39,-3.34 5.04,-7.59 0,-4.59 -3.96,-5.54c-3.97,-0.95 -6.15,0.01 -8.77,2.09z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M198.9,103.97c-2.54,2.17 -4.24,5.32 -4.25,7.89q0.28,4.45 4.42,5.18c4.14,0.73 6.02,-0.66 8.57,-2.83q4.09,-3.7 4.38,-7.99 -0.38,-4.57 -4.42,-5.18c-4.03,-0.61 -6.13,0.54 -8.57,2.83z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M185.8,117.64c-2.38,2.34 -3.85,5.61 -3.68,8.17q0.6,4.42 4.78,4.85c4.18,0.44 5.96,-1.09 8.34,-3.43q3.82,-3.98 3.8,-8.29 -0.71,-4.53 -4.78,-4.85c-4.07,-0.32 -6.07,0.97 -8.34,3.43z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M174.54,132.88c-2.18,2.53 -3.37,5.91 -2.98,8.45q0.96,4.35 5.17,4.44c4.2,0.08 5.85,-1.58 8.02,-4.12q3.47,-4.29 3.09,-8.58 -1.09,-4.46 -5.17,-4.44c-4.08,0.02 -5.97,1.48 -8.02,4.12z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M164.23,148.37c-1.89,2.75 -2.72,6.24 -2.06,8.72q1.42,4.22 5.61,3.86c4.19,-0.36 5.65,-2.2 7.54,-4.95q2.99,-4.63 2.16,-8.86 -1.56,-4.32 -5.61,-3.86c-4.05,0.46 -5.78,2.11 -7.54,4.95z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M155.69,165.94c-1.39,3.04 -1.61,6.61 -0.53,8.94q2.13,3.92 6.19,2.84c4.06,-1.08 5.18,-3.14 6.58,-6.17q2.15,-5.08 0.6,-9.1 -2.28,-3.98 -6.19,-2.84c-3.91,1.15 -5.33,3.07 -6.58,6.17z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M148.92,184.43c-1.11,3.15 -1,6.73 0.28,8.96q2.47,3.71 6.42,2.27c3.95,-1.44 4.88,-3.59 5.99,-6.74q1.68,-5.25 -0.22,-9.11 -2.63,-3.76 -6.42,-2.27c-3.79,1.5 -5.03,3.54 -5.99,6.74z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M143.02,219.37c-0.31,2.67 0.67,5.43 2.46,6.91q3.3,2.4 6.78,0.5c3.48,-1.89 3.85,-3.75 4.16,-6.42q0.35,-4.41 -2.44,-7.04 -3.47,-2.41 -6.78,-0.5c-3.31,1.91 -4.01,3.74 -4.16,6.42z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M144.56,203.09c-0.74,3.26 -0.22,6.8 1.31,8.86q2.88,3.4 6.64,1.51c3.76,-1.89 4.43,-4.13 5.18,-7.39q1.07,-5.41 -1.27,-9.03 -3.04,-3.43 -6.64,-1.51c-3.6,1.92 -4.59,4.09 -5.18,7.39z"
        android:strokeWidth="1.5"
        android:fillColor="#de2010"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M480,194.7q3.1,-3.5 6.7,-2.1c-1,-6.1 -2.8,-8.2 -5.2,-7.4 -2.3,0.7 -3.4,3.8 -1.5,9.5m3.3,18.2c2.3,-2.1 4.3,-2.6 6.8,-1.6 -0.4,-6 -2.2,-8.3 -4.6,-7.7 -2.4,0.5 -3.6,3.6 -2.2,9.3m-7.8,-35q2.1,-4 6,-3.7c-2.5,-5.6 -4.8,-7.1 -7,-5.8 -2,1.3 -2.2,4.6 1,9.6zM467.4,161.3q2.1,-4.1 6,-3.7c-2.5,-5.7 -4.9,-7.2 -7,-5.9s-2.2,4.6 1,9.6m-19.5,-30.5q1.4,-4.4 5.2,-4.6c-3.3,-5.2 -5.9,-6.3 -7.7,-4.7s-1.5,5 2.5,9.3M435,117.5c1,-3 2.4,-4.4 5,-4.8 -3.4,-5 -6,-6 -7.8,-4.4 -1.8,1.7 -1.4,5 2.8,9.2M408.3,95c0,-3.2 1,-5 3.3,-6.2 -4.8,-3.7 -7.7,-3.8 -8.8,-1.7s0.2,5.1 5.5,7.9m14,10.5q0.5,-4.5 4.3,-5.5c-4.2,-4.5 -6.9,-5.2 -8.4,-3.3s-0.7,5.1 4,8.8zM392.3,86.2c-0.3,-3.1 0.5,-5 2.7,-6.5 -5.2,-3.2 -8,-3 -9,-0.8s0.8,5.1 6.4,7.3zM376.2,79c-0.8,-3 -0.3,-5 1.7,-6.8 -5.6,-2.4 -8.4,-1.9 -9,0.5s1.5,4.9 7.3,6.3m-17.5,-5.4c-1.1,-3 -0.8,-5 1.1,-7 -5.8,-1.9 -8.5,-1.1 -9,1.3 -0.3,2.4 2,4.8 7.9,5.7m-17.3,-3.4q-1.8,-4.1 0.7,-7c-6,-1.5 -8.6,-0.5 -8.8,2 -0.3,2.4 2.2,4.6 8.1,5m-18.2,-1.4q-2.4,-3.9 0,-7c-6.1,-0.9 -8.7,0.4 -8.6,2.8 0,2.5 2.7,4.4 8.6,4.2m-35.7,2.7q-3.2,-3.2 -1.8,-6.8c-6,0.6 -8.2,2.4 -7.6,4.8s3.7,3.6 9.4,2m17.6,-2.3q-2.8,-3.6 -0.9,-7c-6.1,-0.2 -8.5,1.3 -8.2,3.8 0.3,2.4 3.2,4 9,3.2zM270.2,75.2q-3.6,-2.7 -2.7,-6.4c-6,1.5 -7.8,3.6 -6.9,5.8 1,2.3 4.2,3 9.6,0.6m-33,15.2q-4.1,-2 -3.9,-5.8c-5.6,2.6 -7,5 -5.6,7 1.3,2 4.6,2.2 9.5,-1.2m16,-8.1q-4,-2.4 -3.2,-6.2c-5.9,1.8 -7.6,4 -6.5,6.2s4.4,2.8 9.6,0zM207.6,111.1q-4.4,-1 -5,-5c-5,3.8 -5.8,6.4 -4.1,8.1s5,1.2 9,-3.1zM221.7,99.5q-4.4,-1.4 -4.6,-5.2c-5.2,3.2 -6.3,5.8 -4.7,7.7 1.6,1.8 5,1.5 9.3,-2.5m-26.7,24q-4.4,-0.7 -5.4,-4.5c-4.6,4.1 -5.2,6.9 -3.3,8.4 1.9,1.6 5,0.7 8.7,-4zM183.7,138q-4.7,-0.2 -5.8,-3.8c-4.2,4.5 -4.6,7.2 -2.6,8.6s5.1,0.2 8.4,-4.8m-9.4,15c-3.2,0.2 -5,-0.7 -6.4,-3 -3.4,5.1 -3.4,8 -1.2,9s5.1,-0.6 7.6,-6m-8.3,16q-4.3,1 -6.7,-2.2c-2.7,5.5 -2.3,8.3 0,9 2.3,0.8 5,-1.2 6.7,-6.9zM159.4,185.3c-3,1.1 -5,0.8 -7,-1 -1.8,5.8 -1,8.5 1.5,8.9 2.4,0.3 4.7,-2 5.5,-8zM154.9,203.5q-4,2 -7,-0.5c-1.4,6 -0.4,8.5 2,8.7 2.5,0.3 4.7,-2.2 5,-8.2m-1.8,15.2q-3.7,2.1 -7,0.5c-0.7,4.9 0.6,6.8 3,6.6s4.3,-2.4 4,-7.1m307,-72.2q1.5,-4.4 5.3,-4.6c-3.2,-5.2 -5.7,-6.3 -7.6,-4.8 -1.9,1.6 -1.6,5 2.3,9.4m25.2,85q3.3,-3.3 6.8,-1.6c-0.4,-6.1 -2.2,-8.3 -4.6,-7.8s-3.6,3.6 -2.2,9.4"
        android:fillColor="#ffe300"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M461.3,132s-7,6.9 -5.7,15.2 6.5,18.6 5.5,27.3 -2.5,14.3 -2.5,14.3 8,-9.1 8,-18c0.2,-8.7 -5.4,-20.3 -5.4,-26.7s0.3,-12.2 0.1,-12zM474,160s-5.3,4 -4.8,13 10,25.8 10,25.8 6.8,14 6.5,17.7 1.3,-3.5 0.6,-11.5 -11,-25.8 -11,-25.8 -2.2,-3.6 -2,-9.7 0.8,-9.4 0.8,-9.5z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.9"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M313,47.4c0,-1.8 -2.2,-3.4 -4.8,-3.4s-4.7,1.6 -4.7,3.4 2,3.4 4.7,3.4 4.7,-1.5 4.7,-3.4zM315,55.4c0,-1.8 -2.2,-3.3 -5,-3.3 -2.6,0 -4.8,1.4 -4.8,3.2s2.2,3.3 4.9,3.3 4.8,-1.5 4.8,-3.3zM321,46s-5.6,4.1 -3.3,5.8 8.6,-3.6 8.6,-3.6zM484.3,141.4q0,-1.9 -1.4,-2.9a3,3 0,0 0,-3 0q-1.4,1 -1.4,2.9c0,1.9 0.5,2.3 1.4,2.9a3,3 0,0 0,3 0q1.4,-1 1.4,-2.9m-2.1,-8c0,-2 -1.4,-3.5 -3,-3.5s-3.1,1.6 -3.1,3.6 1.4,3.5 3,3.5 3.1,-1.6 3.1,-3.5zM483.7,149.6q-0.2,-1.7 -1.2,-1.8c-1,-0.1 -1.2,0.8 -1.2,1.8s0.5,1.9 1.2,1.9 1.2,-0.9 1.2,-1.9m4.9,-14.4q-0.2,-2 -1.2,-2.1 -1.1,0.2 -1.2,2c-0.1,1.8 0.5,2.2 1.2,2.2s1.2,-1 1.2,-2.1m-13.4,1.4q-0.2,-1.6 -1.2,-1.7c-1,-0.1 -1.2,0.8 -1.2,1.7s0.5,1.8 1.2,1.8 1.2,-0.8 1.2,-1.8m2.1,11.4c0,-1.5 -1.1,-2.7 -2.5,-2.7s-2.5,1.2 -2.5,2.7 1,2.8 2.5,2.8 2.5,-1.2 2.5,-2.8m-6.6,-5c0,-1.3 -1,-2.4 -2.1,-2.4s-2.2,1 -2.2,2.4 1,2.4 2.2,2.4q1.8,-0.1 2,-2.4zM469.7,136.2a2,2 0,0 0,-1 -1.7,2 2,0 0,0 -1.9,0 2,2 0,0 0,-0.9 1.7,2 2,0 0,0 1,1.8 2,2 0,0 0,1.8 0q1,-0.6 1,-1.8m30.1,34.4c-2.7,-3.6 -7.1,-5.1 -9.8,-3.4 -2.6,1.7 -2.6,6 0.2,9.7 2.7,3.6 7,5.1 9.7,3.4 2.7,-1.8 2.7,-6 0,-9.7zM507.6,237.3c0.5,-4.5 -1.6,-8.4 -4.8,-8.8 -3.2,-0.3 -6.2,3 -6.8,7.4s1.7,8.3 4.9,8.7 6.2,-3 6.7,-7.3m1.6,38.8c1,-4.4 -0.9,-8.5 -4,-9.2 -3.2,-0.6 -6.5,2.4 -7.4,6.8 -0.9,4.3 1,8.4 4,9 3.2,0.7 6.5,-2.3 7.4,-6.7zM516.7,239.7c0,-1.7 -1.2,-3.1 -2.6,-3.1s-2.7,1.4 -2.7,3.1 1.2,3.2 2.7,3.2 2.6,-1.4 2.6,-3.2m-6.5,7.3 l-5.4,6.2s-5.1,0.1 -4.8,1.8 7,6.6 7,8.6c-0.1,2 4.4,-0.5 4.4,-0.5l3.7,-11.1s-0.2,-7.7 -2.2,-7.6 -2.5,2.5 -2.7,2.6m-357.4,-2.4a2.6,2.6 0,1 0,-5.1 0,2.6 2.6,0 0,0 5.1,0m2.5,-8c0,-1.4 -1.2,-2.6 -2.6,-2.6s-2.6,1.2 -2.6,2.7 1.2,2.7 2.6,2.7a3,3 0,0 0,2.6 -2.7zM134,199.2c-1,-2.2 -3,-3.4 -4.5,-2.7s-2,3 -1,5.2 3,3.4 4.5,2.7c1.6,-0.7 2,-3 1,-5.2m6.5,23.4c-1.4,-2.8 -4.1,-4.4 -6.2,-3.5s-2.7,3.9 -1.4,6.7 4,4.4 6.2,3.5 2.7,-3.9 1.4,-6.7m6.8,17.2c0.5,-3.5 -1,-6.8 -3.2,-7.2s-4.4,2 -4.9,5.5 1,6.7 3.3,7.2 4.4,-2 4.8,-5.5m-11,2.5c1.4,-3.3 0.9,-6.8 -1.1,-7.8s-4.8,0.7 -6.2,4 -0.9,6.7 1.2,7.8 4.7,-0.7 6.1,-4"
        android:fillColor="#6b18b5"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M131.3,234.8c0,-1.7 -1.3,-3 -2.9,-3s-2.9,1.4 -2.9,3 1.3,3 3,3 2.8,-1.3 2.8,-3m-2.3,-7.5c0,-1.5 -1.2,-2.7 -2.6,-2.7a2.7,2.7 0,0 0,-2.6 2.8c0,1.4 1.2,2.7 2.6,2.7s2.6,-1.3 2.6,-2.8m2.4,-14.3c0,-2.6 -1.1,-4.6 -2.5,-4.6s-2.5,2 -2.5,4.6 1.1,4.6 2.5,4.6 2.5,-2 2.5,-4.6m7.8,-1.1c0.3,-2.5 -0.5,-4.7 -2,-5 -1.3,-0.1 -2.7,1.8 -3,4.3 -0.4,2.5 0.5,4.7 1.9,4.9s2.7,-1.7 3,-4.2zM194.2,101.9c1.1,-3.6 0,-7.2 -2.3,-8 -2.3,-0.9 -5,1.3 -6,4.9s0,7.2 2.3,8c2.3,0.9 5,-1.3 6,-4.9m-10.4,15.9c3,-2.7 4.4,-6.3 3,-8.1 -1.5,-1.9 -5.2,-1.2 -8.3,1.4s-4.3,6.2 -2.9,8c1.5,1.9 5.2,1.3 8.2,-1.3m-6,-8.8c3,-2.3 4.2,-5.4 2.8,-7s-5,-1 -8,1.2c-3.1,2.3 -4.4,5.4 -3,7s5,1 8.1,-1.2zM201.3,93.5c2,-1.6 2.9,-3.8 2,-4.9 -1,-1 -3.5,-0.7 -5.6,0.9s-2.9,3.7 -1.9,4.8 3.4,0.7 5.5,-0.8"
        android:fillColor="#6b18b5"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M189,108.5c1.4,-2.2 1.5,-4.5 0.2,-5.2s-3.5,0.4 -4.9,2.6 -1.5,4.5 -0.2,5.2 3.5,-0.5 4.9,-2.6m17.7,-9.3c2.2,-1.9 3,-4.3 2,-5.5 -1.2,-1.2 -4,-0.6 -6.2,1.2s-3,4.3 -1.9,5.5 3.9,0.6 6.1,-1.2m-32,21.6c2,0 3.6,-1.1 3.4,-2.3 0,-1.1 -1.9,-2 -4,-1.9 -2,0.1 -3.6,1.2 -3.4,2.3 0.1,1.2 2,2 4,2zM213.4,87.7c1.4,-2.2 1.5,-4.5 0.2,-5.2 -1.2,-0.7 -3.4,0.4 -4.8,2.6s-1.5,4.5 -0.2,5.2 3.4,-0.4 4.8,-2.6"
        android:fillColor="#6b18b5"
        android:fillType="evenOdd"/>
    <path
        android:strokeWidth="1"
        android:pathData="M446.8,153.4s6.4,-10.1 4,-19.7 -13,-2.7 -12.8,0.7c0.2,3.3 6.8,7.9 7.2,10s-0.8,8.3 1.6,9z"
        android:fillColor="#ffd200"
        android:fillType="evenOdd"
        android:strokeColor="#ef8a10"/>
    <path
        android:pathData="M324.9,69.3s23,0.9 23.1,15.8c0,2.3 0,5 -0.8,11.7 4.6,-2.5 7.6,-9.3 7,-13.7 0.1,-15.1 -20.5,-25.5 -29.3,-13.8z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.3"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M310.3,68.6s18.5,-3.8 18.7,11c0.1,15 -6,17.3 -6,17.3s14.3,-3 14.4,-18.1 -18.3,-21.9 -27.1,-10.2z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.3"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M373.3,390s86.8,-24.6 113,-131C471,368.1 376,403 376,403l-2.8,-13z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.5"
        android:fillColor="#f7df73"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M382.2,396.6c2.7,0 9.3,-7.4 13.1,-8.2s4.2,-4.7 -0.1,-4.8c-4.4,-0.2 -8.2,5 -8.2,5s-3.8,3.2 -7.8,3.6 -1.4,5.4 3,4.4"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M432.2,359.3s-9,5.2 -7.8,6.5 9.3,-5 9.5,-5c0.1,-0.2 3.4,-4.2 -1.7,-1.5z"
        android:strokeWidth="2.5"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"
        android:strokeColor="#8c1800"/>
    <path
        android:pathData="M400.7,383s-2.7,-1 4.2,-3.6c7,-2.7 6.5,-5.8 8.8,-7.1 2.2,-1.3 7,-4.7 8,-2.7s-5,6 -6.5,6.4c-1.4,0.4 -8.3,6.8 -10.3,7.3q-3,0.8 -4.2,-0.3"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M445.6,346c-6,6.7 -6,6.5 -6,6.5"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m454.5,335.2 l-5.7,6.9"
        android:strokeWidth="2.8"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m463.4,321 l-6.4,10.2"
        android:strokeWidth="2.5"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m470.9,306.6 l-5.3,10.5m8.6,-18 l-1.6,3.6"
        android:strokeWidth="2.3"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m478,289 l-1.4,4.4"
        android:strokeWidth="1.4"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M259.46,387.73s-86.07,-25 -111.9,-133.5c15.06,111.39 109.25,146.59 109.25,146.59z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.52"
        android:fillColor="#f7df73"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M250.7,394.4c-2.7,0 -9.2,-7.5 -13,-8.3s-4.2,-4.8 0.1,-5c4.4,0 8.1,5.1 8.1,5.1s3.8,3.3 7.7,3.8 1.4,5.4 -2.9,4.4"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M201.19,356.44s8.94,5.44 7.61,6.8c-1.16,1.19 -9.1,-5.1 -9.27,-5.27s-3.31,-4.25 1.66,-1.53z"
        android:strokeWidth="2.52"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"
        android:strokeColor="#8c1800"/>
    <path
        android:pathData="M232.4,380.6s2.6,-1 -4.2,-3.7 -6.5,-6 -8.7,-7.2 -7,-4.7 -8,-2.7 5,6 6.5,6.4c1.4,0.5 8.2,7 10.2,7.5q3,0.8 4.2,-0.3"
        android:fillColor="#8c1800"
        android:fillType="evenOdd"/>
    <path
        android:pathData="m187.95,343.01 l5.79,6.63"
        android:strokeWidth="3.02"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m179.01,331.95 l5.63,6.97"
        android:strokeWidth="2.85"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m170.24,317.5 l6.29,10.37"
        android:strokeWidth="2.52"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m162.79,302.87 l5.3,10.71m-8.61,-18.54 l1.66,3.74"
        android:strokeWidth="2.35"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="m155.67,284.85 l1.49,4.59"
        android:strokeWidth="1.34"
        android:fillColor="#00000000"
        android:strokeColor="#8c1800"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M490.4,239.3c0.5,6.7 7,13 4.4,24.7 -3.2,13.3 -14.8,44.7 -12.8,50.2 -3.4,-4.7 -2.8,-9 -3,-15.6 -0.4,-6.5 9.4,-30.9 10.3,-39.1 0,-7.4 0.7,-17.5 1.1,-20.2z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.3"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M490.4,239.4s10.7,13.5 10.1,24.2 -6.5,23.2 -4.5,28.7a22,22 0,0 1,-4.6 -14.2c-0.3,-6.5 5.3,-13.8 4.1,-20.9 -1.1,-7 -5.2,-17.8 -5,-17.8zM141.1,242.4c-0.4,6.7 -5.4,12.7 -2.8,24.3 3.2,13.3 15,30 13,49.2 3.5,-4.7 4.5,-10 4.8,-16.5s-13,-28.5 -13.8,-36.8c0,-7.4 -0.8,-17.5 -1.2,-20.2z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.3"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="M141.1,242.6s-10.4,13 -9.8,23.7c0.1,7.1 13.2,36.8 11.2,42.4 3.5,-4.7 4.4,-4.1 4.3,-11.2 -3.8,-18.3 -10.7,-29.9 -9.5,-37s4,-18 3.8,-18z"
        android:strokeLineJoin="round"
        android:strokeWidth="2.3"
        android:fillColor="#217900"
        android:fillType="evenOdd"
        android:strokeColor="#000"/>
    <path
        android:pathData="m318.52,123 l25.63,79.48l83.89,0l-67.58,49.18 25.75,79.48 -67.58,-49.06 -67.58,49.06 25.63,-79.48L208.99,202.48l83.66,0z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.98"
        android:fillColor="#fff"
        android:strokeColor="#000"
        android:strokeLineCap="square"/>
  </group>
</vector>
