<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:strokeWidth="1"
      android:fillColor="#ffe800"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M0,240h640v240H0z"
      android:strokeWidth="1"
      android:fillColor="#00148e"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M0,360h640v120H0z"
      android:strokeWidth="1"
      android:fillColor="#da0010"
      android:fillType="evenOdd"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M269.6,290.8 L197,367.3l-1.2,-1.6 72.4,-76.5 1.2,1.6zM320,303.3 L225.2,403.3 223.6,401.7 318.3,301.7z"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M225.49,402.13l-8.86,8.86l-10.1,7.62 7.62,-10.1L223.01,399.65z"
      android:strokeWidth="0.49"
      android:fillColor="#808080"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M201.09,363.03l-8.86,8.86l-10.1,7.62 7.62,-10.1L198.61,360.55z"
      android:strokeWidth="0.49"
      android:fillColor="#808080"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M207.94,114.79s-7.97,215.42 23.89,215.42S279.67,307.56 279.67,307.56l-0.36,-118.4z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M227.43,134.89c0,11.33 -3.56,172.67 12.38,172.67s39.87,-11.33 39.87,-11.33l-0.36,-107.07z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M237.77,145.77c0,11.33 2.02,139.14 18,139.14l23.89,0l-0.36,-95.74z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m206.2,116 l72.4,76.5 1.3,-1.6 -72.4,-76.5z"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M184.3,158.94S163.76,367.63 225.36,367.63c51.33,0 51.33,-32.92 61.6,-32.92l-10.27,-76.88z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M235.18,193.63c0.44,-0.03 16.02,-33.75 54.96,-55.09"
      android:strokeWidth="2.3"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M228.81,161.85c5.43,2.53 8.87,5.45 11.99,8.85 -6.21,-1.3 -9.42,-1.83 -14.85,-4.36s-9.58,-6.41 -12.87,-9.55c4.04,1.23 10.27,2.51 15.73,5.06z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M242.43,182.5c0.4,0.68 0.06,1.64 -0.79,2.11s-1.86,0.29 -2.27,-0.41 -0.05,-1.62 0.8,-2.09 1.86,-0.29 2.26,0.39z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M259.49,140.27c-2.35,5.97 -4.95,9.91 -7.94,13.58 1.34,-6.65 1.92,-10.06 4.27,-16.03s5.79,-10.77 8.56,-14.58c-1.2,4.37 -2.52,11.03 -4.89,17.03z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M251.55,139.96c-0.54,5.97 -1.73,10.6 -3.3,15.29 -0.41,-5.81 -0.71,-8.71 -0.17,-14.68s2.21,-11.79 3.57,-16.44c0.02,4.02 0.45,9.83 -0.1,15.83z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M256.73,146.79c-3.08,3.62 -5.66,5.19 -8.39,6.21 2.74,-5.02 4.08,-7.69 7.16,-11.31s6.38,-5.32 9.02,-6.64c-1.97,3.06 -4.7,8.1 -7.8,11.74z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M230.52,151.85c3.62,4.34 5.42,8.53 6.78,13.15 -4.74,-3.22 -7.24,-4.73 -10.86,-9.07s-5.66,-9.78 -7.26,-14.17c2.94,2.52 7.7,5.72 11.34,10.09z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M223.23,185.33c4.53,3.62 6.91,7.35 8.82,11.57 -5.76,-2.39 -8.78,-3.46 -13.3,-7.08s-7.28,-8.52 -9.45,-12.47c3.61,1.97 9.39,4.34 13.94,7.98z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M244.42,183.34c-3.62,5.79 -7.54,9.68 -12.02,13.36 2.16,-6.35 3.1,-9.6 6.72,-15.4s8.79,-10.55 12.96,-14.34c-1.88,4.2 -4.02,10.56 -7.66,16.38z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M248.21,183.11c-5.79,3.98 -10.83,6.05 -16.21,7.69 4.95,-5.1 7.35,-7.78 13.15,-11.77s12.25,-6.36 17.44,-8.23c-3.62,3.19 -8.56,8.31 -14.38,12.31z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M287.25,141.49c-2.53,2.9 -4.81,4.61 -7.25,6.11 2.09,-3.46 3.09,-5.25 5.62,-8.15s5.46,-4.94 7.81,-6.55c-1.55,2.21 -3.63,5.68 -6.18,8.59z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M281.76,132.95c-1.45,3.08 -3,5.39 -4.76,7.7 0.89,-3.08 1.28,-4.62 2.72,-7.7s3.49,-5.97 5.14,-8.28c-0.76,2.11 -1.64,5.18 -3.1,8.28z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M290.24,138.47c-2.53,1.45 -4.44,1.89 -6.34,2.03 2.53,-2.23 3.81,-3.44 6.34,-4.88s4.91,-1.86 6.82,-2.16c-1.74,1.31 -4.27,3.56 -6.82,5.02z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M255.27,154.69c-4.53,2.72 -8.25,4.3 -12.12,5.66 4.13,-3.28 6.17,-4.98 10.69,-7.7s9.25,-4.59 13.04,-6.07c-2.94,2.09 -7.07,5.38 -11.61,8.11z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M268.48,158.75c-5.97,3.62 -11.06,5.33 -16.43,6.55 5.25,-4.85 7.81,-7.42 13.78,-11.04s12.47,-5.52 17.68,-7.01c-3.79,2.99 -9.03,7.86 -15.03,11.5z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M227.89,217.34c0.38,-0.22 -3.26,-31.98 21.04,-65.02"
      android:strokeWidth="2.4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M265.13,150.4c0.4,0.68 0.06,1.64 -0.79,2.11s-1.86,0.29 -2.27,-0.41 -0.05,-1.62 0.8,-2.09 1.86,-0.29 2.26,0.39z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M275.93,144.1c0.4,0.68 0.06,1.64 -0.79,2.11s-1.86,0.29 -2.27,-0.41 -0.05,-1.62 0.8,-2.09 1.86,-0.29 2.26,0.39z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M245.79,165.27c0.28,0.52 -0.24,1.34 -1.18,1.81s-1.93,0.42 -2.22,-0.11 0.25,-1.33 1.19,-1.8 1.93,-0.42 2.21,0.1z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M248.99,163.87c0.28,0.52 -0.24,1.34 -1.18,1.81s-1.93,0.42 -2.22,-0.11 0.25,-1.33 1.19,-1.8 1.93,-0.42 2.21,0.1z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M247.09,166.87c0.28,0.52 -0.24,1.34 -1.18,1.81s-1.93,0.42 -2.22,-0.11 0.25,-1.33 1.19,-1.8 1.93,-0.42 2.21,0.1z"
      android:strokeWidth="1"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M252.7,167.3q0.5,1.4 -1,2.3c-1.5,0.9 -2,0.2 -2.4,-0.7s0,-1.8 1,-2.3 2,-0.2 2.3,0.7z"
      android:strokeWidth=".3"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M255,164.9q0.5,1.4 -1,2.3c-1.5,0.9 -2,0.2 -2.5,-0.6s0,-1.9 1,-2.4 2,-0.1 2.4,0.7z"
      android:strokeWidth=".3"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M255.5,166c0.4,0.8 0,1.9 -1,2.3s-2,0.2 -2.4,-0.6 0,-1.8 1,-2.3 2,-0.2 2.4,0.6z"
      android:strokeWidth=".3"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M204.83,180.95c0,10.97 -20.53,131.81 30.86,164.73 20.53,10.97 51.33,-10.97 51.33,0l-10.27,-87.85z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M225.36,202.89c0,10.97 -10.27,98.83 20.59,120.84 20.53,21.95 71.92,16 71.92,16l-41.12,-81.9z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m183.8,158 l94.8,100 1.7,-1.6 -94.8,-99.9z"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m169.21,146.15 l6.02,1.06l1.77,1.77c-0.24,-0.02 1.77,1.77 -0.71,4.25S170.29,152.15 170.29,152.15s1.06,6.02 4.61,9.57 7.79,2.83 7.79,2.83 -4.03,-3.31 -1.06,-6.02c2.98,-2.74 4.25,-0.71 4.25,-0.71l3.54,3.54l2.49,-2.49L177.74,144.7l-1.06,-6.02 -9.13,-1.51 1.67,8.95z"
      android:strokeWidth="1"
      android:fillColor="#cececc"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m183.18,104.12 l7.28,11.29 9.22,2.49 -6.02,3.89 10.97,-0.35l4.97,3.55L211.38,122.14L206.4,118.58l-3.88,-10.99 -1.07,7.45 -5.67,-8.17 -12.6,-2.78z"
      android:strokeWidth="1"
      android:fillColor="#cececc"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M212.8,110.9c0,0.6 -0.8,1.11 -1.72,1.11s-1.76,-0.51 -1.76,-1.11 0.8,-1.14 1.76,-1.14 1.72,0.51 1.72,1.14zM210.32,113.27c0,10.74 8.36,56.34 9.04,57.45 0.04,1.35 -1.92,1.92 -2.84,0.27 -2.64,-5.22 -8.32,-48 -8.52,-57.99 -0.12,-4.11 1.48,-4.44 3.32,-4.35 1.48,0.06 3.32,1.11 3.32,2.37 0,1.5 -2.32,2.61 -4.32,2.25z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M216,121.55c-0.3,0.6 -1.32,0.91 -2.19,0.68s-1.42,-0.95 -1.12,-1.55 1.33,-0.94 2.24,-0.7 1.38,0.94 1.06,1.57zM212.46,123.3c2.76,21.57 -6.5,54.88 -6.42,56.19 -0.62,1.33 -2.78,1.44 -2.82,-0.47 -0.45,-1.24 10.2,-31.33 7.17,-56.57 1.94,-4.14 3.63,-4.07 5.33,-3.52 1.38,0.43 2.6,1.94 1.97,3.2 -0.75,1.5 -3.51,2.03 -5.23,1.17z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M186.7,151.5c0,1 -1,2 -2.2,2s-2.2,-1 -2.2,-2 1,-1.8 2.2,-1.8q2,0.2 2.1,1.8zM183.5,155.5c0,17.9 10.5,93.9 11.3,95.8 0,2.2 -2.3,3.2 -3.5,0.4 -3.3,-8.7 -10.4,-80 -10.7,-96.6 -0.1,-6.9 1.9,-7.4 4.2,-7.3 1.9,0.1 4.1,1.9 4.1,4 0,2.4 -2.9,4.3 -5.4,3.7z"
      android:strokeWidth=".7"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M192.1,160.74c-0.4,0.96 -1.7,1.48 -2.8,1.13s-1.77,-1.48 -1.37,-2.44 1.72,-1.52 2.87,-1.16 1.72,1.46 1.3,2.47zM187.54,163.6c2.84,34.2 -9.96,87.32 -9.91,89.4 -0.83,2.13 -3.58,2.35 -3.57,-0.68 -0.54,-1.96 13.94,-50 10.87,-90.02 2.6,-6.62 4.74,-6.55 6.88,-5.71 1.74,0.65 3.24,3.02 2.4,5.04 -1,2.4 -4.52,3.31 -6.68,1.98z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m370.4,290.8 l72.5,76.5 1.3,-1.6 -72.5,-76.5zM320,303.3l94.8,100 1.6,-1.6 -94.7,-100z"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M414.51,402.13l8.86,8.86l10.1,7.62 -7.62,-10.1L416.99,399.65z"
      android:strokeWidth="0.49"
      android:fillColor="#808080"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M438.91,363.03l8.86,8.86l10.1,7.62 -7.62,-10.1L441.39,360.55z"
      android:strokeWidth="0.49"
      android:fillColor="#808080"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M432.06,114.79s7.97,215.42 -23.89,215.42S360.33,307.56 360.33,307.56l0.36,-118.4z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M412.57,134.89c-0,11.33 3.56,172.67 -12.38,172.67s-39.87,-11.33 -39.87,-11.33l0.36,-107.07z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M402.22,145.77c-0,11.33 -2.02,139.14 -18,139.14l-23.89,0l0.36,-95.74z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m433.8,116 l-72.4,76.5 -1.3,-1.6 72.4,-76.5z"
      android:strokeWidth="1"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M376.65,175.08s20.3,26.04 30.09,46.17c9.78,20.17 21.21,50.9 21.21,50.9s-6.53,-30.25 -18.85,-54.51C392.96,187.12 376.64,176.81 376.65,175.08z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M375.95,154.39s22.65,32.2 32.99,56.09c10.32,23.93 21.82,59.8 21.82,59.8s-5.58,-34.51 -18.67,-63.39C394.84,170.46 375.8,156.31 375.95,154.39z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M395.5,122.55s16.02,47.69 20.91,77.18c4.86,29.52 7.82,70.14 7.82,70.14s3.09,-34.09 -3.46,-70.28C411.71,153.25 394.83,124.03 395.5,122.55z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M381.11,120.37s22.19,42.35 30.83,71.13c8.62,28.82 16.73,70.4 16.73,70.4s-1.17,-37.77 -12.34,-72.81C401.36,144.59 380.63,122.28 381.11,120.37z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M407.76,133.84s11.04,45.6 13.82,72.81c2.75,27.22 3.54,63.95 3.54,63.95s3.84,-29.73 -0.01,-63.22C419.62,164.36 407.16,135.03 407.76,133.84z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M378.58,135.1s21.47,36.28 30.15,61.64c8.67,25.4 17.2,62.52 17.2,62.52s-2.07,-34.4 -13.24,-65.2C397.78,155.02 378.18,136.9 378.58,135.1z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M374.55,160.37s22.65,28.76 32.99,50.79c10.32,22.07 21.82,55.59 21.82,55.59s-5.58,-32.83 -18.67,-59.4C393.44,173.9 374.4,162.24 374.55,160.37z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M377.49,147.15s20.84,33.83 29.91,58.31c9.06,24.52 18.7,60.89 18.7,60.89s-3.86,-34.47 -15.41,-64.11C395.38,164.76 377.26,149.02 377.49,147.15z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M400.05,123.27s13.4,46.69 16.72,75.21c3.29,28.54 4.15,67.55 4.15,67.55s4.78,-32.35 0.16,-67.39C414.5,153.74 399.32,124.64 400.05,123.27z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M381.67,111.63s19.56,43.98 26.64,73.35c7.06,29.41 13.06,71.49 13.06,71.49s0.53,-37.73 -8.71,-73.53C400.15,137.39 381.12,113.49 381.67,111.63z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M412.81,137.18s8.41,43.79 9.62,69.72c1.19,25.95 -0.13,60.83 -0.13,60.83s5.53,-28.01 3.61,-59.96C422.92,166.71 412.15,138.28 412.81,137.18z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M379.17,129.76s19.56,37.91 26.64,63.86c7.06,25.99 13.06,63.61 13.06,63.61s0.53,-34.36 -8.71,-65.93C397.65,151.22 378.62,131.52 379.17,129.76z"
      android:strokeWidth="1"
      android:fillColor="#005b00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M455.7,158.94S476.24,367.63 414.64,367.63c-51.33,0 -51.33,-32.92 -61.6,-32.92l10.27,-76.88z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M435.17,180.95c-0,10.97 20.53,131.81 -30.86,164.73 -20.53,10.97 -51.33,-10.97 -51.33,0l10.27,-87.85z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M414.64,202.89c-0,10.97 10.27,98.83 -20.59,120.84 -20.53,21.95 -71.92,16 -71.92,16l41.12,-81.9z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m456.2,158 l-94.8,100 -1.7,-1.6 94.8,-99.9z"
      android:strokeWidth="1"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m470.79,146.15 l-6.02,1.06l-1.77,1.77c0.24,-0.02 -1.77,1.77 0.71,4.25S469.71,152.15 469.71,152.15s-1.06,6.02 -4.61,9.57 -7.79,2.83 -7.79,2.83 4.03,-3.31 1.06,-6.02c-2.98,-2.74 -4.25,-0.71 -4.25,-0.71l-3.54,3.54l-2.49,-2.49L462.26,144.7l1.06,-6.02 9.13,-1.51 -1.67,8.95z"
      android:strokeWidth="1"
      android:fillColor="#cececc"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m456.82,104.12 l-7.28,11.29 -9.22,2.49 6.02,3.89 -10.97,-0.35l-4.97,3.55L428.62,122.14L433.6,118.58l3.88,-10.99 1.07,7.45 5.67,-8.17 12.6,-2.78z"
      android:strokeWidth="1"
      android:fillColor="#cececc"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M427.3,110.9c-0,0.6 0.8,1.11 1.72,1.11s1.76,-0.51 1.76,-1.11 -0.8,-1.14 -1.76,-1.14 -1.72,0.51 -1.72,1.14zM429.78,113.27c-0,10.74 -8.36,56.34 -9.04,57.45 -0.04,1.35 1.92,1.92 2.84,0.27 2.64,-5.22 8.32,-48 8.52,-57.99 0.12,-4.11 -1.48,-4.44 -3.32,-4.35 -1.48,0.06 -3.32,1.11 -3.32,2.37 -0,1.5 2.32,2.61 4.32,2.25z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M424,121.55c0.3,0.6 1.32,0.91 2.19,0.68s1.42,-0.95 1.12,-1.55 -1.33,-0.94 -2.24,-0.7 -1.38,0.94 -1.06,1.57zM427.54,123.3c-2.76,21.57 6.5,54.88 6.42,56.19 0.62,1.33 2.78,1.44 2.82,-0.47 0.45,-1.24 -10.2,-31.33 -7.17,-56.57 -1.94,-4.14 -3.63,-4.07 -5.33,-3.52 -1.38,0.43 -2.6,1.94 -1.97,3.2 0.75,1.5 3.51,2.03 5.23,1.17z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M453.4,151.5c0,1 1,2 2.1,2s2.2,-1 2.2,-2 -1,-1.8 -2.2,-1.8q-2,0.2 -2.1,1.8zM456.4,155.5a1152,1152 0,0 1,-11.2 95.8c0,2.2 2.3,3.2 3.5,0.4 3.3,-8.7 10.4,-80 10.7,-96.6 0.1,-6.9 -1.9,-7.4 -4.2,-7.3 -1.9,0.1 -4.1,1.9 -4.1,4 0,2.4 2.9,4.3 5.4,3.7z"
      android:strokeWidth=".7"
      android:fillColor="#e10000"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M447.9,160.74c0.4,0.96 1.7,1.48 2.8,1.13s1.77,-1.48 1.37,-2.44 -1.72,-1.52 -2.87,-1.16 -1.72,1.46 -1.3,2.47zM452.46,163.6c-2.84,34.2 9.96,87.32 9.91,89.4 0.83,2.13 3.58,2.35 3.57,-0.68 0.54,-1.96 -13.94,-50 -10.87,-90.02 -2.6,-6.62 -4.74,-6.55 -6.88,-5.71 -1.74,0.65 -3.24,3.02 -2.4,5.04 1,2.4 4.52,3.31 6.68,1.98z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M287.07,422.56a32.86,30.74 90,0 0,30.8 0L317.87,334.71l-20.53,0c10.27,32.92 10.27,65.91 -10.32,87.85z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M303.33,336.65c0,61.95 -14.88,75.74 -14.88,88.2 6.2,0 10.32,-5.81 14.88,-13.79 6.2,-12.46 6.79,-75.39 6.2,-74.41z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M290.94,336.65c-0.31,36.19 -15.68,69.72 -12.77,77.14 3.29,6.65 12.77,-15.19 25.17,-2.8 6.2,-12.39 6.79,-75.32 6.2,-74.41z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M292.33,331.37c0,40.71 -14.24,43.42 -14.24,54.28 6.2,0 14.21,-5.93 14.24,-5.38 6.2,-8.19 6.79,-49.54 6.2,-48.9z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M279.94,331.37c-0.31,23.78 -15.33,44.02 -12.43,48.9 3.33,4.32 12.43,-8.19 24.82,0 6.2,-8.19 6.79,-49.54 6.2,-48.9z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M352.93,422.56a32.86,30.74 90,0 1,-30.8 0L322.13,334.71l20.53,0c-10.27,32.92 -10.27,65.91 10.32,87.85z"
      android:strokeWidth="1"
      android:fillColor="#e10000"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M336.67,336.65c-0,61.95 14.88,75.74 14.88,88.2 -6.2,0 -10.32,-5.81 -14.88,-13.79 -6.2,-12.46 -6.79,-75.39 -6.2,-74.41z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M349.06,336.65c0.31,36.19 15.68,69.72 12.77,77.14 -3.29,6.65 -12.77,-15.19 -25.17,-2.8 -6.2,-12.39 -6.79,-75.32 -6.2,-74.41z"
      android:strokeWidth="1"
      android:fillColor="#0000c4"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M347.67,331.37c-0,40.71 14.24,43.42 14.24,54.28 -6.2,0 -14.21,-5.93 -14.24,-5.38 -6.2,-8.19 -6.79,-49.54 -6.2,-48.9z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M360.06,331.37c0.31,23.78 15.33,44.02 12.43,48.9 -3.33,4.32 -12.43,-8.19 -24.82,0 -6.2,-8.19 -6.79,-49.54 -6.2,-48.9z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m241.63,341.56 l-10.76,7.79 10.76,7.79 5.38,-3.89 2.69,3.89 2.69,7.79 2.7,-7.79 2.69,-3.89L402,353.24L402,345.45L257.76,345.45l-2.69,-3.92c0,-3.89 0.56,-7.79 2.7,-7.79l10.75,0c0,-3.89 -5.38,-11.68 -16.14,-11.68 -10.75,0 -16.14,7.79 -16.14,11.68l10.76,0c2.12,0 2.69,3.89 2.69,7.81l-2.69,3.89z"
      android:strokeWidth="0.73"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M273.2,360.44L367.48,360.44l0,4.78L273.23,365.22zM273.2,355.63L367.48,355.63l0,4.81L273.23,360.44zM273.2,350.85L367.48,350.85L367.48,355.66L273.23,355.66zM273.2,346.08L367.48,346.08l0,4.78L273.23,350.85zM273.2,341.3L367.48,341.3l0,4.78L273.23,346.08zM273.2,336.49L367.48,336.49l0,4.81L273.23,341.3zM273.2,331.74L367.48,331.74L367.48,336.49L273.23,336.49z"
      android:strokeWidth="0.49"
      android:fillColor="#b74d00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m277.7,331.45 l34.45,33.48l4.93,0L282.65,331.48l-4.93,0z"
      android:strokeWidth="0.86"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m277.7,365.95 l34.45,-33.48l4.93,-0L282.65,365.92l-4.93,-0z"
      android:strokeWidth="0.86"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M278.66,331.45l0,33.48l4.96,0l0,-33.48z"
      android:strokeWidth="0.85"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m322,331.45 l34.45,33.48l4.93,0L326.95,331.48l-4.93,0z"
      android:strokeWidth="0.86"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="m322,365.95 l34.45,-33.48l4.93,-0L326.95,365.92l-4.93,-0z"
      android:strokeWidth="0.86"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M357.46,331.45l0,33.48l4.96,0l0,-33.48z"
      android:strokeWidth="0.85"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M323.06,331.45l0,33.48l4.96,0l0,-33.48z"
      android:strokeWidth="0.85"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M313.06,331.45l0,33.48l4.96,0l0,-33.48z"
      android:strokeWidth="0.85"
      android:fillColor="#908f8a"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M389.79,235.88c0,54.56 -32.25,98.83 -71.92,98.83s-71.92,-44.27 -71.92,-98.83 32.19,-98.89 71.92,-98.89 71.92,44.27 71.92,98.83zM379.52,235.88c0,48.48 -27.61,87.85 -61.65,87.85s-61.65,-39.37 -61.65,-87.85c0,-48.55 27.61,-87.92 61.65,-87.92s61.65,39.37 61.65,87.92z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M345.88,144.8c-8.64,-5.02 -18.1,-7.81 -28.01,-7.81s-19.37,2.79 -28.01,7.75l4.06,10.17a51.46,48.14 90,0 1,47.97 0z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M346.08,324.3c-8.64,5.02 -18.1,7.81 -28.01,7.81s-19.37,-2.79 -28.01,-7.75l4.06,-10.17a51.46,48.14 90,0 0,47.97 -0z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M379.6,235.9c0,48.5 -27.7,87.8 -61.7,87.8s-61.6,-39.3 -61.6,-87.8 27.6,-88 61.6,-88 61.7,39.4 61.7,88"
      android:fillColor="#a7cfff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M379.52,235.88c0.64,16.49 -2.67,24.55 -6.9,41.73 -1.1,1.86 -6.38,-4.03 -9.51,-6.94s-4.52,2.6 -8.47,-1.86c-3.89,-4.59 -6.38,1.24 -9.28,-2.6s-29.75,-4.34 -29.75,-4.71c2.67,-1.36 16.36,0.12 14.15,-6.88 -2.49,-7.32 -17.98,-0.25 -20.07,-9.55 -1.45,-9.3 -31.09,-9.67 -33.23,-11.9 0.87,3.47 23.08,4.96 22.56,13.95 -0.52,3.72 -21.87,4.77 -23.95,7.87 -1.74,3.91 16.82,-1.05 17.46,3.72 0,1.98 -2.73,0 -12.35,3.1 -4.87,1.55 8.93,6.39 3.71,9.05s-16.41,3.84 -15.95,4.96c1.74,5.58 25.93,12.15 23.55,13.08 -8.58,4.09 -13.11,6.76 -17.23,9.11a104.16,97.44 90,0 1,-18.04 -62.12c18.39,-7.07 14.5,-8.56 48.49,-8.56s45.24,1.43 74.82,8.56z"
      android:strokeWidth="1"
      android:fillColor="#afff7b"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M379.52,235.88c-4.64,0 -9.51,2.11 -14.5,2.11s-10.15,-2.17 -15.37,-2.17 -11.19,2.85 -16.53,2.85c-5.39,0 -10.09,-2.79 -15.49,-2.79s-10.79,2.11 -16.12,2.11 -10.61,-2.17 -15.78,-2.17 -10.27,2.17 -15.2,2.17 -9.74,-2.17 -14.33,-2.17q0.12,-18.23 4.87,-34.16c15.66,1.24 6.38,-9.73 15.66,-9.73a20.46,19.14 90,0 1,14.67 6.08c1.39,0 8.35,-7.07 16.18,-6.08s4.93,16.93 15.31,17.73c5.22,4.22 8.29,6.88 15.49,8.62 10.27,0.99 39.67,-1.55 39.85,-0.12a109.04,116.56 0,0 1,1.28 17.73z"
      android:strokeWidth="1"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M327.18,287.02c0,-2.74 2.7,-4.56 2.7,-8.93s-2.4,-4.46 -2.48,-8.54c-0.04,-1.68 2.06,-3.36 2.98,-3.55s1.7,3.55 1.7,4.37 -0.94,-2.02 -1.72,-2.06 -2.46,0.29 -2.46,1.1c0,1.63 2.96,3.65 2.74,9.12 -0.22,5.38 -2.54,7.01 -2.54,8.64s1,6.14 1,6.14 -1.94,-3.6 -1.94,-6.34z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M335.82,286.62c-0,-2.74 -2.7,-4.56 -2.7,-8.93s2.4,-4.46 2.48,-8.54c0.2,-1.92 -2.36,-3.17 -3.28,-3.31s-2,3.84 -2,4.61 1.08,-2.5 1.86,-2.54 2.92,0.29 2.92,1.1c-0,1.63 -2.96,3.65 -2.74,9.12 0.22,5.38 2.54,7.01 2.54,8.64s-1,6.14 -1,6.14 1.94,-3.6 1.94,-6.34z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M333,264.9c0,1.3 -0.7,2.4 -1.5,2.4s-1.4,-1.1 -1.4,-2.5 0.6,-2.4 1.4,-2.4 1.5,1.1 1.5,2.5m17,9h0.4v19.5h-0.5zM354.1,272.3h0.5L354.6,292h-0.5z"
      android:fillType="evenOdd"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M352.6,281.2v-0.5l4,0.7v0.5z"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M295.43,286.86s15.08,7.07 18.39,11.28 1.97,4.22 1.97,4.22l33.47,1.43c0,-2.11 5.92,-2.11 7.25,-7.07s1.33,-6.32 1.33,-6.32l-10.5,3.53 0.58,-6.32L338.76,287.6l-1.33,6.32 -19.72,-0.68L319.04,276.32l-3.94,0.68 -0.58,16.12c-0.7,0 -18.44,-4.9 -19.14,-6.26z"
      android:strokeWidth="1"
      android:fillColor="#b74d00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M265.09,185.91s21.87,-6.94 53.42,-6.45 53.42,7.94 52.95,7.94 -6.03,-11.41 -6.03,-11.41 -22.27,-6.51 -47.39,-7.01c-25.06,-0.5 -46.92,4.96 -46.4,5.46z"
      android:strokeWidth="1"
      android:fillColor="#fede00"
      android:fillType="evenOdd"
      android:strokeColor="#fede00"/>
  <path
      android:pathData="m306,169.8 l13.6,-0.2 0.2,7.9 -13.6,0.3zM274.8,174.1L270,182l16.2,-2 -1.3,-7.7 -10.1,2zM288.5,171.1 L302.1,170 302.7,177.8 289.2,179zM337.4,170.4 L323.9,169.4 323.4,177.4 336.9,178.2zM366.6,175.4 L371.5,185 356.9,181.4 358.5,173.7 366.5,175.3zM354.8,172.4 L341.4,170.7 340.4,178.5 353.9,180.3z"
      android:fillColor="#38a9f9"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m318.51,190.37 l-1.45,-8.37 -4.41,7.13 1.62,-8.37 -6.55,4.77 4.41,-7.01 -7.77,1.74 6.67,-4.71 -7.83,-1.55 7.83,-1.61 -6.67,-4.71 7.77,1.74 -4.47,-7.01 6.61,4.77 -1.62,-8.37 4.41,7.13 1.45,-8.37 1.51,8.37 4.35,-7.13 -1.57,8.37 6.55,-4.77 -4.47,7.01 7.83,-1.74 -6.67,4.71 7.83,1.61 -7.83,1.55 6.67,4.71 -7.83,-1.74 4.47,7.01L322.8,180.76l1.62,8.37 -4.41,-7.13z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:pathData="M330.09,168.75c0,4.65 -3.76,8.4 -8.44,8.4s-8.44,-3.75 -8.44,-8.4S316.98,160.3 321.65,160.3s8.44,3.8 8.44,8.5z"
      android:strokeWidth="1"
      android:fillColor="#ffdf00"
      android:fillType="evenOdd"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M320.7,170.6c0,0.5 -1.3,1 -2.8,1s-2.8,-0.5 -2.8,-1 1.2,-1 2.8,-1 2.8,0.4 2.8,1m6,0q-0.2,0.9 -2,1c-1.8,0.1 -2.2,-0.5 -2.2,-1s1,-1 2.1,-1 2.1,0.4 2.1,1"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m320.41,174.05 l0.58,0.34c-1.4,-0.86 -0.76,-0.54 1.44,1.63 1.26,-0.73 1.5,-1.4 2.28,-2.3"
      android:strokeWidth="1"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m314.82,178.47 l0.46,0.5c-1.1,-1.24 -0.58,-0.62 1.86,0.99 2.32,0.31 4.7,-0.74 6.09,-2.48"
      android:strokeWidth="1"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M281.4,194.8s1,8 -5.1,18 -5.1,6.4 -5.1,6.4 2.8,5.5 2.3,5.5 -4.7,-5 -4.7,-5l-5,5.5s4.6,-8.5 4.1,-8.5 -1.4,-3 -1.4,-3l3.7,-1.5s5.6,-10.4 5.6,-9.9 -16.7,11.4 -16.7,11.4zM285.1,197.8c-0.5,0 2.3,8 4.7,12 2.3,4 2.3,8.9 2.3,8.9l7.9,4 -10.2,-15.4 6.5,2.4zM285.1,218.7s5.6,6 6,8 0.5,6.4 0.5,6.4l-2.3,-5 -3.3,4.5s2.4,-7 1.9,-7.5 -3.7,2.5 -3.7,2.5 1.4,-4 1.4,-4.4 0,-3 -0.5,-4.5m-27.9,13.9c1.9,-1.5 5.6,-2.5 5.6,-2.5s-2.3,4 -2.8,4 -1.8,0 -2.8,-1.5m64.1,-21.8s12.1,7.4 12.1,7.9 -7.9,-3.5 -7.9,-3.5zM299.5,195.3c1,0.5 17.7,14.4 17.2,14.4s-7.4,-4 -7.4,-3.4v3.4l-3.8,-7.9 -0.9,3zM303.7,217.7 L308.3,227.1 313,226.1s-8.8,-8.4 -9.3,-8.4m14.4,-6 l0.5,9s2.7,2.5 2.7,2 -2.7,-10.5 -3.2,-11"
      android:fillColor="#b7e1ff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M182.6,89.5s21,-11.6 44.9,-20.2a304,304 0,0 1,54.2 -14.4c7,0 19.2,17.8 21.2,17.8s10.1,-5 20.3,-5 16.1,8 18.2,8h18.2c2,0 -6.1,-19.8 0,-18.8 3,0.5 28.8,4.5 52.8,12.2 24,7.8 58,21.6 58,21.6S414.9,98 400,95.9c-2,1 0,13 -3,7.7 -4.6,-1 -21.7,-3.7 -24.7,-3.7s-8.7,3.4 -16.8,5.4c-8,2 -18.2,5 -18.2,5l13.2,20.7 -16.2,8s-10.1,-23.8 -14.2,-23.8 -6,16.8 -11.1,15.8 -7,-15.8 -11.1,-19.8c-4,-3.9 -25.4,-5.4 -33.5,-7.3 -8,-2 -21.1,-3.5 -28.2,-5.5s-14.2,5 -17.2,5 4,-6 1,-7 -5,3 -7,3 -23.3,-4 -25.3,-5 5,-4.9 3,-4.9z"
      android:fillColor="#984000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M333.46,79.6c0,5.51 -4.53,9.98 -10.1,9.98S313.29,85.11 313.29,79.6s4.51,-9.96 10.08,-9.96S333.46,74.11 333.46,79.6z"
      android:strokeWidth="0.88"
      android:fillColor="#808080"
      android:fillType="evenOdd"
      android:strokeColor="#772600"/>
  <path
      android:pathData="M282.27,81.5c1.81,-2.11 4.05,-5.31 9.91,-5.31 1.35,-0.54 1.35,-3.74 4.48,-3.74s2.24,3.2 4.51,4.29c2.24,1.06 20.68,-1.09 20.68,-1.09s1.78,1.6 1.78,3.74 -1.35,3.71 -1.78,3.71 -18.44,-1.6 -19.79,-1.06 -2.24,1.06 -5.4,1.06 -4.05,-3.71 -6.29,-3.71 -4.48,-0.54 -8.1,2.14z"
      android:strokeWidth="1.56"
      android:fillColor="#984000"
      android:fillType="evenOdd"
      android:strokeColor="#772600"/>
  <path
      android:pathData="M337.13,80.39l7.18,0c2.59,0.03 3.48,0.96 5.86,1.06l6.29,0c2.3,-0.1 3.51,-0.74 5.83,-1.06 3.92,-0.06 2.59,-0.42 1.81,-3.2 -0.7,-2.08 -0.81,-3.07 -1.81,-4.26 -0.38,-2.56 -1.24,-3.46 -1.35,-6.4 -0.41,-2.24 -1.16,-3.74 -0.43,-6.4 1.03,1.82 1.43,2.56 4.05,2.69 2.7,0 3.75,-0.1 5.4,1.06 1.24,1.57 2.92,1.06 5.4,1.06 2.05,0.45 2.4,1.7 4.02,2.66 1.92,1.28 3.16,1.6 5.83,1.6 2.16,0.48 4.05,0.83 5.4,1.6 1.16,1.63 2.97,2.05 5.4,2.14 1.35,0 2.43,-0.16 3.59,-0.54 2.08,0.13 4.18,0 6.75,0 1.67,2.3 2.05,3.17 4.94,3.2 2,1.66 3.67,1.92 5.4,2.66l6.75,0c2.43,0.22 3.19,1.41 5.4,2.14a64.32,54.27 90,0 0,5.83 2.66,34.56 29.16,90 0,0 5.86,0.54c1.65,1.02 3.67,1.09 4.94,2.11 1.86,0.48 4.4,1.06 5.83,1.6 2.38,0.38 3.29,1.28 5.4,1.6 1.81,0.64 -0.59,1.15 -1.81,1.6 -2.65,-0.1 -2.84,-1.28 -5.4,-1.6 -1.35,-0.93 -3.4,-0.8 -5.4,-1.6l-0.43,0c1.78,1.28 3.51,2.08 2.7,4.29 -2.3,0 -4.16,-0.35 -6.29,-0.54 -2.27,-0.51 -3.29,-1.06 -5.83,-1.06 2.54,-0.74 3.19,0.06 4.94,1.06 0.65,2.56 -0.76,1.6 -3.16,1.6 -2.05,-0.86 -3.29,-1.92 -5.83,-2.14l-5.4,0c2.67,0.13 3.24,1.31 4.94,2.69 0.14,0.83 -0.03,0.77 -1.35,1.06 -1.4,-1.44 -3.21,-1.92 -5.4,-2.66 -2.13,-0.1 -4.37,-0.32 -6.29,-1.09 -1.32,-0.1 -1.11,-1.95 1.81,1.09 1.76,1.22 2.38,2.24 2.24,3.2 -2.32,-0.7 -3.46,-1.79 -4.94,-2.69a19.52,16.47 90,0 0,-6.29 -1.06c-2.32,-0.74 0.24,-1.15 1.78,1.6 3.35,2.82 -0.43,1.6 -1.78,1.06 -2.03,-0.67 -4.13,-1.66 -5.86,-2.11 -1.35,-0.48 -2.54,-0.93 -4.05,-1.09 2.35,0.26 3.13,1.25 4.05,3.2 2.38,1.92 -0.11,0.99 -2.24,0.54 -1.35,-1.41 -3.11,-2.14 -4.05,-3.74 -2.24,-1.31 -2.32,-2.88 -0.89,0.54 0.35,1.09 0.27,3.46 0.43,3.74 -1.65,-2.14 -1.3,-2.69 -3.13,-2.69 -1.27,-1.02 -3.11,-1.82 -4.05,-3.2 -1.73,-0.19 -1.84,-0.93 -3.59,-1.6 1.43,2.02 3.35,4 4.48,5.89 2.05,1.34 2.62,2.56 4.05,3.71 0.57,1.18 2.16,2.14 0.46,0.54 -1.94,-2.24 -3.38,-2.78 -6.29,-4.26 -1.67,-1.6 -2.27,-2.24 -4.05,-3.74 -2.43,-0.93 -1.49,-1.44 0,1.6 1.7,1.86 3,3.9 4.48,5.34a21.76,18.36 90,0 0,4.05 4.26c0.41,0.7 1.62,0.83 0,1.06 -1.35,-1.47 -3.35,-2.4 -4.94,-3.74 -2.24,-0.74 -3.29,-2.11 -4.94,-3.71 -1.08,-1.89 -2.81,-3.33 -4.05,-4.8 -2.59,-1.22 0.51,1.15 1.35,2.66 0.57,1.95 1.3,3.52 1.78,5.34 0.76,1.76 -2.27,-0.42 -3.13,-1.06a18.24,15.39 90,0 0,-5.86 -2.69,95.36 80.46,90 0,0 -5.4,-1.06c-2.03,-0.64 1.11,1.54 2.27,3.74 2.27,2.53 -2.24,0.45 -3.59,0a40,33.75 90,0 0,-6.75 -0.54c-2.89,0.19 -0.81,0.64 0,2.11 0.89,0.83 1.51,2.56 1.35,3.2 -1.89,-0.32 -3.51,-0.96 -5.86,-1.06 -1.89,0.74 -4.13,0.54 -6.29,0.54 -0.08,1.34 0.41,0.19 1.35,1.6 1.57,0.74 2.59,1.63 2.27,2.66l-13.04,0c-1.43,0.45 0.3,0.22 1.35,2.14 -0.59,1.73 -1.35,1.06 -3.16,1.06 -1.22,-0.58 -3.67,-0.83 -4.94,-1.6 -1.27,0 -1.16,-0.38 -2.24,-0.54 3.78,3.2 1.89,2.4 4.05,4.8 0.35,0.16 1.22,0.54 0.43,0.54M305.65,128.36c-0.27,0 -0.86,-1.73 -1.78,-3.2 -0.65,-1.92 -1.46,-0.83 -2.27,-3.74 0.11,-3.01 0.19,-3.52 0,-6.4 -1.84,-1.54 -2.62,-1.98 -4.05,-4.8 -2.27,-2.24 0.35,-6.08 0.78,-8.9 -2,-0.51 -2.97,3.68 -6.02,5.18 -2.21,1.76 -0.27,-3.65 -1.35,-6.78 -0.73,-4.45 -2.92,2.24 -3.73,4.13 -1.49,1.18 -1.94,2.72 -4.05,0.51 0,-1.63 1.32,-6.24 0.76,-7.14 -2.11,1.25 -4.86,4.96 -7.05,5.54 -2.21,-0.54 0.46,-4.74 0.46,-7.84 -0.81,-0.32 -3.97,8.26 -5.27,8.32s-0.73,-8.32 -1.35,-8.13c-0.95,1.6 -2.86,4.93 -4.18,6.08 -2.57,-0.42 0.38,-5.6 0.76,-7.52 0.43,-2.3 -2.4,4.42 -3.92,4.83 -1.11,1.15 -3.29,1.92 -4.05,2.14 0.86,-1.98 5.13,-5.98 4.86,-7.14 -2.51,0.1 -6.32,4 -8.45,4.48 -1.4,0 -3.35,0.54 -3.59,0.51 0.11,-2.56 5.24,-4.67 6.05,-6.24 -2.51,0.48 -7.34,3.1 -8.75,5.18 -1.67,0.42 -3.78,0.96 -5.4,0 0.24,-2.4 4.02,-7.04 4.86,-8.58 -2.3,1.18 -3.43,2.11 -5.86,2.66 -0.46,0.54 -1.76,1.7 -1.35,0.54 0.73,-2.24 1.81,-3.2 2.7,-4.8 -2.03,0.45 -2.78,1.82 -4.94,3.2 -1.38,0.06 -5.32,4.77 -6.64,4.83a22.08,18.63 90,0 1,-7.64 4.26c-2.05,0.45 -3.05,0.96 -4.05,2.14 1.03,-2.14 2.59,-4.16 4.05,-5.31a9.6,8.1 90,0 1,4.05 -4.29c0.51,-0.74 1.92,-0.93 0.43,-1.06 -1.27,1.34 -3.43,2.88 -4.48,3.74 -1.38,1.02 -2.97,2.05 -3.62,1.6 0.73,-2.3 2.03,-2.88 3.16,-4.8 2.38,-1.79 0,-0.64 -1.81,0 -1.16,1.22 -3.24,2.14 -4.94,3.2 -1.89,0.58 -3.16,1.92 -5.4,2.66 0,0.77 0.65,-1.38 0.92,-2.14 1.84,-2.37 2.32,-3.04 4.48,-3.71 0.54,-0.93 1.19,-0.54 -0.43,-0.54 -1.24,1.28 -2.92,1.98 -4.51,3.2 -1.59,0.42 -3.67,0.54 -5.83,0.54 -2.81,0.38 -2.57,0.64 0,-1.09 1.43,-1.6 2.62,-1.18 2.7,-3.2 -1.7,1.47 -3.78,2.56 -5.86,3.74 -1.73,0.06 -2.19,0.54 -4.05,0.54 0.89,-1.09 1.43,-2.46 2.7,-3.2 1.89,-2.46 -1.19,-0.48 -1.81,0l-6.75,0c-1.19,1.79 -0.05,0 0.92,-1.09 1.16,-0.7 2.38,-1.63 2.24,-2.11 -1.35,1.38 -2.89,2.02 -4.05,3.2 -1.24,0.45 -2.7,0.64 -2.7,1.06 1,-1.7 2.59,-2.75 3.62,-4.26 1.51,-0.38 2.81,-0.96 3.13,-1.6l-6.29,0 4.48,0c2.3,0 4.24,-0.26 6.32,-0.54 3.56,-0.7 -0.32,-1.06 -2.27,-1.06 -0.35,-0.83 1.13,-1.28 2.7,-1.6C203.59,82.82 205.29,82.15 206.75,81.44a14.4,12.15 90,0 0,3.59 -2.11c-1.35,0.06 -1.35,0.45 -2.7,0.51 2.03,-0.16 3.46,-0.51 5.86,-0.51a76.48,64.53 90,0 0,5.83 -2.66c0.81,-1.15 1.13,-1.09 -0.89,-1.09 1.81,-0.67 4.37,-1.02 5.83,-1.6a30.08,25.38 90,0 0,5.4 -2.11c1.32,-1.06 2.08,-2.3 3.59,-3.2 1.76,1.41 1.78,1.6 4.94,1.6 2.54,-0.03 4.1,-0.77 5.4,-2.14 1.94,-0.64 2.3,-1.73 4.51,-2.11a42.88,36.18 90,0 0,6.29 0.51c2.16,-0.67 4.24,-1.92 5.83,-3.2 1.38,-0.8 3.02,-1.95 4.51,-2.66 1.62,0.99 2.92,1.79 4.94,2.14 2.08,-0.38 3.19,-1.6 4.94,-2.14a8.96,7.56 90,0 1,4.05 -2.66c2.62,-1.54 2.43,0.48 3.59,2.11 1.35,1.66 3.08,0.86 4.94,1.6 1.05,1.92 1.81,3.2 3.16,4.26 1.05,1.54 2.3,1.92 4.51,2.14 0.81,1.28 0.16,2.18 1.78,2.66 0.65,0.83 1.76,1.15 2.7,1.6"
      android:strokeWidth="1.03"
      android:fillColor="#00000000"
      android:strokeColor="#772600"/>
  <path
      android:pathData="M331.95,114.76l0,0.26c0,-0.74 0,-0.32 0.46,1.09 0.16,1.15 -0.22,1.02 -0.68,1.86 -0.08,1.18 -0.22,2.05 -0.22,3.46 -0.16,1.15 -0.7,2.24 -1.13,3.2 -0.43,0.99 -2.3,1.57 -2.24,2.94 -1.08,0.35 -1.11,-0.48 -1.13,-1.89 -0.65,-0.86 -0.86,-2.05 -1.35,-3.2 -0.27,-1.09 -0.92,-1.92 -1.35,-3.2 -0.65,-1.18 -1.22,-1.54 -1.78,-2.91 -0.24,-0.64 -0.19,-1.47 -0.68,-1.86 -0.73,-1.12 -1.22,-1.28 -2.48,-1.34 -0.97,0.26 -1.35,0.77 -2.24,1.06 -0.7,0.29 -2.05,0.19 -2.92,0.54 -0.22,0.42 0.43,0.64 0.68,1.6 -0.59,0.58 -0.81,1.57 -1.13,2.4 -0.54,0.8 -0.89,1.18 -1.13,2.4 0.38,0.74 0.05,1.73 -0.43,2.4 -0.16,1.22 -0.68,1.89 -0.92,2.91 -0.7,0.61 -0.97,1.15 -1.57,2.14 -0.51,0.86 -0.97,0.8 -2.24,0.8 -1,-0.32 -1.13,-0.96 -2.03,-1.34q-0.05,-0.42 -0.46,-0.51"
      android:strokeWidth="0.91"
      android:fillColor="#00000000"
      android:strokeColor="#782600"/>
  <path
      android:pathData="M307.6,125.5s-0.4,5 -3.6,8.3 -9.8,7.1 -9.8,7.1 8.9,-4.2 9.8,-3.3 -5.7,8.3 -5.7,8.3 8.7,-7.7 9.8,-7.7 3.6,7.5 4.6,7.3 -2,-9.5 -1.6,-11c0.4,-1.4 0,-9.3 0,-9.3l-3.4,0.3zM328.1,124.9s-0.4,5.3 -3.6,8.8 -9.8,7.6 -9.8,7.6 8.9,-4.5 9.8,-3.5 -5.7,8.8 -5.7,8.8 8.7,-8.2 9.8,-8.2 3.6,8 4.6,7.8 -2,-10.2 -1.6,-11.7c0.4,-1.6 0,-10 0,-10zM299.4,76.9c0,0.9 -1,1.7 -2.1,1.7s-2.2,-0.8 -2.2,-1.8 1,-1.8 2.2,-1.8 2.1,0.8 2.1,1.8z"
      android:fillColor="#812e00"
      android:fillType="evenOdd"/>
</vector>
