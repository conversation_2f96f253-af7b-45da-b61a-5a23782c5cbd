<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M320,0h320v480H320Z"
      android:fillColor="#cf142b"/>
  <path
      android:pathData="M131.69,55.23l60.21,0l0,57.19A16.08,14.17 58.31,0 1,209.97 130.49L267.17,130.49l0,60.21L209.97,190.71A14.17,16.08 58.31,0 1,191.91 208.77L191.91,265.97l-60.21,0L131.69,208.77A16.08,14.17 58.31,0 1,113.63 190.71L56.43,190.71l0,-60.21l57.19,0A14.17,16.08 58.31,0 1,131.69 112.43Z"
      android:strokeWidth="7.53"
      android:fillColor="#00000000"
      android:strokeColor="#cf142b"/>
  <path
      android:pathData="M191.55,112.45A16.08,14.17 58.31,0 1,209.95 130.85l0,59.51A14.17,16.08 58.31,0 1,191.55 208.75l-59.51,0A16.08,14.17 58.31,0 1,113.65 190.35l0,-59.51A14.17,16.08 58.31,0 1,132.05 112.45Z"
      android:strokeWidth="1.41"
      android:fillColor="#fff"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M132.05,55.58l59.51,0L191.55,265.62l-59.51,0zM56.78,130.85L266.82,130.85l0,59.51L56.78,190.35Z"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M135.46,58.99l52.68,0L188.14,262.21l-52.68,0zM60.19,134.26L263.41,134.26l0,52.68L60.19,186.94Z"
      android:strokeWidth="1.41"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M161.8,160.6m-47.04,0a47.04,47.04 0,1 1,94.08 0a47.04,47.04 0,1 1,-94.08 0"
      android:strokeWidth="1.41"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M161.8,160.6m-34.81,0a34.81,34.81 0,1 1,69.62 0a34.81,34.81 0,1 1,-69.62 0"
      android:strokeWidth="1.41"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M207.13,122.89c-1.63,1.25 -4.73,4.44 -6.62,6.78l-1.75,-1.09c2.4,-2.07 5.72,-5.14 7.56,-7.34zM205.65,120.18c-2.95,0.87 -8.6,2.53 -11.01,4.06l-0.53,-0.76c1.01,-1.46 2.16,-7.44 2.12,-9.14l1.84,0.41c-0.6,1.47 -1.19,4.62 -1.35,6.66 1.9,-0.34 6.39,-1.81 7.67,-2.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M209.44,126.91l-2.33,0a8.82,4.85 58.31,1 0,-3.56 2.55l1.65,-1.97 2.03,2.03 -2.27,2.27a15.73,13.82 58.31,0 1,-14.32 -18.86,15.73 13.82,58.31 0,1 18.81,13.98z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M199.51,205.93c-1.25,-1.63 -4.44,-4.73 -6.78,-6.62l1.09,-1.75c2.07,2.4 5.14,5.72 7.34,7.56zM202.23,204.45c-0.87,-2.95 -2.53,-8.6 -4.06,-11.01l0.76,-0.53c1.46,1.01 7.44,2.16 9.14,2.12l-0.41,1.84c-1.47,-0.6 -4.62,-1.19 -6.66,-1.35 0.34,1.9 1.81,6.39 2.73,7.67z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M195.49,208.24l-0,-2.33a4.85,8.82 58.31,1 0,-2.55 -3.56l1.97,1.65 -2.03,2.03 -2.27,-2.27a13.82,15.73 58.31,0 1,18.86 -14.32,13.82 15.73,58.31 0,1 -13.98,18.81z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M116.47,198.31c1.63,-1.25 4.73,-4.44 6.62,-6.78l1.75,1.09c-2.4,2.07 -5.72,5.14 -7.56,7.34zM117.95,201.03c2.95,-0.87 8.6,-2.53 11.01,-4.06l0.53,0.76c-1.01,1.46 -2.16,7.44 -2.12,9.14l-1.84,-0.41c0.6,-1.47 1.19,-4.62 1.35,-6.66 -1.9,0.34 -6.39,1.81 -7.67,2.73z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M114.16,194.29l2.33,-0a8.82,4.85 58.31,1 0,3.56 -2.55l-1.65,1.97 -2.03,-2.03 2.27,-2.27a15.73,13.82 58.31,0 1,14.32 18.86,15.73 13.82,58.31 0,1 -18.81,-13.98z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M124.09,115.27c1.25,1.63 4.44,4.73 6.78,6.62l-1.09,1.75c-2.07,-2.4 -5.14,-5.72 -7.34,-7.56zM121.38,116.75c0.87,2.95 2.53,8.6 4.06,11.01l-0.76,0.53c-1.46,-1.01 -7.44,-2.16 -9.14,-2.12l0.41,-1.84c1.47,0.6 4.62,1.19 6.66,1.35 -0.34,-1.9 -1.81,-6.39 -2.73,-7.67z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M128.11,112.96l0,2.33a4.85,8.82 58.31,1 0,2.55 3.56l-1.97,-1.65 2.03,-2.03 2.27,2.27a13.82,15.73 58.31,0 1,-18.86 14.32,13.82 15.73,58.31 0,1 13.98,-18.81z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.71"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M124.45,186.91c-1.06,-1.85 -2.45,-4.22 -3.22,-4.84 0.47,-0.32 1.1,-0.79 1.43,-0.88 -0.01,1.18 1.76,3.62 1.76,3.62l2.62,-1.72c-0.74,-1.13 -1.38,-2 -1.91,-2.34 0.65,-0.47 1.34,-0.87 1.53,-0.91 0.01,0.57 0.35,1.53 1.35,2.7 0,0 2.44,-1.29 3.23,-2.29l1.03,1.68c-1.15,0.22 -6.29,3.43 -7.82,4.98m-3.63,-22.3c-2.75,0.5 -4.15,2.84 -3.67,5.47 0.47,2.66 2.85,4.48 5.47,4.04 2.62,-0.46 4.1,-2.87 3.6,-5.66s-2.65,-4.35 -5.39,-3.85m0.4,1.59c2.2,-0.4 4.06,0.68 4.37,2.38s-1.06,3.4 -3.28,3.79 -3.98,-1.01 -4.23,-2.44c-0.26,-1.46 0.93,-3.34 3.15,-3.73m5.38,-16.17c-0.91,1.44 -2.93,2.76 -4.7,3.98 -0.25,-1.26 -1.19,-2.07 -2.28,-2.15 -1.16,-0.06 -2.35,0.87 -2.56,1.48 -0.29,0.85 -0.66,4.51 -0.66,4.51 2.29,-0.18 7.31,0.22 9.03,0.85l0.21,-1.94c-1.13,0.29 -3.69,-0.24 -3.69,-0.24l0.06,-0.85 2.65,-2.09c0.34,-0.26 2.32,-2.12 2.38,-3.44 0.01,-0.41 -0.34,-0.31 -0.43,-0.13m-5.31,6.48c-1.04,0.03 -3.22,-0.09 -3.82,-0.35 0.24,-2.1 0.88,-2.85 2,-2.75 1.03,0.1 2.12,0.69 1.82,3.1m10.03,-26.05c-2,0.16 -4.5,2.04 -4.44,3.66 0.01,0.85 0.31,2.03 1.44,2.88 1.12,0.85 2.28,1.1 3.2,0.93 0.9,-0.16 2.38,-1.41 2.37,-2.1 0,-0.68 -1.59,-2.56 -2.59,-2.56l1.66,-1.78c0.43,1.03 2.13,2.53 2.76,2.72 -0.72,1.72 -1.62,3.51 -3.5,4.38 -1.88,0.88 -3.84,0.84 -5.62,-0.84 -1.68,-1.59 -1.03,-4.65 0.03,-5.84 1.16,-1.31 3.32,-1.87 3.37,-2.88zM140.69,120.57 L141.04,127.25s0.13,2.37 -0.41,3.84l2.07,-1.46c-0.81,-0.65 -0.65,-2.63 -0.65,-2.63l3.35,-1.79s1.59,1.01 1.43,2.25l2.85,-1.16c-1.66,-0.63 -3.72,-2.09 -3.72,-2.09zM141.76,122.64 L144.82,124.67 141.84,126.28zM156.73,115.68c-0.38,1.63 0.03,4.85 0.63,7.53 2.07,-0.19 3.66,-0.4 4.37,-1.28l0.19,2.2c-2.04,0 -4.29,0.24 -6.35,0.56 0.03,-3.01 -0.28,-6.97 -1.13,-8.67zM172.3,116.66c-0.96,1.38 -1.75,4.53 -2.16,7.23 2,0.59 3.56,0.97 4.54,0.4l-0.63,2.13c-1.9,-0.74 -4.09,-1.35 -6.12,-1.81 1.12,-2.78 2.28,-6.59 2.12,-8.47zM188.04,123.79 L182.22,127.1s-2.06,1.19 -3.62,1.37l2.22,1.18c0.22,-1 2.07,-1.75 2.07,-1.75l3.12,2.19s-0.21,1.9 -1.38,2.29l2.31,2.03c-0.16,-1.76 0.21,-4.28 0.21,-4.28zM186.66,125.67 L186.2,129.33 183.42,127.38zM191.13,139.37 L190.22,137.65c1.5,-0.12 6.01,-2.76 8.09,-4.12l0.32,0.59 -1.94,8.86c1.73,-0.69 4.48,-2.13 5.23,-2.93l1.03,1.96c-1.93,-0.28 -5.5,1.48 -8.2,3.13l-0.26,-0.47 1.66,-8.63s-4.48,2.03 -5.01,3.32m13.67,12.82c-0.18,-1.31 -0.21,-2.2 -0.84,-2.5l-0.07,-0.54 1.6,-0.26c0.66,1.72 1.41,6.51 1.31,7.75l-1.65,0.26 -0.07,-0.46c0.54,-0.62 0,-2.88 0,-2.88 -2.44,0.25 -6.69,0.94 -7.29,1.78l-0.38,-2.26c0.87,0.29 5.14,-0.16 7.39,-0.88M196.9,171.65c0.91,-1.44 2.95,-2.75 4.73,-3.95 0.25,1.26 1.18,2.09 2.28,2.15 1.16,0.07 2.35,-0.82 2.56,-1.46 0.31,-0.85 0.69,-4.5 0.69,-4.5 -2.29,0.15 -7.29,-0.28 -9.01,-0.93l-0.22,1.94c1.13,-0.28 3.69,0.25 3.69,0.25l-0.06,0.85 -2.66,2.07c-0.35,0.26 -2.35,2.1 -2.43,3.43 -0.01,0.41 0.34,0.29 0.43,0.15m5.37,-6.45c1.04,-0.01 3.22,0.12 3.82,0.38 -0.25,2.1 -0.91,2.85 -2.03,2.73 -1.03,-0.12 -2.1,-0.71 -1.79,-3.12m-8.32,12.29 l-1.37,2.35c1.06,-0.16 2.53,0.68 3.72,1.46 0.4,1.06 1.96,3.76 1.84,6.41l1.76,-3.03c-0.78,-0.35 -1.69,-0.74 -2.28,-3.57 2.32,-0.87 4.06,-1.15 4.79,-0.74l1.56,-2.65c-1.81,1.09 -6.06,2.2 -6.76,1.97 -0.74,-0.43 -2.82,-1.07 -3.26,-2.2"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M183.48,195.31m-1.84,0a1.84,1.84 0,1 1,3.67 0a1.84,1.84 0,1 1,-3.67 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M140.12,195.31m-1.84,0a1.84,1.84 0,1 1,3.67 0a1.84,1.84 0,1 1,-3.67 0"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M164.67,205.44c-0.19,0.13 -0.71,-0.13 -0.82,-0.54 0,0.13 -0.03,0.54 -0.28,0.78 -0.6,0.57 -1.25,0.29 -1.76,0.29s-1.16,0.28 -1.76,-0.29c-0.25,-0.24 -0.28,-0.65 -0.28,-0.78 -0.12,0.41 -0.63,0.68 -0.82,0.54 -0.18,-0.13 -0.09,-0.71 0.26,-0.94 -0.12,0.03 -0.53,0.13 -0.82,-0.03 -0.74,-0.4 -0.66,-1.1 -0.82,-1.59s-0.63,-1.01 -0.26,-1.76c0.15,-0.31 0.53,-0.47 0.65,-0.51 -0.43,0.01 -0.82,-0.4 -0.76,-0.6 0.07,-0.22 0.65,-0.31 0.98,-0.04 -0.09,-0.1 -0.29,-0.46 -0.24,-0.79 0.15,-0.82 0.84,-0.97 1.25,-1.28 0.43,-0.31 0.78,-0.91 1.6,-0.79 0.34,0.04 0.6,0.35 0.69,0.46 -0.15,-0.4 0.12,-0.91 0.34,-0.91s0.49,0.51 0.34,0.91c0.09,-0.1 0.35,-0.41 0.69,-0.46 0.82,-0.12 1.18,0.49 1.6,0.79 0.41,0.31 1.1,0.46 1.25,1.28 0.06,0.34 -0.15,0.69 -0.24,0.79 0.34,-0.26 0.91,-0.18 0.98,0.04 0.06,0.21 -0.34,0.62 -0.76,0.6 0.12,0.04 0.5,0.21 0.65,0.51 0.37,0.75 -0.1,1.28 -0.26,1.76s-0.09,1.19 -0.82,1.59c-0.29,0.16 -0.71,0.06 -0.82,0.03 0.35,0.24 0.44,0.81 0.26,0.94M160.43,203.38c0.24,0.18 -0.4,0.71 -0.15,1.37 0.28,0.78 1.06,0.54 1.51,0.54s1.23,0.24 1.51,-0.54c0.25,-0.66 -0.38,-1.19 -0.15,-1.37s0.56,0.59 1.25,0.57c0.84,-0.04 0.85,-0.84 0.98,-1.28 0.15,-0.44 0.62,-1.1 -0.04,-1.62 -0.54,-0.44 -1.26,0 -1.35,-0.28s0.74,-0.34 0.94,-1.01c0.22,-0.81 -0.54,-1.06 -0.91,-1.34 -0.38,-0.26 -0.87,-0.91 -1.56,-0.46 -0.57,0.4 -0.38,1.21 -0.68,1.21s-0.1,-0.81 -0.68,-1.21c-0.69,-0.46 -1.18,0.19 -1.56,0.46 -0.37,0.28 -1.13,0.53 -0.91,1.34 0.21,0.68 1.03,0.74 0.94,1.01s-0.81,-0.16 -1.35,0.28c-0.66,0.51 -0.19,1.18 -0.04,1.62 0.13,0.44 0.15,1.23 0.98,1.28 0.69,0.01 1.01,-0.75 1.25,-0.57"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M161.8,204.43c-0.13,0 -0.29,-0.31 -0.21,-0.56 -0.04,0.07 -0.21,0.25 -0.41,0.28 -0.5,0.07 -0.71,-0.29 -0.96,-0.47 -0.25,-0.19 -0.66,-0.28 -0.75,-0.76 -0.04,-0.21 0.09,-0.43 0.13,-0.49 -0.21,0.16 -0.54,0.1 -0.59,-0.03 -0.04,-0.12 0.21,-0.37 0.46,-0.37 -0.07,-0.01 -0.29,-0.12 -0.38,-0.29 -0.22,-0.46 0.06,-0.76 0.16,-1.06 0.09,-0.29 0.04,-0.72 0.49,-0.96 0.18,-0.1 0.43,-0.04 0.5,-0.01 -0.22,-0.15 -0.26,-0.49 -0.16,-0.57 0.12,-0.07 0.43,0.09 0.5,0.32 0,-0.07 0.01,-0.32 0.16,-0.46 0.37,-0.35 0.75,-0.18 1.06,-0.18s0.69,-0.18 1.06,0.18c0.15,0.13 0.16,0.38 0.16,0.46 0.07,-0.24 0.38,-0.4 0.5,-0.32 0.1,0.09 0.06,0.43 -0.16,0.57 0.07,-0.03 0.32,-0.09 0.5,0.01 0.44,0.24 0.4,0.66 0.49,0.96 0.1,0.29 0.38,0.6 0.16,1.06 -0.09,0.18 -0.31,0.28 -0.38,0.29 0.25,0 0.5,0.25 0.46,0.37 -0.04,0.13 -0.38,0.19 -0.59,0.03 0.04,0.06 0.18,0.28 0.13,0.49 -0.09,0.49 -0.5,0.57 -0.75,0.76 -0.25,0.18 -0.46,0.54 -0.96,0.47 -0.21,-0.03 -0.37,-0.21 -0.41,-0.28 0.09,0.25 -0.07,0.56 -0.21,0.56M161.8,202.75c0.16,0 0.06,0.44 0.37,0.65 0.37,0.25 0.63,-0.09 0.84,-0.24s0.62,-0.29 0.49,-0.72c-0.1,-0.37 -0.54,-0.4 -0.5,-0.56 0.04,-0.15 0.43,0.09 0.74,-0.15 0.35,-0.28 0.1,-0.63 0.03,-0.87 -0.09,-0.24 -0.09,-0.68 -0.54,-0.69 -0.38,-0.01 -0.54,0.4 -0.68,0.31s0.22,-0.38 0.09,-0.74c-0.16,-0.43 -0.57,-0.29 -0.82,-0.29s-0.66,-0.13 -0.82,0.29c-0.13,0.35 0.22,0.65 0.09,0.74s-0.29,-0.32 -0.68,-0.31c-0.46,0.01 -0.46,0.46 -0.54,0.69 -0.07,0.24 -0.32,0.59 0.03,0.87 0.31,0.24 0.69,0 0.74,0.15 0.04,0.16 -0.4,0.19 -0.5,0.56 -0.13,0.43 0.28,0.57 0.49,0.72s0.47,0.49 0.84,0.24c0.31,-0.21 0.21,-0.65 0.37,-0.65"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M161.8,201.5m-0.71,0a0.71,0.71 0,1 1,1.41 0a0.71,0.71 0,1 1,-1.41 0"/>
  <path
      android:pathData="M137.9,185.8a588,588 0,0 1,48 0"
      android:strokeLineJoin="round"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m160,143.3 l1.3,1.5m2,19.2q-0.1,2 0.2,3.2m-3.2,-3c0.4,0.5 1,2.3 1,3"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.2,185.6c-0.6,0.2 -1.6,0.8 -1.6,1.3s1.2,0.6 1.6,0.7c-0.7,0.2 -2.7,0.6 -3.6,0.2 -0.6,0.3 -5.2,-0.7 -6.2,-0.7 -0.6,0 -1.4,-0.6 -1.3,-1.7 0,-0.2 0.2,-0.8 0.7,-1 -0.3,-0.6 1.1,-1.5 2.5,-2.4 -2,0.3 -4.7,-0.5 -5.4,-1.3 -1.6,-0.6 -2.3,0 -3,1.4 0,2.4 -1.3,3.6 -3,3.4 -1,-0.2 -1.3,0.3 -1.6,0.5l3.3,1c2.2,2 1,3.6 0.5,4s-1.7,0.1 -2.4,0.1c-0.8,0 -0.8,0.3 -1,0.4q-1,0.5 -1.3,0.2c0,-0.3 0.3,-1.7 1.2,-1.9 0.5,0 1.5,-0.3 1.5,-0.8s-1.8,-0.4 -3.2,-0.9c-1.1,-0.4 -1.5,-1 -1.1,-2.9 0.3,-2 2.7,-2.3 3.9,-2.3 -0.2,-0.8 0,-2.3 1,-3.3 1.6,-1.5 4,-1 6.1,-0.8 1.2,-0.7 3.4,-1.7 5.6,-1.8 2,0 10.6,-2.3 14.5,-1.8 -1,-1.3 -0.7,-4.4 0.9,-5.3 2,-1.2 4.2,2 4.8,1.4s-0.7,-1.4 -2.2,-3c-0.4,-0.6 -0.3,-0.6 -0.3,-0.8q0,-0.8 0.3,-1.2c0.2,-0.1 1.4,0.3 1.7,0.8q0.7,1 1.3,0.5c0.5,-0.4 -0.2,-0.2 -0.5,-1 -0.1,-0.5 -0.5,-0.9 -0.4,-1.3a7,7 0,0 1,2.3 -1.6q0.5,0.7 0.9,1.3c0.7,1.1 1.4,0.8 1.9,1.6q0,0.6 0.5,1.7a10,10 0,0 0,3.5 3.6q0.5,0.3 0.6,0.7 0,0.7 -0.4,1c-0.2,0.2 -1,0.2 -1.6,-0.1q-1.4,-0.8 -2.1,-0.7c-1.4,0.2 -2.3,2.4 -4.3,2.4 -1.8,-0.2 -3,-2.9 -4.1,-2.1s2.5,3 2,4c0.4,0.2 0.9,1 0.7,1.7a6,6 0,0 0,3.9 -2.7q1.5,-2 2.2,-2.2c0.6,-0.3 1.3,0 1.3,0.5l0.9,0.3q0.6,0.5 0.2,1l-0.6,-0.4q-0.5,0 -1.2,0.6 -0.5,0.8 -1.2,1.7l-2,2.2q0.3,0.8 0.2,1.5 0.3,0 0.7,0.2 0.2,0.1 0,0.7c0,0.5 -2,2.7 -2.4,2.6 -0.8,0 -1.4,-1 -1.3,-1.3 -0.9,0.4 -1.3,-0.6 -1.3,-1a7,7 0,0 1,-2.2 1.5q-0.6,0.3 -0.8,-0.4c-0.1,0.4 -1.3,0.8 -1.6,0.9 -1.5,0.3 -3.1,1 -4.6,1l-3.8,-0.1z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.5,171.4c0,0.6 -0.9,2 0.4,3.2 0.8,0.6 2.1,1.1 2.5,2 0.4,0.7 -0.1,1.3 -0.3,1.3 -0.8,-0.2 -1.9,1.2 -2.3,2.2 -0.4,-1.4 -1.3,-1 -2.5,-0.8q0.3,-2.3 -2.6,-2.6c0.3,-0.3 -0.2,-0.7 -0.6,-1.1s-1.8,-0.1 -2,-0.4c0,-0.3 0.7,-1 0.3,-1.5 -0.4,-0.7 -2,-0.3 -2.6,-0.5q0.3,-1 -0.2,-2c-0.5,-1 -2.2,-0.5 -3.1,-0.7q1.1,-0.8 0.6,-2.1c-0.4,-1 -2.4,0.1 -2.4,-1 0.1,-0.5 2.4,-0.8 3.3,-0.9s3.7,0.7 6.5,1.3c0.6,0.6 5,1.9 5,3.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.8,182.6a20,20 0,0 0,4.1 -2.8m-25.6,-1c1.2,0 3.9,0.4 4.7,1.4 0.7,1 1.2,2.7 -4.1,4 1.6,0 4,-0.3 4.8,-0.1 0.8,-0.2 2.5,-0.7 3.2,-0.5 -0.3,0.1 -1.2,0.4 -1.2,1q0,0.5 1.1,0.8m5.1,-3.6c1.9,0.4 4.6,-0.2 5,1.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M181.2,166.8c0.5,0.6 2,1.5 2.7,2 1,0.8 1.4,-0.1 0.7,-1.4"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.1,175.2a30,30 0,0 1,6.1 -0.7m-8.4,-1.3c3,-0.8 6.2,-0.5 8.2,-0.5m-0.5,6.4 l2.6,-2.5m-5.4,0c1,-0.3 2.6,-1 4,-1.2m-4.2,-7.6c1.6,-1.5 3.4,-1.3 5.2,-2.2 1,-0.6 2,-2 3,-3.1q0.6,-0.7 2,-0.7 2.2,0.3 -0.1,0.7c-1.2,0.2 -1.3,0.7 -1.7,1.6 -0.4,0.8 0,1.7 0.2,2.3l-0.8,2.2q0,0.4 0.5,1.1m-16.3,0.8c4,-0.9 10,0.7 11.1,0.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.7,177c1.3,-0.7 1.3,-2 3,-2.6m-6.6,9.1c1.7,-0.8 2,-1.5 3,-2.2m0.9,-13.7q-0.4,0 -0.8,-0.3 -0.4,-0.6 -0.1,-0.8 0.1,-0.3 0.7,0.2c0.6,0.5 0.3,0.7 0.2,0.9m0.3,3c-0.8,0.2 -1,-0.5 -1.7,-0.6 -0.9,-0.2 -1.4,0.4 -1.4,0.9s0.5,1 1,1 1,-0.3 2.1,-0.2m-3.8,-7.2q1,0.2 1.7,-0.2"
      android:strokeLineJoin="round"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.7,150.7a0.4,0.2 75,0 1,-0.2 -0.7,0.4 0.2,76 0,1 -0.2,-0.7 0.4,0.2 77,0 1,-0.2 -0.7,0.4 0.2,79 0,1 -0.1,-0.7 0.4,0.2 80,0 1,-0.2 -0.7,0.2 0.4,-8 0,1 -0.1,-0.7 0.2,0.4 -7,0 1,-0.1 -0.8,0.2 0.4,-5 0,1 -0.1,-0.7 0.2,0.4 -3,0 1,-0.1 -0.7,0.2 0.4,-1 0,1 0,-0.8 0.2,0.4 1,0 1,-0.1 -0.7,0.2 0.4,4 0,1 0,-0.8 0.2,0.4 6,0 1,0 -0.8,0.2 0.4,9 0,1 0.1,-0.7 0.2,0.4 12,0 1,0.2 -0.8,0.2 0.4,15 0,1 0.1,-0.7 0.2,0.4 19,0 1,0.2 -0.8,0.2 0.4,23 0,1 0.3,-0.7 0.2,0.4 27,0 1,0.3 -0.7,0.2 0.4,31 0,1 0.3,-0.7 0.2,0.4 35,0 1,0.5 -0.7,0.2 0.4,40 0,1 0.5,-0.7 0.2,0.4 45,0 1,0.5 -0.6,0.2 0.4,49 0,1 0.6,-0.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".1"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m163.6,150.4 l-0.9,0.3m0.7,-1 l-0.9,0.3m0.8,-1 l-1,0.3m0.9,-1 l-1,0.3m0.9,-0.9 l-1.1,0.2m1,-1 l-1.2,0.3m1,-1 l-1.1,0.3m1.1,-1 l-1.2,0.2m1.2,-0.8h-1.4m1.4,-0.7h-1.4m1.4,-0.7h-1.5m1.5,-0.7h-1.5m1.6,-0.7 l-1.6,-0.1m1.7,-0.6 l-1.6,-0.2m1.7,-0.4 l-1.7,-0.3m1.9,-0.4 l-1.7,-0.4m1.9,-0.2 l-1.8,-0.5m2,-0.2 l-1.8,-0.6m2,0 l-1.7,-0.7m2,0.2 l-1.7,-1m2.1,0.4 l-1.8,-1m2.2,0.5 l-1.7,-1.2m2.1,0.7 l-1.6,-1.4m2.1,1 l-1.6,-1.6m2.1,1 l-1.5,-1.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".1"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m173,140.7 l-2.5,0.3c-0.2,0.5 -0.2,1.9 -0.2,2.4 2.3,2.3 4.6,5.2 4.8,7.9a5,5 0,0 1,1.6 2.3c1.7,-0.9 3.4,-2.3 4.8,-2.7 1,-0.2 2.6,-0.3 3.5,0.2l5.6,3.4q1.8,1.2 2.6,2.6c0.5,0.8 0.6,2.3 0.4,2.7l-3.4,-1q-0.2,-0.8 0.3,-1.7c0.2,-0.6 0.2,-0.6 -0.3,-1q-0.6,-0.3 -1,0 -0.7,0 -1.2,-0.6a21,21 0,0 0,-4.5 -2.2q-1,-0.2 -2.2,0.6c-0.4,0.6 -1.6,2.4 -2.5,3.3q-0.3,0.3 -0.1,0.8l3.6,0.1c1.4,0.2 3,1.6 3.8,2.5 3.5,3.2 4.8,5 5.9,6q0.9,0.7 1,1.8h-3.4q0,-1.8 -0.4,-2.8c-0.4,-0.5 -1.1,-0.3 -1.4,-0.3q-0.5,-0.1 -1,-1c-0.5,-1 -2.3,-2.3 -3.1,-2.8s-1.1,-0.3 -2,-0.4q-1,-0.3 -2.5,0c-0.9,0.3 -3.4,1.1 -5.7,1.4 -0.8,0.4 -3.3,0.8 -6,1.4 -2,0.4 -6.5,1.2 -7.4,1.2 -0.3,2.1 -2.6,5.2 -3.2,5.5q-1,1.5 -0.2,2c0.5,0.5 3.9,0.5 4.7,0.5l2.9,0.1c0.5,0.1 1.3,0.8 2.2,1.3 0.6,0.3 0.7,0.7 1.1,1.3q0.5,0.6 0.8,1.2h-4q-0.1,-1.6 -1,-1.7c-0.6,0.4 -1.3,0.9 -2.1,0.6 -3.1,-0.2 -5.1,0 -8,-0.3 -0.9,0 -1.6,-1.2 -1.3,-2.3q0.4,-1.4 0,-2.6c-0.3,0.3 -1.5,1.4 -2.1,2.2 -1,1.1 -0.4,2.3 0,3 0.5,0.5 3.9,3.7 4.3,4.6 0.3,0.6 1.7,1.6 3,2.7q0.7,1.3 1.8,2.1h-5l0.2,-1.3c0,-0.3 -0.3,-0.9 -1,-0.8s-1.2,-0.7 -1.4,-1.3 -2.5,-3.6 -3.2,-4.2l-3,-2.9c-0.4,-0.1 -0.5,-0.8 0,-1.3a13,13 0,0 0,2 -3.8c0.3,-0.9 0,-1 -0.2,-1.2a20,20 0,0 1,-5.4 -6,7 7,0 0,1 -4,-0.3q-1.7,-1 -2.9,0.4c-0.4,0.7 -0.4,1.8 0.3,2.3q1.5,1.3 3.3,3.4c1,1.3 0.1,2.3 -1.3,3.5 0.2,0.5 0,0.4 0.2,1.3 0.5,0.7 0.5,2.4 -0.1,2.7q-1,0.5 -2,1.6c-0.8,0.9 -1,0 -0.8,-1.4 0.2,-0.9 -0.5,-1.6 -1.1,-2.3s-0.3,-2.6 0,-3.2c0.5,-0.6 0.8,-1.7 0,-2.6 -0.9,-0.8 -2.5,-2.6 -2.3,-5a3.8,3.8 0,0 1,5.2 -3.3c3,1.3 4.7,1 5.6,0.8q1.2,-2.2 3.8,-3.8c1.7,-1 5,-2.3 8.3,-2.8l8.1,-3c1.1,-0.2 1,-1.2 1,-1.8 -1,-5.5 -0.8,-11.3 3.7,-14.2 0.5,-0.5 0.3,-1.7 1,-2.6l1.3,2.2c2,0.7 5.4,1.9 6.8,2.6q1,0.3 1.7,0.5t1,1q0.3,1 -0.4,1.5c-0.3,0.1 -1.9,-0.4 -2.3,0q0,0.5 1.4,1c0.3,0.2 -0.3,1 -0.7,1q-0.5,0 -0.9,-0.3 -1.5,-0.3 -3,-1"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.3,137.1c0,3.4 1.1,5.1 3,6.3m6.4,10.2a10,10 0,0 1,0 4m-24.7,13c2.5,-1.1 4.8,-4 4.7,-5.8 0,-1 -0.9,-2.5 -3.1,-2.6m3,2.6c0.8,0.3 2.3,0.1 3.5,0.3m12.6,-8.3c2,0.1 4.6,1.3 6,1.2m-7,2.8c1.8,0.4 1.8,1.2 5.3,1m-6,-12.2c1.9,0 3,1 4,1.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.3,166.4q2.1,0.2 3,-1.4m8,-27.9c0,2.8 1,3.5 3.2,3.9"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178,137.6c-0.1,0.7 -0.2,1.3 1,1.2m-6.1,-3.1c-0.9,2.2 3.6,2.5 3.7,4m-25,14.7c2.2,1 2,5.3 2,7.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.6,139.7c-0.7,1.4 -2.3,3.5 -3.8,4.6q-3.5,2.6 -8.1,3.7 -0.7,0.3 -1.6,0.1 -1,0 -1.7,-1.4c-0.2,-0.4 -0.1,-2.6 0.7,-2.9a6,6 0,0 1,2.4 -0.4q0.9,0 1,0.8 -0.1,0.8 -1,0.7c-0.6,-0.2 -1.5,-0.6 -2,-0.2 -0.4,0.2 -0.7,1.2 -0.4,1.7q0.5,1 1.1,1l1.3,-0.1q4.4,-1.2 7.8,-3.6c1.5,-1 3,-3.4 3.6,-4.4zM163.6,144c-0.9,-0.2 -2.4,0 -2.5,0.4q1,-0.5 2.5,0 0.5,-0.1 0,-0.4m6,-7.5 l0.3,-0.7c0.1,-0.1 -0.2,-1.7 1.4,-0.9 1.7,1 0.4,1.7 0.3,1.8l-0.4,0.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".2"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m166.7,135 l9.5,5"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.7,135.4a28,28 0,0 1,-6.4 -0.5c-0.8,-0.1 -3.1,3.2 -4.3,3.4 -1.2,0.3 -2.4,-2.4 -4.1,-1.8 -0.9,0.7 0,4 -0.5,4.3 -0.5,0.5 -2,-1.6 -3,-1 -1,0.8 0.8,5.1 0.3,5.6s-2.6,-1.9 -3.3,-1.4 0.7,7.2 0,7.5c-0.6,0.4 -3.7,-1.8 -4,-0.9 -0.5,1 4.3,3.1 5.6,3.2l1.3,-0.5c0.4,0.2 4.2,0.5 4.6,0.5l2.1,-2.2c3.3,-4.3 7,-8.3 11.7,-8z"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m140.2,136.5 l6.3,-0.3m-10.1,3.6 l3,-1m-6,5 l2.9,-1.2m-7,8 l4,-3.6m3,6.2c1.7,-3 5,-6.2 7.9,-7.9m-10.7,6.2c2.4,-2.9 9.1,-7.6 11.3,-8m-5.4,-2.7c2.8,-1.2 6.8,-1.4 10,-1.7m-5.5,-0.8c0.4,0 5.2,-0.6 7.3,-0.5m-14.5,7.7q5.4,-3.3 11.5,-5.3"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.2,191.2c0.3,0.1 0.6,0.8 0.6,0.8s-0.6,0.3 -0.8,0.2l-29.5,-8.8c-0.8,-0.2 -0.5,-1.6 0.4,-1.3zM140.2,150.2q-0.4,-1.1 -1.2,-1.3c-0.2,0 -0.9,0.7 -0.8,0.9 0,0.4 0.9,1 1.2,1zM142.9,151.2 L140.4,153.7c-0.3,0.3 0.3,0.8 0.6,0.5l2.5,-2.4c0.3,-0.3 -0.3,-0.9 -0.6,-0.6m-1.2,2.4 l7.7,7.9 1.5,0.2 -0.2,-1.6 -8,-7.6z"
      android:strokeLineJoin="round"
      android:strokeWidth=".4"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m149.8,160.6 l-7.6,-7.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".1"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160,138.6q0,-0.7 0.5,-1c0.5,-0.3 0.3,-0.1 0.6,0l0.8,0.5q0.3,0.2 0.4,0 0,-0.4 0.3,-0.8l0.2,-0.2q0.3,0 0.2,-0.3t0.3,-0.4c0.2,0 0.6,0.3 0.6,0l0.3,-1.6c0.6,0.1 0.4,-0.6 0.6,-0.6 0.6,0.2 0.9,-0.8 0.5,-1.2q0.1,-1.1 1.7,-1c-0.1,-0.5 -0.9,-0.7 -1.4,-1.4s-2,-1.6 -3.8,-1.6 -3.1,1.6 -3.8,2.3 -2,1 -2.3,1q-0.5,-0.3 -1,-0.4L153,131.9c-0.6,0 -0.8,0.4 -1.5,0.3 -0.4,0 -2,0 -2.5,0.2s0,1.2 0.5,1.3c0.2,0 1.5,0 2,0.3s2,0.4 2.6,0.1c1.3,-0.4 2.1,0.4 3.5,0.8l0.5,0.1q0.1,1.2 -0.4,1.1h-1.2l-1.3,-0.6q-0.9,0 -1.2,0.2c-1.7,0.7 -7.7,5.4 -8.3,6.5 -0.7,1.2 -1.7,1.9 -2.6,3.7 -0.5,1 -1.5,2.2 -2.1,3.8 -1,0.5 -2.1,1.4 -2.3,1.7q0,0.4 0.4,0.6 0,0.3 0.6,0.5 -0.1,0.4 0.4,0.4 0,0.5 0.5,0.7l2.4,-2.5q0.2,-1.7 1,-2.8a41,41 0,0 0,3.4 -4.9c0.3,-0.4 3.2,-1.5 4,-2.2a14,14 0,0 0,0 4.6c0.5,2 1.1,3.3 1.5,5s0,2.7 0,3.5q0,0.7 1,1c0.5,0.4 2.9,0 3.6,0.3q2.2,0.5 3.1,0.6a5,5 0,0 1,3.5 0.6c0.6,0.5 1.7,3 2.1,4s2.6,2.1 3,2.7c0.5,0.5 0.9,2 0.8,2.7q-0.2,1 0.7,0.8c0.5,0 2.5,0.3 2.8,0.5 0.4,0.2 1.2,0.9 1.5,0.9s1.7,0.2 2.4,0.1 0.6,-1.1 0.3,-1.2l-2,-0.4a8,8 0,0 1,-2.7 -2.1q-0.8,-1.2 -1.3,-2.5l-3.1,-4.5 -1.6,-3.8c-0.3,-0.7 -0.8,-0.7 -1.3,-0.9 -1.8,-0.5 -2.5,-0.7 -4.4,-1 -1.3,-0.3 -1.6,-0.4 -1.8,-1.4 0,-0.8 -0.6,-2.2 -0.3,-3.2l0.4,-1.8c0,-0.5 0.4,-1.3 0.4,-1.7l0.2,-3zM161.4,146.8c-0.2,0 -1,0.2 -1.5,0 -0.1,0 0,-0.4 0,-0.4s-0.2,-0.3 0,-0.4q-0.1,-0.2 0,-0.4 -0.3,-0.3 0,-0.5l1.4,-0.1q0.4,0.9 0.1,1.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#fff"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.1,135.1c0.4,0.3 0.4,0 1.1,-0.2s1.3,0.4 1,1q-0.4,1 0.6,1.2c1,0.2 1,-0.7 1.2,-1q0.3,-0.3 0.7,-0.7c0.2,-0.8 1,-0.6 1.5,-0.5m1,-1.9c-0.3,0.2 -1.2,-0.5 -1.4,-1.1q-0.3,-1.3 -1.8,-1.4c-1.5,-0.1 -3,0.6 -3.3,1.7l-0.3,1.7q-0.6,0 -0.7,0.2l-0.1,0.8m-6.3,6.3 l2,-1"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.4,140.3c0.5,1.3 1,2.6 1.7,3m1.1,0.5q1.2,0.6 2.5,0.2m-5.1,9.5c2.1,0 3,1 4.7,1.6 1.8,0.5 4.2,0.2 6.5,1.3m-11.9,-9.7q1,0.6 2,0.6m0.9,0q1.8,0.4 2.3,1.4m-2.8,1.1c1,1 3,1.8 4.2,1.8"
      android:strokeLineJoin="round"
      android:strokeWidth=".3"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M157,136c0.9,3.2 1,4.4 3.2,5m-4.1,-5.1c1,3.1 2,4.8 4.1,5m-5,-5.4c1,3.1 2.4,5.3 5,5.4m11.6,24.8q-0.6,-0.4 -1,-1.3l-0.7,-1.6 -0.5,0.3q-0.6,0 -0.2,-0.4l0.4,-0.2 -0.6,-1.2q-0.3,0 0.2,-0.3t0.4,0l0.6,1.2 0.4,-0.2c0.3,-0.2 0.5,0.3 0.2,0.4l-0.5,0.2 0.8,1.6c0.4,0.7 0.5,0.9 0.5,1.5m-0.1,-3.4 l-2.4,1.3m-2.2,-1.8 l3.2,-1.6m-3.7,1 l3.3,-1.6"
      android:strokeLineJoin="round"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m140.1,153 l1.1,-1m-1.5,0.5 l1,-1m-1.6,0.5 l1.1,-1m19.8,-4.6h1m-1.1,-0.8h1m-1,0.4h1m1.3,-9.2 l0.6,0.3"
      android:strokeLineJoin="round"
      android:strokeWidth=".1"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
</vector>
