<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#012169"/>
  <path
      android:pathData="M0,0h400v200H0z"
      android:fillColor="#012169"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M412.9,308.9a11.3,12.7 0,0 0,-1.7 0c-3,0.6 -7.6,11.3 -10,17.6 -4.1,5.4 -6.2,9.6 -7.8,12.7 -0.7,1.2 -1.7,2.6 -1.4,4.2 -6,1.6 -6.3,7.4 -6.3,9.6 0,2.5 0.7,3.7 0.7,3.7l3.6,7.5v0.1c3.8,9 9.4,20.9 12.7,21.8 4.4,1.5 16,-3.3 24.8,-11.7a140.6,157.8 0,0 0,53.2 12.1c20.4,0 38.7,-5.5 53,-12.1 8.8,8.4 20.4,13.2 25,11.7 3,-1 8.7,-13 12.6,-21.8l3.5,-7.6s0.7,-1.2 0.7,-3.6c0,-2.2 -0.3,-8 -6.3,-9.6 0.3,-1.6 -0.6,-3 -1.3,-4.3 -1.7,-3.2 -3.8,-7.4 -7.7,-13 -2.6,-6.2 -7.2,-16.7 -10,-17.2 -4.2,-0.8 -13,1.5 -13,2.4v0.4l-17.7,18.6c0,0.5 0.9,14 4.3,26a126,141.5 0,0 1,-43.3 8.2c-17,0 -31.5,-3.4 -43.2,-8.3a116.6,130.9 0,0 0,4.3 -26L424,311.8v-0.4c0,-0.7 -6.8,-2.5 -11.1,-2.5zM417.3,331.9c2.3,0 4.6,2.2 6.5,4.4l-0.1,1.3a67.5,75.7 0,0 1,-2.8 10.2,73.6 82.6,0 0,1 -12.2,-9.9c1.9,-2.3 5,-5.4 8.2,-5.8h0.4zM543.9,331.9h0.4c3.3,0.6 6.3,3.7 8.3,6a73.4,82.4 0,0 1,-12.3 10,67.1 75.4,0 0,1 -2.8,-10.3 14.6,16.4 0,0 1,-0.2 -1.3c2,-2.3 4.3,-4.3 6.6,-4.3zM401.5,358.3q3.6,3.1 9.1,6.8a10.5,11.8 0,0 1,-3.2 1.6q-2.4,0.5 -4.7,0a15,16.8 0,0 1,-1 -5.6,60.3 67.7,0 0,1 -0.2,-2.8zM559.7,358.3 L559.6,361.1a15,17 0,0 1,-1 5.5,10.3 11.5,0 0,1 -8,-1.5q5.6,-3.7 9.1,-6.8z"
      android:strokeWidth="10"
      android:fillType="evenOdd"
      android:strokeColor="#fff">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="218.6"
          android:startY="569.33"
          android:endX="241.32"
          android:endY="554.8"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M426.2,339.6s-6.1,-11.1 -11.1,-10.2c-5.1,0.7 -10,7.8 -10.2,8.2a44.5,52 0,0 1,-7.5 -9.5c0.3,-0.4 7.8,-20.9 12,-21.8 4,-0.9 12.7,1.5 12.7,2.3z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="427.56"
          android:startY="335.46"
          android:endX="398.82"
          android:endY="316.34"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M439.8,327.7c0,0.8 -2.2,30.2 -10.9,40.4 -8.6,10.3 -23,16.6 -28.2,15.1 -5,-1.6 -16.5,-31.3 -16.5,-31.3s12.2,14.2 21.3,11.8 14.6,-20.1 16.3,-28.9c1.7,-8.6 0.3,-25.7 0.3,-25.7l17.8,18.6z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="442.41"
          android:startY="347.71"
          android:endX="378.9"
          android:endY="334.57"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M532.9,339.6s6.2,-11.1 11.2,-10.2c5,0.7 9.8,7.8 10.2,8.2a44.5,52 0,0 0,7.5 -9.5c-0.4,-0.4 -7.9,-20.9 -12,-21.8s-12.8,1.5 -12.8,2.3z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="562.01"
          android:startY="318.36"
          android:endX="532.62"
          android:endY="320.26"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M519.3,327.7c0,0.8 2.2,30.2 10.9,40.4 8.7,10.3 23,16.6 28.2,15.1 5,-1.6 16.6,-31.3 16.6,-31.3s-12.3,14.2 -21.4,11.8 -14.5,-20.1 -16.2,-28.9c-1.8,-8.6 -0.4,-25.7 -0.4,-25.7z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="577.75"
          android:startY="335.78"
          android:endX="514.96"
          android:endY="341.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M401.2,326.2c-4,5.5 -6.1,9.8 -7.9,13q-2.5,4.7 3.2,14c4.3,6.7 39,33.4 84.1,33.4 45.2,0 79.8,-26.6 84,-33.4q6,-9.4 3.3,-14c-1.7,-3.2 -3.9,-7.5 -7.9,-13 0,8.3 -28.7,38.5 -79.4,38.5s-79.4,-30.2 -79.4,-38.5z"
      android:strokeWidth="1.5"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="568.77"
          android:startY="356.29"
          android:endX="392.51"
          android:endY="356.29"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m557.7,378.8 l7.8,-15.1 -3.9,-3.2c-1,3.8 -5.5,5.1 -6.3,10 -0.7,4.7 3,9.4 2.4,8.3z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="395.88"
          android:startY="370.24"
          android:endX="652.79"
          android:endY="372.71"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M564.8,374.5s1.4,-6.8 -3,-7.6 -6.8,3.8 -6.1,2c0.6,-2 3.2,-4 3.7,-10.7 0.3,-6.7 -0.4,-10.3 -0.4,-10.3s1.8,-8.7 8.8,-7.5c7.2,1.3 7.5,7.6 7.5,9.9a9,9 0,0 1,-0.6 3.5z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="577.87"
          android:startY="352.79"
          android:endX="556.92"
          android:endY="354.31"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:fillColor="#FF000000"
      android:pathData="m539.2,351.7 l1.5,2.8 1,-0.6 -0.7,-2 0.1,-0.3 1.7,-1.2 4.4,8.7v0.5l-1,0.7 0.5,0.8 4.2,-3 -0.4,-0.8 -1,0.6h-0.3l-4.4,-8.8 1.6,-1.2h0.4l1,1.6 1,-0.7 -1.5,-2.8 -8,5.7zM528.7,358.4 L529.1,359.2 530,358.8 530.3,358.9 534.2,367.8 534.1,368.2 533.3,368.8 533.6,369.6 537.7,367.1 537.4,366.3 536.5,366.7h-0.4l-1.8,-4.3 4.3,-2.4 1.8,4v0.5l-1,0.6 0.4,0.9 4.2,-2.5 -0.4,-0.8 -0.9,0.4 -0.3,-0.2 -3.8,-8.8v-0.4l0.8,-0.6 -0.3,-0.8 -4.1,2.5 0.3,0.8 1,-0.5 0.3,0.2 1.5,3.6 -4.2,2.5 -1.6,-3.7 0.2,-0.4 0.8,-0.5 -0.4,-0.9zM526.3,366 L526.5,366.8 527.7,366.5 528.1,366.6 529,370.3 528.2,370.7q-3.9,1.1 -5.3,-3.7c-0.8,-3.2 0.2,-5.6 2.5,-6.4a3.5,4 0,0 1,1.4 -0.1l0.3,0.2 0.8,1.7 1.2,-0.4 -0.7,-3q-2,0 -3.5,0.6c-3.6,1.3 -5.2,4.5 -4.3,8.2s3.9,5.4 7.2,4.3a11.3,12.7 0,0 0,3.4 -2L530,366l0.2,-0.4 0.9,-0.4 -0.3,-0.9zM516.6,376.3 L521.3,374.6 521,373.7 520,373.9 519.7,373.8 517.1,364.2 517.2,363.9 518.2,363.4 518,362.6 513.3,364.2 513.6,365.1 514.6,364.8 515,365 517.5,374.5 517.3,374.9 516.3,375.3 516.6,376.2zM491.1,380.9 L499.4,380.1 499.1,376.7 498.1,376.8 497.9,378.8 497.7,379.1 494.2,379.5 493.8,374.6 495.8,374.4 496.1,374.7 496.3,376h1l-0.4,-4.4h-1v1.4l-0.2,0.4 -2,0.1 -0.4,-4.4 3.1,-0.3 0.2,0.2 0.4,1.7 1.1,-0.1 -0.2,-3 -7.8,0.8v0.9h1l0.3,0.3 0.7,9.9 -0.1,0.3 -1,0.2v1zM478.7,368.6v1h1l0.2,0.3 0.1,9.9 -0.2,0.3 -0.9,0.2v0.8h4.4v-0.9h-1l-0.2,-0.4L482.1,375h4.4v4.6l-0.2,0.3 -0.9,0.1v1h4.4v-1h-1l-0.1,-0.4 -0.2,-10 0.2,-0.3h1v-1h-4.5v1l1,0.1 0.2,0.3v4l-4.3,0.1 -0.2,-4 0.3,-0.3h0.9v-1zM469.3,368.3v3.3h1l0.3,-2 0.2,-0.3h1.9v10.2l-0.3,0.4h-1v1h4.6v-0.9l-1,-0.1 -0.3,-0.4 0.1,-10h1.8l0.3,0.2 0.3,2h1v-3.3zM455,378.9l8,0.4 0.2,-3.3h-1l-0.3,1.9 -0.2,0.2 -3.5,-0.1 0.2,-5 1.9,0.2 0.2,0.3 0.2,1.4h0.8l0.2,-4.3h-0.9l-0.2,1.2 -0.2,0.3h-2l0.2,-4.6 3,0.2 0.2,0.2 0.1,1.7h1.1v-2.9l-7.5,-0.4v0.9l1,0.2 0.1,0.3 -0.4,9.9 -0.2,0.3h-1v0.9zM436.4,373.5 L440.8,375.1 441,374.3 440,373.8v-0.4l2.5,-9.5 0.3,-0.2 1,0.3 0.2,-0.8 -4.3,-1.7 -0.2,0.9 0.9,0.4 0.2,0.4 -2.7,9.4 -0.2,0.3 -1,-0.3zM428.8,370.2a8.4,9.4 0,0 0,2.6 1.6c2.4,0.8 4.3,-0.2 4.8,-2.2a3,3.5 0,0 0,-0.5 -3.2l-1.8,-2q-0.9,-1.2 -0.6,-2.5 0.5,-1.5 2.2,-1 0.6,0.3 1,0.7v0.3l-0.2,2 1,0.3 0.8,-2.8a8,9 0,0 0,-2.5 -1.7c-2,-0.8 -3.8,0.3 -4.4,2.2a3.2,3.5 0,0 0,0.4 2.8q0.7,1.4 1.5,2 1.3,1.5 1,2.9 -0.5,1.6 -2.4,1a4,4.5 0,0 1,-1.2 -0.8l-0.1,-0.3 0.4,-2.2 -1.1,-0.4zM419,365.6 L426.4,369.6 427.6,366.6 426.6,366.1 425.7,367.8 425.4,367.9 422.3,366.1 424.1,361.8 425.9,362.8v0.3l-0.3,1.3 0.8,0.5 1.6,-4 -0.8,-0.4 -0.7,1.1 -0.3,0.2 -1.7,-1 1.6,-4 2.7,1.5 0.1,0.3 -0.4,1.6 1,0.5 1,-2.6 -6.8,-3.9 -0.4,0.8 0.8,0.6 0.1,0.4 -3.6,9h-0.3l-0.9,-0.3zM417.9,350.6 L414.2,347.7 413.7,348.5 414.4,349.2 414.5,349.6 409.9,357.8 409.5,357.9 408.8,357.4 408.3,358.2 411.9,361q2,1.5 3.6,1.4 2.5,-0.3 4,-3c1.8,-3.4 1.3,-6.6 -1.6,-8.8m-1.6,0 l0.8,0.6q3,2.2 0.5,6.6 -2.6,4.5 -5.4,2l-0.8,-0.4 5,-8.7zM445.6,363.6 L445.5,364.4 446.4,364.7 446.5,365.1 444.8,374.8 444.5,375h-1v0.8l4.1,1 0.2,-0.8 -1,-0.4 -0.1,-0.3 0.7,-4.1h0.3c1.5,0.4 1.8,1.3 1.8,3.2q0,1.3 0.8,2.7 0.4,0.7 1.4,0.9a10.1,11.4 0,0 0,1.5 0.2l0.1,-0.8h-0.1q-1.5,-0.3 -1.5,-1.8c-0.2,-1 0,-2 -0.4,-3a2.6,2.9 0,0 0,-1.3 -1.4q2.6,0 3,-2.5 0.6,-3.1 -2.9,-4zM448.6,365.3 L449.2,365.5q2.1,0.6 1.7,2.9 -0.4,2.6 -2.8,1.8l-0.5,-0.1 0.9,-4.8zM502.8,366.3 L502.9,367.3h1l0.3,0.2 1.4,9.8 -0.2,0.3 -1,0.3 0.2,0.8 4.5,-0.8v-1l-1.1,0.2 -0.3,-0.3 -0.6,-4h0.3c1.6,-0.5 2.2,0.2 3,1.9 0.3,0.8 0.5,1.6 1.5,2.2q0.6,0.4 1.8,0.3a12,13.6 0,0 0,1.6 -0.5l-0.1,-0.9h-0.2q-1.5,0.5 -2.2,-0.9 -0.5,-1.3 -1.3,-2.5 -0.6,-0.8 -1.8,-0.8 2.6,-1.2 2.3,-3.7 -0.4,-3.1 -4.2,-2.5zM506.3,366.8h0.7q2.4,-0.6 2.8,1.8t-2.2,3h-0.6l-0.8,-4.8z"/>
  <path
      android:pathData="m401.5,378.8 l-7.8,-15.1 3.9,-3.2c1,3.8 5.5,5.1 6.3,10 0.7,4.7 -3,9.4 -2.4,8.3z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="391.78"
          android:startY="377.66"
          android:endX="411.79"
          android:endY="381.21"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M394.3,374.5s-1.4,-6.8 3,-7.6 6.8,3.8 6.2,2c-0.7,-2 -3.3,-4 -3.7,-10.7s0.3,-10.3 0.3,-10.3 -1.7,-8.7 -8.8,-7.5c-7.1,1.3 -7.5,7.6 -7.5,9.9 0,2.2 0.7,3.5 0.7,3.5z"
      android:strokeWidth="1.2"
      android:fillType="evenOdd"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="406.65"
          android:startY="372.55"
          android:endX="372.22"
          android:endY="351.27"
          android:type="linear">
        <item android:offset="0" android:color="#FFA43907"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M480.5,356.1s-82,-27 -81.4,-129.4l0.6,-135.6H561l0.6,134.8c0.5,102.4 -81.4,130.1 -81.4,130.1z"
      android:strokeWidth="3.9"
      android:fillColor="#0072c4"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="m410.3,123.6 l2,-2q1.9,-1.3 3.7,-2.4c1,-0.6 3,-1.1 3.7,-1.6a9.2,10.3 0,0 0,3 -2.8c0.2,-1.7 0.5,-3 0.5,-5a12,12 0,0 1,3.6 -2c1.7,-0.6 2,-1.4 3.8,-1.7 1,-0.4 2.8,-0.4 4.4,-0.4s2.7,1.4 4.2,1.6c1.8,0.8 2.6,1.4 4.1,2.8a8,8 0,0 0,2.6 3c1.1,0.6 2.2,1.6 3.4,2 1.5,0.9 2.7,1.4 3.8,2.8 1,0.8 1.4,2 2.3,2.7a26.5,29.7 0,0 0,3.4 2.8c1.5,1.1 3,2 5.2,1.7 1.3,0 2.6,-0.3 4,0q2.7,-0.1 4.8,0.5 2.6,0 4.8,0.4 2.2,0.3 4.7,0.3 2.2,0.6 4.8,0.4h9.5q2.6,0 4.7,0.4a37.4,42 0,0 0,4.7 -0.4q2.6,-0.1 5,-0.4h5.8c1.5,0.5 3.4,0.8 4.4,1.7 1.4,0.4 2,1.1 3.4,1.5a11,11 0,0 1,3.4 2.8,18.1 20.3,0 0,1 2.7,4.4c1,1.5 1.3,3.3 2,5.2q0.6,2.6 0.7,5.5 0.5,2.6 0.3,5.6 -0.3,2.8 -1,5.5 -0.1,3 -0.7,5.2c-0.2,1.6 -0.6,2.7 -0.7,4.7v5.6c0,1.8 -0.4,3.2 -0.6,5.1a52,58.4 0,0 0,-2.4 6,10.8 12,0 0,0 -2.7,3.2c-0.7,0.5 -1.1,1.3 -1,1.6 1.4,0.5 1,1.6 1,3.5l0.7,5.2c0.1,2 0.5,3 1,4.4 0.6,0.8 0.7,0.4 -0.7,0.4q-1.7,1.2 -2.3,-1.2a8.8,9.9 0,0 1,-1.4 -4.4q-0.3,-2.9 -1,-4.8c-0.2,-2.2 -0.7,-2.2 -2.7,-2.4q-1.4,-1.2 -4,-1.1c-2,0 -2,-0.2 -2,2v5.6q-0.7,2.2 -1.5,4.3c-1,0.8 -1.4,2.2 -2.7,3.2 -0.5,1.8 -1.4,2 -3,2.4l-2,-2.8c1,-1.8 1.7,-2 2.3,-4 0.8,-1.6 1.2,-2.6 1.7,-4 -0.7,-1.2 -1.3,-1.8 -2,-3.5a19.3,21.6 0,0 1,-1.7 -4.4c-1.1,-0.4 -2.9,-0.5 -3.7,-1.2 -1.7,-0.4 -2.1,1.7 -4,1.7h-4.8c-2.1,0 -2.6,1.2 -4.4,1.1q-0.8,0.2 -3.7,0c-1.5,0.6 -3.2,0.4 -4.8,0.4q-2.4,-0.2 -4.4,-0.8 -2.2,-0.8 -4.7,-1.2c-2,0 -2.7,0.2 -4,1.2 -1,0.7 -2.6,2.3 -3.7,3.2 -0.5,1.3 -0.8,3.5 -1.5,5.1 -0.3,1.7 -0.9,2.6 -1.2,4a13.5,15.1 0,0 0,-1 4.8c-0.2,2.6 -0.7,2.3 -2.1,1.6q-1,-1.7 -2,-3.6c0.2,-2 0.7,-3 0.7,-5.1 -0.4,-1.7 -1.1,-1.5 -2,-0.5 0,2.1 0,3.7 -0.8,4.8a4.4,5 0,0 1,-2 3.1,5 5,0 0,1 -2.7,2.8q-0.2,0 0,-1.6c-0.8,-1.7 0,-3.3 0,-5.5 0,-2 -0.2,-3.3 0.4,-5.1 0,-2 -0.8,-3.8 -1,-5.2 -0.7,-1.5 -1.1,-2.2 -2.8,-2.8 -1.2,-0.6 -3,-1.2 -3.7,-2.5a7,7.9 0,0 0,-3.1 -2.7c-1.5,-0.6 -2.3,-1.2 -3.4,-2 -1,-1.3 -1.9,-1.8 -2.3,-3.5 -0.7,-1.2 -0.8,-3.5 -1.3,-5.2a15.9,17.8 0,0 0,-1 -4.7,17.8 20,0 0,0 -1,-4.8l-1.1,-5.2q0.1,-2.9 -0.4,-5.5v-5.6c-0.2,-1.7 -0.3,-4 -0.6,-5.2 0,-2.1 -0.3,-3.3 -0.8,-4.8 -0.7,-1.4 -0.7,-2 -2,-2.3q-1.3,1.8 -4,2c-1.2,-0.3 -3,-0.6 -4.4,-1.2 -1.4,-0.4 -2.2,-1 -3.3,-1.7a31.7,35.5 0,0 0,-2.1 -3c-0.2,-2.1 -0.6,-3.2 -0.7,-5.3 0,-1.5 0,-0.6 1.4,-2.7"
      android:fillColor="#bcbcbc"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m430.3,138 l0.3,-0.4c-0.6,0.7 -0.4,0.3 1,-0.3 0.6,-0.4 2,-1.4 2.5,-2 0.6,-0.3 1.7,-0.7 2.2,-1.3q0.9,-1.2 2.2,-2.2c0.9,-1 1.7,-1.3 2.8,-2 1.3,-0.2 1.9,-0.8 3.1,-0.3 1.2,0.3 1.7,0.3 2.6,-0.9q1.7,-0.9 2.5,-2.4v-1.8c0,1.8 -0.4,2.5 -1.5,3 -0.7,1.1 -1.8,2.2 -2.2,3.2 -0.7,1.2 -0.9,1.9 -1.6,2.5 -0.7,0.9 -1.7,1.4 -2,2.8 0.4,0.7 0,1 0.8,1.1 0.7,0.7 1.4,0.4 2.4,0a10.4,11.7 0,0 0,1.9 -3.3l1.7,-2.9q1.2,-0.7 1.6,-2.2 1.5,-1 2.5,-2c0.7,-0.4 1,-0.9 2,-1.1a4.4,5 0,0 1,-0.7 3.1c-0.2,1.6 -0.1,2.3 0.3,3.3 0.7,0.6 0.6,1 1,2 0.4,-1 0.6,-2.7 0.7,-3.9v-0.2c0,1.1 0,2.8 -0.3,3.6 0,1.6 0.4,2.4 0.8,3.6q0.5,1 1.1,1.4c0,-1.6 0.5,-2.6 0.6,-4.1l0.4,3.9q0.1,1.9 1,3.3c0.5,0.4 0.7,1.5 0.9,0.6 0.6,-2 0.6,-4 1,-6.2 0,-1 0,-2.6 0.5,-3.3l-0.6,3.3c0.1,-1 0.2,-2.5 0.6,-3.3v-0.4a28.6,32 0,0 1,0.5 4.6q-0.1,1.8 0.2,3.6 1,-0.7 1.7,-2.5a13.3,15 0,0 1,1.9 -3.1l-2,3a13.3,15 0,0 1,2 -3v-0.2a29.5,33.1 0,0 0,-0.2 3.8q0,2.2 0.2,3.7c0.3,1.5 0.6,1.7 0.7,3.3q-0.1,1.4 0.2,2c1,0.3 1,-0.3 1.2,-2q0.2,-2.3 0.5,-4.7c0,-2.1 0.2,0.3 0.5,1a9.5,10.7 0,0 1,0.7 3.8q0,1.3 0.2,2c1,-0.5 1.3,-1.7 2,-2.3 0.4,-1.4 1,-2 1.4,-2.8 1.2,-0.3 1.2,-0.9 1.2,-2.5q-0.2,-2.2 0.3,-4a30,33.7 0,0 0,0.9 -3.7c0.4,-0.2 0.9,0.5 1.2,1.2q0.6,1.4 0.4,3.6a14,14 0,0 0,1.3 3.4,11 12.3,0 0,0 1.6,2.8c0.3,0.9 0.3,2.5 0.3,3.7 1,-1.2 1,-3 1.1,-4.8 -0.1,-1.2 -0.1,-2.8 -0.4,-4q-0.1,-1.9 -0.5,-3.3 -1.2,-0.4 -0.2,-2c0.7,-0.7 1.6,-0.4 2.8,-0.2 0.7,0.7 0.9,1.1 1,2.8q-0.1,2.2 0.5,3.6c0.4,0.8 0.9,1.4 1.2,2.8 0.3,0.4 0.6,1 1.1,0.3 0.3,-0.8 0.5,-2.4 0.8,-3.4q0.3,-1.5 1.1,-2c0,1.6 0,2.5 0.6,3.7l1.1,3.1c0.5,0.4 0.6,1.3 1.3,1.7l0.2,-4.5v-4q0.1,-1.6 0.7,-3c0.8,1.1 0.8,1.5 0.8,3 0.7,-0.3 0.8,-1 1.1,-1.9 0.5,-0.6 0.8,-0.8 1,0.6q0.2,2.2 0.7,4.2c0.5,0.6 1,2.2 1.6,2.8a12,13.5 0,0 0,1.5 2.3c0.4,0.8 0.4,1.4 1.5,1.6q0,-2.9 -0.3,-5.6c0,-1.6 -0.3,-2.2 -0.5,-3.7 -0.4,-0.9 -0.6,-2.4 -0.5,-2.8a8,9 0,0 1,2.2 3.4l1.5,3.7a14.7,16.5 0,0 1,1.8 2.8c0.9,0.8 1.3,1.7 2.2,2.5 0.4,0.5 1.5,0.8 1.7,0.5q0,-2.3 -0.7,-4.4a6.6,7.4 0,0 0,-0.8 -3.4c-1,-0.9 -1.7,-1.5 -2.2,-2.8 -0.3,-0.4 -0.3,-0.6 -0.4,-1.4a19.3,21.6 0,0 1,3.4 3.9,5.1 5.8,0 0,0 1.9,2c0.4,0.6 1.8,1.4 2.8,1.6a4.1,4.6 0,0 0,2.4 1.7l-2.4,-1.6a4.1,4.6 0,0 0,2.4 1.6v0.4a9.5,10.7 0,0 0,-1.4 -4.6,6.4 7.2,0 0,0 -2.2,-3 6.8,7.6 0,0 0,-2.4 -2.5c-0.6,-0.9 -1.6,-2.4 -2.2,-2.8 1.1,-0.9 2.3,0.4 3.4,1 1,1.2 1.8,2.7 2.4,3.4a13.2,14.8 0,0 0,2.2 3.7q1.4,1 2.6,2.2c1.4,0.4 0.9,-0.1 0.5,-1.4q-0.6,-2.2 1,-0.6 0.9,1 2.4,2 0.6,1 1.6,0.6c0.3,-0.4 0.3,-0.4 0.3,-0.8q-0.6,-2.8 -2.2,-3.7 -1,-1.3 -2.2,-2.5c-0.5,-0.8 -1,-1.3 -1.4,-2.3l1.4,2.2c-0.5,-0.8 -1,-1.2 -1.4,-2.2h-0.2c1.4,0 2,0.5 3,1.2a5,5.6 0,0 1,2.2 2q0.9,0.9 1.7,2.1l2.4,2c0.8,1.2 1,2 1,3.6 0.4,0.9 0.5,2.5 0.7,3.4 -0.2,1.6 -0.7,0.3 -0.8,-0.6 -0.1,-1.2 -0.6,-1.1 -1.4,-0.8 0,1.7 0.2,2.2 0.7,3.4a5.9,6.6 0,0 1,-0.9 2.8c-0.6,0.4 -0.7,-0.8 -1.2,-1.4 0,-1.7 -0.5,-2 -1.5,-2.8v1.1q1,1.7 1.2,3.9a8.8,9.9 0,0 1,0.6 3.7q0,2 -0.3,4c0,1.4 -0.3,2 -0.3,3.6v7.9a17,19.2 0,0 0,-0.2 3.6l0.2,-3.6q-0.2,1.5 -0.2,3.6v0.2q-0.7,-2 -1.2,-4a7.5,8.5 0,0 1,-1.6 -3q-0.3,-2 -0.5,-3.6c-0.4,-1 -0.8,-2 -1.5,-1.4 -0.3,2 0,3.3 0.7,5.3 0.4,1.3 0.4,2.5 0.8,3.7 0.5,1.5 1,1.6 0,3.3 -0.6,1 -0.8,1.2 -1,2.3 -0.9,0.5 -1.1,0.8 -1.2,2q-0.6,-1.7 -1.4,-3.7 -0.8,-2.2 -1.2,-4c-0.6,-0.9 -1.3,-2.4 -2,-3l-1.8,-3.3c-1,-1.3 -1.2,-2 -2,-2.6 -0.1,-0.5 -0.4,-1.3 -1,-0.8q0,2 0.3,3.9c0.3,1.6 0.7,2.5 0.8,4.2v3q-1,-2.3 -1.7,-4.4c-0.6,-0.9 -1.1,-2.5 -1.7,-3q-0.5,-1.7 -1.2,-2.9c-0.1,-0.8 -0.5,-0.8 -0.8,-1.4 0,1.6 0.5,2.2 0.8,3.3 0.5,1.2 0.6,2.9 0.5,3.5q-0.8,-3 -2,-6l-1.8,-3.7c-0.8,-0.9 -1.5,-1 -2.2,-1.6a4.3,4.9 0,0 1,-1.7 -1.4q0.4,2.1 1,3.6 0.5,2 0.9,3.7c-0.2,2 -0.6,-0.5 -1,-1.4 -0.2,-1.6 -0.7,-2.4 -1.1,-3.3q-1,-1 -2,-2c-0.7,-0.3 -0.4,0 -0.4,1q-0.1,2.6 0.5,4 0.6,1.2 0.5,3.4c-0.1,1.4 -0.3,2.4 -0.3,4v2.1c-0.5,-1.1 -1,-1.9 -1.5,-3.3q-0.9,-1.8 -1.6,-3.7c-0.5,-0.2 -1.5,-0.4 -2,0 0.2,1.4 0.7,2 0.8,3.7 -1,1 -0.8,1.4 -1.4,-0.3 -0.6,-1.3 -1.2,-1.2 -2,-2 -1.2,-0.1 -0.8,-1 -1.1,-2.3q0,-1.5 -0.3,-3c-1.3,0 -2,0 -2.9,0.8 -0.3,0.8 -0.2,2.5 0,3.7v3.9c-0.3,1 -0.7,1 -1,2.3 -0.4,1.1 -0.5,2.5 -0.9,3.3 -0.9,0.9 -0.5,1 -1,-0.5v-9c0,-1.5 0.3,-2.3 0.3,-3.7 -0.5,-1.1 -0.6,-1.4 -2.2,-1.4a6,6 0,0 0,0 2.8q0,2.2 0.3,3.7l0.2,3.6q0,2.3 -1,3.3c-0.8,0.4 0,1 -0.9,1.3a11,12.3 0,0 1,-2.6 -2q-1.3,-0.5 -3.1,-0.6c-1.6,0 -1.6,-0.2 -2.2,-1.6a23,25.8 0,0 1,-1 -4.6c-0.5,-1.3 -0.6,-2.2 -1,-3.7a4.5,5.1 0,0 0,-1.4 -2c-0.3,1.6 -0.6,2.7 -0.6,4.3 -0.3,0.7 -0.4,2.4 -0.6,3 -0.4,-0.7 -0.6,-2.4 -1,-3.5 -0.3,-1.7 -0.7,-2.8 -2.3,-2.2 -1.6,0 -1.6,0 -1.6,2l1.9,3.2v4c0,1.6 0,2.1 -1,2.4q-0.6,-2.5 -2.2,-4.7c0,-1.1 -0.4,-0.8 -1.4,-0.8q-1,-0.1 -1.7,0.3v2.4q-0.6,0.3 -1.1,-0.5c-0.5,0.9 -0.2,2 0,3a10,10 0,0 1,-1.2 3.2c-0.2,-0.4 -0.4,-0.2 0,-0.2q-2.4,-1 -4.4,-1.7c-0.7,0 -1.4,1 -1.8,1.7 -1.3,0.4 -1.8,0.8 -3.2,0.8l-3,0.2q-1.4,0.4 -3.2,0.4c-0.6,-0.3 -1.8,-0.3 -2.1,-0.6 0.5,-0.8 1,-1.2 1.4,-2.5a14.7,16.5 0,0 0,2 -2l1.8,-2.3c0.4,-1.4 1,-1.6 1.7,-2.2 1.5,0 1.7,0.6 3.1,0.8 1.4,0.6 2.2,0.9 3,-0.3 1,-0.5 2,-1.4 3.2,-1.6l2.4,-1.7c1,-0.7 1.5,-1.5 2.4,-2.3 0.3,-1.1 1,-2 0.7,-3.3q-1.8,-0.7 -3,0.5c-0.9,1 -1,1.4 -2.3,2.2 -1,0.4 -1.4,0.9 -2.6,1.2 -1,0.4 -2.2,0.2 -3.3,0.2s-2.5,0 -3.1,0.4q1.6,-1.4 3.3,-2c1,-0.7 1.4,-1 2.7,-1.2 0.6,-0.8 1.1,-0.8 1.4,-2.2 0.4,-1.6 -1,-1.1 -2,-0.5 -0.9,0.2 -2,1 -2.8,1.3a10.5,11.8 0,0 1,-3 0.6c-0.4,-0.5 0.1,-0.9 0.4,-1.6 1,-0.5 1.3,-0.7 1.4,-2 -1.3,0.2 -1.7,0.7 -2.9,0.8a18,18 0,0 1,-3.8 0.6h-3.3c-1.2,0 -1.8,-0.3 -3.2,-0.3 -1,-0.6 -1,-0.8 -1,-2.5 0.8,-1 1.5,-0.8 3,-0.8l2.6,-1.5q1.4,-0.3 1.2,-1h-3.3c-1.4,-0.7 0,-1 0.7,-1.5 1,0 2.3,0 3,0.5 1.5,0 2,-0.5 3.1,-1.6 1,-0.8 1.5,-1.4 2.5,-2q1.1,-1.2 1.9,-2.2 1.5,-1.4 2.2,-2.6 1,-0.5 0.7,-1.4c-1.5,0.2 -2.2,0.9 -3.2,1.7a7.3,8.2 0,0 1,-1.9 1.6,6 6.7,0 0,1 -3.1,1.3q-1.7,-0.1 -3.3,-0.4c-1.3,-0.1 -0.2,-0.5 0.2,-1.1q1,-0.6 0.5,-2 -1.6,-0.3 -3.3,-0.2h-3.4c-1.3,0 -1.8,-0.4 -2.7,-0.9 -0.2,-0.8 0,-0.5 1,-0.5a13.2,14.8 0,0 0,3.1 -0.6c1.1,-0.3 1.3,-0.8 1.5,-2.2q-2,0 -3.1,-0.3zM409.6,131.5c-0.2,-2 -0.7,-3.1 -0.7,-5.2 0,-1.5 0,-0.6 1.4,-2.7l2,-2q1.9,-1.3 3.7,-2.4c1,-0.6 3,-1.1 3.7,-1.6a9.2,10.3 0,0 0,3 -2.8c0.2,-1.7 0.4,-3 0.4,-5q2,-1.5 3.7,-2c1.7,-0.6 2,-1.4 3.8,-1.7 1,-0.4 2.8,-0.4 4.4,-0.4s2.7,1.4 4.2,1.6c1.8,0.8 2.6,1.4 4,2.8 0.8,1.2 1.4,2.2 2.7,3 1.1,0.6 2.2,1.6 3.4,2 0,1.4 0.3,1.2 -0.6,1.2 -0.1,1.5 -0.4,1.3 -1.7,1.4a5.9,6.6 0,0 1,-2.2 -2.3c0.6,-1.4 0.3,1.2 0,1.7 -0.5,0.6 -0.9,1.2 -1.6,0.6 -0.4,-1.4 -0.7,-2.3 -0.7,-4 -0.2,-1.7 -0.7,-0.5 -1,0.3 -0.3,1.5 -0.2,2 0.3,3.1q-1.9,0 -3.1,-0.3 -1.4,-0.4 -3,0.6c-1.2,0.9 -0.7,0.9 -0.2,2q1.6,0.6 3,1.6l3.3,-0.8c1.1,-0.5 2.2,-0.2 3.3,0q1,0.4 1.7,0.8 0,1.5 -0.3,2.3c-0.4,0.4 -1,1.4 -1.7,1.6a11.8,13.2 0,0 1,-3.2 1.5c-1.5,0 -2.1,0.4 -3.2,1 -0.1,1.6 -0.5,1.9 -1.7,2.4a7.5,8.5 0,0 1,-3.3 0.8q-1.1,0 -1.7,-0.3a9.4,10.5 0,0 0,2 -3.3c0.2,-0.7 0.6,-1 0.2,-1.5 -1.4,0 -1.8,0 -2,1.6s-0.6,1.9 -1.6,2.4q0.3,-2 0.7,-3.7a18.3,20.6 0,0 1,-3 0.3c-0.8,0.5 -1.1,0.7 -0.8,1.6q-1.6,0.5 -3.4,0c-1,0 -0.4,-0.5 -1.2,-1q-0.6,-1.5 -2.2,-1.4c-0.5,0.8 -1.4,1 -2,1.6q-1.5,0.4 -2.7,-0.5c-1.2,0 -1,0.4 -0.7,1.4 0.5,0.3 0,0.7 0.7,0.8 0.7,0.7 1.3,0.9 2.6,0.9q1.5,-0.4 3.4,-0.3c1.2,0 1,0.3 1,1.6 -0.8,0.7 -2,0.6 -3.2,0.6a12.2,13.7 0,0 1,-2.9 -0.8c-1,0 -2,-0.3 -2.8,0 -0.4,0.5 -1.5,0.7 -0.2,0.8 0.5,0.6 3.3,0.3 3.4,1.3 0.7,0.8 0.5,1.9 -0.6,1.9a1.6,1.8 0,0 0,-1.7 0l-3.1,0.1c1.8,0.2 -1.3,-2.2 -2,-3.2"
      android:fillColor="#fefefe"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m417.1,122.2 l-1.4,1 -2.9,2.3c-0.5,0.5 -0.5,0.8 -1.1,0.8 1,-0.1 1.6,-0.5 2.6,-1q2,-0.2 3,-1.2c0.7,-0.2 0,-1.3 -0.2,-2zM428.1,109c0.2,0 0.5,0.8 1,1.4q0.6,1.4 0.7,3 0.6,-1.8 1,-3.9c-0.4,-0.8 -0.4,-1 -1.5,-1.1 0,0.8 0.2,0.2 -1.2,0.6"
      android:fillColor="#bcbcbc"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M421.52,118.92c-0.54,0 0.87,-0.63 1.41,-0.76q0.43,-1.39 1.41,-2.28 1.74,0 2.39,1.39c1.08,0.38 1.41,0.89 2.6,1.14 0.54,0.51 1.08,0.51 0,0.89q-1.08,0.51 -2.82,0.51a8.87,7.59 90,0 0,-2.82 0.89c-1.63,0 -1.41,0 -2.17,-1.77z"
      android:strokeWidth="1.05"
      android:fillColor="#c4c4c2"
      android:strokeColor="#000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M432.5,109.2v0.3q0,-1.2 0.2,1.2a6.6,7.4 0,0 1,0.5 3.3,5 5.5,0 0,0 1.5,-2.5c0,-2.2 -0.4,-2.1 -2.2,-2.3m8.8,32.3h0.3c-0.8,0 -0.3,0 0.9,-0.8 0.8,-0.6 1.5,-1.6 2.6,-2a9.5,10.7 0,0 1,3.1 -2c0.6,-0.3 1.1,-0.8 1.7,-0.2q0,1.9 -1.5,2.5 -0.9,1.2 -2,1.6c-0.8,0.5 -1.7,0.5 -2,1.5 -1.3,0.7 -1.5,0.5 -3,-0.6zM445.9,149.7c0.3,-0.4 1.9,-2.2 2.6,-2.8a11,12.3 0,0 1,2.4 -2.8q1.2,-1.6 2.2,-3.1l1.9,-2.8c0,1.2 -0.2,2.8 0.2,3.6a5.1,5.8 0,0 1,-0.8 3.4q-0.6,1.4 -2.2,2.3c-1,0.6 -1.6,0.8 -2.5,1.6a19,21.4 0,0 1,-2.2 1.4c-0.7,0.3 -0.4,0.3 -1.6,-0.8m-7.5,6.4h0.3c-0.7,0 -0.4,0 1,-0.8q1.3,-1.6 3,-2.2a7.5,8.5 0,0 0,3 -0.9c0.5,-0.1 1,-0.5 1.3,0 0.8,1.2 1,1.5 -0.2,2.3a6.9,7.7 0,0 1,-2.4 1.4c-0.6,0.5 -1.8,0.5 -3,0.8 -0.8,0 -1.4,0.1 -1.8,-0.6l2,0.6c-1,0 -1.5,0.1 -3.2,-0.6m10.3,0v-0.2q-0.1,1.2 0.5,-1.2c1,-1.6 1.2,-2.5 2.4,-2.8 0.5,-0.8 1.5,-1 2.6,-1.4 0.5,0.8 0.1,1.8 0,2.8q-0.6,1.5 -1.7,2.6c-0.7,0.5 -1,1.1 -2.2,1.4 -0.8,0 -0.3,0 -1.6,-1.2"
      android:fillColor="#bcbcbc"
      android:fillType="evenOdd"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M425.3,119.7q0,1.2 -1.2,1.4c-1.2,0.2 -1.2,-0.7 -1.2,-1.4q0.2,-1.3 1.2,-1.4 1.1,0 1.2,1.4"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M428.82,196.13l0.98,0c-3.04,0 -1.19,-0.25 3.8,4.43 2.17,-2.03 2.17,-2.28 3.8,1.27 1.08,1.9 1.3,-0.25 2.93,-2.28 0.76,4.05 1.19,8.23 3.8,4.43 3.04,0 6.07,-0.25 7.7,1.14 1.95,4.18 1.3,-0.76 2.82,1.14 4.34,0.51 4.23,-4.31 8.78,-5.57 2.82,4.18 4.12,1.01 7.59,-1.01 2.71,0.76 5.21,5.45 5.75,0.89 2.17,-4.05 2.28,-3.8 3.8,0 2.17,2.28 3.04,-1.01 4.77,-2.15q4.66,3.17 11.5,1.14c0.87,3.67 2.82,2.03 4.88,1.01 4.23,2.15 5.64,2.79 10.52,0 2.39,-3.29 1.08,0.13 2.82,2.28 3.58,-1.27 2.6,-1.52 6.72,-1.14 4.23,0 4.55,0 5.75,-3.29 2.17,2.41 2.82,4.18 5.75,1.14 4.55,0 3.14,0.63 5.75,-1.27 2.06,5.07 2.49,1.52 4.88,3.42a19,16.27 90,0 1,-6.72 8.87c-1.84,3.8 -3.04,7.6 -6.72,9.12 -2.71,3.17 -5.64,5.07 -8.68,7.85 -4.12,1.27 -8.68,1.01 -13.34,1.01L468.08,228.56c-2.82,1.77 -7.59,2.53 -11.5,3.42 -4.34,0 -9.43,0 -12.47,-1.14 -2.06,-2.41 -5.64,-5.07 -7.59,-7.85 -2.17,-1.9 -2.17,-5.57 -3.9,-7.85 -1.52,-3.17 -2.17,-8.23 -3.8,-11.15z"
      android:strokeWidth="1"
      android:fillColor="#005120"
      android:fillType="evenOdd"
      android:strokeColor="#002b0d"/>
  <path
      android:pathData="M521.76,206.26l0,1.01c0,-3.04 0.11,-1.39 -1.95,4.56 -0.33,1.39 -1.3,1.77 -2.82,2.28m-3.8,-3.42l0.98,0c-3.58,3.8 -5.96,5.7 -6.72,10.13l6.72,-10.13c-3.58,3.8 -5.96,5.7 -6.72,10.13m-7.7,-15.71s-0.11,3.93 -0.98,5.57m-2.82,2.28l0,1.01zM488.25,206.26l0.98,0 -1.08,0c0.43,0 0.76,0 0,0zM434.57,206.26c0.43,0 2.39,3.55 4.77,5.57 -0.54,3.29 -1.52,7.09 0,10.13m2.93,-8.99c0.43,0 2.28,3.55 4.77,5.57l-4.77,-5.57c0.43,0 2.28,3.55 4.77,5.57l0,1.14c0,-3.55 -0.22,-7.09 0.98,-8.99 1.08,2.15 2.39,5.45 2.82,2.28 5.42,-0.25 8.03,-2.03 11.5,-3.42 0,1.65 -0.22,0.89 1.08,2.28m65.07,-4.56l0.98,0c-1.84,1.77 -3.25,5.45 -4.77,7.85m-35.46,-11.15l7.59,0"
      android:strokeWidth="1"
      android:fillColor="#005120"
      android:fillType="evenOdd"
      android:strokeColor="#002b0d"/>
  <path
      android:pathData="M425.9,214.3c-11.5,0 -15.4,13.5 -26.9,13.5h-0.6v0.8q0,14 1.8,26.2c10.6,-0.8 14.5,-13.4 25.7,-13.4 11.5,0 15.3,13.4 26.8,13.4s15.4,-13.4 26.9,-13.4 15.4,13.4 26.9,13.4 15.3,-13.4 26.8,-13.4c11,0 15,12.5 25.6,13.4a139,156.1 0,0 0,1.9 -27h-0.7c-11.5,0 -15.3,-13.4 -26.8,-13.4s-15.4,13.5 -26.8,13.5 -15.4,-13.5 -27,-13.5c-11.4,0 -15.3,13.5 -26.8,13.5s-15.3,-13.5 -26.8,-13.5zM425.9,267c-9,0 -13.2,8.2 -20.2,11.8a111,124.5 0,0 0,8.6 20.5c3.3,-2.8 6.7,-5.2 11.6,-5.2 11.5,0 15.3,13.5 26.8,13.5s15.4,-13.5 26.9,-13.5 15.4,13.5 26.8,13.5 15.4,-13.5 26.9,-13.5a18.3,20.6 0,0 1,11.3 5,112 125.7,0 0,0 8.6,-20.4c-6.7,-3.7 -11,-11.7 -20,-11.7 -11.4,0 -15.3,13.5 -26.8,13.5S491,267 479.6,267s-15.4,13.5 -26.9,13.5 -15.3,-13.5 -26.8,-13.5m53.7,52.7c-11.5,0 -15.4,13.5 -26.9,13.5 -10.9,0 -14.9,-12 -25,-13.4a132,132 0,0 0,39.3 33c3.6,-3.2 7.3,3.6 12.5,3.6s9,-6.9 12.5,-3.8a129.1,145 0,0 0,39 -32.7c-9.8,1.7 -14,13.3 -24.6,13.3 -11.5,0 -15.3,-13.5 -26.8,-13.5"
      android:fillColor="#fff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M408.26,268.28a138.8,118.89 90,0 1,11.35 -3.79l17.62,19.81 -14.48,3.41z"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M443.01,300.61c0.27,-4.6 8.07,-30.99 8.07,-30.99m-5.41,31.29 l6.14,-31.09m-4.03,31.19 l5.13,-31.19m-9.62,26.99c1.37,-0.8 5.32,-2 5.32,-2m-3.39,-2.6c1.28,0 3.94,-1.2 3.94,-1.2m-3.21,-2.3a23.99,22 90,0 1,4.03 -1.2m-3.48,-3.2 l4.31,-0.8m-2.38,-3c0,0.3 2.93,0.3 2.93,0.3m-2.2,-3.7 l2.93,0.5"
      android:strokeWidth="0.57"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M493.32,319.85c-0.32,-7.86 -9.51,-52.96 -9.51,-52.96m6.38,51.42 l-7.24,-51.25m4,47.32c-0.22,-2.9 -5.3,-47.15 -5.3,-47.15m11.35,46.12c-1.62,-1.37 -6.27,-3.42 -6.27,-3.42m4,-4.44c-1.51,0 -4.65,-2.05 -4.65,-2.05m3.78,-3.93a41,25.94 90,0 0,-4.76 -2.05m4.11,-5.47 l-5.08,-1.37m2.81,-5.12c-0,0.51 -3.46,0.51 -3.46,0.51m2.59,-6.32 l-3.46,0.85"
      android:strokeWidth="0.54"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M520.04,303.68c-0.32,-5.8 -9.51,-39.12 -9.51,-39.12m6.38,37.98 l-7.24,-37.85m4,34.95c-0.22,-2.15 -5.3,-34.83 -5.3,-34.83m11.35,34.07c-1.62,-1.01 -6.27,-2.52 -6.27,-2.52m4,-3.28c-1.51,0 -4.65,-1.51 -4.65,-1.51m3.78,-2.9a30.28,25.94 90,0 0,-4.76 -1.51m4.11,-4.04 l-5.08,-1.01m2.81,-3.79c-0,0.38 -3.46,0.38 -3.46,0.38m2.59,-4.67 l-3.46,0.63"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="m476.14,307.14 l0.97,-92.11s2.81,-0.38 2.81,0 -0.32,91.73 -0.65,92.11 -3.46,0.63 -3.13,0zM506.94,219.06 L506.29,296.41 504.13,299.44 504.78,219.06z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M441.23,292s3.24,37.85 39.34,36.72 46.15,-38.11 46.15,-38.11l-15.67,-0.76c-0.32,0 -12.54,17.67 -28.32,17.29s-20.75,-4.79 -24.21,-8.45 -6.48,-9.21 -6.48,-9.21l-18.37,-8.45 1.08,8.08zM538.82,288.21 L537.74,283.16l0,-4.29l1.73,-4.54s-27.34,4.67 -27.34,5.05l-0.11,8.58 26.8,0.13z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="m533.63,278.87 l-0.65,7.57l4.43,0l-0.32,-7.57zM528.99,279.25 L528.66,287.33 531.47,286.95 531.8,279.25zM524.23,287.33l0,-6.31l2.81,-1.14 0.32,6.31zM518.83,286.95 L519.48,281.4 522.72,281.78 522.93,287.71zM512.88,286.95 L513.21,282.16 516.45,281.78l0,5.55zM440.04,291.62c0.97,0 8.21,1.89 8.21,1.89l7.57,11.73"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M445.33,293.13c0.32,0.38 6.59,24.98 36.75,24.98s37.4,-27.89 37.4,-27.89"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M524.23,290.99s-14.48,36.59 -42.15,34.07c-27.56,-2.52 -31.99,-14.64 -34.15,-20.95s-3.46,-11.36 -3.46,-11.36"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M452.25,289.47c0.32,-2.15 0.32,-68.64 0.32,-68.64l-1.84,-0.25 -0.32,67.76 1.84,1.26zM407.4,267.9 L431.17,281.4 430.63,283.29S407.72,270.67 407.72,270.29s0.22,-1.89 -0.32,-2.52z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M418.31,263.86c0,1.01 3.46,23.47 3.46,23.47s2.16,0.38 2.16,0l-3.67,-23.09zM440.36,264.49c1.19,0 22.7,-4.04 22.7,-4.04l-0.43,4.04 -21.08,2.52z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M443.39,234.08c0.76,0 7.57,4.04 10.48,3.03s0.97,-3.79 1.51,-3.79c0.65,0 2.81,1.89 4.11,0.38s2.16,-5.8 1.62,-5.43 -17.29,7.32 -17.62,5.8z"
      android:strokeWidth="0.58"
      android:fillColor="#fecf3e"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M440.68,243.29c1.19,0.38 25.72,-7.7 25.72,-7.7s0,2.52 -0.32,2.52l-25.4,7.7z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M497.43,231.94s1.62,4.42 3.24,3.66 3.13,-2.27 3.13,-2.27 1.3,3.79 3.46,3.03 3.46,-4.79 3.46,-4.79 2.16,0.76 3.13,0.38 2.16,-6.31 2.16,-6.31zM495.59,267.52c-0.32,0.76 1.62,2.52 3.24,2.52s2.16,-2.52 2.49,-1.77c0.32,0.63 0.32,4.04 4.32,2.9s4.11,-4.42 4.11,-4.42 -1.19,1.14 1.62,1.51 5.62,-4.79 5.4,-4.79c-0.43,0 -20.54,4.42 -21.18,4.04z"
      android:strokeWidth="0.58"
      android:fillColor="#fecf3e"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M441.66,232.57a6.31,5.4 90,0 0,0.22 3.41l20.54,-8.08s0.86,-1.89 0,-1.89 -21.08,7.32 -20.86,6.69zM468.68,227.52c0.54,0 20.97,-7.7 20.97,-7.7s0.32,3.28 0,3.28l-19.13,6.56 -1.95,-2.15z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M495.59,243.67s-3.13,4.04 -2.16,9.46c0.97,5.55 2.16,10.35 2.49,10.35s3.78,-7.32 7.57,-7.7 10.7,4.04 10.7,4.04 -0.97,-8.83 -0.65,-12.87 5.62,-10.98 5.62,-10.98l-23.56,7.57z"
      android:strokeWidth="0.58"
      android:fillColor="#fecf3e"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M467.06,236.98s-0.65,3.41 -0.32,3.41l26.7,-8.08L493.43,229.92l-26.37,6.94z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M433.12,281.4s-6.27,-1.01 -5.94,0.76c0.32,1.89 3.13,3.66 3.46,4.79s-0.97,5.05 0.97,4.79c1.84,-0.38 3.67,-1.89 3.67,-3.03l-0.32,-6.31z"
      android:strokeWidth="0.58"
      android:fillColor="#b6b6b4"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M468.03,263.1l0,2.52l22.59,-3.66 -0.65,-1.89z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M441.23,267.52c0.43,0 0,3.28 2.59,2.52 2.49,-0.76 3.13,-2.52 3.13,-2.52s0.65,4.04 4.11,3.66 5.4,-4.79 5.4,-4.79 0.22,3.28 2.38,2.15 2.81,-3.66 2.81,-3.66l-19.78,3.28zM441.23,245.81s-2.81,1.14 -1.84,7.7 3.24,10.35 3.46,10.35 2.81,-6.31 6.59,-7.32c3.78,-1.26 10.38,3.28 10.38,3.28s-0.65,-5.93 0.32,-11.36a22.71,19.45 90,0 1,4.11 -9.59s-22.05,6.94 -22.91,6.94z"
      android:strokeWidth="0.58"
      android:fillColor="#fecf3e"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="m494.08,264.87 l0.32,2.27s22.7,-3.28 22.7,-3.79c0,-0.25 0,-2.52 -0.43,-2.52 -0.32,0 -22.26,4.42 -22.7,4.04zM493.75,241.4 L494.08,243.92 520.77,235.59s0.65,-2.27 0.32,-2.27 -26.7,9.21 -27.34,8.08z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M496.56,229.66s-0.32,2.27 0,2.27 19.78,-5.55 19.78,-5.93 1.95,-2.52 0.32,-2.15c-1.51,0.38 -19.45,6.94 -20.1,5.8zM538.39,272.19 L518.94,236.35m-2.59,24.86c0.32,-1.01 1.3,-23.09 1.3,-23.09"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M469.33,228.65c1.08,0.25 3.35,4.04 4.65,3.28s3.13,-3.03 3.13,-3.03 -0.32,4.42 2.16,3.79c2.59,-0.76 2.92,-3.79 2.92,-3.79s1.84,1.26 3.46,0c1.51,-1.01 3.03,-6.56 2.49,-6.56s-18.27,6.56 -18.91,6.31zM468.68,239.25c0,0.76 -3.24,6.56 -2.16,12.11s2.7,10.6 3.03,10.6 5.08,-5.8 9.19,-6.94 10.05,3.66 9.73,3.66 -1.62,-5.55 -1.62,-9.59 4.76,-16.4 4.76,-16.4zM468.68,265.75c0,0.25 0.86,2.78 3.13,2.78s2.49,-2.52 2.49,-2.52 -0.32,4.42 3.13,4.04 4.65,-4.42 4.65,-4.42 0.32,2.27 3.24,1.14c2.81,-1.14 6.48,-4.79 6.27,-4.79s-22.7,4.79 -23.02,3.79z"
      android:strokeWidth="0.58"
      android:fillColor="#fecf3e"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M471.27,251.71a1.97,1.68 90.32,1 1,-0.13 3.95,1.97 1.68,90.32 0,1 0.13,-3.95z"
      android:strokeWidth=".6"
      android:fillColor="#be0f17"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M482.69,248.31a1.97,1.68 90.32,1 1,-0.13 3.95,1.97 1.68,90.32 0,1 0.13,-3.95z"
      android:strokeWidth=".6"
      android:fillColor="#be0f17"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M476.98,255.3a1.97,1.68 90.32,1 1,-0.13 3.95,1.97 1.68,90.32 0,1 0.13,-3.95z"
      android:strokeWidth=".6"
      android:fillColor="#be0f17"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M476.98,244.62a1.97,1.68 90.32,1 1,-0.13 3.95,1.97 1.68,90.32 0,1 0.13,-3.95z"
      android:strokeWidth=".6"
      android:fillColor="#be0f17"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M479.27,216.04c0,-0.25 9.73,4.79 9.73,5.17s34.59,54.01 34.91,54.01m-17.29,-54.01 l8.86,4.42 2.81,8.08m-66.36,-13.12c-0.32,0 9.73,8.83 9.73,8.83l1.3,7.19M417.77,274.08c4.32,-2.15 34.8,-33.31 34.8,-33.31m-43.02,27.38 l34.26,-33.69m22.91,5.05 l32.42,62.84m-3.89,-36.84s3.78,14.76 7.57,17.03a108.52,92.95 90,0 1,8.21 5.8"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M494.83,265.88s2.7,17.41 6.48,20.69 7.57,6.94 7.57,6.94"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M468.81,265.73s3.67,24.23 11.24,29.78l13.83,10.22"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M468.48,265.35s8.11,19.18 15.13,24.98c6.81,5.93 16.21,11.36 16.21,11.36M442.11,266.99c2.16,4.42 1.84,12.87 4.32,15.77a83.28,71.33 90,0 1,6.27 9.59m-10.05,-25.11c1.3,2.15 4.76,13.12 6.92,16.4 1.62,3.03 17.62,21.7 17.62,21.7"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M461.89,306.24a2.78,2.38 90,1 1,-4.76 0,2.78 2.38,90 0,1 4.76,0zM473.45,312.17a2.4,2.05 90,1 1,-4.11 0,2.4 2.05,90 0,1 4.11,0zM488.26,313.68a2.4,2.05 90,1 1,-4.11 0,2.4 2.05,90 0,1 4.11,0zM501.12,308q-0.11,2.02 -1.73,2.15 -1.62,-0.25 -1.73,-2.15c0,-1.14 0.76,-2.27 1.73,-2.27q1.62,0.13 1.73,2.27zM511.5,300.81q-0.22,1.89 -2.05,2.02c-1.08,0 -2.05,-0.88 -2.05,-2.02s0.97,-2.02 2.05,-2.02q1.84,0.13 2.05,2.02z"
      android:strokeWidth="0.58"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M450.54,222.07c-0.97,0.76 -16.64,5.17 -14.48,4.79s14.81,3.79 14.48,2.52 0,-6.56 0,-7.32zM477.24,216.65c-0.65,0 -9.73,4.04 -9.4,4.04s10.38,3.28 10.05,2.52 -0.32,-5.55 -0.65,-6.56zM504.58,220.31c-0.65,0.38 -11.35,3.28 -9.4,3.66s9.08,3.28 9.08,2.52c0,-0.63 0,-5.8 0.32,-6.31z"
      android:strokeWidth="0.58"
      android:fillColor="#ff0000"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M493.23,306.11c0.32,-5.8 9.51,-39.12 9.51,-39.12m-6.38,37.98 l7.24,-37.85m-4,34.95c0.22,-2.15 5.3,-34.83 5.3,-34.83m-11.35,34.07c1.62,-1.01 6.27,-2.52 6.27,-2.52m-4,-3.28c1.51,0 4.65,-1.51 4.65,-1.51m-3.78,-2.9a30.28,25.94 90,0 1,4.76 -1.51m-4.11,-4.04 l5.08,-1.01m-2.81,-3.79c0,0.38 3.46,0.38 3.46,0.38m-2.59,-4.67 l3.46,0.63"
      android:strokeWidth="0.58"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M466.43,310.34c0.32,-6.07 9.51,-40.92 9.51,-40.92m-6.38,41.32 l7.24,-41.05m-4.76,41.19 l6.05,-41.19m-11.35,35.64c1.62,-1.06 6.27,-2.64 6.27,-2.64m-4,-3.43c1.51,0 4.65,-1.58 4.65,-1.58m-3.78,-3.04a31.68,25.94 90,0 1,4.76 -1.58m-4.11,-4.22 l5.08,-1.06m-2.81,-3.96c0,0.4 3.46,0.4 3.46,0.4m-2.59,-4.88 l3.46,0.66"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M466.34,307.68c-0.32,-5.89 -9.51,-39.72 -9.51,-39.72m6.38,38.56 l-7.24,-38.44m4,35.49c-0.22,-2.18 -5.3,-35.36 -5.3,-35.36m11.35,34.59c-1.62,-1.02 -6.27,-2.56 -6.27,-2.56m4,-3.33c-1.51,0 -4.65,-1.54 -4.65,-1.54m3.78,-2.95a30.75,25.94 90,0 0,-4.76 -1.54m4.11,-4.1 l-5.08,-1.02m2.81,-3.84c-0,0.38 -3.46,0.38 -3.46,0.38m2.59,-4.74 l-3.46,0.64"
      android:strokeWidth="0.59"
      android:fillColor="#00000000"
      android:strokeColor="#512007"/>
  <path
      android:pathData="m509.75,289.85 l29.83,0.76 0.32,-2.52 -30.8,-0.38zM511.05,280.64c3.46,0 29.51,-5.8 29.51,-5.8s0.97,-3.79 0,-3.79 -29.51,7.07 -29.51,7.07z"
      android:strokeWidth="0.58"
      android:fillColor="#dd8b59"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M476.98,249.96a1.97,1.68 90.32,1 1,-0.13 3.95,1.97 1.68,90.32 0,1 0.13,-3.95z"
      android:strokeWidth=".6"
      android:fillColor="#be0f17"
      android:fillType="evenOdd"
      android:strokeColor="#512007"/>
  <path
      android:pathData="M0,0h320v240H0z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0z"
      android:fillColor="#FFF"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5zM120,150.5 L123,168 27,240L0,240zM320,0v1.5l-124.5,94 1,-22L295,0zM0,0l119.5,88h-30L0,21z"
      android:fillColor="#C8102E"/>
  <path
      android:pathData="M120.5,0v240h80V0zM0,80v80h320V80z"
      android:fillColor="#FFF"/>
  <path
      android:pathData="M0,96.5v48h320v-48zM136.5,0v240h48V0z"
      android:fillColor="#C8102E"/>
</vector>
