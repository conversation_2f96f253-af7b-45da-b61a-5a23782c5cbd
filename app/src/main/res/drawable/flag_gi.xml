<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#da000c"/>
  <path
      android:pathData="M0,0h640v321.6H0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M208.63,218.06l120,0l0,82.5l-120,0z"
      android:fillColor="#000001"
      android:strokeColor="#00000000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M270.88,288.56l-73.13,0l-16.31,10.69l0,11.25l89.44,0m-30.56,-69.38c10.5,0 19.13,8.81 19.13,19.69l0,27.56l13.69,0l0,-105l-75.56,0l0,105l23.63,0l0,-27.56c0,-10.5 8.44,-19.69 19.13,-19.69z"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M223.44,112.5l34.88,0l0,63.75l-34.88,0z"
      android:fillColor="#000001"
      android:strokeColor="#00000000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M258.13,166.31l-30.38,0l0,-10.88l-22.31,0l0,10.88l-15,0l0,-10.88L181.25,155.44l0,19.5l76.88,0m-67.88,0l65.63,0l0,8.44l-65.63,0zM216.5,89.25L216.5,155.63l11.25,0l0,-18.19c0,-6.75 4.69,-12.38 11.44,-12.75l0.75,0a13.13,13.13 0,0 1,12.75 12.75L252.69,155.63l10.69,0L263.38,89.25zM212.19,80.25l0,9l54.94,0l0,-9zM205.25,63.19l0,17.06l65.63,0l0,-17.06l-9.94,0l0,8.81l-12.38,0l0,-8.81l-18.75,0l0,8.81l-12.19,0l0,-8.81zM181.25,299.25l90,0m58.13,-5.25l-60.75,0l-18.38,8.81l0,13.13L329.38,315.94"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M250.25,302.81L331.25,302.81"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="butt"/>
  <path
      android:pathData="M431.38,218.06l-120,0l-0,82.5l120,0z"
      android:fillColor="#000001"
      android:strokeColor="#00000000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M369.13,288.56l73.13,0l16.31,10.69l-0,11.25l-89.44,0m30.56,-69.38c-10.5,0 -19.13,8.81 -19.13,19.69l-0,27.56l-13.69,0l-0,-105l75.56,0l-0,105l-23.63,0l-0,-27.56c-0,-10.5 -8.44,-19.69 -19.13,-19.69z"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M416.56,112.5l-34.88,0l-0,63.75l34.88,0z"
      android:fillColor="#000001"
      android:strokeColor="#00000000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M381.88,166.31l30.38,0l-0,-10.88l22.31,0l-0,10.88l15,0l-0,-10.88L458.75,155.44l-0,19.5l-76.88,0m67.88,0l-65.63,0l-0,8.44l65.63,0zM423.5,89.25L423.5,155.63l-11.25,0l-0,-18.19c-0,-6.75 -4.69,-12.38 -11.44,-12.75l-0.75,0a13.13,13.13 0,0 0,-12.75 12.75L387.31,155.63l-10.69,0L376.63,89.25zM427.81,80.25l-0,9l-54.94,0l-0,-9zM434.75,63.19l-0,17.06l-65.63,0l-0,-17.06l9.94,0l-0,8.81l12.38,0l-0,-8.81l18.75,0l-0,8.81l12.19,0l-0,-8.81zM458.75,299.25l-90,0m-58.13,-5.25l60.75,0l18.38,8.81l-0,13.13L310.63,315.94"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="square"/>
  <path
      android:strokeWidth="1"
      android:pathData="M389.75,302.81L308.75,302.81"
      android:fillColor="#da000c"
      android:strokeColor="#000"
      android:strokeLineCap="butt"/>
  <path
      android:pathData="M352.44,281.25q-7.31,3 -14.06,7.13a135,135 0,0 0,-16.69 11.25q-3,1.88 -5.06,4.5c-1.88,1.5 -3.75,3.75 -3.38,6.56 0,1.13 1.5,-1.5 2.63,-1.69a7.5,7.5 0,0 1,5.81 -0.75q3.94,-3.75 8.25,-6.38a144.38,144.38 103.96,0 1,24.38 -14.25z"
      android:strokeWidth="1.5"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M328.25,295.13l0,7.31m4.5,-10.5l0,7.31m4.5,-10.13l0,7.13m4.69,-9.94l0,7.5"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m287.94,281.63 l-2.25,6.19a163.13,163.13 59.08,0 1,29.63 15q5.06,3 9,7.31c0.56,1.5 -0.94,2.81 -2.44,2.25 -1.31,-0.38 -2.81,-0.94 -4.13,0 -2.06,0.94 -3.94,4.13 -0.94,5.06 4.5,3 11.44,1.69 13.5,-3.38 1.13,-2.63 1.31,-6 -0.94,-8.25 -3.75,-4.31 -9,-7.5 -13.88,-10.69a166.88,166.88 109.58,0 0,-27.56 -13.5z"
      android:strokeWidth="1.5"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="m316.25,296.25 l-0.56,6.38m5.81,-3.19 l-1.5,6.19m7.13,-1.88 l-3.38,4.88m5.06,6.75 l-4.88,-2.63m6.38,-2.63 l-5.63,0.56m-1.5,7.5 l-0.38,-4.88m-1.88,-0.56 l-4.5,3.38m-17.63,-29.44l0,5.81m11.81,0.56l0,6.56m-6,-9.75l0,6.19"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M282.13,426.75l0,15l9.38,0l0,-7.5l12.94,0l0,7.5l10.13,0l0,-15zM282.13,447.38l0,15L314.38,462.38l0,-15l-10.13,0l0,7.5l-12.94,0l0,-7.5z"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M314.38,363.19l9.38,0l0,108.75l-9.38,0z"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M314.38,372.56l9.38,0l0,93.75l-9.38,0zM319.25,336.38 L339.13,348 319.25,359.62 299.19,348zM292.44,343.88l-5.06,0l0,8.25l5.06,0l26.81,15.56 26.63,-15.56l5.25,0l0,-8.25l-5.25,0l-26.63,-15.56z"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M318.69,308.06a9.38,9.38 0,0 0,-7.5 9.56l0,21a9.38,9.38 0,0 0,8.63 8.63,9.38 9.38,0 0,0 9,-5.25l-3.19,-1.88a5.63,5.63 0,0 1,-5.63 3.38c-3,0 -5.44,-3 -5.06,-5.81l0,-21a5.63,5.63 0,0 1,5.63 -4.88c1.13,-0.38 2.81,1.31 3.56,0 1.13,-1.69 -0.75,-2.81 -1.88,-3.75zM329.56,315.38a9.38,9.38 0,0 1,-3.56 3l0,14.06l3.75,2.06l0,-19.13z"
      android:fillColor="#f8d80e"
      android:strokeColor="#000"/>
  <path
      android:pathData="M291.5,72l54.94,0l0,99.75l-54.94,0z"
      android:fillColor="#000001"
      android:strokeColor="#00000000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M287.75,72l0,83.44l17.44,0L305.19,130.69c0,-5.63 3.75,-13.69 14.81,-13.69s15,8.06 15,13.69L335,155.63l17.25,0L352.25,72zM317.38,81.38l5.25,0l0,28.5l-5.25,0zM301.81,87l5.63,0l0,20.81l-5.63,0zM332.75,87l5.44,0l0,20.81l-5.63,0zM281.75,60l0,11.81l76.5,0L358.25,60zM274.63,46.13L274.63,60l90.94,0l0,-13.88l-11.44,0l0,7.5l-13.13,0l0,-7.5l-14.63,0l0,7.5l-12.75,0l0,-7.5l-14.81,0l0,7.5L286.25,53.63l0,-7.5zM257.75,183.37l0,8.63l124.69,0l0,-8.63z"
      android:fillColor="#da000c"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M252.5,155.44l0,28.13l135,0l0,-28.13l-12.75,0l0,10.88L357.5,166.31l0,-10.88l-22.88,0l0,10.88L305,166.31l0,-10.88l-22.88,0l0,10.88l-16.88,0l0,-10.88z"
      android:fillColor="#da000c"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M268.81,192l0,102l24,0l0,-38.25c0,-17.81 12,-26.25 27.19,-26.25 14.63,0 27.19,8.44 27.19,26.25l0,38.25l24,0l0,-102z"
      android:strokeLineJoin="round"
      android:fillColor="#da000c"
      android:strokeColor="#000"/>
</vector>
