<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0v480h640V0Z"
      android:fillColor="#006233"/>
  <path
      android:pathData="M259.69,248.62c-9.42,12.87 -2.62,21.24 7.09,21.83 6.4,0.43 19.7,-8.24 19.67,-18.43l-3.64,-1.85c2.04,5.23 -0.07,6.88 -4.4,10.45 -8.54,7.05 -25.99,7.54 -18.72,-12.01z"
      android:strokeWidth="0.14"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M334.33,282.37c53.01,1.36 69.87,-45.32 37.15,-85.28l-3.24,4.4c2.11,18.62 5.16,36.98 5.45,56.07 -6.4,6.85 -15.71,4.3 -17.56,-3.31 0.11,-10.32 -3.6,-19.05 -8.29,-27.39l-2.62,4.4c3.05,6.85 6.36,14.56 7.05,22.99 -15.12,16.51 -31.85,19.85 -25.63,-1.85 -11.96,19.02 6.14,32.42 26.65,3.14 4.4,19.98 21.41,7.58 22.43,3.27 1.85,-13.1 0.91,-34.2 -2.84,-50.88 14.76,23.26 15.27,40.03 7.42,51.24 -8.73,12.47 -27.7,18.29 -45.99,23.19z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M364.39,229.6c-11.34,0.76 -17.16,-1.36 -11.02,-9 6.07,-7.48 11.74,-1.52 13.27,8.47 1.42,9.36 -19.92,21.3 -27.3,21.3 -11.16,0 -16.32,-13.07 -6.03,-24.81 -13.23,34.27 28.58,14.39 31.08,4.04zM356.54,221.66c-1.38,-0.07 -2.4,2.15 -1.71,2.58 2,1.26 5.16,0.5 5.49,-0.13 0.69,-1.39 -1.85,-2.38 -3.78,-2.45z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M302.81,245.78c-10.91,19.52 -0.04,27.59 13.96,25.34 8.14,-1.36 18.47,-6.62 24.43,-13.79 0.11,-15.81 -0.15,-31.49 -1.67,-46.81 5.45,-5.92 -0.47,-5.89 -2.54,-12.24 -0.95,3.7 -3.24,7.71 -1.02,10.59q2.4,23.16 2.4,47.04c-10.98,8.04 -19.23,11.02 -25.12,10.95 -12.18,-0.1 -14.8,-9.43 -10.43,-21.07z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M325.09,250.94c-0.18,-14.72 -0.44,-31.43 -1.89,-41.71 5.67,-5.72 -0.29,-5.86 -2.14,-12.27 -1.09,3.64 -3.49,7.61 -1.38,10.55 0.95,15.75 1.85,31.49 2.04,47.24 1.31,-0.76 2.8,-1.06 3.38,-3.8z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M282.77,250.25c-1.38,-1.59 -2.36,-3.37 -3.49,-4.93 -0.18,-2.22 1.45,-4.27 1.67,-5.39 1.85,2.61 2.94,4.6 4.44,5.89m1.96,1.03c2.73,0.99 6.07,0.99 9.16,1.06 11.92,0.2 24.47,-1.59 23.12,13.1a21.83,23.99 90,0 1,-23.7 20.48c-15.16,-0.13 -28.1,-15.35 -4.73,-43.37 2.25,-0.33 5.2,0.23 7.63,0.43 4.18,0.3 8.47,-0.07 13.38,-3.64 -0.58,-9.23 -0.58,-17.96 -1.82,-26.3 -2.11,-2.94 0.29,-6.88 1.38,-10.55 1.85,6.42 7.78,6.55 2.14,12.31 1.35,9.26 1.49,18.69 1.49,24.31 -2.84,3.94 -5.05,8.1 -13.34,9.69 -8.47,-1.12 -12.29,-11.91 -21.12,-8.34 2.44,-9.73 24.87,-11.94 27.12,-4.27 -1.49,8.01 -22.43,4.8 -27.99,30.67 -1.71,7.97 7.53,15.32 17.01,14.72 9.27,-0.56 19.16,-6.42 20.14,-16.28 0.76,-8.24 -12,-7.28 -17.34,-7.18 -7.78,0.17 -12.69,-0.93 -15.63,-2.48m7.96,-17.83c1.38,-1.19 6.22,-2.02 7.96,-0.1q-2,1.16 -3.64,2.68c-1.82,-0.86 -3.02,-1.72 -4.33,-2.58z"
      android:strokeWidth="0.14"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M304.08,206.02a16.21,17.81 90,0 1,1.93 6.95c-0.8,3.44 -4.04,6.65 -7.38,6.75 -2.07,0.07 -4.4,-0.46 -6.03,-3.41 -0.18,-0.36 -1.05,-1.22 -1.89,-0.83 -3.67,5.49 -6.4,7.81 -9.71,7.77 -6.62,-0.1 -4.65,-5.46 -10.76,-7.11 -2.54,-0.07 -6.73,2.28 -8.87,6.88 -8.14,21.01 -15.56,-0.07 -12.4,-9.86 0.47,9.36 2.94,14.92 5.49,14.75 1.85,-0.17 3.49,-4.07 5.85,-8.17 1.82,-3.14 6.18,-8.8 10.8,-8.8 4.22,0.1 1.56,7.15 10,7.05 4.07,-0.07 7.82,-2.91 11.6,-8.6 0.84,-0.13 1.05,1.22 1.24,1.69 0.58,1.95 4.29,7.31 9.31,2.41 -0.25,-1.06 -0.15,-2.81 -1.42,-3.18z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M330.58,188.62c-4.47,2.02 -7.74,0.17 -9.6,-1.62 3.24,-0.6 5.74,-1.65 6.47,-3.97 -1.45,-2.98 -4.91,-4.27 -9.78,-4.3 -6.51,0.17 -9.85,2.55 -10.25,5.82 3.02,0.1 5.74,-0.66 6.91,1.98 -5.34,2.38 -11.63,3.24 -18.47,3.21 -11.2,0.53 -12.83,-4.07 -15.78,-8.1 -0.22,-0.26 -1.2,-0.69 -1.71,-0.63 -3.45,0 -6,10.98 -9.89,10.95 -3.89,-0.46 -3.02,-7.08 -4.14,-10.85 -0.95,5.92 1.2,27.95 13.23,4.04 0.36,-0.79 0.87,-0.56 1.2,0.1 3.24,6.68 9.82,9 16.91,9.33 5.93,0.3 13.49,-2.05 21.59,-6.22 2.14,2.15 3.85,4.6 8.36,5.06 5.27,0.23 10.91,-3.24 12.18,-7.54 0.65,-2.22 0.76,-6.58 -1.82,-6.65 -3.6,-0.1 -6.22,7.84 -5.38,14.99 0.07,-0.1 0.47,-1.79 0.47,-1.79m-15.92,-9.53c2.36,-0.99 4.65,-1.46 6.47,0.73a8.93,9.82 90,0 0,-3.05 1.32c-1.02,-0.73 -2.4,-1.09 -3.42,-2.05zM332.54,187.82c0.58,-2.35 0.91,-4.23 3.02,-5.46 0.44,2.48 0.51,3.87 -3.02,5.46zM346.72,191.46c-0.69,-2.02 -1.38,-3.77 -1.6,-5.95 -0.51,-4.43 3.67,-6.95 7.45,-6.58 3.89,0.36 6.47,1.69 10.18,2.84 2.91,0.89 6.83,1.59 10.58,2.55 2.11,0.86 0,3.11 -0.55,3.41 -9.38,3.34 -16.03,8.63 -21.99,8.87q-5.31,0.23 -9.6,-6.29c-0.18,-8.4 -0.51,-18.26 -1.42,-24.45 1.38,-1.26 1.67,-2.18 2.33,-3.21 0.73,8.17 1.02,16.77 1.2,25.44 0.76,1.49 1.71,2.75 3.42,3.37zM352.72,192.12c-5.02,1.29 -4.4,-2.58 -4.87,-4.96 -0.55,-2.78 -0.18,-5.92 3.71,-5.13 5.05,1.22 9.67,2.84 14.14,4.57z"
      android:strokeWidth="0.14"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="m347.81,196.23 l0.69,3.08l0.55,0l-0.22,-2.88"
      android:strokeWidth="0.14"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="m267.8,228.38 l2.54,-2.38 2.94,2.28 -2.73,2.22zM274.27,227.59 L276.85,225.2 279.79,227.49 277.07,229.7zM256.27,202.91 L258.85,200.53 261.8,202.81 259.07,205.03zM257.44,209.92 L260.02,207.54 262.92,209.82 260.2,212.04zM304.15,221.66 L306.51,219.91 308.7,222.06 306.22,223.65zM272.96,176.77 L274.63,175.22 276.56,176.71 274.78,178.16zM277.21,176.28 L278.88,174.69 280.81,176.21 279.03,177.64zM366.5,194.04 L364.9,195.27 363.37,193.84 365.05,192.72z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.39"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m291.25,237.94 l-0.18,2.61 4.58,0.4 3.67,-2.51z"
      android:strokeWidth="0.14"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="m329.96,189.11 l1.35,-0.26 -0.15,3.41 -0.84,0.3z"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#fff"/>
  <path
      android:pathData="M320,326.3c51.6,0 93.6,-38.2 93.6,-85.2a82,82 0,0 0,-32.6 -64.4,70 70,0 0,1 19.2,48c0,40.8 -35.9,73.9 -80.2,73.9s-80.2,-33.1 -80.2,-74c0,-18.3 7.2,-35.1 19.2,-48a82,82 0,0 0,-32.6 64.6c0,46.9 42,85.1 93.6,85.1"
      android:fillColor="#fff"/>
  <path
      android:pathData="M307.33,373.73c-20.64,9.39 -46.45,6.83 -68.26,-5.98 -0.94,-0.43 -1.41,0.21 -0.47,2.56 2.35,6.19 4.22,11.1 -2.81,20.28 -3.05,4.06 0.47,4.7 5.63,4.27 26.27,-2.35 52.08,-7.68 64.51,-12.17zM306.86,384.83c-8.21,2.99 -22.29,6.62 -38,9.18 -6.33,0.85 -6.1,4.48 5.16,5.76 11.49,1.07 26.27,-6.4 35.19,-13.02z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M320,364.34c2.81,0 7.98,-1.07 13.14,-1.71 9.62,-1.49 2.58,11.95 -13.14,11.95l0,4.48c15.95,0 32.61,-15.8 29.09,-22.84 -4.93,-10.25 -18.53,-1.49 -29.09,-1.49s-24.16,-8.75 -29.09,1.49c-3.52,7.04 13.14,22.84 29.09,22.84l0,-4.48c-15.72,0 -22.75,-13.45 -13.14,-11.95 5.16,0.64 10.32,1.71 13.14,1.71z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M332.67,373.73c20.64,9.39 46.45,6.83 68.26,-5.98 0.94,-0.43 1.41,0.21 0.47,2.56 -2.35,6.19 -4.22,11.1 2.81,20.28 3.05,4.06 -0.47,4.7 -5.63,4.27 -26.27,-2.35 -52.08,-7.68 -64.51,-12.17zM333.14,384.83c8.21,2.99 22.05,6.62 38,9.18 6.33,0.85 6.1,4.48 -5.16,5.76 -11.49,1.07 -26.27,-6.4 -35.19,-13.02z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M320.7,382.69c0.47,3.63 1.17,11.53 6.57,8.11 7.27,-4.48 8.91,-7.9 8.91,-14.3 0,-4.06 -5.4,-10.03 -16.19,-10.03s-16.19,5.98 -16.19,10.03c0,6.4 1.64,9.82 8.91,14.3 5.4,3.42 5.86,-4.48 6.57,-8.11 0.23,-1.28 1.41,-0.85 1.41,0z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M313.2,109.26c-3.05,-15.8 -28.62,-16.86 -32.61,-19.42 -4.69,-2.77 -3.99,0 -2.35,4.27 4.69,11.1 20.64,15.58 27.92,16.86 5.86,0.85 7.74,1.28 7.04,-1.71z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M320.94,109.69c2.58,-16.22 -22.75,-23.91 -25.8,-27.54 -3.52,-3.84 -3.99,-1.49 -2.35,2.99 3.05,9.61 14.07,20.92 20.64,23.91 5.4,2.56 7.04,3.63 7.51,0.64z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M341.82,119.08c2.35,-19.42 -18.3,-22.41 -23.69,-28.6 -3.52,-3.84 -3.75,-1.71 -2.58,2.77 2.35,9.82 12.67,21.34 19,24.97 4.93,2.77 7.04,3.84 7.27,0.85z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M335.48,114.81c-21.35,-12.59 -36.36,-5.55 -42.46,-6.19 -5.86,-0.64 -7.74,2.77 2.35,7.9 12.43,6.19 29.79,5.34 36.59,2.99 7.04,-2.56 4.93,-3.84 3.52,-4.7zM367.62,123.35c-6.57,-20.92 -21.82,-17.5 -26.27,-20.06s-4.93,-1.92 -3.99,2.77c1.88,8.32 17.59,17.5 25.33,20.28 2.81,0.85 6.33,2.13 4.93,-2.99z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M364.57,126.98c-18.3,-13.45 -32.61,-3.42 -38.24,-4.91 -4.22,-1.07 -2.35,1.49 -0.7,2.56 11.73,7.47 26.27,11.53 37.53,6.83 4.46,-1.71 4.69,-2.13 1.41,-4.48zM404.21,140.64c0.23,-13.23 -29.79,-18.78 -36.13,-26.89 -3.75,-4.91 -7.04,-2.35 -5.16,5.55 2.81,10.25 23.46,21.56 34.72,23.69 6.8,1.28 6.57,-0.85 6.57,-2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M403.28,142.99c-19,-15.58 -34.95,-10.46 -40.82,-11.95 -5.86,-1.28 -8.21,1.92 0.94,8.32 11.26,7.68 28.62,9.18 35.89,7.68s5.4,-2.99 3.99,-4.06zM437.29,165.83c-5.4,-22.63 -22.52,-27.32 -26.74,-31.59 -3.99,-4.27 -8.21,-2.99 -4.69,7.26 4.22,12.17 18.06,22.84 25.33,25.4 7.04,2.77 6.57,0.64 6.1,-1.07z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M437.06,168.82c-13.84,-20.28 -31.9,-19.64 -37.53,-22.41 -5.4,-2.99 -9.15,-0.43 -1.88,8.32 8.44,10.67 25.8,16.65 33.78,17.08s6.57,-1.49 5.63,-2.99z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M454.88,192.94c7.98,-23.05 -10.32,-31.59 -12.2,-35.43 -2.11,-3.84 -4.22,-3.84 -5.4,0.21 -5.16,16.44 11.49,32.44 14.07,35.65 2.58,2.99 3.05,1.49 3.52,-0.43z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M451.13,199.34c-6.33,-25.83 -22.99,-24.33 -26.74,-27.96 -3.99,-3.63 -4.46,-1.07 -3.75,3.63 1.88,12.59 18.53,21.13 26.04,25.4 2.35,1.28 5.16,2.77 4.46,-1.07zM467.08,229.65c11.49,-24.33 -2.11,-40.77 -6.33,-44.4 -4.22,-3.42 -6.8,-4.91 -5.4,0 1.88,7.47 -4.69,26.68 5.4,40.77 3.28,4.7 3.75,9.18 6.33,3.63z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460.98,234.56c2.58,-14.94 -6.8,-28.6 -16.89,-33.94 -5.86,-3.2 -6.1,-2.35 -6.1,2.13 0.47,13.87 14.78,25.4 19,31.8 3.99,5.98 3.75,1.49 3.99,0z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M458.4,273.41c-8.44,-28.18 9.15,-44.4 14.54,-47.6 5.16,-3.42 8.44,-4.7 6.1,0.64 -3.52,7.9 0.23,29.88 -13.14,43.76 -4.22,4.7 -5.86,9.61 -7.51,3.2z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M460.28,267.22c7.04,-24.55 -8.21,-38.42 -12.9,-41.2 -4.46,-2.77 -7.27,-3.84 -5.16,0.64 2.81,6.83 -0.23,26.04 11.49,37.99 3.75,4.06 5.16,8.11 6.57,2.56z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M451.6,255.69c-12.67,12.38 -12.9,30.52 -7.27,41.2 3.52,6.19 3.99,5.76 7.27,1.28 8.91,-13.02 3.52,-31.8 3.99,-40.13 0.23,-7.9 -2.58,-3.63 -3.99,-2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M472.48,275.33c0,17.08 -11.49,30.95 -23.69,35.22 -7.04,2.35 -7.04,1.71 -6.1,-3.42 3.28,-19.21 19.47,-26.25 25.33,-32.44 5.63,-5.98 4.46,-1.07 4.46,0.64z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M428.85,326.13c20.64,-17.08 9.62,-29.03 10.56,-40.13 0.47,-5.98 -2.11,-4.48 -4.46,-2.35 -13.14,11.74 -13.84,32.66 -11.03,40.77 1.17,3.63 3.05,3.2 4.93,1.71z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M456.53,315.03c-6.8,18.78 -24.87,30.31 -40.11,30.95 -8.91,0.43 -8.68,-0.21 -5.63,-5.76 11.49,-20.06 31.9,-22.41 41.05,-27.54 8.44,-4.7 5.4,0.43 4.69,2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M400.46,350.25c26.74,-12.17 21.35,-32.44 26.27,-37.57 3.52,-3.63 -0.7,-3.2 -2.81,-1.92 -15.72,8.32 -28.38,21.56 -28.62,35.65 0,5.34 0.47,5.98 5.16,3.84z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M363.87,372.88c33.78,4.91 49.5,-18.36 59.35,-20.49 5.16,-1.07 1.41,-2.99 -1.17,-3.2 -22.52,-2.35 -51.14,7.26 -59.82,17.93 -3.52,4.27 -3.52,5.12 1.64,5.76z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M398.12,336.38c-6.8,20.28 -32.14,36.93 -51.14,38.21 -8.91,0.64 -8.91,-0.21 -5.63,-5.55 15.25,-25.19 41.76,-29.46 51.14,-35.86 7.98,-5.55 6.33,1.28 5.63,3.2zM326.8,109.26c3.05,-15.8 28.62,-16.86 32.61,-19.42 4.69,-2.77 3.99,0 2.35,4.27 -4.69,11.1 -20.64,15.58 -27.92,16.86 -5.86,0.85 -7.74,1.28 -7.04,-1.71z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M319.06,109.69c-2.58,-16.22 22.75,-23.91 25.8,-27.54 3.52,-3.84 3.99,-1.49 2.35,2.99 -3.05,9.61 -14.07,20.92 -20.64,23.91 -5.4,2.56 -7.04,3.63 -7.51,0.64z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M298.18,119.08c-2.35,-19.42 18.3,-22.41 23.69,-28.6 3.52,-3.84 3.75,-1.71 2.58,2.77 -2.35,9.82 -12.67,21.34 -19,24.97 -4.93,2.77 -7.04,3.84 -7.27,0.85z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M304.52,114.81c21.35,-12.59 36.36,-5.55 42.46,-6.19 5.86,-0.64 7.74,2.77 -2.35,7.9 -12.43,6.19 -29.79,5.34 -36.59,2.99 -7.04,-2.56 -4.93,-3.84 -3.52,-4.7zM272.38,123.35c6.57,-20.92 21.82,-17.5 26.27,-20.06s4.93,-1.92 3.99,2.77c-1.88,8.32 -17.59,17.5 -25.33,20.28 -2.81,0.85 -6.33,2.13 -4.93,-2.99z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M275.43,126.98c18.3,-13.45 32.61,-3.42 38.24,-4.91 4.22,-1.07 2.35,1.49 0.7,2.56 -11.73,7.47 -26.27,11.53 -37.53,6.83 -4.46,-1.71 -4.69,-2.13 -1.41,-4.48zM235.79,140.64c-0.23,-13.23 29.79,-18.78 36.13,-26.89 3.75,-4.91 7.04,-2.35 5.16,5.55 -2.81,10.25 -23.46,21.56 -34.72,23.69 -6.8,1.28 -6.57,-0.85 -6.57,-2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M236.72,142.99c19,-15.58 34.95,-10.46 40.82,-11.95 5.86,-1.28 8.21,1.92 -0.94,8.32 -11.26,7.68 -28.62,9.18 -35.89,7.68s-5.4,-2.99 -3.99,-4.06zM202.71,165.83c5.4,-22.63 22.52,-27.32 26.74,-31.59 3.99,-4.27 8.21,-2.99 4.69,7.26 -4.22,12.17 -18.06,22.84 -25.33,25.4 -7.04,2.77 -6.57,0.64 -6.1,-1.07z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M202.94,168.82c13.84,-20.28 31.9,-19.64 37.53,-22.41 5.4,-2.99 9.15,-0.43 1.88,8.32 -8.44,10.67 -25.8,16.65 -33.78,17.08s-6.57,-1.49 -5.63,-2.99z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M185.12,192.94c-7.98,-23.05 10.32,-31.59 12.2,-35.43 2.11,-3.84 4.22,-3.84 5.4,0.21 5.16,16.44 -11.49,32.44 -14.07,35.65 -2.58,2.99 -3.05,1.49 -3.52,-0.43z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M188.87,199.34c6.33,-25.83 22.99,-24.33 26.74,-27.96 3.99,-3.63 4.46,-1.07 3.75,3.63 -1.88,12.59 -18.53,21.13 -26.04,25.4 -2.35,1.28 -5.16,2.77 -4.46,-1.07zM172.92,229.65c-11.49,-24.33 2.11,-40.77 6.33,-44.4 4.22,-3.42 6.8,-4.91 5.4,0 -1.88,7.47 4.69,26.68 -5.4,40.77 -3.28,4.7 -3.75,9.18 -6.33,3.63z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M179.02,234.56c-2.58,-14.94 6.8,-28.6 16.89,-33.94 5.86,-3.2 6.1,-2.35 6.1,2.13 -0.47,13.87 -14.78,25.4 -19,31.8 -3.99,5.98 -3.75,1.49 -3.99,0z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M181.6,273.41c8.44,-28.18 -9.15,-44.4 -14.54,-47.6 -5.16,-3.42 -8.44,-4.7 -6.1,0.64 3.52,7.9 -0.23,29.88 13.14,43.76 4.22,4.7 5.63,9.61 7.51,3.2z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M179.72,267.22c-7.04,-24.55 8.21,-38.42 12.9,-41.2 4.46,-2.77 7.27,-3.84 5.16,0.64 -2.81,6.83 0.23,26.04 -11.49,37.99 -3.75,4.06 -5.16,8.11 -6.57,2.56z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M188.4,255.69c12.67,12.38 12.9,30.52 7.27,41.2 -3.52,6.19 -3.99,5.76 -7.27,1.28 -8.91,-13.02 -3.52,-31.8 -3.99,-40.13 -0.23,-7.9 2.58,-3.63 3.99,-2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M167.52,275.33c0,17.08 11.49,30.95 23.69,35.22 7.04,2.35 7.04,1.71 6.1,-3.42 -3.28,-19.21 -19.47,-26.25 -25.33,-32.44 -5.63,-5.98 -4.46,-1.07 -4.46,0.64z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M211.15,326.13c-20.64,-17.08 -9.62,-29.03 -10.56,-40.13 -0.47,-5.98 2.11,-4.48 4.46,-2.35 13.14,11.74 13.84,32.66 11.03,40.77 -1.17,3.63 -3.05,3.2 -4.93,1.71z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M183.47,315.03c6.8,18.78 24.87,30.31 40.11,30.95 8.91,0.43 8.68,-0.21 5.63,-5.76 -11.49,-20.06 -31.9,-22.41 -41.05,-27.54 -8.44,-4.7 -5.4,0.43 -4.69,2.35z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M239.54,350.25c-26.74,-12.17 -21.35,-32.44 -26.27,-37.57 -3.52,-3.63 0.7,-3.2 2.81,-1.92 15.72,8.32 28.38,21.56 28.62,35.65 0,5.34 -0.47,5.98 -5.16,3.84z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M276.13,372.88c-33.78,4.91 -49.5,-18.36 -59.35,-20.49 -5.16,-1.07 -1.41,-2.99 1.17,-3.2 22.52,-2.35 51.14,7.26 59.82,17.93 3.52,4.27 3.52,5.12 -1.64,5.76z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M241.88,336.38c6.8,20.28 32.14,36.93 51.14,38.21 8.91,0.64 8.91,-0.21 5.63,-5.55 -15.25,-25.19 -41.76,-29.46 -51.14,-35.86 -7.98,-5.55 -6.33,1.28 -5.63,3.2z"
      android:strokeWidth="1.79"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M359.6,128.9c-4.4,-3 -20.8,-1.3 -23.9,-3.3 5.9,4.5 19,1.3 24,3.3zM399.3,136.5c-3.5,-5.7 -24.4,-9.6 -27.5,-14.7 5.5,9.8 21.6,8.5 27.5,14.7m-3,6.6c-7.8,-6.8 -25.8,-4 -31.3,-8 12.7,10.4 19.7,2.3 31.2,8zM351,112.8c4.9,2.4 11,4.7 14,10.3 -3.5,-4.3 -9.8,-6 -15,-9.6q0.5,-0.1 1,-0.7m77,44c-3.1,-6.4 -14,-13.4 -14.9,-15.8 3,8.3 12,10.3 14.8,15.8zM430.7,168.1c-9.4,-13.4 -24.1,-12 -30,-17 4.5,4.9 21.4,8 30,17m21.8,20.7c0.7,-14.3 -11,-19.6 -11.4,-27.7 -0.3,9.6 12,22.6 11.4,27.7m-5.8,7.7c-2.4,-12.4 -18.3,-13.2 -21.1,-20.5 0,6.8 18.7,13.9 21,20.5zM459.8,189.5c8.5,9.4 2.6,23.7 6.1,34.1 -4.2,-7.7 -2.1,-26.9 -6,-34.1zM446,229.5c12.6,12.5 7.5,26.3 12.6,32.3 -6.3,-8.3 -5.4,-24.5 -12.6,-32.2zM472.3,231.3c-10.9,10.9 -4.3,27.3 -10,35 6.4,-6.6 5.5,-27 10,-35m-13.7,0c-1.4,-12.6 -14.3,-19.2 -15.4,-26 -1.5,6.8 12.4,17.5 15.4,26m-6.5,30c2,8.8 -5.7,27.6 -3.3,33.4 -5.2,-10 4.4,-29 3.3,-33.3zM468.7,281.4c-5.1,15.6 -15.5,14.6 -18.7,24 2.3,-9 16,-17.1 18.7,-24m-33.5,7.3c-6.8,10.5 -1.2,22.4 -6.8,29.9 8,-7.5 3.7,-21.4 6.8,-29.9m16.4,28.6c-8.2,13.9 -25.1,12.6 -31.9,22.6 6.8,-12.6 27.7,-14.7 32,-22.6zM421.8,315.6c-14.5,9.2 -10,18.8 -21.1,29 13.8,-10.2 12.7,-21.5 21.1,-29m-6.8,37.2c-14,-0.5 -34.2,16.2 -46.4,14.9 12.2,2.4 34.7,-12.6 46.4,-15zM392.3,337.8c-1,13 -37.6,21.4 -41.5,30.1 4.4,-11.5 36.6,-20 41.5,-30zM309.5,97.8c-4.7,-3.7 -10.4,-6.7 -12,-10.3 1.2,4.7 5.8,8 10.5,11.3 0.5,-0.2 1,-0.9 1.5,-1.1zM301.5,101.5c-7.3,-3.2 -15.7,-3 -19.5,-7.4 2.4,4.4 10.3,6.1 17.1,8.5q1.2,-0.7 2.4,-1zM280.4,128.8c4.4,-3 20.8,-1.2 23.9,-3.2 -5.9,4.5 -19,1.3 -24,3.2zM240.7,136.5c3.5,-5.7 24.4,-9.6 27.5,-14.7 -5.4,9.8 -21.6,8.5 -27.5,14.7m3,6.6c7.8,-6.8 25.9,-4 31.3,-8 -12.7,10.4 -19.7,2.3 -31.2,8zM275,123.1c4.4,-8.6 17,-9.6 20.4,-14.8 -5,7.7 -15.7,9 -20.4,14.8m36,-7.5c13,-5.5 25.7,-0.8 31.8,-3.4 -7.5,3.6 -25.4,1.9 -31.7,3.4zM212.1,156.8c3,-6.4 13.8,-13.5 14.8,-15.8 -3,8.3 -12,10.3 -14.8,15.8m-2.8,11.3c9.4,-13.4 24.1,-12 30,-17 -4.4,4.9 -21.3,8 -30,17m-21.8,20.7c-0.7,-14.3 11,-19.6 11.5,-27.7 0.2,9.6 -12,22.6 -11.5,27.7m5.8,7.7c2.4,-12.4 18.3,-13.2 21.1,-20.5 0,6.8 -18.7,13.9 -21,20.5zM180.2,189.5c-8.4,9.4 -2.6,23.6 -6,34.1 4.1,-7.7 2,-26.9 6,-34.1m13.8,40c-12.6,12.5 -7.5,26.3 -12.6,32.3 6.3,-8.3 5.4,-24.5 12.6,-32.2zM167.8,231.3c10.8,10.9 4.2,27.3 9.8,35 -6.3,-6.6 -5.4,-27 -9.8,-35m13.6,0c1.4,-12.6 14.3,-19.2 15.4,-26 1.5,6.8 -12.4,17.5 -15.4,26m6.5,30c-2,8.8 5.7,27.6 3.3,33.4 5.2,-10 -4.4,-29 -3.3,-33.3zM171.3,281.4c5.2,15.6 15.5,14.6 18.8,24 -2.4,-9 -16,-17.1 -18.8,-24m33.5,7.3c6.8,10.5 1.2,22.4 6.8,29.9 -8,-7.5 -3.7,-21.4 -6.8,-29.9m-16.4,28.6c8.2,13.9 25.1,12.6 32,22.6 -6.9,-12.6 -27.8,-14.7 -32,-22.6m29.8,-1.7c14.5,9.2 10.1,18.8 21.1,29 -13.8,-10.2 -12.6,-21.5 -21.1,-29m6.8,37.1c14,-0.4 34.3,16.3 46.4,15 -12.1,2.3 -34.7,-12.6 -46.4,-15m22.8,-15c0.9,13.1 37.5,21.4 41.5,30.2 -4.5,-11.5 -36.6,-20 -41.6,-30.1zM301,116c2.8,-11.5 17,-13.6 18.8,-20.5 -0.7,7.3 -17.4,15.4 -18.8,20.5m41.5,-28.6c-2,8.8 -17.3,13.7 -19.4,20.3 0.7,-9 16.4,-14 19.4,-20.3m-12,20.8c7.3,-10.7 22.3,-8 27.5,-14.1 -3.8,7.2 -22.3,7.4 -27.5,14z"
      android:fillColor="#006233"/>
  <path
      android:pathData="M429.8,240c0,55.5 -49.3,100.4 -110.3,100.4 -60.9,0 -110.3,-44.9 -110.3,-100.3 0,-55.5 49.4,-100.4 110.3,-100.4 61,0 110.3,45 110.3,100.4z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.8"
      android:fillColor="#00000000"
      android:strokeColor="#f7c608"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m298,340.5 l-0.5,1.2q-0.5,1.3 -2.1,1.2l-8,-1.9 2.6,-7.7 8,1.7q1.5,0.4 1,1.8l-0.2,1m-19,-4.8 l0.4,-1.2q0.5,-1.4 2,-1l7.8,2.5 -2.5,7.7q-4.2,-1 -7.9,-2.3c-0.8,-0.4 -1,-2 -0.7,-2.9"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m296.4,339.8 l-0.3,0.9q-0.5,1 -1.7,0.8l-6.6,-1.6 1.8,-5.6c2.4,0.7 4.9,1.2 6.6,1.5q1.3,0.4 1,1.4l-0.2,0.7m-15.8,-4 l0.3,-1q0.4,-0.8 1.6,-0.6 2.8,1 6.5,2l-1.8,5.6 -6.5,-1.9c-0.7,-0.4 -1,-1.5 -0.7,-2.1"
      android:fillColor="#006233"/>
  <path
      android:pathData="m267.7,330.8 l-0.7,1q-0.9,1.2 -2.4,0.7c-2,-1.2 -4.7,-2.5 -7,-3.9l4.8,-6.8 7.1,3.7q1.2,0.8 0.5,2l-0.6,1m-16.7,-9.6 l0.7,-1q1,-1.2 2.3,-0.5 2.8,2 6.7,4.4l-4.9,6.8 -6.7,-4.2c-0.7,-0.7 -0.4,-2.3 0,-3"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m266.5,329.7 l-0.6,0.8q-0.6,0.8 -1.9,0.3c-1.6,-1 -3.8,-2 -5.8,-3.2l3.5,-4.9c2,1.3 4.3,2.4 5.9,3.1q1,0.6 0.5,1.6l-0.5,0.6m-13.8,-7.9 l0.5,-0.8q0.7,-0.8 1.8,-0.2l5.6,3.6 -3.5,4.9q-3.1,-1.7 -5.6,-3.5c-0.6,-0.5 -0.5,-1.7 -0.1,-2.2"
      android:fillColor="#006233"/>
  <path
      android:pathData="m241.8,313.7 l-1,0.8q-1.3,0.8 -2.6,0c-1.5,-1.6 -3.7,-3.5 -5.5,-5.5l6.7,-5.3c2,2.1 4.2,4 5.7,5.4q1,1 -0.1,2l-0.9,0.8m-13,-13.4 l1,-0.9q1.2,-0.8 2.3,0.2a73,73 0,0 0,5 6l-6.7,5.2q-2.9,-3 -5.2,-5.8c-0.5,-0.8 0.3,-2.2 1,-2.8"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m240.9,312.4 l-0.8,0.6q-0.8,0.6 -1.9,-0.2l-4.6,-4.6 4.9,-3.8 4.7,4.5q0.7,1 0,1.7l-0.7,0.5m-10.8,-11.2 l0.7,-0.6q1,-0.6 1.8,0.2 1.8,2.3 4.3,5l-4.9,3.7 -4.3,-4.8c-0.4,-0.6 0.1,-1.7 0.6,-2.1"
      android:fillColor="#006233"/>
  <path
      android:pathData="m222.2,290.7 l-1.3,0.5q-1.3,0.4 -2.4,-0.6l-3.6,-6.8 8.1,-3.3c1.3,2.5 2.7,5 3.8,6.6q0.5,1.3 -0.8,2l-1,0.4m-8.4,-16.2 l1.2,-0.6q1.5,-0.4 2.2,0.8a71,71 0,0 0,3 7l-8,3.3a60,60 0,0 1,-3.3 -6.8c-0.2,-1 1,-2.1 1.9,-2.5"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m221.7,289.2 l-0.9,0.3q-1,0.4 -1.8,-0.6l-3,-5.6 5.8,-2.4 3.2,5.5q0.4,1.2 -0.5,1.6l-0.8,0.3m-7,-13.5 l1,-0.3q1,-0.4 1.6,0.6l2.5,5.8 -5.7,2.4 -2.7,-5.7c-0.2,-0.7 0.6,-1.6 1.2,-1.9"
      android:fillColor="#006233"/>
  <path
      android:pathData="m210.5,263.5 l-1.4,0.2a2,2 0,0 1,-2 -1.2l-1.5,-7.4 8.8,-1.1a64,64 0,0 0,1.7 7.3q0,1.4 -1.4,1.7l-1.2,0.2m-3,-17.7 l1.4,-0.2q1.5,-0.1 1.8,1.2 0.1,3.3 0.7,7.5l-8.8,1.1 -1,-7.4c0.2,-0.9 1.7,-1.7 2.6,-1.8"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m210.5,262 l-1,0.1q-1,0.1 -1.5,-1l-1.1,-6.2 6.3,-0.8 1.3,6.1q0.1,1.1 -1,1.4l-0.8,0.1m-2.5,-14.7 l1,-0.2q1.2,0 1.3,1.1 0.2,2.8 0.7,6.2l-6.3,0.8q-0.6,-3.1 -0.8,-6.1c0,-0.7 1.1,-1.4 1.8,-1.5"
      android:fillColor="#006233"/>
  <path
      android:pathData="m207.7,234.5 l-1.4,-0.2q-1.5,-0.2 -1.6,-1.7c0.3,-2 0.5,-4.8 1,-7.4l8.7,1.2a65,65 0,0 0,-0.7 7.4q-0.3,1.4 -1.8,1.3l-1.2,-0.2m2.6,-17.7 l1.4,0.1q1.4,0.4 1.4,1.7a69,69 0,0 0,-1.7 7.4l-8.8,-1.2q0.6,-3.8 1.4,-7.4c0.4,-0.8 2.1,-1.2 3,-1"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="M208.2,233h-1q-1,-0.4 -1.1,-1.5l0.8,-6.1 6.3,0.8 -0.6,6.2q-0.2,1.1 -1.4,1h-0.8m2.1,-14.9 l1,0.2q1.1,0.2 1,1.4 -0.7,2.6 -1.3,6l-6.3,-0.7 1.1,-6.2c0.3,-0.7 1.5,-1 2.2,-1"
      android:fillColor="#006233"/>
  <path
      android:pathData="m214,206 l-1.3,-0.6q-1.3,-0.5 -1,-2c1,-2 2,-4.6 3.2,-6.9l8,3.4a70,70 0,0 0,-3 7q-0.6,1.1 -2,0.7l-1.2,-0.5m8,-16.4 l1.3,0.6q1.3,0.6 0.8,2a73,73 0,0 0,-3.8 6.6l-8.1,-3.4q1.7,-3.6 3.6,-6.7c0.6,-0.7 2.4,-0.7 3.2,-0.3"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m215,204.7 l-1,-0.4c-0.6,-0.2 -0.8,-1 -0.6,-1.6l2.6,-5.7 5.8,2.4 -2.5,5.8q-0.6,1 -1.6,0.6l-0.8,-0.3m6.7,-13.6 l0.9,0.4q1,0.4 0.5,1.6l-3.2,5.5 -5.7,-2.4q1.4,-3.1 3,-5.6c0.4,-0.6 1.7,-0.7 2.3,-0.4"
      android:fillColor="#006233"/>
  <path
      android:pathData="m228.9,180.2 l-1.1,-0.9q-1,-0.9 -0.4,-2.2c1.6,-1.6 3.4,-3.9 5.2,-5.8l6.8,5.3a72,72 0,0 0,-5 6,2 2,0 0,1 -2.4,0l-0.9,-0.6m12.8,-13.7 l1,0.8q1.2,1 0.2,2.2l-5.7,5.3 -6.8,-5.3q2.8,-3 5.6,-5.5c0.8,-0.5 2.5,0 3.2,0.5"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m230.2,179.2 l-0.8,-0.6q-0.7,-0.7 -0.1,-1.7l4.3,-4.9 4.8,3.8 -4.2,5q-0.8,0.8 -1.8,0.2l-0.6,-0.5m10.6,-11.4 l0.8,0.6q0.7,0.7 0,1.6l-4.8,4.6 -4.8,-3.8q2.3,-2.6 4.6,-4.6c0.7,-0.5 2,-0.2 2.4,0.2"
      android:fillColor="#006233"/>
  <path
      android:pathData="m251,159.2 l-0.7,-1q-0.7,-1.2 0.4,-2.3c2,-1.1 4.4,-2.8 6.8,-4.2l4.8,6.8a78,78 0,0 0,-6.7 4.4,2 2,0 0,1 -2.2,-0.4l-0.7,-1m16.5,-9.8 l0.7,1q0.8,1.4 -0.4,2.1 -3.3,1.5 -7.2,3.7l-4.8,-6.8q3.5,-2.2 7,-3.9c1,-0.2 2.4,0.7 2.9,1.4"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m252.7,158.6 l-0.6,-0.7q-0.4,-1 0.4,-1.7 2.6,-1.7 5.7,-3.5l3.4,4.8 -5.5,3.7q-1.1,0.6 -1.8,-0.3l-0.5,-0.6m13.7,-8.2 l0.6,0.8q0.4,0.9 -0.5,1.5l-6,3.1 -3.4,-4.8 5.8,-3.3c0.8,-0.2 1.9,0.4 2.3,0.9"
      android:fillColor="#006233"/>
  <path
      android:pathData="m279,144.9 l-0.5,-1.3q-0.2,-1.3 1,-2l7.9,-2.3 2.5,7.7a83,83 0,0 0,-7.8 2.6q-1.4,0.3 -2,-1l-0.3,-1m18.8,-5.4 l0.4,1.3q0.3,1.3 -1,1.8l-8.1,1.7 -2.5,-7.7a85,85 0,0 1,8 -2c0.9,0 2,1.3 2.3,2"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m280.6,144.7 l-0.3,-1q-0.1,-0.8 1,-1.4l6.5,-2 1.8,5.6 -6.5,2q-1.2,0.4 -1.6,-0.6l-0.3,-0.7m15.7,-4.4 l0.3,0.9q0.3,1 -1,1.4 -3,0.5 -6.6,1.4l-1.8,-5.5 6.6,-1.6c0.8,-0.1 1.6,0.8 1.8,1.4"
      android:fillColor="#006233"/>
  <path
      android:pathData="M310,138.2v-1.3q0.2,-1.3 1.7,-1.7l8.2,-0.2v8.1a84,84 0,0 0,-8.2 0.4q-1.6,0 -1.6,-1.5v-1m19.7,-0.2v1.2q-0.1,1.4 -1.7,1.5l-8.2,-0.4V135q4.3,0 8.2,0.2c1,0.2 1.7,1.7 1.7,2.6"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="M311.8,138.5v-1q0,-0.9 1.3,-1.2l6.9,-0.1v5.8q-4,0 -6.9,0.3 -1.2,-0.1 -1.3,-1v-0.9m16.3,-0.1v0.9q0,1 -1.3,1l-6.8,-0.2v-5.8l6.8,0.1c0.8,0.2 1.3,1.2 1.3,1.9"
      android:fillColor="#006233"/>
  <path
      android:pathData="m340,139.6 l0.3,-1.2q0.5,-1.2 2.1,-1.2l8,1.8 -2.5,7.8 -8,-1.6q-1.4,-0.5 -1.1,-1.9l0.3,-1m19,4.7 l-0.4,1.2q-0.5,1.4 -2,1l-7.8,-2.4 2.5,-7.8q4.1,1 7.8,2.3c0.8,0.4 1,2 0.8,2.8"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m341.5,140.3 l0.2,-0.9q0.5,-1 1.7,-0.8l6.6,1.5 -1.7,5.6 -6.7,-1.4q-1.1,-0.4 -1,-1.4l0.3,-0.7m15.8,4 l-0.3,0.8q-0.4,1 -1.6,0.7l-6.5,-2 1.7,-5.6q3.5,0.9 6.6,1.9c0.7,0.3 1,1.5 0.7,2"
      android:fillColor="#006233"/>
  <path
      android:pathData="m370.2,149.1 l0.7,-1q0.9,-1.2 2.4,-0.7c2,1.1 4.7,2.4 7.1,3.8l-4.7,6.9a81,81 0,0 0,-7.3 -3.6q-1.2,-0.9 -0.5,-2.1l0.7,-1m16.8,9.5 l-0.8,1a2,2 0,0 1,-2.2 0.5l-6.7,-4.3 4.7,-6.9q3.7,2 6.8,4.2c0.7,0.6 0.4,2.2 -0.1,3"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m371.5,150.2 l0.5,-0.8q0.6,-0.7 1.9,-0.4 2.6,1.4 5.8,3.2l-3.4,5 -6,-3.1q-1,-0.6 -0.4,-1.6l0.4,-0.7m14,7.9 l-0.6,0.8q-0.6,0.8 -1.8,0.2l-5.6,-3.6 3.4,-4.9 5.7,3.4c0.6,0.6 0.5,1.7 0.1,2.3"
      android:fillColor="#006233"/>
  <path
      android:pathData="m396.3,166 l1,-0.9q1.2,-0.8 2.5,0l5.6,5.5 -6.6,5.3a75,75 0,0 0,-5.8 -5.3q-1,-1 0.1,-2l0.9,-0.8m13.2,13.3 l-1,0.9a2,2 0,0 1,-2.4 -0.2,72 72,0 0,0 -5,-5.9l6.7,-5.3q2.8,3 5.2,5.7c0.4,0.8 -0.3,2.3 -1,2.8"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m397.2,167.3 l0.7,-0.6q0.8,-0.5 2,0.1 2,2.1 4.6,4.6l-4.8,3.8 -4.8,-4.5q-0.8,-0.9 0,-1.6l0.7,-0.5m11,11 l-0.8,0.7q-0.9,0.6 -1.8,-0.2l-4.3,-4.9 4.8,-3.8 4.4,4.7c0.4,0.7 -0.1,1.8 -0.6,2.2"
      android:fillColor="#006233"/>
  <path
      android:pathData="m416.1,188.9 l1.3,-0.6q1.4,-0.4 2.4,0.7l3.7,6.6 -8.1,3.5q-2,-4 -4,-6.6 -0.5,-1.3 0.9,-2l1,-0.5m8.6,16.2 l-1.3,0.5c-0.8,0.4 -1.8,0 -2.1,-0.7a71,71 0,0 0,-3.1 -7l8,-3.4 3.3,6.9c0.2,0.9 -1,2 -1.8,2.4"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m416.6,190.4 l0.9,-0.4q1,-0.4 1.8,0.6l3,5.5 -5.8,2.5 -3.2,-5.5q-0.4,-1 0.5,-1.6l0.8,-0.3m7,13.5 l-0.8,0.3q-1,0.4 -1.7,-0.6 -1,-2.6 -2.6,-5.8l5.8,-2.5 2.8,5.7c0.1,0.8 -0.7,1.7 -1.3,2"
      android:fillColor="#006233"/>
  <path
      android:pathData="m428,215.9 l1.4,-0.2a2,2 0,0 1,2.1 1.2l1.5,7.3 -8.8,1.3a65,65 0,0 0,-1.7 -7.3q-0.1,-1.4 1.4,-1.7l1.1,-0.2m3.2,17.7 l-1.4,0.2q-1.5,0.1 -1.8,-1.3l-0.8,-7.4 8.8,-1.3q0.6,3.9 1,7.5c0,0.9 -1.6,1.7 -2.5,1.8"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m428,217.4 l1,-0.1q1.1,0 1.5,1 0.6,2.8 1.2,6.1l-6.3,1 -1.3,-6.2q-0.2,-1.2 1,-1.3l0.8,-0.2m2.6,14.7 l-1,0.2q-1.1,0 -1.4,-1l-0.7,-6.3 6.3,-0.9q0.7,3.3 0.9,6.2c0,0.7 -1.1,1.4 -1.8,1.5"
      android:fillColor="#006233"/>
  <path
      android:pathData="m431.1,244.9 l1.4,0.1q1.5,0.3 1.7,1.8l-0.9,7.4 -8.8,-1.1c0.4,-2.7 0.6,-5.5 0.6,-7.5 0.1,-0.8 1,-1.4 1.9,-1.2l1.1,0.1m-2.4,17.8 l-1.4,-0.2q-1.5,-0.2 -1.4,-1.7 1,-3.1 1.6,-7.3l8.8,1q-0.6,4 -1.3,7.4c-0.4,0.9 -2.1,1.3 -3,1.2"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="M430.6,246.4h1q1,0.4 1.2,1.5l-0.8,6.2 -6.3,-0.8 0.6,-6.2q0.1,-1.1 1.3,-1.1h0.9m-2,14.9 l-1,-0.1q-1.1,-0.2 -1,-1.4 0.7,-2.7 1.2,-6.1l6.3,0.8 -1,6c-0.3,0.8 -1.6,1.2 -2.2,1"
      android:fillColor="#006233"/>
  <path
      android:pathData="m425.1,273.5 l1.3,0.5q1.3,0.7 1,2l-3,7 -8.2,-3.3a66,66 0,0 0,3 -7q0.6,-1.2 2,-0.8l1.2,0.4m-7.9,16.5 l-1.2,-0.5q-1.4,-0.7 -0.9,-2 1.9,-2.8 3.8,-6.6l8.1,3.3 -3.5,6.7c-0.6,0.7 -2.4,0.7 -3.3,0.3"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m424.2,274.8 l1,0.3q0.7,0.5 0.6,1.7l-2.6,5.7 -5.9,-2.3 2.5,-5.8q0.6,-1 1.6,-0.8l0.8,0.4m-6.5,13.6 l-1,-0.3q-1,-0.6 -0.4,-1.6l3,-5.5 5.9,2.3 -3,5.6c-0.5,0.6 -1.8,0.7 -2.4,0.4"
      android:fillColor="#006233"/>
  <path
      android:pathData="m410.5,299.4 l1.1,0.8q1,1 0.4,2.3c-1.6,1.6 -3.4,3.8 -5.2,5.8L400,303c2,-2 3.8,-4.3 5,-6q1,-0.9 2.3,-0.1l0.9,0.7m-12.6,13.8 l-1,-0.8q-1.2,-1 -0.3,-2.1 2.6,-2.2 5.7,-5.5l6.8,5.3 -5.5,5.6c-0.8,0.5 -2.5,0 -3.2,-0.6"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m409.2,300.4 l0.8,0.6q0.7,0.6 0.1,1.7l-4.3,4.8 -4.9,-3.7q2.6,-2.8 4.2,-5 0.9,-0.8 1.8,-0.2l0.6,0.5m-10.4,11.5 l-0.8,-0.6q-0.8,-0.7 0,-1.7l4.6,-4.5 5,3.7q-2.4,2.6 -4.7,4.7c-0.6,0.4 -1.8,0.1 -2.4,-0.3"
      android:fillColor="#006233"/>
  <path
      android:pathData="m388.5,320.5 l0.7,1q0.7,1.3 -0.3,2.3l-6.7,4.3 -5,-6.8a78,78 0,0 0,6.7 -4.4,2 2,0 0,1 2.2,0.4l0.7,0.9m-16.4,10 l-0.7,-1q-0.8,-1.3 0.4,-2.2l7.2,-3.7 4.8,6.8 -7,4c-0.9,0.2 -2.3,-0.7 -2.9,-1.4"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m386.9,321.1 l0.5,0.8q0.5,0.8 -0.4,1.7l-5.6,3.5 -3.5,-4.8 5.6,-3.7q1,-0.6 1.7,0.2l0.5,0.7m-13.6,8.3 l-0.6,-0.8q-0.4,-0.9 0.5,-1.6l6,-3.1 3.4,4.8q-3,1.9 -5.8,3.3c-0.7,0.3 -1.9,-0.3 -2.2,-0.8"
      android:fillColor="#006233"/>
  <path
      android:pathData="m360.8,335.1 l0.4,1.2q0.3,1.4 -1,2l-7.8,2.5 -2.6,-7.8a75,75 0,0 0,7.7 -2.6q1.5,-0.2 2,1l0.4,1m-18.8,5.5 l-0.4,-1.3q-0.3,-1.4 1,-1.8 3.6,-0.6 8,-1.8l2.7,7.8q-4,1.1 -8,2c-1,0 -2,-1.3 -2.3,-2"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="m359,335.3 l0.4,0.9q0.2,1 -1,1.5l-6.4,2 -1.9,-5.6 6.4,-2q1.3,-0.4 1.7,0.6l0.2,0.7m-15.6,4.5 l-0.3,-0.9q-0.2,-1 1,-1.4l6.6,-1.5 1.9,5.6 -6.6,1.6c-0.8,0 -1.7,-0.8 -2,-1.4"
      android:fillColor="#006233"/>
  <path
      android:pathData="M329.7,342v1.3q-0.1,1.4 -1.6,1.7c-2.4,0 -5.4,0.3 -8.2,0.3l-0.1,-8.1a82,82 0,0 0,8.2 -0.5q1.6,0 1.6,1.5v1m-19.6,0.4v-1.2q0,-1.4 1.6,-1.5 3.5,0.2 8.2,0.3v8.1l-8.2,-0.1c-0.9,-0.2 -1.6,-1.7 -1.6,-2.6"
      android:fillColor="#f7c608"/>
  <path
      android:pathData="M328,341.8v0.9q-0.1,0.9 -1.4,1.2l-6.8,0.2v-5.7q4,-0.1 6.8,-0.4 1.2,0 1.4,1v0.8m-16.4,0.3v-1q0,-0.9 1.3,-1 3,0.3 6.9,0.2v5.8H313c-0.8,-0.2 -1.4,-1.3 -1.4,-1.9"
      android:fillColor="#006233"/>
</vector>
