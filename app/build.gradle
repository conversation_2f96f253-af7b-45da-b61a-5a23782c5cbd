apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: "androidx.navigation.safeargs"
apply plugin: 'com.android.application'
apply plugin: 'dagger.hilt.android.plugin'
apply plugin: 'kotlin-parcelize'

android {

    compileSdkVersion = 35
    flavorDimensions = ["default"]

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    defaultConfig {
        applicationId "com.eatapp.clementine"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode 100
        versionName "5.0.0"
        buildConfigField("String", "USER_AGENT", '"clementine"')
        buildConfigField("String", "ADJUST_TOKEN", '"r4f09ukrsmio"')
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {

        config_prod {
            storeFile file('keystore_lime.jks')
            storePassword "E69be0DQ7J2zL3i1duFoM67J"
            keyAlias "eatapp"
            keyPassword "a1LgopjDQeB5lFNk8l6QSqRJ"
        }
        config_stage {
            storeFile file('keystore_lime_staging.jks')
            storePassword "eatapp"
            keyAlias "eatapp"
            keyPassword "eatapp"
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }

    buildTypes {

        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled true
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    productFlavors {

        stage {
            setIsDefault(true)
            dimension "default"
            applicationIdSuffix ".staging"
            resValue "string", "app_name", "Eat Staging"
            buildConfigField("String", "ENVIRONMENT", '"staging"')
            buildConfigField("String", "HUBSPOT_SCRIPT_ID", '"14531893"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"18fe0696edadb52b70ead258af671dca"')
            buildConfigField("String", "LAUNCH_DARKLY_KEY", '"mob-388e031f-2635-4c0c-8531-c8877d0a8da1"')
            buildConfigField("String", "BASE_URL", '"https://api.eatapp.dev/restaurant/v2/"')
            buildConfigField("String", "BASE_URL_ADMIN", '"https://admin.eatapp.co"')
            buildConfigField("String", "BASE_URL_POS", '"https://api.eatapp.dev/services/pos/v2/"')
            buildConfigField("String", "BASE_URL_MESSAGING", '"https://api.eatapp.dev/messaging/v2/"')
            buildConfigField("String", "BASE_URL_PAYMENTS", '"https://api.eatapp.dev/services/payments/v2/"')
            buildConfigField("String", "BASE_URL_HUBSPOT", '"https://api.eatapp.dev/services/hubspot/v2/"')
            buildConfigField("String", "WS_URL", '"wss://ws.eatapp.dev/cable"')
            buildConfigField("String", "QUALTRICS_URL", '"https://qfreeaccountssjc1.az1.qualtrics.com/jfe/form/SV_eS4ZMmQEXH2hQq2"')
            buildConfigField("String", "WS_SERVICE_URL", '"https://ws.eatapp.dev/"')
            buildConfigField("String", "DATADOG_APP_ID", '"e01465bb-9a11-4aea-95f6-9ed5ede6e2fc"')
            buildConfigField("String", "DATADOG_TOKEN", '"pubd5d168f7a2b423c04e2df725c21b75cc"')
            buildConfigField("String", "RECAPTCHA_KEY", '"6LeYL20oAAAAAEYpXTfvQt8nX9PNt9COFFrVmrru"')
            manifestPlaceholders = [
                    appIcon     : "@mipmap/ic_launcher_staging",
                    appIconRound: "@mipmap/ic_launcher_staging_round"
            ]
            signingConfig signingConfigs.config_stage
        }

        prod {
            dimension "default"
            resValue "string", "app_name", "Eat"
            buildConfigField("String", "ENVIRONMENT", '"production"')
            buildConfigField("String", "HUBSPOT_SCRIPT_ID", '"3390327"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"39a6e76a4d44b727ba67a8813eb29ba3"')
            buildConfigField("String", "LAUNCH_DARKLY_KEY", '"mob-4c5d284f-e119-4297-87ec-cbe5b33050f7"')
            buildConfigField("String", "BASE_URL", '"https://api.eatapp.co/restaurant/v2/"')
            buildConfigField("String", "BASE_URL_ADMIN", '"https://admin.eatapp.dev"')
            buildConfigField("String", "BASE_URL_POS", '"https://api.eatapp.co/services/pos/v2/"')
            buildConfigField("String", "BASE_URL_MESSAGING", '"https://api.eatapp.co/messaging/v2/"')
            buildConfigField("String", "BASE_URL_PAYMENTS", '"https://api.eatapp.co/services/payments/v2/"')
            buildConfigField("String", "BASE_URL_HUBSPOT", '"https://api.eatapp.co/services/hubspot/v2/"')
            buildConfigField("String", "WS_URL", '"wss://ws.eatapp.co/cable"')
            buildConfigField("String", "QUALTRICS_URL", '"https://qfreeaccountssjc1.az1.qualtrics.com/jfe/form/SV_eQgF0ZTHbOEbTBs"')
            buildConfigField("String", "WS_SERVICE_URL", '"https://ws.eatapp.co/"')
            buildConfigField("String", "DATADOG_APP_ID", '"e01465bb-9a11-4aea-95f6-9ed5ede6e2fc"')
            buildConfigField("String", "DATADOG_TOKEN", '"pubd5d168f7a2b423c04e2df725c21b75cc"')
            buildConfigField("String", "RECAPTCHA_KEY", '"6LeYL20oAAAAAEYpXTfvQt8nX9PNt9COFFrVmrru"')
            manifestPlaceholders = [
                    appIcon     : "@mipmap/ic_launcher",
                    appIconRound: "@mipmap/ic_launcher_round"
            ]
            signingConfig signingConfigs.config_prod
        }
    }

    buildFeatures {
        dataBinding = true
    }

    configurations {
        configureEach {
            //noinspection DuplicatePlatformClasses
            exclude module: 'httpclient'
            exclude module: 'commons-logging'
        }
    }
    namespace 'com.eatapp.clementine'
}

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Core
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "com.android.support:support-core-utils:34.0.0"
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'com.google.android.material:material:1.12.0'

    // Test
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test:runner:1.6.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    // Viewmodel
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1'

    // Navigation
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    // Retrofit & Gson®
    implementation "com.google.code.gson:gson:2.12.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2'
    implementation 'com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2'

    // Recyclerview sticky headers
    implementation 'com.github.qiujayen:sticky-layoutmanager:1.0.1'

    // Firebase
    implementation 'com.google.firebase:firebase-messaging:24.1.2'
    implementation 'com.google.firebase:firebase-config-ktx:22.1.2'
    implementation 'com.google.firebase:firebase-crashlytics-ktx:19.4.4'
    implementation 'com.google.firebase:firebase-analytics-ktx:22.5.0'
    implementation 'com.google.firebase:firebase-messaging:24.1.2'

    // Recaptcha
    implementation 'com.google.android.recaptcha:recaptcha:18.7.1'

    // Adjust
    implementation 'com.adjust.sdk:adjust-android:4.38.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    implementation 'com.google.android.gms:play-services-analytics:18.1.1'

    // Mixpanel
    implementation 'com.mixpanel.android:mixpanel-android:8.2.0'

    // Misc
    implementation 'com.github.scottyab:showhidepasswordedittext:0.8'

    // Websockets
    implementation 'com.neovisionaries:nv-websocket-client:2.14'

    // LaunchDarkly
    implementation 'com.launchdarkly:launchdarkly-android-client-sdk:5.8.0'

    // Datadog
    implementation 'com.datadoghq:dd-sdk-android:1.19.3'

    // Hilt
    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-compiler:$hilt_version"

    // Androidx support for shared preferences
    implementation 'androidx.preference:preference-ktx:1.2.1'

    // Keyboard visibility
    implementation 'net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC3'

    // Zywell printer support
    implementation files('libs/PosPrinterSDK.jar')

    // Rotate layout
    implementation 'com.github.rongi:rotate-layout:v3.0.0'

    // Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    // Barcode scanner
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    // Room
    implementation "androidx.room:room-ktx:$room_version"
    implementation "androidx.room:room-runtime:$room_version"
    kapt "androidx.room:room-compiler:$room_version"

    // Lottie animations
    implementation 'com.airbnb.android:lottie:6.5.2'

    // Phone number
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.26'

    //debugImplementation because LeakCanary should only run in debug builds.
    //debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.11'

}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-kapt'

