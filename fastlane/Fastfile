# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  
  desc "Submit a new Staging Build to Crashlytics Beta"
  lane :stage do
    changelog = prompt(text: "Build notes: ")
    gradle(
      task: 'assemble',
      build_type: 'release',
      flavor: 'stage'
    )
    
    firebase_app_distribution(
      app: "1:494383230801:android:b7f5965f99d3994b5bed70", 
      groups: "clementine-staging-testers",
      firebase_cli_path: "/usr/local/bin/firebase",
      release_notes: changelog
    )

  end

  desc "Submit a new Production Build to Crashlytics Beta"
  lane :prod do
    gradle(
      task: 'assemble',
      build_type: 'release',
      flavor: 'prod'
    )

    firebase_app_distribution(
      app: "1:539184489510:android:22db4ea08b5248946cb2e7", 
      groups: "clementine-testers",
      firebase_cli_path: "/usr/local/bin/firebase"
    )
  
  end
end
